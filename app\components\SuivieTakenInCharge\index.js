import React, { useEffect, useState } from 'react';
import moment from 'moment';
import { Link, useHistory } from 'react-router-dom';

import { createStructuredSelector } from 'reselect';
import { useDispatch, useSelector } from 'react-redux';
import { useInjectReducer, useInjectSaga } from 'redux-injectors';
import listStyles from 'Css/list.css';

import GenericFilter from 'containers/Common/Filter/GenericFilter';
import tagStyles from 'Css/tag.css';
import {
  makeSelectServices,
  makeSelectStatuses,
} from 'containers/TakenInCharge/TakenInCharges/selectors';
import reducer from 'containers/TakenInCharge/TakenInCharges/reducer';
import saga from 'containers/TakenInCharge/TakenInCharges/saga';
import { makeSelectActiveServices } from 'containers/Service/ListService/selectors';
import serviceReducer from 'containers/Service/ListService/reducer';
import serviceSaga from 'containers/Service/ListService/saga';
import { ServiceTypeEnum } from 'containers/Service/ListService/enum';
import { fetchActiveServicesRequest } from 'containers/Service/ListService/actions';
import operationTakenInChargeReducer from './reducer';
import { getListOperationsTakenInCharge } from './actions';
import operationTakenInChargeSaga from './saga';
import {
  makeSelectLoadingOperationsTakenInCharge,
  makeSelectOperationChangedStatus,
  makeSelectOperationChangedStatusError,
  makeSelectOperationChangedStatusLoading,
  makeSelectOperationsTakenInCharge,
} from './selectors';
import CustomPagination from '../Common/CustomPagination';
import DataTable from '../Common/DataTable';
import { VIEW_ICON } from '../Common/ListIcons/ListIcons';

const formatDate = date => moment(date).format('DD/MM/YYYY');

const getTag = status => {
  if (status === 'Planifié') {
    return tagStyles.tagBlue;
  }
  if (status === 'Exécuté') {
    return tagStyles.tagGreen;
  }
  if (status === 'Réservé') {
    return tagStyles.tagYellow;
  }
  return tagStyles.tagGrey;
};

const stateSelector = createStructuredSelector({
  operationsTakenInChargeLoading: makeSelectLoadingOperationsTakenInCharge,
  operationsTakenInChargeData: makeSelectOperationsTakenInCharge,
  services: makeSelectServices,
  statuses: makeSelectStatuses,
  changedOperationStatus: makeSelectOperationChangedStatus,
  changedOperationStatusLoading: makeSelectOperationChangedStatusLoading,
  changedOperationStatusError: makeSelectOperationChangedStatusError,
});

const pageSize = 4;

const state = 'suiviOperation';
const key = 'takenInCharge';
const KeyServices = 'serviceList';

const omdbSelector2 = createStructuredSelector({
  activeServices: makeSelectActiveServices,
});

export default function SuivieTakenInCharge(props) {
  useInjectReducer({ key: state, reducer: operationTakenInChargeReducer });
  useInjectSaga({ key: state, saga: operationTakenInChargeSaga });
  useInjectReducer({ key, reducer });
  useInjectSaga({ key, saga });

  useInjectReducer({
    key: KeyServices,
    reducer: serviceReducer,
  });

  useInjectSaga({ key: KeyServices, saga: serviceSaga });
  const dispatch = useDispatch();
  const {
    operationsTakenInChargeData,
    operationsTakenInChargeLoading,
  } = useSelector(stateSelector);
  const { activeServices } = useSelector(omdbSelector2);
  const [rows, setRows] = useState([]);
  const [openOperationId, setOperationId] = useState('');
  const history = useHistory();

  useEffect(() => {
    dispatch(fetchActiveServicesRequest(ServiceTypeEnum.KAFALAT));
  }, []);
  let activeServicesList = null;
  
  useEffect(() => {
    if (activeServices) {
      activeServicesList = activeServices.map(activeService => ({
        value: activeService.id,
        label: activeService.name,
      }));
    }
  }, [activeServices]);

  const [currentPage, setCurrentPage] = useState(1);

  useEffect(() => {
    if (operationsTakenInChargeData) {
      setRows(operationsTakenInChargeData);
    }
  }, [operationsTakenInChargeData]);
  const [principalInputsConfig, setPrincipalInputsConfig] = useState([
    {
      field: 'searchByStatus',
      type: 'select',
      placeholder: 'Statut operation',
      options: [
        { value: 'Planifié', label: 'Planifié' },
        { value: 'Exécuté', label: 'Exécuté' },
        { value: 'Clôturé', label: 'Clôturé' },
        { value: 'Réservé', label: 'Réservé' },
      ],
      widthStyles: {
        width: 250,
      },
    },
    {
      field: 'searchByServiceId',
      type: 'select',
      placeholder: 'Service',
      options:
        activeServices &&
        activeServices.map(activeService => ({
          value: activeService.id,
          label: activeService.name,
        })),
      widthStyles: {
        width: 250,
      },
    },

    {
      field: 'searchByNameDonor',
      type: 'text',
      placeholder: 'Nom Donateur',
    },
    // beneficiary name
    {
      field: 'searchByBeneficiary',
      type: 'text',
      placeholder: 'Nom Bénéficiaire',
    },
  ]);

  const [filterValues, setFilterValues] = useState({
    searchByNameDonor: '',
    searchByNameDonorAr: '',
    searchByBeneficiary: '',
    searchByStatus: '',
    minPlanningDate: '',
    maxPlanningDate: '',
    minExecutionDate: '',
    maxExecutionDate: '',
    minClosureDate: '',
    maxClosureDate: '',
    searchByServiceId: '',
    searchByStatusId: '',
  });

  const handleResetFilterComplete = () => {
    setFilterValues({
      searchByNameDonor: '',
      searchByNameDonorAr: '',
      searchByBeneficiary: '',
      searchByStatus: '',
      minPlanningDate: '',
      maxPlanningDate: '',
      minExecutionDate: '',
      maxExecutionDate: '',
      minClosureDate: '',
      maxClosureDate: '',
      searchByServiceId: '',
      searchByStatusId: '',
    });
  };

  const columns = [
    {
      field: 'code',
      headerName: "Code d'opération",
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
    {
      field: 'nameBeneficiaryComplet',
      headerName: 'Bénéficiaire',

      flex: 1,
      headerAlign: 'center',
      align: 'center',
      renderCell: params => (
        <Link
          to={{
            pathname: `beneficiaries/fiche/${params.row.operation.beneficiaryId}/info`,
          }}
        >
          {params.row.nameBeneficiaryComplet}
        </Link>
      ),
    },
    {
      field: 'nameDonorComplet',
      headerName: 'Donateur',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
      renderCell: params => (
        <Link
          to={{
            pathname: `donors/fiche/${params.row.operation.donorId}/info`,
          }}
        >
          {params.row.nameDonorComplet}
        </Link>
      ),
    },
    {
      field: 'service',
      headerName: 'Service',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
    {
      field: 'amount',
      headerName: 'Montant (DH)',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
    {
      field: 'managementFees',
      headerName: 'Frais (%) ',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
    {
      field: 'planningDate',
      headerName: 'Date de planification',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
    {
      field: 'executionDate',
      headerName: "Date d'éxecution",
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
    {
      field: 'statusOperation',
      headerName: 'Statut',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
      renderCell: params => (
        <span className={getTag(params.value)}>{params.value}</span>
      ),
    },
    {
      field: 'actions',
      headerName: 'Actions',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
      renderCell: params => (
        <>
          <input
            type="image"
            className="p-2"
            src={VIEW_ICON}
            width="40px"
            height="40px"
            onClick={() => {
              const entityUrl = `/takenInCharges/fiche/${params.row.operation.takenInChargeId}/planification`;
              if (params.row.operation.id) {
                setOperationId(params.row.operation.id);

                history.push({
                  pathname: entityUrl,
                  state: { openOperationId: params.row.operation.id },
                });
              }
            }}
            title="consulter"
          />
        </>
      ),
    },
  ];

  const getStatusOperation = operation => {
    if (operation.closureDate) {
      return 'Clôturé';
    }
    if (operation.executionDate && !operation.closureDate) {
      return 'Exécuté';
    }
    if (operation.reserved && !operation.executionDate) {
      return 'Réservé';
    }
    if (operation.planningDate && !operation.reserved) {
      return 'Planifié';
    }
  };

  const populateDataTable = () => {
    if (
      operationsTakenInChargeData &&
      operationsTakenInChargeData.content &&
      operationsTakenInChargeData.content.length > 0
    ) {
      setRows(() => {
        const newRows = operationsTakenInChargeData.content.map(operation => ({
          id: operation.id,
          code: operation.code,
          nameBeneficiaryComplet: operation.beneficiaryName,
          nameDonorComplet: operation.donorName,
          managementFees: operation.managementFees,
          amount: operation.amount,

          executionDate: operation.executionDate
            ? formatDate(operation.executionDate)
            : '-',

          planningDate: operation.planningDate
            ? formatDate(operation.planningDate)
            : '-',
          statusOperation: operation.status ? operation.status : '-',
          service: operation.services ? operation.services.name : '-',
          operation,
        }));

        return newRows;
      });
    }
  };

  useEffect(() => {
    populateDataTable();
  }, [operationsTakenInChargeData]);

  const handleApplyFilter = filters => {
    dispatch(getListOperationsTakenInCharge(0, filters));
  };

  useEffect(() => {
    dispatch(getListOperationsTakenInCharge(0));
  }, []);

  const handlePageChange = pageNumber => {
    setCurrentPage(pageNumber - 1);
    setFilterValues(prevFilterValues => {
      dispatch(
        getListOperationsTakenInCharge(pageNumber - 1, prevFilterValues),
      );
      return prevFilterValues;
    });
  };

  return (
    <div className={listStyles.backgroundStyle}>
      <GenericFilter
        className="col-12"
        principalInputsConfig={principalInputsConfig}
        onApplyFilter={handleApplyFilter}
        filterValues={filterValues}
        setFilterValues={setFilterValues}
        onResetFilterComplete={handleResetFilterComplete}
      />
      <div className="sub-container">
        {operationsTakenInChargeLoading ? (
          <div className="d-flex justify-content-center pb-3">
            <div
              className="spinner-border"
              style={{ width: '5rem', height: '5rem' }}
              role="status"
            >
              <span className="sr-only">Loading...</span>
            </div>
          </div>
        ) : (
          <div>
            <DataTable
              rows={rows}
              columns={columns}
              fileName={`Liste de Suivie des Kafalats, ${new Date().toLocaleString()}`}
              totalElements={
                operationsTakenInChargeData
                  ? operationsTakenInChargeData.totalElements
                  : 0
              }
              numberOfElements={
                operationsTakenInChargeData
                  ? operationsTakenInChargeData.numberOfElements
                  : 0
              }
              pageable={
                operationsTakenInChargeData
                  ? operationsTakenInChargeData.pageable
                  : null
              }
            />
            <div className="justify-content-center mt-3">
              {operationsTakenInChargeData && (
                <CustomPagination
                  totalElements={operationsTakenInChargeData.numberOfElements}
                  totalCount={
                    operationsTakenInChargeData
                      ? operationsTakenInChargeData.totalPages
                      : 1
                  }
                  pageSize={
                    operationsTakenInChargeData &&
                    operationsTakenInChargeData.pageable &&
                    operationsTakenInChargeData.pageable.pageSize
                      ? operationsTakenInChargeData.pageable.pageSize
                      : pageSize
                  }
                  currentPage={
                    operationsTakenInChargeData
                      ? operationsTakenInChargeData.number + 1
                      : currentPage
                  }
                  onPageChange={handlePageChange}
                />
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
