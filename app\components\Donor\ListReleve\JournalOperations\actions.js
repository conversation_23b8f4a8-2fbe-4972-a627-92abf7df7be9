import {
  LOAD_RELEVE_DONOR,
  LOAD_RELEVE_DONOR_ERROR,
  LOAD_RELEVE_DONOR_SUCCESS,
  LOAD_CANAL_DONATIONS,
  LOAD_CANAL_DONATIONS_SUCCESS,
  LOAD_CANAL_DONATIONS_ERROR,
  LOAD_SERVICECATEGORIES,
  LOAD_SERVICECATEGORIES_SUCCESS,
  LOAD_SERVICECATEGORIES_ERROR,
  LOAD_SERVICES_ERROR,
  LOAD_SERVICES_SUCCESS,
  LOAD_SERVICES,
} from './constants';

export function loadReleveDonor(id) {
  return {
    type: LOAD_RELEVE_DONOR,
    id,
  };
}

export function releveDonorLoaded(releves) {
  return {
    type: LOAD_RELEVE_DONOR_SUCCESS,
    releves,
  };
}

export function releveDonorLoadingError(error) {
  return {
    type: LOAD_RELEVE_DONOR_ERROR,
    error,
  };
}

export function loadCanalDonations(search) {
  return {
    type: LOAD_CANAL_DONATIONS,
    search,
  };
}

export function canalDonationsLoaded(canalDonations) {
  return {
    type: LOAD_CANAL_DONATIONS_SUCCESS,
    canalDonations,
  };
}

export function canalDonationsLoadingError(error) {
  return {
    type: LOAD_CANAL_DONATIONS_ERROR,
    error,
  };
}

export function loadServiceCategories(search) {
  return {
    type: LOAD_SERVICECATEGORIES,
    search,
  };
}

export function serviceCategoriesLoaded(serviceCategories) {
  return {
    type: LOAD_SERVICECATEGORIES_SUCCESS,
    serviceCategories,
  };
}

export function serviceCategoriesLoadingError(error) {
  return {
    type: LOAD_SERVICECATEGORIES_ERROR,
    error,
  };
}

export function loadServices(search) {
  return {
    type: LOAD_SERVICES,
    search,
  };
}

export function servicesLoaded(services) {
  return {
    type: LOAD_SERVICES_SUCCESS,
    services,
  };
}

export function servicesLoadingError(error) {
  return {
    type: LOAD_SERVICES_ERROR,
    error,
  };
}
