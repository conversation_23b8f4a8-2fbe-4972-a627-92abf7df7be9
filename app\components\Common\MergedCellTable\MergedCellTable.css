.data-grid-container {
  height: 100%;
  width: 100%;
}

.data-grid-container .MuiDataGrid-root {
  background-color: white;
  border: 2px solid white;
  border-radius: 10px;
  color: #3b3b3b;
  padding: 0 1%;
}

.data-grid-container .MuiDataGrid-columnsContainer {
  text-align: left;
}

.data-grid-container .MuiDataGrid-header {
  display: flex;
  justify-content: space-between;
  align-items: left;
  margin: 2% 1% 2% 1%;
}

.data-grid-container .MuiDataGrid-cell {
  border-right: 1px solid #ccc;
  border-bottom: 1px solid #ccc;
  text-align: left;
  font-family: 'Nunito', 'Helvetica Neue', Helvetica, Arial, sans-serif;
}

.data-grid-container .MuiDataGrid-colCellTitle {
  text-align: left;
  font-family: 'Nunito', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  display: flex;
  justify-content: center;
  font-weight: bold !important;
}

.data-grid-container .MuiDataGrid-cell--selected {
  outline: none !important;
}

[data-mui-testid="cell-selected"] {
  outline: none !important;
}

.custom-data-grid-footer {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  height: 100%;
  padding-right: 16px;
}

.MuiDataGrid-footer {
  position: absolute;
  bottom: 0;
  width: 100%;
  background-color: #f0f0f0;
  padding: 8px 16px;
  box-shadow: 0 -2px 4px rgba(0, 0, 0, 0.1);
}
