import React, { useEffect, useState } from 'react';

import sideBarStyles from 'Css/sideBar.css';
import tagStyles from 'Css/tag.css';
import numberStyle from 'Css/familyNumber.css';
import GetBeneficiariesInformationsFromFamily from '../../Common/Functions/GetBeneficiariesInformationsFromFamily';
import { useHistory } from 'react-router-dom';
import { Alert } from 'react-bootstrap';
import { useDispatch, useSelector } from 'react-redux';
import { useInjectReducer, useInjectSaga } from 'redux-injectors';
import {
  addFamilyMember,
  resetMember,
} from '../../../../containers/Family/FamilyAdd/actions';
import omdbSaga from '../../../../containers/Family/FamilyAdd/saga';
import { createStructuredSelector } from 'reselect';
import { makeSelectSuccess } from '../../../../containers/Family/FamilyAdd/selectors';
import reducer from '../../../../containers/Family/FamilyAdd/reducer';
import { setAddMemberSuccess } from '../../../../containers/Family/FamilyProfile/actions';
import EditFamilyModal from './EditFamilyModal';
import { EDIT_ICON } from '../../../Common/ListIcons/ListIcons';
import { hasRoleAssistantWithZone } from '../../../../utils/hasAccess';

const key = 'familyAddContainer';

const omdbSelector = createStructuredSelector({
  success: makeSelectSuccess,
});

const SideBar = props => {
  useInjectReducer({
    key,
    reducer,
  });
  useInjectSaga({ key, saga: omdbSaga });

  const { success } = useSelector(omdbSelector);

  const dispatch = useDispatch();

  const image = require('../../../../images/user.png');
  const history = useHistory();

  const [alert, setAlert] = useState(null);
  const [showAlert, setShowAlert] = useState(false);
  const zoneAssistantId = hasRoleAssistantWithZone();

  const [showModal, setShowModal] = useState(false);
  const [formValues, setFormValues] = useState({
    familyId: props.data && props.data.id ? props.data.id : '',
    phoneNumberFamily:
      props.data && props.data.phoneNumberFamily
        ? props.data.phoneNumberFamily
        : '',
    generalCommentFamily: (props.data && props.data.generalCommentFamily) || '',
    city: (props.data && props.data.city && props.data.city) || '',
    region:
      (props.data &&
        props.data.city &&
        props.data.city.region &&
        props.data.city.region) ||
      '',
    country:
      (props.data &&
        props.data.city &&
        props.data.city.region &&
        props.data.city.region.country &&
        props.data.city.region.country) ||
      '',
    addressFamily: (props.data && props.data.addressFamily) || '',
    addressFamilyAr: (props.data && props.data.addressFamilyAr) || '',
    accommodationType:
      (props.data &&
        props.data.accommodationType &&
        props.data.accommodationType.id) ||
      '',
    accommodationNature:
      (props.data &&
        props.data.accommodationNature &&
        props.data.accommodationNature.id) ||
      '',
    zone: (props.data && props.data.zoneDTO && props.data.zoneDTO.id) || '',
    sousZone:
      (props.data && props.data.sousZoneDTO && props.data.sousZoneDTO.id) || '',
  });

  useEffect(() => {
    if (props.data) {
      setFormValues({
        familyId: props.data.id || '',
        phoneNumberFamily: props.data.phoneNumberFamily || '',
        generalCommentFamily: props.data.generalCommentFamily || '',
        city: props.data.city || '',
        region: props.data.city.region || '',
        country: props.data.city.region.country || '',
        addressFamily: props.data.addressFamily || '',
        addressFamilyAr: props.data.addressFamilyAr || '',
        accommodationType:
          (props.data.accommodationType && props.data.accommodationType.id) ||
          '',
        accommodationNature:
          (props.data.accommodationNature &&
            props.data.accommodationNature.id) ||
          '',
        zone: (props.data.zoneDTO && props.data.zoneDTO.id) || '',
        sousZone: (props.data.sousZoneDTO && props.data.sousZoneDTO.id) || '',
      });
    }
  }, [props.data]);

  useEffect(() => {
    if (success) {
      //setShowAlert(true);

      dispatch(setAddMemberSuccess(true));
      dispatch(resetMember());

      setAlert(
        <Alert
          className="alert-style"
          variant="success"
          onClose={() => setShowAlert(false)}
          dismissible
        >
          Membre modifié avec succès
        </Alert>,
      );
    }
  }, [success]);

  const handleShowModal = () => setShowModal(true);
  const handleCloseModal = () => {
    setShowModal(false);
    setFormValues({
      familyId: props.data && props.data.id ? props.data.id : '',
      phoneNumberFamily:
        props.data && props.data.phoneNumberFamily
          ? props.data.phoneNumberFamily
          : '',
      generalCommentFamily:
        props.data && props.data.generalCommentFamily
          ? props.data.generalCommentFamily
          : '',
      city: props.data && props.data.city ? props.data.city : '',
      region:
        props.data && props.data.city && props.data.city.region
          ? props.data.city.region
          : '',
      country:
        props.data &&
        props.data.city &&
        props.data.city.region &&
        props.data.city.region.country
          ? props.data.city.region.country
          : '',
      addressFamily:
        props.data && props.data.addressFamily ? props.data.addressFamily : '',
      addressFamilyAr:
        props.data && props.data.addressFamilyAr
          ? props.data.addressFamilyAr
          : '',
      accommodationType:
        props.data &&
        props.data.accommodationType &&
        props.data.accommodationType.id
          ? props.data.accommodationType.id
          : '',
      accommodationNature:
        props.data &&
        props.data.accommodationNature &&
        props.data.accommodationNature.id
          ? props.data.accommodationNature.id
          : '',
      zone:
        props.data && props.data.zoneDTO && props.data.zoneDTO.id
          ? props.data.zoneDTO.id
          : '',
      sousZone:
        props.data && props.data.sousZoneDTO && props.data.sousZoneDTO.id
          ? props.data.sousZoneDTO.id
          : '',
    });
  };

  const validateForm = values => {
    const {
      phoneNumberFamily,
      addressFamily,
      addressFamilyAr,
      city,
      accommodationType,
      accommodationNature,
      zone,
    } = values;
    if (
      !phoneNumberFamily ||
      !addressFamily ||
      !addressFamilyAr ||
      !city ||
      !accommodationType ||
      !accommodationNature ||
      !zone
    ) {
      return false;
    }
    return true;
  };

  const handleConfirm = values => {
    if (!validateForm(values)) {
      return;
    }

    const payload = {
      familyId: values.familyId,
      generalComment: values.generalCommentFamily,
      phoneNumber: values.phoneNumberFamily,
      address: values.addressFamily,
      addressAr: values.addressFamilyAr,
      cityId: formValues.city.id,
      accommodationTypeId: values.accommodationType,
      accommodationNatureId: values.accommodationNature,
      zoneId: values.zone,
      sousZoneId: values.sousZone,
    };

    dispatch(addFamilyMember(payload));

    handleCloseModal();
  };

  const handleLocationChange = locationValues => {
    setFormValues({
      ...formValues,
      ...locationValues,
    });
  };

  let family;
  let top = null;
  let text2 = null;
  let pere = null;
  let mere = null;
  let tutor = null;
  let familyName = null;
  let familySize = 0;
  let beneficiariesInfo = null;

  if (props.data) {
    family = props.data;

    if (family && family.familyMembers) {
      familySize = family.familyMembers.length;
      beneficiariesInfo = GetBeneficiariesInformationsFromFamily(
        family.familyMembers,
      );

      pere = family.familyMembers.filter(
        member => member.familyRelationship.name == 'Père',
      )[0];
      mere = family.familyMembers.filter(
        member => member.familyRelationship.name == 'Mère',
      )[0];
      tutor = family.familyMembers.filter(
        member => member.tutor == true && member.person.deceased === false,
      )[0];

      // Set familyName
      if (pere && pere.person.lastName) familyName = pere.person.lastName;
      else if (mere && mere.person.lastName) familyName = mere.person.lastName;
      else if (tutor && tutor.person.lastName)
        familyName = tutor.person.lastName;
    }

    top = (
      <div className={sideBarStyles.topCard}>
        <div className={`${sideBarStyles.top}`}>

          <div className="d-flex align-items-start">
          <img
            src={image}
            className={`rounded-circle  ${sideBarStyles.imgBorder}`}
            style={{
              width: '100px', // Set a fixed width
              height: '100px', // Set a fixed height
              objectFit: 'cover', // Ensures the image maintains aspect ratio
              borderRadius: '50%', // Ensures it remains circular
              border: '2px solid #ddd', // Optional: to add a border
            }}
          />
          <input
            type="image"
            src={EDIT_ICON}
            title="modifier les informations communes de la famille"
            width="20px"
            height="20px"
            onClick={handleShowModal}
          />
        </div>
          <div className={sideBarStyles.info}>
            <h5>Famille {familyName}</h5>
            <p>{family.code}</p>
          </div>
        </div>
      </div>
    );

    text2 = (
      <div className={sideBarStyles.data}>
        <p className="mt-3">
          <span className={` ${numberStyle.numberBlue}`}>{familySize}</span>
        </p>
        <p className="mt-3">
          <span className={` ${numberStyle.numberGreen}`}>
            {beneficiariesInfo.count}
          </span>
        </p>
        <p className="mt-3">
          <span className={` ${numberStyle.numberYellow}`}>
            {beneficiariesInfo.countCandidate}
          </span>
        </p>
      </div>
    );
  }

  return (
    <>
      <div className={sideBarStyles.sideBar}>

        {top}
        <div className={`${sideBarStyles.infosHeader} mt-5 px-2 text-center`}>
          <h5>Informations communes de la famille</h5>
          <EditFamilyModal
            show={showModal}
            onClose={handleCloseModal}
            initialValues={formValues}
            onSubmit={handleConfirm}
            handleLocationChange={handleLocationChange}
            zoneAssistantId={zoneAssistantId}
          />
        </div>

        <div className={sideBarStyles.text}>
          {/**/}
          <div
            className={sideBarStyles.infoGrid}
            style={{
              display: 'grid',
              gridTemplateColumns: 'max-content 1fr',
              gap: '8px 16px',
              alignItems: 'start',
              marginLeft: '14px',
              marginRight: '14px',
            }}
          >
            <p style={{ whiteSpace: 'nowrap', fontWeight: 'bold' }}>
              Téléphone :
            </p>
            <p>
              {family && family.phoneNumberFamily
                ? family.phoneNumberFamily
                : ' -'}
            </p>

            <p style={{ whiteSpace: 'nowrap', fontWeight: 'bold' }}>
              Adresse :
            </p>
            <p>
              {family && family.addressFamily ? family.addressFamily : ' -'}
            </p>
            <p style={{ whiteSpace: 'nowrap', fontWeight: 'bold' }}>Ville :</p>
            <p>
              {family && family.city && family.city.name
                ? family.city.name
                : ' -'}
            </p>
            <p style={{ whiteSpace: 'nowrap', fontWeight: 'bold' }}>Région :</p>
            <p>
              {family &&
              family.city &&
              family.city.region &&
              family.city.region.name
                ? family.city.region.name
                : ' -'}
            </p>
            <p style={{ whiteSpace: 'nowrap', fontWeight: 'bold' }}>Pays :</p>
            <p>
              {family &&
              family.city &&
              family.city.region &&
              family.city.region &&
              family.city.region.country &&
              family.city.region.country.nameFrench
                ? family.city.region.country.nameFrench
                : ' -'}
            </p>
            <p style={{ whiteSpace: 'nowrap', fontWeight: 'bold' }}>Zone :</p>
            <p>
              {family && family.zoneDTO && family.zoneDTO.name
                ? family.zoneDTO.name
                : ' -'}
            </p>
            <p style={{ whiteSpace: 'nowrap', fontWeight: 'bold' }}>
              Sous-Zone :
            </p>
            <p>
              {family && family.sousZoneDTO && family.sousZoneDTO.name
                ? family.sousZoneDTO.name
                : ' -'}
            </p>
            <p style={{ whiteSpace: 'nowrap', fontWeight: 'bold' }}>
              Type d'hébergement :
            </p>
            <p>
              {family &&
              family.accommodationNature &&
              family.accommodationNature.name
                ? family.accommodationNature.name
                : ' -'}
            </p>
            <p style={{ whiteSpace: 'nowrap', fontWeight: 'bold' }}>
              Nature de l'hébergement :
            </p>
            <p>
              {family &&
              family.accommodationType &&
              family.accommodationType.name
                ? family.accommodationType.name
                : ' -'}
            </p>
            <p style={{ whiteSpace: 'nowrap', fontWeight: 'bold' }}>
              Commentaire :
            </p>
            <p style={{ textAlign: 'justify' }}>
              {family && family.generalCommentFamily
                ? family.generalCommentFamily
                : ' -'}
            </p>
          </div>
        </div>
        <hr className={sideBarStyles.hr} />
        <div className={sideBarStyles.text}>
          <div className={sideBarStyles.label}>
            <p className="mt-3">Nombre des membres:</p>
            <p className="mt-3">Nombre des bénéficiaires:</p>
            <p className="mt-3">Nombre des candidats:</p>
          </div>
          {text2}
        </div>
      </div>

      <button
        onClick={() => {
          history.push('/families');
        }}
        className={`w-100 btn-style secondary`}
      >
        Quitter
      </button>
    </>
  );
};

export default SideBar;
