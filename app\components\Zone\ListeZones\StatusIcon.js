import React from 'react';

const StatusIcon = ({ isActive, onClick }) => {
  return (
    <i
      className={isActive ? 'fas fa-toggle-on p-2' : 'fas fa-toggle-off p-2'}
      onClick={onClick}
      title={isActive ? 'Actif' : 'Inactif'}
      style={{
        fontSize: '27px',
        cursor: 'pointer',
        // width: '40px',
        // height: '40px',
        color: isActive ? 'green' : 'red',
      }}
    />
  );
};

export default StatusIcon;
