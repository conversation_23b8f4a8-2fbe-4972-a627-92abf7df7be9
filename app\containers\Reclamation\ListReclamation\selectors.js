import { createSelector } from 'reselect';
import { initialState } from './reducer';

const selectReclamationDomain = state => state.reclamationList || initialState;

const makeSelectReclamations = () =>
  createSelector(
    selectReclamationDomain,
    substate => substate.reclamations,
  );

const makeSelectLoading = () =>
  createSelector(
    selectReclamationDomain,
    substate => substate.loading,
  );

const makeSelectError = () =>
  createSelector(
    selectReclamationDomain,
    substate => substate.error,
  );



export {
  selectReclamationDomain,
  makeSelectReclamations,
  makeSelectLoading,
  makeSelectError,
};
