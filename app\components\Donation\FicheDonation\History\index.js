import React, { useEffect, useState } from 'react';
import moment from 'moment';
import { Link, useParams } from 'react-router-dom';
import DataTable from 'components/Common/DataTable';
import CustomPagination from 'components/Common/CustomPagination';
import { useDispatch, useSelector } from 'react-redux';
import { useInjectReducer, useInjectSaga } from 'redux-injectors';
import { createStructuredSelector } from 'reselect';
import { loadDonationHistory } from 'containers/Donation/FicheDonation/actions';
import {
  makeSelectDonationHistory,
  makeSelectError,
} from 'containers/Donation/FicheDonation/selectors';
import reducer from 'containers/Donation/FicheDonation/reducer';
import { Alert } from 'react-bootstrap';
import 'bootstrap/dist/css/bootstrap.min.css';
import saga from 'containers/Donation/FicheDonation/saga';
import stylesList from 'Css/profileList.css';
import list from 'Css/profileList.css';
import DonationHistoryType from './DonationHistoryType';

const key = 'donationFich';

const formatDate = date => moment(date).format('DD/MM/YYYY');

const omdbSelector = createStructuredSelector({
  donationHistory: makeSelectDonationHistory,
  error: makeSelectError,
});

export default function DonationHistory(props) {
  const data = props.data;
  const dispatch = useDispatch();
  useInjectReducer({ key, reducer });
  useInjectSaga({ key, saga });
  const { id } = useParams();

  const [currentPage, setCurrentPage] = useState(1);
  const pageSize = 5;

  const { donationHistory, error } = useSelector(omdbSelector);

  useEffect(() => {
    if (data) {
      dispatch(loadDonationHistory(id));
    }
  }, [data, dispatch, id]);

  const listHistory = donationHistory
    ? [...donationHistory]
        .sort((a, b) => new Date(b.executionDate) - new Date(a.executionDate))
        .slice((currentPage - 1) * pageSize, currentPage * pageSize)
        .map(item => ({
          id: item.id,
          date: item.executionDate ? formatDate(item.executionDate) : '',
          amount: item.amount,
          amountInitial: item.amountInitial,
          amountReserved: item.amountReserved,
          newService: item.newServiceName,
          oldService: item.oldServiceName,
          aide: item.aideComplementaireName,
          newServiceId: item.newServiceId,
          oldServiceId: item.oldServiceId,
          aideId: item.aideComplementaireId,
          type: item.type,
          description:
            item.type === DonationHistoryType.EXECUTED ? (
              <>
                Un montant initial de <strong>{item.amountInitial} Dh</strong>{' '}
                dont <strong>{item.amountReserved} Dh</strong> a été exécuté,
                dans le cadre de la campagne{' '}
                <strong>
                  <Link
                    to={`/aide-complementaire/fiche/${item.aideComplementaireId}/info`}
                  >
                    {item.aideComplementaireName}
                  </Link>
                </strong>
              </>
            ) : item.type === DonationHistoryType.REMAINING ? (
              <>
                Réaffectation de <strong>{item.amount} Dh</strong> restant de la
                campagne{' '}
                <strong>
                  <Link
                    to={`/aide-complementaire/fiche/${item.aideComplementaireId}/info`}
                  >
                    {item.aideComplementaireName}
                  </Link>
                </strong>{' '}
                du ancien service <strong>{item.oldServiceName}</strong> au
                nouveau service <strong>{item.newServiceName}</strong>
              </>
            ) : (
              ''
            ),
        }))
    : [];

  const columns = [
    {
      field: 'date',
      headerName: "Date de l'opération",
      flex: 0.25,
      headerAlign: 'center',
      align: 'center',
    },
    {
      field: 'type',
      headerName: 'Type',
      flex: 0.25,
      headerAlign: 'center',
      align: 'center',
    },
    {
      field: 'description',
      headerName: 'Description',
      flex: 1.5,
      headerAlign: 'center',
      align: 'center',
      renderCell: params => <div>{params.value}</div>,
    },
  ];

  return (
    <div className={`pb-5 ${stylesList.backgroundStyle}`}>
      {error && <Alert className="alert-style" variant="danger">Une erreur est survenue</Alert>}
      <div className={list.backgroudStyle}>
        <div className={list.global}>
          <div className={list.header}>
            <h4>Historique des lignes de donation restante</h4>
          </div>
          <DataTable
            rows={listHistory}
            columns={columns}
            fileName={`Historique des dons ${new Date().toLocaleString()}`}
          />
          {donationHistory && (
            <div className="justify-content-center my-4">
              <CustomPagination
                totalCount={Math.ceil(donationHistory.length / pageSize)}
                pageSize={pageSize}
                currentPage={currentPage}
                onPageChange={page => setCurrentPage(page)}
              />
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
