// Fetch General Dashboard
import { createSelector } from 'reselect';
import { initialState } from './reducer';


const selectDashboard = state => state.dashboardAll || initialState;

// Memoized selector for tags
const makeSelectGeneralDashboard = createSelector(
    selectDashboard,
    dashboardState => dashboardState.data || []
);

const makeSelectGeneralDashboardSuccess = createSelector(
    selectDashboard,
    dashboardState => dashboardState.success || false
);

const makeSelectGeneralDashboardLoading = createSelector(
    selectDashboard,
    dashboardState => dashboardState.loading || false
);

const makeSelectGeneralDashboardError = createSelector(
    selectDashboard,
    dashboardState => dashboardState.error || null
);

const makeSelectDonorsDashboard = createSelector(
    selectDashboard,
    dashboardState => dashboardState.data || []
);

const makeSelectDonorsDashboardSuccess = createSelector(
    selectDashboard,
    dashboardState => dashboardState.success || false
);

const makeSelectDonorsDashboardLoading = createSelector(
    selectDashboard,
    dashboardState => dashboardState.loading || false
);

const makeSelectDonorsDashboardError = createSelector(
    selectDashboard,
    dashboardState => dashboardState.error || null
);

const makeSelectBeneficiariesDashboard = createSelector(
    selectDashboard,
    dashboardState => dashboardState.data || []
);

const makeSelectBeneficiariesDashboardSuccess = createSelector(
    selectDashboard,
    dashboardState => dashboardState.success || false
);

const makeSelectBeneficiariesDashboardLoading = createSelector(
    selectDashboard,
    dashboardState => dashboardState.loading || false
);

const makeSelectBeneficiariesDashboardError = createSelector(
    selectDashboard,
    dashboardState => dashboardState.error || null
);

const makeSelectDonationsDashboard = createSelector(
    selectDashboard,
    dashboardState => dashboardState.data || []
);

const makeSelectDonationsDashboardSuccess = createSelector(
    selectDashboard,
    dashboardState => dashboardState.success || false
);

const makeSelectDonationsDashboardLoading = createSelector(
    selectDashboard,
    dashboardState => dashboardState.loading || false
);

const makeSelectDonationsDashboardError = createSelector(
    selectDashboard,
    dashboardState => dashboardState.error || null
);

const makeSelectKafalatDashboard = createSelector(
    selectDashboard,
    dashboardState => dashboardState.data || []
);

const makeSelectKafalatDashboardSuccess = createSelector(
    selectDashboard,
    dashboardState => dashboardState.success || false
);

const makeSelectKafalatDashboardLoading = createSelector(
    selectDashboard,
    dashboardState => dashboardState.loading || false
);

const makeSelectKafalatDashboardError = createSelector(
    selectDashboard,
    dashboardState => dashboardState.error || null
);

// Aide Dashboard
const makeSelectAideDashboard = createSelector(
    selectDashboard,
    dashboardState => dashboardState.data || []
);  

const makeSelectAideDashboardSuccess = createSelector(
    selectDashboard,
    dashboardState => dashboardState.success || false
);

const makeSelectAideDashboardLoading = createSelector(
    selectDashboard,
    dashboardState => dashboardState.loading || false
);

const makeSelectAideDashboardError = createSelector(
    selectDashboard,
    dashboardState => dashboardState.error || null
);

// EPS Dashboard
const makeSelectEPSDashboard = createSelector(
    selectDashboard,
    dashboardState => dashboardState.data || []
);

const makeSelectEPSDashboardSuccess = createSelector(
    selectDashboard,
    dashboardState => dashboardState.success || false
);

const makeSelectEPSDashboardLoading = createSelector(
    selectDashboard,
    dashboardState => dashboardState.loading || false
);

const makeSelectEPSDashboardError = createSelector(
    selectDashboard,
    dashboardState => dashboardState.error || null
);


























export { selectDashboard, 
    makeSelectGeneralDashboard, 
    makeSelectGeneralDashboardSuccess, 
    makeSelectGeneralDashboardLoading, 
    makeSelectGeneralDashboardError, 
    makeSelectDonorsDashboard, 
    makeSelectDonorsDashboardSuccess, 
    makeSelectDonorsDashboardLoading, 
    makeSelectDonorsDashboardError,
    makeSelectBeneficiariesDashboard,
    makeSelectBeneficiariesDashboardSuccess,
    makeSelectBeneficiariesDashboardLoading,
    makeSelectBeneficiariesDashboardError,
    makeSelectDonationsDashboard,
    makeSelectDonationsDashboardSuccess,
    makeSelectDonationsDashboardLoading,
    makeSelectDonationsDashboardError,
    makeSelectKafalatDashboard,
    makeSelectKafalatDashboardSuccess,
    makeSelectKafalatDashboardLoading,
    makeSelectKafalatDashboardError,
    makeSelectAideDashboard,
    makeSelectAideDashboardSuccess,
    makeSelectAideDashboardLoading,
    makeSelectAideDashboardError,
    makeSelectEPSDashboard,
    makeSelectEPSDashboardSuccess,
    makeSelectEPSDashboardLoading,
    makeSelectEPSDashboardError
};
