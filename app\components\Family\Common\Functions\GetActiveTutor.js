const GetActiveTutor = family => {
  let tutor = null;
  if (family && family.familyMembers) {
    let members = family.familyMembers;
    for (let i = 0; i < members.length; i++) {
      const member = members[i];
      const endDateValid =
        member.tutorEndDate === null ||
        new Date(member.tutorEndDate) >= new Date();

      if (
        members[i].tutor === true &&
        members[i].person.deceased === false &&
        endDateValid
        //checkIsTutor(members[i])
      ) {
        tutor = members[i];
        break;
      }
    }
  }
  return tutor;
};

export default GetActiveTutor;
