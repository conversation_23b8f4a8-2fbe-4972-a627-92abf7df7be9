import { createSelector } from 'reselect';
import { initialState } from './reducer';

const agendaRapportSelector = state => state.agendaRapport || initialState;

const makeSelectRapports = createSelector(
  agendaRapportSelector,
  stateSlice => stateSlice.rapports,
);

const makeSelectLoading = createSelector(
  agendaRapportSelector,
  stateSlice => stateSlice.loading,
);

const makeSelectError = createSelector(
  agendaRapportSelector,
  stateSlice => stateSlice.error,
);

const makeSelectTotalElements = createSelector(
  agendaRapportSelector,
  stateSlice => stateSlice.totalElements,
);

const makeSelectPageNumber = createSelector(
  agendaRapportSelector,
  stateSlice => stateSlice.pageNumber,
);

const makeSelectPageSize = createSelector(
  agendaRapportSelector,
  stateSlice => stateSlice.pageSize,
);

const makeSelecRapportViewSuccess = createSelector(
  agendaRapportSelector,
  stateSlice => stateSlice.successView,
);

const makeSelectRapportLoading = createSelector(
  agendaRapportSelector,
  stateSlice => stateSlice.rapportViewLoading,
);

const makeSelectNotifyLoading = createSelector(
  agendaRapportSelector,
  stateSlice => stateSlice.loadingNotify,
);
const makeSelectNotifyError = createSelector(
  agendaRapportSelector,
  stateSlice => stateSlice.errorNotify,
);

const makeSelectNotifySuccess = createSelector(
  agendaRapportSelector,
  stateSlice => stateSlice.successNotify,
);

const makeSelectDonors = createSelector(
  agendaRapportSelector,
  stateSlice => stateSlice.donors,
);

const makeSelectDonorLoading = createSelector(
  agendaRapportSelector,
  stateSlice => stateSlice.loadingDonor,
);

const makeSelectDonorError = createSelector(
  agendaRapportSelector,
  stateSlice => stateSlice.errorDonor,
);

const makeSelecRapportWithDonorViewSuccess = createSelector(
  agendaRapportSelector,
  stateSlice => stateSlice.successViewRapportWithDonor,
);

const makeSelecRapportWithDonorViewError = createSelector(
  agendaRapportSelector,
  stateSlice => stateSlice.errorViewRapportWithDonor,
);

const makeSelecRapportWithDonorViewLoading = createSelector(
  agendaRapportSelector,
  stateSlice => stateSlice.rapportWithDonorViewLoading,
);

const makeSelectLoadingPlanification = createSelector(
  agendaRapportSelector,
  stateSlice => stateSlice.loadingPlanificationAutomatic,
);

const makeSelectSuccessPlanification = createSelector(
  agendaRapportSelector,
  stateSlice => stateSlice.successPlanificationAutomatic,
);

const makeSelectErrorPlanification = createSelector(
  agendaRapportSelector,
  stateSlice => stateSlice.errorPlanificationAutomatic,
);

export {
  makeSelectRapports,
  makeSelectLoading,
  makeSelectError,
  makeSelectTotalElements,
  makeSelectPageNumber,
  makeSelectPageSize,
  makeSelecRapportViewSuccess,
  makeSelectRapportLoading,
  makeSelectNotifyLoading,
  makeSelectNotifyError,
  makeSelectNotifySuccess,
  makeSelectDonors,
  makeSelectDonorLoading,
  makeSelectDonorError,
  makeSelecRapportWithDonorViewSuccess,
  makeSelecRapportWithDonorViewError,
  makeSelecRapportWithDonorViewLoading,
  makeSelectLoadingPlanification,
  makeSelectSuccessPlanification,
  makeSelectErrorPlanification,
};
