import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import eye from 'images/icons/eye.svg';
import plus from 'images/icons/plus.svg';
import minus from 'images/icons/minus.svg';
import stylesList from '../../../../Css/profileList.css';
import styles from '../../../../Css/profileList.css';
import tagStyles from '../../../../Css/tag.css';
import Details from '../OperationsDetail';
import CustomPagination from 'components/Common/CustomPagination';
import { hasRoleAssistant } from '../../../../utils/hasAccess';

export default function TakenInCharge(props) {
  const Beneficiary = props.data;
  const [currentPage, setCurrentPage] = useState(1);
  const [expandedRows, setExpandedRows] = useState({}); // State to track expanded rows
  const pageSize = 5;

  const handleViewPlanning = id => {
    setExpandedRows(prev => ({
      ...prev,
      [id]: !prev[id], // Toggle the specific row
    }));
  };

  let listBeneficiaryTakenIncharges = [];
  let totalPlanned = 0;
  let totalReserved = 0;
  let totalExecuted = 0;
  let serviceName = '-';
  const isRoleAssistant = hasRoleAssistant();

  if (Beneficiary) {
    const BeneficiaryTakenIncharges = Beneficiary.takenInChargeBeneficiaries;
    if (BeneficiaryTakenIncharges) {
      const sortedBeneficiaryTakenIncharges =
        BeneficiaryTakenIncharges && BeneficiaryTakenIncharges.length > 0
          ? [...BeneficiaryTakenIncharges].sort((a, b) =>
              a.id < b.id ? 1 : -1,
            )
          : [];
      const startIndex = (currentPage - 1) * pageSize;
      const endIndex = Math.min(
        startIndex + pageSize,
        sortedBeneficiaryTakenIncharges.length,
      );
      const paginatedBeneficiaryTakenIncharges = sortedBeneficiaryTakenIncharges.slice(
        startIndex,
        endIndex,
      );
      listBeneficiaryTakenIncharges = paginatedBeneficiaryTakenIncharges.map(
        takenInCharge => {
          totalReserved = 0;
          totalPlanned = 0;
          totalExecuted = 0;

          if (
            takenInCharge.takenInCharge &&
            takenInCharge.takenInCharge.services
          ) {
            serviceName = takenInCharge.takenInCharge.services.name;
          }

          takenInCharge.takenInCharge.takenInChargeDonors &&
            takenInCharge.takenInCharge.takenInChargeDonors[0]
              .takenInChargeOperations &&
            takenInCharge.takenInCharge.takenInChargeDonors[0].takenInChargeOperations.map(
              operation => {
                if (operation.planningDate && !operation.reserved) {
                  totalPlanned += operation.amount;
                } else if (operation.reserved && !operation.executionDate) {
                  totalReserved += operation.amount;
                } else if (operation.executionDate) {
                  totalExecuted += operation.amount;
                }
              },
            );

          const getTag = status => {
            if (status === 'Inactif') {
              return tagStyles.tagRed;
            }
            if (status === 'Actif') {
              return tagStyles.tagGreen;
            }
            if (status === 'Fermé') {
              return tagStyles.tagGrey;
            }
            return tagStyles.tagGrey;
          };

          let donorName = <span>-</span>;
          if (
            takenInCharge.takenInCharge.takenInChargeDonors[0] &&
            takenInCharge.takenInCharge.takenInChargeDonors[0].donor.type ===
              'Physique'
          ) {
            donorName = isRoleAssistant ? (
              `${takenInCharge.takenInCharge.takenInChargeDonors[0].donor.firstName} ${takenInCharge.takenInCharge.takenInChargeDonors[0].donor.lastName}`
            ) : (
              <Link
                to={`/donors/fiche/${takenInCharge.takenInCharge.takenInChargeDonors[0].donor.id}/info`}
              >
                {`${takenInCharge.takenInCharge.takenInChargeDonors[0].donor.firstName} ${takenInCharge.takenInCharge.takenInChargeDonors[0].donor.lastName}`}
              </Link>
            );
          } else if (
            takenInCharge.takenInCharge.takenInChargeDonors[0] &&
            takenInCharge.takenInCharge.takenInChargeDonors[0].donor.type ===
              'Moral'
          ) {
            donorName = isRoleAssistant ? (
              takenInCharge.takenInCharge.takenInChargeDonors[0].donor.company
            ) : (
              <Link
                to={`/donors/fiche/${takenInCharge.takenInCharge.takenInChargeDonors[0].donor.id}/info`}
              >
                {
                  takenInCharge.takenInCharge.takenInChargeDonors[0].donor
                    .company
                }
              </Link>
            );
          } else if (
            takenInCharge.takenInCharge.takenInChargeDonors[0] &&
            takenInCharge.takenInCharge.takenInChargeDonors[0].donor.type ===
              'Anonyme'
          ) {
            donorName = isRoleAssistant ? (
              takenInCharge.takenInCharge.takenInChargeDonors[0].donor.name
            ) : (
              <Link
                to={`/donors/fiche/${takenInCharge.takenInCharge.takenInChargeDonors[0].donor.id}/info`}
              >
                {takenInCharge.takenInCharge.takenInChargeDonors[0].donor.name}
              </Link>
            );
          }

          return (
            <React.Fragment key={takenInCharge.takenInCharge.id}>
              <tr>
                <td className="col-md-1">
                  <button
                    type="button"
                    onClick={() =>
                      handleViewPlanning(takenInCharge.takenInCharge.id)
                    }
                    className="bg-transparent"
                  >
                    <img
                      src={
                        expandedRows[takenInCharge.takenInCharge.id]
                          ? minus
                          : plus
                      }
                      width="20px"
                      height="20px"
                      alt="toggle"
                    />
                  </button>
                </td>
                <td className="col-md-1">{donorName}</td>
                <td className="col-md-1">{serviceName}</td>
                <td className="col-md-1">{totalPlanned}</td>
                <td className="col-md-1">{totalReserved}</td>
                <td className="col-md-1">{totalExecuted}</td>
                <td className="col-md-1">
                  <div
                    style={{ whiteSpace: 'nowrap' }}
                    className={`${
                      takenInCharge.takenInCharge.status
                        ? getTag(takenInCharge.takenInCharge.status)
                        : ''
                    }`}
                  >
                    {takenInCharge.takenInCharge.status || '--'}
                  </div>
                </td>
                {!isRoleAssistant && (
                  <td className="col-md-1">
                    <Link
                      to={`/takenInCharges/fiche/${takenInCharge.takenInCharge.id}/info`}
                    >
                      <img
                        src={eye}
                        width="20px"
                        height="20px"
                        alt="rightArrow"
                      />
                    </Link>
                  </td>
                )}
              </tr>
              {expandedRows[takenInCharge.takenInCharge.id] && (
                <tr>
                  <td colSpan={8} className="border-top-0">
                    <Details data={takenInCharge} target="beneficiary" />
                  </td>
                </tr>
              )}
            </React.Fragment>
          );
        },
      );
    }
  }

  return (
    <div className={`pb-5 ${stylesList.backgroudStyle}`}>
      <div>
        <div className={styles.global}>
          <div className={styles.header}>
            <h4>Liste des Kafalats</h4>
          </div>
          <div className={stylesList.content}>
            <table className="table small">
              <thead>
                <tr>
                  <th scope="col" />
                  <th style={{ whiteSpace: 'nowrap' }}>Donateur</th>
                  <th style={{ whiteSpace: 'nowrap' }}>Service</th>
                  <th style={{ whiteSpace: 'nowrap' }}>Total Planifié (DH)</th>
                  <th style={{ whiteSpace: 'nowrap' }}>Total Réservé (DH)</th>
                  <th style={{ whiteSpace: 'nowrap' }}>Total Executé (DH)</th>
                  <th style={{ whiteSpace: 'nowrap' }}>Statut</th>
                  {!isRoleAssistant && (
                    <th style={{ whiteSpace: 'nowrap' }}>Actions</th>
                  )}
                </tr>
              </thead>
              <tbody>
                {listBeneficiaryTakenIncharges.length > 0 ? (
                  listBeneficiaryTakenIncharges
                ) : (
                  <tr>
                    <td colSpan="8" style={{ textAlign: 'center' }}>
                      Aucune kafalat trouvée
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
            {Beneficiary && Beneficiary.takenInChargeBeneficiaries.length > 0 && (
              <div className="justify-content-center my-4">
                <CustomPagination
                  totalElements={Beneficiary.takenInChargeBeneficiaries.length}
                  totalCount={Math.ceil(
                    Beneficiary.takenInChargeBeneficiaries.length / pageSize,
                  )}
                  pageSize={pageSize}
                  currentPage={currentPage}
                  onPageChange={setCurrentPage}
                />
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
