import React from 'react'
import {Field} from "formik";
const CustomCheckBox = (props) => {
    const {
        id,
        name,
        value,
        isChecked = false,
        label,
        className="",
        ...rest
    } = props;
    return (
        <label
            className={`form-check-label form-check form-bordred col-md-4 pl-3 ${isChecked ? 'form-bordred-checked' : ''} ${className}`}
            htmlFor={id}
        >
            <Field
                id={id}
                type="radio"
                name={name}
                value={value}
                {...rest}
            />
            {label}
        </label>
    )
}
export default CustomCheckBox;
