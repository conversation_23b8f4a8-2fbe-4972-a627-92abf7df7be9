import React, { useEffect, useState } from 'react';
import moment from 'moment';
import { Link, useHistory, useLocation } from 'react-router-dom';
import AccessControl from 'utils/AccessControl';
import Alert from 'react-bootstrap/Alert';
import styles from '../../../../Css/personalInfo.css';
import btnStyles from '../../../../Css/button.css';

const formatDate = date => moment(date).format('DD/MM/YYYY');

export default function GeneralInfo(props) {
  const service = props.data;
  const location = useLocation();
  const history = useHistory();
  const [showSuccessMessage, setShowSuccessMessage] = useState(false);

  // Check if redirected from update action
  useEffect(() => {
    if (location.state === 'updateSuccess') {
      setShowSuccessMessage(true);
    }
  }, [location.state]);

  useEffect(() => {
    if (showSuccessMessage) {
      const timer = setTimeout(() => {
        handleCloseSuccessMessage();
      }, 4000);
      return () => clearTimeout(timer);
    }
  }, [showSuccessMessage]);

  const handleCloseSuccessMessage = () => {
    setShowSuccessMessage(false);
    history.replace({ ...location, state: null });
  };

  let data1 = null;
  if (service) {
    data1 = (
      <div className={styles.data1}>
        <p style={{ fontWeight: '700' }}>{service.name}</p>
        <p style={{ fontWeight: '700' }}>{service.code}</p>
       
        <p style={{ fontWeight: '700' }}>{service.category}</p>
        <p style={{ fontWeight: '700' }}>
          {service.typeCategory ? service.typeCategory : '---'}
        </p>
        {!service.isDedicatedToEps && (
          <>
           <p style={{ fontWeight: '700' }}>{service.costs} %</p>
           <p style={{ fontWeight: '700' }}>{service.amountPerBeneficiary} DH</p>
        <p style={{ fontWeight: '700' }}>
          {service.propositionSystem === true ? 'Oui' : 'Non'}
        </p>
        <p style={{ fontWeight: '700' }}>
          {service.priority === true ? 'Oui' : 'Non'}
        </p>
        </>
        )}
        <p>{service.commentaire ? service.commentaire : '---'}</p>
      </div>
    );
  }

  return (
    <div>
      {showSuccessMessage && (
        <Alert
          className="alert-style"
          variant="success"
          onClose={handleCloseSuccessMessage}
          dismissible
        >
          <p>Service modifié avec succès !</p>
        </Alert>
      )}

      <div className={styles.personalInfo}>
        <div className={styles.header}>
          <h4>Informations sur le service</h4>

          {service ? (
            <AccessControl module="USER" functionality="UPDATE">
              <Link
                to={{
                  pathname: `/serviceS/edit/${service.id}`,
                  state: { redirectTo: 'consultation' },
                }}
              >
                <button className="btn-style secondary">Modifier</button>
              </Link>
            </AccessControl>
          ) : null}
        </div>

        <div className="d-flex justify-content-center mt-4" style={{}}>
          <div className={`d-flex py-3 px-5 ${styles.donationInfo}`}>
            <div className={styles.label1} style={{ marginRight: '30px' }}>
              <p>Nom</p>
              <p>Code</p>
              {!service.isDedicatedToEps && (
                <>
              <p>Frais de gestion</p>
              <p>Montant par bénéficiaire</p>
              <p>Service dédié aux bénéficiaires</p>
              <p>Priorité</p>
              </>
              )}
              <p>Catégorie</p>
              <p>Type</p>
             
              <p>Commentaire</p>
            </div>
            {data1}
          </div>
        </div>
      </div>
    </div>
  );
}
