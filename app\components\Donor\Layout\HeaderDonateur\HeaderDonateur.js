import React from 'react';
import { NavLink, useParams } from 'react-router-dom';
import profile from '../../../../Css/profileHeader.css';

const HeaderDonateur = () => {
  const params = useParams();
  const { id } = params;
  return (
    <div className={profile.navBar}>
      <p>
        <NavLink
          to={`/donors/fiche/${id}/info`}
          activeClassName={profile.selected}
        >
          Détails
        </NavLink>
      </p>

      <p>
        <NavLink
          to={`/donors/fiche/${id}/donation`}
          activeClassName={profile.selected}
        >
          Donations
        </NavLink>
      </p>

      <p>
        <NavLink
          to={`/donors/fiche/${id}/takenInCharge`}
          activeClassName={profile.selected}
        >
          Kafalat
        </NavLink>
      </p>
      <p>
        <NavLink
          to={`/donors/fiche/${id}/aideComplementaire`}
          activeClassName={profile.selected}
        >
          Aide complémentaire
        </NavLink>
      </p>
      <p>
        <NavLink
          to={`/donors/fiche/${id}/note`}
          activeClassName={profile.selected}
        >
          Notes
        </NavLink>
      </p>

      <p>
        <NavLink
          to={`/donors/fiche/${id}/document`}
          activeClassName={profile.selected}
        >
          Documents
        </NavLink>
      </p>
      <p>
        <NavLink
          to={`/donors/fiche/${id}/action`}
          activeClassName={profile.selected}
        >
          Actions
        </NavLink>
      </p>
      <p>
        <NavLink
          to={`/donors/fiche/${id}/correspondences`}
          activeClassName={profile.selected}
        >
          Correspondances
        </NavLink>
      </p>
      <p>
        <NavLink
          to={`/donors/fiche/${id}/releve`}
          activeClassName={profile.selected}
        >
          Relevé 
        </NavLink>
      </p>
    </div>
  );
};

export default HeaderDonateur;
