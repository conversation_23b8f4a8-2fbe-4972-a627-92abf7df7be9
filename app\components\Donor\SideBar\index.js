/* eslint-disable jsx-a11y/alt-text */
import React from 'react';
import style from '../../../Css/sideBar.css';
import tag from '../../../Css/tag.css';

const SideBar = props => {
  const image = require('../../../images/user.png');

  let donor;
  let top = null;
  let text = null;
  const emptyValue = <span>-</span>;
  if (props.data) {
    donor = props.data;

    if (props.typeDonor == 'P') {
      top = (
        <div className={style.topCard}>
          <div className={style.top}>
            <img
              src={
                donor.picture64 == null
                  ? image
                  : `data:image/png;base64,${atob(donor.picture64)}`
              }
              className={`rounded-circle ${style.imgBorder}`}
              style={{
                width: '100px', // Set a fixed width
                height: '100px', // Set a fixed height
                objectFit: 'cover', // Ensures the image maintains aspect ratio
                borderRadius: '50%', // Ensures it remains circular
                border: '2px solid #ddd', // Optional: to add a border
              }}
            />
            <div className={style.info}>
              <h5>
                {donor.firstName} {donor.lastName}
              </h5>
              <p>{donor.code}</p>
              {donor.status == null ? (
                <span>-</span>
              ) : (
                <p
                  className={
                    donor.status.name == 'actif' ? tag.tagGreen : tag.tagRed
                  }
                >
                  {donor.status.name}
                </p>
              )}
              {donor.type ? <p className={tag.tagBlue}>{donor.type} </p> : null}
              {donor.anonymeId? <p className={tag.tagGreyDark}>Ancien donateur anonyme </p> : null}

            </div>
          </div>
        </div>
      );

      text = (
        <div className={style.data}>
          <p>
            <span className={style.value}>
              {donor.phoneNumber ? donor.phoneNumber : emptyValue}
            </span>
          </p>
          <p>
            <span className={style.value}>
              {donor.email ? donor.email : emptyValue}
            </span>
          </p>
        </div>
      );
    } else if (props.typeDonor == 'M') {
      top = (
        <div className={style.topCard}>
          <div className={style.top}>
            <img
              src={
                donor.logo64 == null
                  ? image
                  : `data:image/png;base64,${atob(donor.logo64)}`
              }
              className={`rounded-circle ${style.imgBorder}`}
              style={{
                width: '100px', // Set a fixed width
                height: '100px', // Set a fixed height
                objectFit: 'cover', // Ensures the image maintains aspect ratio
                borderRadius: '50%', // Ensures it remains circular
                border: '2px solid #ddd', // Optional: to add a border
              }}
            />
            <div className={style.info}>
              <h5>
                {donor.company}{' '}
                {donor.shortCompany ? `(${donor.shortCompany})` : ''}{' '}
              </h5>
              <p>{donor.code}</p>
              {donor.status == null ? (
                <span>-</span>
              ) : (
                <p
                  className={
                    donor.status.name == 'actif' ? tag.tagGreen : tag.tagRed
                  }
                >
                  {donor.status.name}
                </p>
              )}
              {donor.type ? <p className={tag.tagBlue}>{donor.type} </p> : null}
              {donor.anonymeId? <p className={tag.tagGreyDark}>Ancien donateur anonyme </p> : null}

            </div>
          </div>
        </div>
      );

      text = null;
    } else if (props.typeDonor == 'A') {
      top = (
        <div className={style.topCard}>
          <div className={style.top}>
            <img
              src={
                donor.logo64 == null
                  ? image
                  : `data:image/png;base64,${atob(donor.logo64)}`
              }
              className={`rounded-circle ${style.imgBorder}`}
              style={{
                width: '100px', // Set a fixed width
                height: '100px', // Set a fixed height
                objectFit: 'cover', // Ensures the image maintains aspect ratio
                borderRadius: '50%', // Ensures it remains circular
                border: '2px solid #ddd', // Optional: to add a border
              }}
            />
            <div className={style.info}>
              <h5>{donor.name}</h5>
              <p>{donor.code}</p>
              {donor.status == null ? (
                <span>-</span>
              ) : (
                <p
                  className={
                    donor.status.name == 'actif' ? tag.tagGreen : tag.tagRed
                  }
                >
                  {donor.status.name}
                </p>
              )}
              {donor.type ? <p className={tag.tagBlue}>{donor.type} </p> : null}
            </div>
          </div>
        </div>
      );
    }
  }

  return (
    <div className={style.sideBar}>
      {top}
      {props && props.typeDonor == 'P' ? <hr className={style.hr} /> : null}
      <div className={`${style.text} my-4 ml-3`} >
        <div className={style.label} >
          {props && props.typeDonor == 'P' ? <p>Téléphone</p> : null}
          {props && props.typeDonor == 'P' ? <p>Email</p> : null}
        </div>
        {text}
      </div>

      <h5>Synthèse financière</h5>
      <div
        className={style.text}
        style={{ display: 'flex', flexDirection: 'column', gap: '12px' , marginTop: '20px',
      }}
        >
          {[
            {label: 'Total Donations',
            value: donor ? donor.totalDonated || 0 : 0,
            color: '#4F89D7',
          },
          {
            label: 'Total Exécuté',
            value: donor ? donor.totalExecuted || 0 : 0,
            color: '#EF8C5A',
          },
          {
            label: 'Total Réservé',
            value: donor ? donor.totalReserved || 0 : 0,
            color: 'darkgoldenrod',
          },
          {
            label: 'Solde Disponible',
            value: donor ? donor.availableBalance || 0 : 0,
            color: '#6DD5A0',
          },
        ].map((item, idx) => (
          <div
            key={idx}
            style={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              marginBottom: '12px', // Space between rows
            }}
          >
            {/* Label with more space */}
            <div style={{
              fontWeight: '600',
              color: '#555',
              flex: '1',
              paddingRight: '20px', // Add space between label and button
              }}
            >
              {item.label}
            </div>

            <button
                className={style.itemValueStyle}
              style={{backgroundColor: item.color}}
            >
              {item.value} DH
            </button>
          </div>
        ))}
      </div>
    </div>
  );
};

export default SideBar;
