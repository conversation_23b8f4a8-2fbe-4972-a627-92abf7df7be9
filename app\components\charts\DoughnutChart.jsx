import React from 'react';
import { Box, Typography, Paper, useTheme } from '@mui/material';
import { styled } from '@mui/material/styles';

const StyledPaper = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(3),
  display: 'flex',
  flexDirection: 'column',
  alignItems: 'center',
  background: 'linear-gradient(145deg, #ffffff, #f0f0f0)',
  boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
  borderRadius: '16px',
  transition: 'transform 0.3s ease, box-shadow 0.3s ease',
  '&:hover': {
    transform: 'translateY(-4px)',
    boxShadow: '0 8px 30px rgba(0, 0, 0, 0.12)',
  },
}));

const LegendContainer = styled(Box)({
  display: 'flex',
  flexWrap: 'wrap',
  justifyContent: 'center',
  gap: '20px',
  marginTop: '24px',
  padding: '16px',
  borderRadius: '12px',
  background: 'rgba(255, 255, 255, 0.8)',
});

const LegendItem = styled(Box)({
  display: 'flex',
  alignItems: 'center',
  gap: '12px',
  padding: '8px 12px',
  borderRadius: '8px',
  background: 'rgba(255, 255, 255, 0.9)',
  boxShadow: '0 2px 8px rgba(0, 0, 0, 0.05)',
  transition: 'transform 0.2s ease',
  '&:hover': {
    transform: 'scale(1.05)',
  },
});

const ColorBox = styled(Box)(({ color }) => ({
  width: '20px',
  height: '20px',
  backgroundColor: color,
  borderRadius: '6px',
  boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
}));

const colors = [
  '#4E79A7',
  '#F28E2B',
  '#E15759',
  '#76B7B2',
  '#59A14F',
  '#EDC948',
  '#B07AA1',
  '#FF9DA7',
  '#9C755F',
  '#BAB0AC',
];

export function DoughnutChart({ data, labels, title }) {
  const theme = useTheme();
  const total = data.reduce((sum, value) => sum + value, 0);
  
  return (
    <StyledPaper>
      {title && (
        <Typography 
          variant="h5" 
          sx={{ 
            fontWeight: 600,
            color: theme.palette.primary.main,
            mb: 3,
            textAlign: 'center',
          }}
        >
          {title}
        </Typography>
      )}
      <Box sx={{ 
        width: '100%', 
        height: '250px',
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        position: 'relative',
        mb: 2,
      }}>
        {data.map((value, index) => {
          const percentage = (value / total) * 100;
          const startAngle = index === 0 ? 0 : 
            data.slice(0, index).reduce((sum, val) => sum + (val / total) * 360, 0);
          const endAngle = startAngle + (value / total) * 360;
          
          return (
            <Box
              key={index}
              sx={{
                position: 'absolute',
                width: '180px',
                height: '180px',
                borderRadius: '50%',
                background: `conic-gradient(
                  ${colors[index % colors.length]} ${startAngle}deg,
                  ${colors[index % colors.length]} ${endAngle}deg,
                  transparent ${endAngle}deg,
                  transparent ${startAngle}deg
                )`,
                transform: 'rotate(-90deg)',
                transition: 'transform 0.3s ease',
                '&:hover': {
                  transform: 'rotate(-90deg) scale(1.05)',
                },
              }}
            />
          );
        })}
        <Box
          sx={{
            position: 'absolute',
            width: '120px',
            height: '120px',
            borderRadius: '50%',
            background: 'linear-gradient(145deg, #ffffff, #f0f0f0)',
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'center',
            alignItems: 'center',
            boxShadow: 'inset 0 2px 4px rgba(0, 0, 0, 0.1)',
          }}
        >
          <Typography variant="h4" sx={{ fontWeight: 600, color: theme.palette.primary.main }}>
            {total}
          </Typography>
          <Typography variant="body2" sx={{ color: theme.palette.text.secondary }}>
            Total
          </Typography>
        </Box>
      </Box>
      <LegendContainer>
        {labels.map((label, index) => (
          <LegendItem key={index}>
            <ColorBox color={colors[index % colors.length]} />
            <Box>
              <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
                {label}
              </Typography>
              <Typography variant="body2" sx={{ color: theme.palette.text.secondary }}>
                {((data[index] / total) * 100).toFixed(1)}%
              </Typography>
            </Box>
          </LegendItem>
        ))}
      </LegendContainer>
    </StyledPaper>
  );
}