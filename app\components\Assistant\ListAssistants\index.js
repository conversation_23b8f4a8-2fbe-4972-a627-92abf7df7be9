import React from 'react';
import { useHistory } from 'react-router-dom';
import DataTable from 'components/Common/DataTable';
import {
  DELETE_ICON,
  EDIT_ICON,
  VIEW_ICON,
} from 'components/Common/ListIcons/ListIcons';
import moment from 'moment';
import styles from '../../../Css/tag.css';

const ListAssistants = ({ assistants, handleDelete, handleChangeZone }) => {
  const history = useHistory();
  const formatDate = date => moment(date).format('DD/MM/YYYY');

  const viewHandler = id => {
    history.push(`/assistants/consultAssistant/${id}/1`);
  };

  const editHandler = id => {
    history.push(`/assistants/editAssistant/${id}`);
  };

  const columns = [
    {
      field: 'code',
      headerName: 'Code',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
      renderCell: params => <span>{params.row.code}</span>,
    },
    {
      field: 'firstName',
      headerName: 'Nom complet',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
      renderCell: params => (
        <span>
          {params.row.firstName} {params.row.lastName}
        </span>
      ),
    },
    {
      field: 'email',
      headerName: 'Adresse mail',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
      renderCell: params => <span>{params.row.email}</span>,
    },
    // {
    //   field: 'zoneName',
    //   headerName: 'Zone',
    //   flex: 1,
    //   headerAlign: 'center',
    //   align: 'center',
    //   renderCell: params => (
    //     <span>{params.row.zone ? params.row.zone.name : '---'}</span>
    //   ),
    // },
    
    {
      field: 'status',
      headerName: 'Statut',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
      renderCell: params => {
        const { dateAffectationToZone, dateEndAffectationToZone } = params.row;
    
        // Determine status and CSS class based on dates
        let status = 'Inactif';
        let statusClass = styles.tagRed;
    
        if (dateAffectationToZone) {
          if (dateEndAffectationToZone) {
            status = moment(dateEndAffectationToZone).isBefore(moment())
              ? 'Inactif'
              : 'Actif';
            statusClass = moment(dateEndAffectationToZone).isBefore(moment())
              ? styles.tagRed
              : styles.tagGreen;
          } else {
            status = 'Actif';
            statusClass = styles.tagGreen;
          }
        }
    
        return (
          <span className={statusClass}>
            {status}
          </span>
        );
      },
    },
    

    {
      field: 'actions',
      headerName: 'Actions',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
      renderCell: params => (
        <div style={{ display: 'flex', alignItems: 'center' }}>
          {/* <input
            type="image"
            className="p-2"
            src={VIEW_ICON}
            width="40px"
            height="40px"
            onClick={() => {
              const entityUrl = `${params.row.modulepath}/fiche/${params.row.entityId}/action`;
              history.push(entityUrl);
            }}
            onClick={() => {
              setActionToEdit('');
              setActionToReadOnly(prev => ({
                ...prev,
                isRead: true,
                actionData: params.row.actionDTO,
              }));
              handleShow();
            }}
            title="consulter"
          /> */}
          <input
            type="image"
            onClick={() => viewHandler(params.row.id, 'fiche')}
            className="p-2"
            src={VIEW_ICON}
            width="40px"
            height="40px"
            title="consulter"
          />
          <input
            type="image"
            onClick={() => editHandler(params.row.id)}
            src={EDIT_ICON}
            className="p-2"
            width="40px"
            height="40px"
            title="Modifier"
            style={{ cursor: 'pointer' }}
          />
          <input
            type="image"
            onClick={() => handleDelete(params.row.id)}
            src={DELETE_ICON}
            className="p-2"
            width="40px"
            height="40px"
            title="Supprimer"
            style={{ cursor: 'pointer' }}
          />
          {/* <i
            className="fas fa-exchange-alt"
            onClick={() =>
              handleChangeZone(
                params.row.id,
                params.row.zoneId,
                params.row.dateAffectationToZone,
                params.row.dateEndAffectationToZone,
              )
            }
            title="Changer Zone"
            style={{ fontSize: '27px', cursor: 'pointer', color: '#4F89D7' }}
          /> */}
        </div>
      ),
    },
  ];

  return (
    <div className="table-container">
      <DataTable
        rows={
          assistants &&
          assistants.map(assistant => ({
            ...assistant,
            user: assistant.user,
          }))
        }
        columns={columns}
        fileName={`Liste des assistants, ${new Date().toLocaleString()}`}
      />
    </div>
  );
};

export default ListAssistants;
