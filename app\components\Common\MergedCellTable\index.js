import React from 'react';
import {
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
} from '@mui/material';
import '../DataTable/dataTableStyles.css';

const MergedCellTable = ({ columns, data, mergeColumn }) => {
  const getRowSpan = (data, groupKey) => {
    const groupCounts = {};
    data.forEach(row => {
      const key = row[groupKey];
      groupCounts[key] = (groupCounts[key] || 0) + 1;
    });
    return groupCounts;
  };

  const rowSpanCounts = getRowSpan(data, mergeColumn);

  return (
    <TableContainer component={Paper} className="data-grid-container">
      <Table>
        <TableHead>
          <TableRow>
            {columns.map((column, index) => (
              <TableCell key={index} className="MuiDataGrid-colCellTitle">
                {column}
              </TableCell>
            ))}
          </TableRow>
        </TableHead>
        <TableBody>
          {data.map((row, rowIndex) => {
            const isFirstInGroup =
              rowIndex === 0 ||
              data[rowIndex - 1][mergeColumn] !== row[mergeColumn];

            return (
              <React.Fragment key={row.id}>
                {isFirstInGroup && (
                  <TableRow>
                    <TableCell
                      rowSpan={rowSpanCounts[row[mergeColumn]]}
                      className="MuiDataGrid-cell"
                    >
                      {row[mergeColumn]}
                    </TableCell>
                    <TableCell className="MuiDataGrid-cell">
                      {row.code}
                    </TableCell>
                    <TableCell className="MuiDataGrid-cell">
                      {row.firstName}
                    </TableCell>
                    <TableCell className="MuiDataGrid-cell">
                      {row.lastName}
                    </TableCell>
                  </TableRow>
                )}
                {row.beneficiaries &&
                  row.beneficiaries.length > 0 &&
                  row.beneficiaries.map(beneficiary => (
                    <TableRow key={beneficiary.id}>
                      <TableCell className="MuiDataGrid-cell"></TableCell>
                      <TableCell className="MuiDataGrid-cell">
                        {beneficiary.code}
                      </TableCell>
                      <TableCell className="MuiDataGrid-cell">
                        {beneficiary.firstName}
                      </TableCell>
                      <TableCell className="MuiDataGrid-cell">
                        {beneficiary.lastName}
                      </TableCell>
                    </TableRow>
                  ))}
              </React.Fragment>
            );
          })}
        </TableBody>
      </Table>
    </TableContainer>
  );
};

export default MergedCellTable;
