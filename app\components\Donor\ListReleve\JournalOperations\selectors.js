import { createSelector } from 'reselect';
import { initialState } from './reducer';

const selectOmdb = state => state.releveDonor || initialState;

const makeSelectLoading = createSelector(
  selectOmdb,
  omdbState => omdbState.loading,
);

const makeSelectError = createSelector(
  selectOmdb,
  omdbState => omdbState.error,
);

const makeSelectReleveDonor = createSelector(
  selectOmdb,
  releveDoner => releveDoner.releves,
);

const makeSelectCanalDonations = createSelector(
  selectOmdb,
  omdbState => omdbState.canalDonations,
);

const makeSelectServiceCategories = createSelector(
  selectOmdb,
  omdbState => omdbState.serviceCategories,
);

const makeSelectServices = createSelector(
  selectOmdb,
  omdbState => omdbState.services,
);

export {
  selectOmdb,
  makeSelectLoading,
  makeSelectError,
  makeSelectReleveDonor,
  makeSelectCanalDonations,
  makeSelectServiceCategories,
  makeSelectServices,
};
