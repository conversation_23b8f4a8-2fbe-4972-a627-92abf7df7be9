const GetBeneficiariesInformationsFromFamily = familyMembers => {
  const beneficiariesInfo = {
    count: 0,
    totalGiven: 0,
    servicePending: 0,
    serviceInProgress: 0,
    serviceSuspended: 0,
    serviceClosed: 0,
    familySize: familyMembers.length,
    countCandidate: 0,
  };

  familyMembers.map(member => {
    const { beneficiary } = member.person;
    if (
      beneficiary != null &&
      beneficiary.archived !== true &&
      beneficiary.beneficiaryStatut != null
    ) {
      if (beneficiary.beneficiaryStatut.nameStatut === 'beneficiary_actif') {
        beneficiariesInfo.count++;
      }
      if (
        beneficiary.beneficiaryStatut.nameStatut === 'beneficiary_enattente'
      ) {
        beneficiariesInfo.countCandidate++;
      }
      if (
        beneficiary.beneficiaryServices &&
        beneficiary.beneficiaryServices[0]
      ) {
        const service = beneficiary.beneficiaryServices[0];
        const status = service.status.name;
        if (status == 'attente') {
          beneficiariesInfo.servicePending++;
        } else if (status == 'en cours') {
          beneficiariesInfo.serviceInProgress++;
        } else if (status == 'suspendue') {
          beneficiariesInfo.serviceSuspended++;
        } else if (status == 'fermée') {
          beneficiariesInfo.serviceClosed++;
        }
      }
      if (beneficiary.takenInChargeBeneficiaries.length > 0) {
        beneficiary.takenInChargeBeneficiaries.map(takenInChargeBeneficiary => {
          if (
            takenInChargeBeneficiary.takenInCharge.takenInChargeDonors.length >
              0 &&
            takenInChargeBeneficiary.takenInCharge.takenInChargeDonors[0]
              .takenInChargeOperations.length > 0
          ) {
            takenInChargeBeneficiary.takenInCharge.takenInChargeDonors[0].takenInChargeOperations.map(
              operation => {
                if (operation.executionDate) {
                  beneficiariesInfo.totalGiven += operation.amount;
                }
              },
            );
          }
        });
      }
    }
  });

  return beneficiariesInfo;
};
export default GetBeneficiariesInformationsFromFamily;
