import React from 'react';
import { NavLink, useParams } from 'react-router-dom';

import styles from 'Css/profileHeader.css';
import { hasRoleAdmin, useHasRole } from 'utils/hasAccess';
import profile from '../../../../Css/profileHeader.css';

import {
  isBeneficiaryActif,
  isCandidate,
} from '../../../../containers/Beneficiary/BeneficiaryProfile/statutUtils';

const HeaderBeneficiary = ({ statut }) => {
  const params = useParams();
  const { id } = params;
  const isAdmin = hasRoleAdmin();

  return (
    <div className={`${profile.navBar}`}   style={{ fontSize: '12px' }}>
      <p>
        <NavLink
          exact
          to={`/beneficiaries/fiche/${params.id}/info`}
          activeClassName={styles.selected}
        >
          Détails Personnels
        </NavLink>
      </p>
      <p>
        <NavLink
          exact
          to={`/beneficiaries/fiche/${params.id}/educations`}
          activeClassName={styles.selected}
        >
          Educations
        </NavLink>
      </p>
      <p>
        <NavLink
          exact
          to={`/beneficiaries/fiche/${params.id}/family`}
          activeClassName={styles.selected}
        >
          Famille
        </NavLink>
      </p>
      {!isCandidate(statut) && (
        <p>
          <NavLink
            exact
            to={`/beneficiaries/fiche/${params.id}/takenInCharge`}
            activeClassName={styles.selected}
          >
            Kafalat
          </NavLink>
        </p>
      )}
       {  (
        <p>
          <NavLink
            exact
            to={`/beneficiaries/fiche/${params.id}/aideComplementaire`}
            activeClassName={styles.selected}
          >
            Aide complementaire
          </NavLink>
        </p>
      )}
      {!isCandidate(statut) && (
        <p>
          <NavLink
            exact
            to={`/beneficiaries/fiche/${params.id}/bankcards`}
            activeClassName={styles.selected}
          >
            Carte Bancaire
          </NavLink>
        </p>
      )}
      <p>
        <NavLink
          exact
          to={`/beneficiaries/fiche/${params.id}/notes`}
          activeClassName={styles.selected}
        >
          Notes
        </NavLink>
      </p>
      <p>
        <NavLink
          exact
          to={`/beneficiaries/fiche/${params.id}/documents`}
          activeClassName={styles.selected}
        >
          Documents
        </NavLink>
      </p>
      <p>
        <NavLink
          exact
          to={`/beneficiaries/fiche/${params.id}/action`}
          activeClassName={styles.selected}
        >
          Actions
        </NavLink>
      </p>
      <p>
        <NavLink
          exact
          to={`/beneficiaries/fiche/${params.id}/history`}
          activeClassName={styles.selected}
        >
          Historique
        </NavLink>
      </p>
      {isBeneficiaryActif(statut) && (
        <p>
          <NavLink
            exact
            to={`/beneficiaries/fiche/${params.id}/rapport`}
            activeClassName={styles.selected}
          >
            Rapport
          </NavLink>
        </p>
      )}
    </div>
  );
};

export default HeaderBeneficiary;
