import React, { useState, useEffect } from 'react';
import { Box, Typography, Paper, useTheme } from '@mui/material';
import { styled } from '@mui/material/styles';

const StyledPaper = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(3),
  display: 'flex',
  flexDirection: 'column',
  background: 'linear-gradient(145deg, #ffffff, #f0f0f0)',
  boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
  borderRadius: '16px',
  transition: 'transform 0.3s ease, box-shadow 0.3s ease',
  height: '100%',
  '&:hover': {
    transform: 'translateY(-4px)',
    boxShadow: '0 8px 30px rgba(0, 0, 0, 0.12)',
  },
}));

const ChartHeader = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  gap: theme.spacing(1),
  marginBottom: theme.spacing(3),
}));

const StatusContainer = styled(Box)({
  display: 'flex',
  flexDirection: 'column',
  gap: '24px',
  padding: '16px 0',
});

const StatusBox = styled(Box)(({ theme, color, isActive }) => ({
  padding: theme.spacing(3),
  borderRadius: '12px',
  background: isActive ? `${color}15` : '#f8f9fa',
  border: `1px solid ${isActive ? color : '#e9ecef'}`,
  transition: 'all 0.3s ease',
  cursor: 'pointer',
  '&:hover': {
    transform: 'translateX(8px)',
    boxShadow: '0 4px 12px rgba(0, 0, 0, 0.05)',
  },
}));

const StatusContent = styled(Box)({
  display: 'flex',
  flexDirection: 'column',
  gap: '8px',
});

const StatusValue = styled(Typography)(({ color }) => ({
  fontSize: '2rem',
  fontWeight: 700,
  color: color,
  lineHeight: 1,
}));

const StatusLabel = styled(Typography)({
  fontSize: '1rem',
  fontWeight: 500,
  color: '#6c757d',
});

const StatusPercentage = styled(Typography)(({ color }) => ({
  fontSize: '1.1rem',
  fontWeight: 600,
  color: color,
  marginTop: '4px',
}));

const TotalDonors = styled(Typography)(({ theme }) => ({
  fontSize: '0.875rem',
  color: theme.palette.text.secondary,
  marginTop: '4px',
}));

const DonorStatusChart = ({ 
  data = [], 
  title = "Status Overview", 
  icon,
  activeColor ,
  inactiveColor,
  valueLabel = 'items',
  showPercentage = true,
  showTotal = true,
  activeStatus = 'active'
}) => {
  const theme = useTheme();
  const [isVisible, setIsVisible] = useState(false);

  const total = data.reduce((sum, item) => sum + item.value, 0);

  useEffect(() => {
    const timer = setTimeout(() => {
      setIsVisible(true);
    }, 300);
    return () => clearTimeout(timer);
  }, []);

  return (
    <StyledPaper>
      <ChartHeader>
        {icon && React.cloneElement(icon, { color: "primary" })}
        <Typography variant="h6" color="primary">
          {title}
        </Typography>
      </ChartHeader>
      <StatusContainer>
        {data.map((item) => {
          const percentage = ((item.value / total) * 100).toFixed(0);
          const isActive = item.status === activeStatus;
          const color = isActive ? activeColor : inactiveColor;
          const backgroundColor = `${color}10`; // 25% opacity (40 in hex)

          return (
            <StatusBox
              key={item.status}
              color={color}
              isActive={isActive}
              sx={{ 
                background: backgroundColor,
                borderColor: color
              }}
            >
              <StatusContent>
                {showPercentage && (
                  <StatusValue color={color}>
                    {percentage}%
                  </StatusValue>
                )}
                <Box>
                  <StatusLabel>
                    {item.title}
                  </StatusLabel>
                  {showTotal && (
                    <TotalDonors>
                      {item.value} {valueLabel}
                    </TotalDonors>
                  )}
                </Box>
              </StatusContent>
            </StatusBox>
          );
        })}
      </StatusContainer>
    </StyledPaper>
  );
};

export default DonorStatusChart; 