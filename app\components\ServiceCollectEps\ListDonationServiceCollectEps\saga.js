import { call, put, takeLatest } from "redux-saga/effects";
import {  LOAD_Donation_Service_EPS} from "./constants";
import {donationserviceCollectEpsLoaded,donationserviceCollectEpsLoadingError} from "./actions";
import request from 'utils/request';


function buildUrlWithFilters(baseUrl, filters) {
  let url = baseUrl;
  if (filters) {
    const {
      searchByDonateur,
      searchByDonationMinDate,
      searchByDonationMaxDate,
      searchByMinAmount,
      searchByMaxAmount,
      searchByDonationType
    } = filters;

    if (searchByDonateur) url += `&searchByDonateur=${searchByDonateur}`;
    if (searchByDonationMinDate) url += `&searchByDonationMinDate=${searchByDonationMinDate}`;
    if (searchByDonationMaxDate) url += `&searchByDonationMaxDate=${searchByDonationMaxDate}`;
    if (searchByMinAmount) url += `&searchByMinAmount=${searchByMinAmount}`;
    if (searchByMaxAmount) url += `&searchByMaxAmount=${searchByMaxAmount}`;
    if (searchByDonationType) url += `&searchByDonationType=${searchByDonationType}`;
  }

  return url;
}

export function* loadDonationServiceCollectEps({id, page, filters }) {
  let requestURL = `/service-collect-eps/getDonationByServiceCollectEps?page=${page}&serviceCollectEpsId=${id}`;
  requestURL = buildUrlWithFilters(requestURL, filters);
  try {
    const { data } = yield call(request.get, requestURL);
    yield put(donationserviceCollectEpsLoaded(data));
    
  } catch (error) {
    yield put(donationserviceCollectEpsLoadingError(error));
  }
} 

export default function* omdbSaga() {
  yield takeLatest(LOAD_Donation_Service_EPS, loadDonationServiceCollectEps);
}
