import {
  DELETE_AIDE_COMPLEMENTAIRE_FAILURE,
  DELE<PERSON>_AIDE_COMPLEMENTAIRE_REQUEST,
  DELETE_AIDE_COMPLEMENTAIRE_RESET,
  DELETE_AIDE_COMPLEMENTAIRE_SUCCESS,
  EXPORT_AIDE_COMPLEMENTAIRE_CSV,
  EXPORT_AIDE_COMPLEMENTAIRE_CSV_ERROR,
  EXPORT_AIDE_COMPLEMENTAIRE_CSV_SUCCESS,
  LOAD_AIDE_COMPLEMENTAIRE,
  LOAD_AIDE_COMPLEMENTAIRE_ERROR,
  LOAD_AIDE_COMPLEMENTAIRE_SUCCESS,
  REMOVE_AIDE_COMPLEMENTAIRE,
} from './constants';

export function loadAideComplementaire(page, filters) {
  return {
    type: LOAD_AIDE_COMPLEMENTAIRE,
    page,
    filters,
  };
}

export function aideComplementaireLoaded(aideComplementaire) {
  return {
    type: LOAD_<PERSON><PERSON>_COMPLEMENTAIRE_SUCCESS,
    aideComplementaire,
  };
}

export function aideComplementaireLoadingError(error) {
  return {
    type: LOAD_AIDE_COMPLEMENTAIRE_ERROR,
    error,
  };
}

export function removeAideComplementaire(aideComplementaire) {
  return {
    type: REMOVE_AIDE_COMPLEMENTAIRE,
    aideComplementaire,
  };
}

export function deleteAideComplementaireRequest(id) {
  return {
    type: DELETE_AIDE_COMPLEMENTAIRE_REQUEST,
    id,
  };
}

export function deleteAideComplementaireSuccess() {
  return {
    type: DELETE_AIDE_COMPLEMENTAIRE_SUCCESS,
  };
}

export function deleteAideComplementaireFailure(error) {
  return {
    type: DELETE_AIDE_COMPLEMENTAIRE_FAILURE,
    error,
  };
}

export function deleteAideComplementaireReset() {
  return {
    type: DELETE_AIDE_COMPLEMENTAIRE_RESET,
  };
}

export function exportAideComplementaireToCsv(filters) {
  return {
    type: EXPORT_AIDE_COMPLEMENTAIRE_CSV,
    filters,
  };
}

export function aideComplementaireToCsvExported(aideComplementaire) {
  return {
    type: EXPORT_AIDE_COMPLEMENTAIRE_CSV_SUCCESS,
    aideComplementaire,
  };
}

export function aideComplementaireToCsvExportingError(error) {
  return {
    type: EXPORT_AIDE_COMPLEMENTAIRE_CSV_ERROR,
    error,
  };
}
