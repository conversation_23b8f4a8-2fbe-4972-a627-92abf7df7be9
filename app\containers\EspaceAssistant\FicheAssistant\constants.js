// loadConnectedAssistant

export const LOAD_CONNECTED_ASSISTANT =
  'almobadara/FicheAssistant/LOAD_CONNECTED_ASSISTANT';
export const LOAD_CONNECTED_ASSISTANT_SUCCESS =
  'almobadara/FicheAssistant/LOAD_CONNECTED_ASSISTANT_SUCCESS';
export const LOAD_CONNECTED_ASSISTANT_ERROR =
  'almobadara/FicheAssistant/LOAD_CONNECTED_ASSISTANT_ERROR';

export const LOAD_SOUS_ZONES = 'almobadara/FicheZone/LOAD_SOUS_ZONES';
export const LOAD_SOUS_ZONES_SUCCESS =
  'almobadara/FicheZone/LOAD_SOUS_ZONES_SUCCESS';
export const LOAD_SOUS_ZONES_ERROR =
  'almobadara/FicheZone/LOAD_SOUS_ZONES_ERROR';

export const LOAD_RAPPORT_BY_ASSISTANT =
  'almobadara/FicheZone/LOAD_RAPPORT_BY_ASSISTANT';
export const LOAD_RAPPORT_BY_ASSISTANT_SUCCESS =
  'almobadara/FicheZone/LOAD_RAPPORT_BY_ASSISTANT_SUCCESS';
export const LOAD_RAPPORT_BY_ASSISTANT_ERROR =
  'almobadara/FicheZone/LOAD_RAPPORT_BY_ASSISTANT_ERROR';

export const LOAD_ZONES_WITH_SOUS_ZONES = 'app/EspaceAssistant/FicheAssistant/LOAD_ZONES_WITH_SOUS_ZONES';
export const LOAD_ZONES_WITH_SOUS_ZONES_SUCCESS = 'app/EspaceAssistant/FicheAssistant/LOAD_ZONES_WITH_SOUS_ZONES_SUCCESS';
export const LOAD_ZONES_WITH_SOUS_ZONES_ERROR = 'app/EspaceAssistant/FicheAssistant/LOAD_ZONES_WITH_SOUS_ZONES_ERROR';
