import { call, put, takeLatest } from 'redux-saga/effects';
import request from 'utils/request';
import {
  connectedAssistantLoaded,
  connectedAssistantLoadingError,
  rapportByAssistantLoaded,
  rapportByAssistantLoadingError,
  sousZonesLoaded,
  sousZonesLoadingError,
  zonesWithSousZonesLoaded,
  loadZonesWithSousZonesError,
} from './actions';
import {
  LOAD_CONNECTED_ASSISTANT,
  LOAD_RAPPORT_BY_ASSISTANT,
  LOAD_SOUS_ZONES,
  LOAD_ZONES_WITH_SOUS_ZONES,
} from './constants';

export function* loadConnectedAssistant({ id }) {
  const url = `/assistants/${id}`;

  try {
    const { data } = yield call(request.get, url);
    yield put(connectedAssistantLoaded(data));
  } catch (error) {
    yield put(connectedAssistantLoadingError(error));
  }
}

export function* loadSousZones(action) {
  const { id } = action; // Extract the id from the action object
  const url = `/zones/${id}/sous-zones`; // Construct the URL using the id

  try {
    const { data } = yield call(request.get, url); // Make the GET request
    yield put(sousZonesLoaded(data)); // Dispatch success action with the data
  } catch (error) {
    yield put(sousZonesLoadingError(error)); // Dispatch error action if request fails
  }
}

export function* loadRapportByAssistant({ id, pageNumber, filters }) {
  try {
    let url = `/agenda-rapports/assistant/${id}?page=${pageNumber}`;

    const params = new URLSearchParams();

    if (filters) {
      const {
        beneficiaryCode,
        beneficiaryName,
        numberRapport,
        reportStatus,
        plannedDateStart,
        plannedDateEnd,
        validationDateStart,
        validationDateEnd,
      } = filters;

      if (beneficiaryCode) {
        params.append('beneficiaryCode', beneficiaryCode);
      }
      if (beneficiaryName) {
        params.append('beneficiaryName', beneficiaryName);
      }
      if (numberRapport) {
        params.append('numberRapport', numberRapport);
      }
      if (reportStatus) {
        params.append('reportStatus', reportStatus);
      }
      if (plannedDateStart) {
        params.append('plannedDateStart', plannedDateStart);
      }
      if (plannedDateEnd) {
        params.append('plannedDateEnd', plannedDateEnd);
      }
      if (validationDateStart) {
        params.append('validationDateStart', validationDateStart);
      }
      if (validationDateEnd) {
        params.append('validationDateEnd', validationDateEnd);
      }
    }

    if (params.toString()) {
      url += `&${params.toString()}`;
    }
    const { data } = yield call(request.get, url);
    yield put(rapportByAssistantLoaded(data));
  } catch (error) {
    yield put(rapportByAssistantLoadingError(error));
  }
}

export function* getZonesWithSousZones({ assistantId }) {
  const requestURL = `/zones/${assistantId}/all-zones-with-sous-zones`;

  try {
    const { data } = yield call(request.get, requestURL);
    yield put(zonesWithSousZonesLoaded(data));
  } catch (err) {
    yield put(loadZonesWithSousZonesError(err.message));
  }
}

/**
 * Root saga manages watcher lifecycle
 */
export default function* assistantFicheSaga() {
  yield takeLatest(LOAD_CONNECTED_ASSISTANT, loadConnectedAssistant);
  yield takeLatest(LOAD_SOUS_ZONES, loadSousZones);
  yield takeLatest(LOAD_RAPPORT_BY_ASSISTANT, loadRapportByAssistant);
  yield takeLatest(LOAD_ZONES_WITH_SOUS_ZONES, getZonesWithSousZones);
}
