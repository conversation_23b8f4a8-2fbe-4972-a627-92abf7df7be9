import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useInjectReducer, useInjectSaga } from 'redux-injectors';
import { createStructuredSelector } from 'reselect';
import btnStyles from 'Css/button.css';
import listStyles from 'Css/list.css';
import { Link, useHistory } from 'react-router-dom';
import { Alert } from 'react-bootstrap';
import GenericFilter from 'containers/Common/Filter/GenericFilter';
import { Stack } from '@mui/material';
import { makeSelectTagList } from 'containers/tag/selectors';
import { getTagsByType } from 'containers/tag/actions';
import tagReducer from 'containers/tag/reducer';
import tagSaga from 'containers/tag/saga';
import CustomPagination from '../../../components/Common/CustomPagination';
import aideComplementaireReducer from './reducer';
import aideComplementaireListSaga from './saga';
import {
  deleteAideComplementaireReset,
  loadAideComplementaire,
} from './actions';
import {
  makeSekectDeleteAideComplementaireError,
  makeSekectDeleteAideComplementaireSuccess,
  makeSekectExporting,
  makeSelectAideComplementaire,
  makeSelectError,
  makeSelectLoading,
} from './selectors';
import ListAideComplementaire from '../../../components/AideComplementaire/ListAideComplementaire';
import { makeSelectTypePriseEnCharges } from '../../Common/SubComponents/TypePriseEnCharge/selectors';
import typePrisenEnChargesReducer from '../../Common/SubComponents/TypePriseEnCharge/reducer';
import typePrisenEnChargesSaga from '../../Common/SubComponents/TypePriseEnCharge/saga';
import { loadTypePrisenEnCharge } from '../../Common/SubComponents/TypePriseEnCharge/actions';
import ExportIconSvg from '../../../images/icons/ExportIconSvg';

const key = 'aideComplementaireList';
const key2 = 'typePriseEnCharge';
const key3 = 'donationAdd';
const keyTag = 'tagList';

const omdbSelector = createStructuredSelector({
  aideComplementaire: makeSelectAideComplementaire,
  error: makeSelectError,
  loading: makeSelectLoading,
  exportLoading: makeSekectExporting,
  deleteAideComplementaireSuccess: makeSekectDeleteAideComplementaireSuccess,
  deleteAideComplementaireError: makeSekectDeleteAideComplementaireError,
  tagList: makeSelectTagList,
});

const omdbSelector2 = createStructuredSelector({
  typePriseEnCharge: makeSelectTypePriseEnCharges,
});

/*
const omdbSelector3 = createStructuredSelector({
  successDelete: makeSelectDeleteSuccess,
});

 */

export default function AideComplementaire(props) {
  const [activePage, setActivePage] = useState(1);
  const [itemsCountPerPage, setItemsCountPerPage] = useState(1);
  const [totalItemsCount, setTotalItemsCount] = useState(1);
  const [showAlert, setShowAlert] = useState(false);
  const [successMessage, setSuccessMessage] = useState('');

  const [selectedSearch, setSelectedSearch] = useState('');
  const [sortByColumn, setSortByColumn] = useState(null);
  const [sortByColumnValue, setSortByColumnValue] = useState(null);
  const [errorMessages, setErrorMessages] = useState('');
  const [alertTimeout, setAlertTimeout] = useState(null);

  useInjectReducer({ key, reducer: aideComplementaireReducer });
  useInjectSaga({ key, saga: aideComplementaireListSaga });

  useInjectReducer({ key: key2, reducer: typePrisenEnChargesReducer });
  useInjectSaga({ key: key2, saga: typePrisenEnChargesSaga });

  useInjectReducer({ key: keyTag, reducer: tagReducer });
  useInjectSaga({ key: keyTag, saga: tagSaga });

  const {
    aideComplementaire,
    error,
    loading,
    exportLoading,
    deleteAideComplementaireSuccess,
    deleteAideComplementaireError,
    tagList,
  } = useSelector(omdbSelector);
  const { typePriseEnCharge } = useSelector(omdbSelector2);

  const dispatch = useDispatch();
  const history = useHistory();

  const principalInputsConfig = [
    {
      field: 'searchByNom',
      type: 'text',
      placeholder: "Nom de l'Aide Complémentaire",
      label: "Nom de l'Aide Complémentaire",
    },
  ];

  const additionalInputsConfig = [
    {
      field: 'searchByTagId',
      type: 'select',
      placeholder: 'Filtrer par tag',
      label: 'Tag',
      options: tagList && tagList.map(tag => ({
        value: tag.id,
        label: tag.name,
        color: tag.color,
      })),
    },
    {
      field: 'searchByTypePriseEnChargeId',
      type: 'select',
      placeholder: "Type de l'Aide Complémentaire",
      options:
        typePriseEnCharge &&
        typePriseEnCharge.map(typePriseEnCharge => ({
          value: typePriseEnCharge.id,
          label: typePriseEnCharge.name,
        })),
    },
    {
      field: 'searchByStatut',
      type: 'select',
      placeholder: 'Statut',
      options: [
        { value: 'planifier', label: 'Planifiée' },
        { value: 'enCours', label: 'En cours' },
        { value: 'enAttenteDexecution', label: "En attente d'exécution" },
        { value: 'executer', label: 'Exécutée' },
      ],
    },
    {
      field: 'searchByMontant',
      type: 'number',
      placeholder: 'Montant prévu',
    },
    {
      field: 'minDate',
      type: 'date',
      placeholder: 'Date de planification minimale',
    },
    {
      field: 'maxDate',
      type: 'date',
      placeholder: 'Date de planification maximale',
    },
  ];

  const [filterValues, setFilterValues] = useState({
    searchByNom: '',
    searchByStatut: '',
    searchByMontant: '',
    searchByTypePriseEnChargeId: '',
    minDate: '',
    maxDate: '',
    searchByTagId: '',
  });

  const handlePageChange = pageNumber => {
    const nextPage = pageNumber - 1;
    if (nextPage >= 0 && nextPage < aideComplementaire.totalPages) {
      setActivePage(nextPage);
      setFilterValues(prevFilterValues => {
        dispatch(loadAideComplementaire(nextPage, prevFilterValues));
        return prevFilterValues;
      });
    }
  };
  const handleResetFilterComplete = () => {
    setFilterValues({
      searchByNom: '',
      searchByStatut: '',
      searchByMontant: '',
      searchByTypePriseEnChargeId: '',
      minDate: '',
      maxDate: '',
      searchByTagId: '',
    });
  };

  let messageAddedDonation = null;

  useEffect(() => {
    if (props.location.state === 'success') {
      setSuccessMessage('Aide complémentaire ajoutée avec succès !');
      setShowAlert(true);
    } else if (props.location.state === 'updateSuccess') {
      setSuccessMessage('Aide complémentaire modifiée avec succès !');
      setShowAlert(true);
    }
  }, [props.location.state]);

  useEffect(() => {
    if (deleteAideComplementaireSuccess) {
      setSuccessMessage('Aide complémentaire supprimée avec succès !');
      setShowAlert(true);
      dispatch(deleteAideComplementaireReset());
      dispatch(loadAideComplementaire(0));
    }
  }, [deleteAideComplementaireSuccess]);

  const handleCloseSuccessMessage = () => {
    setShowAlert(false);
    history.replace({ ...location, state: null });
  };

  useEffect(() => {
    if (showAlert) {
      const timer = setTimeout(() => {
        handleCloseSuccessMessage();
      }, 4000);
      return () => clearTimeout(timer);
    }
  }, [showAlert]);

  if (showAlert) {
    messageAddedDonation = (
      <Alert
        className="alert-style"
        variant="success"
        onClose={() => {
          setShowAlert(false);
          props.history.replace({ ...props.location, state: null });
          if (alertTimeout) {
            clearTimeout(alertTimeout);
          }
        }}
        dismissible
      >
        <p>{successMessage}</p>
      </Alert>
    );
  }

  useEffect(() => {
    dispatch(loadAideComplementaire(0));
  }, []);

  useEffect(() => {
    dispatch(loadTypePrisenEnCharge());
  }, [dispatch]);

  // Load tags when component mounts
  useEffect(() => {
    dispatch(getTagsByType('aideComplementaire'));
  }, [dispatch]);

  useEffect(() => {
    if (aideComplementaire) {
      setActivePage(aideComplementaire.pageable.pageNumber + 1);
      setItemsCountPerPage(aideComplementaire.size);
      setTotalItemsCount(aideComplementaire.totalElements);
    }
  }, [aideComplementaire]);

  useEffect(() => {
    if (error && error.response && error.response.status === 403) {
      setErrorMessages("Votre profil n'a pas accès au module Donation.");
      const timeout = setTimeout(() => {
        setErrorMessages('');
      }, 4000);
      if (alertTimeout) {
        clearTimeout(alertTimeout);
      }
    }
  }, [error]);

  useEffect(() => {
    if (deleteAideComplementaireError) {
      setErrorMessages(
        "Erreur lors de la suppression de l'aide complémentaire.",
      );
      const timeout = setTimeout(() => {
        setErrorMessages('');
      }, 4000);
      if (alertTimeout) {
        clearTimeout(alertTimeout);
      }
    }
  }, [deleteAideComplementaireError]);

  let listAideComplementaire = (
    <div className="d-flex justify-content-center pb-3">
      {loading && (
        <div
          className="spinner-border"
          style={{ width: '5rem', height: '5rem' }}
          role="status"
        >
          <span className="sr-only">Loading...</span>
        </div>
      )}
    </div>
  );
  if (aideComplementaire) {
    listAideComplementaire = (
      <ListAideComplementaire
        listesGlobal={aideComplementaire}
        aideComplementaire={aideComplementaire.content}
        sortByColumn={sortByColumn}
        setSortByColumn={setSortByColumn}
        sortByColumnValue={sortByColumnValue}
        setSortByColumnValue={setSortByColumnValue}
        activePage={activePage}
      ></ListAideComplementaire>
    );
  }

  const handleApplyFilter = filters => {
    dispatch(loadAideComplementaire(0, filters));
  };

  const handleExport = () => {
    // dispatch(exportDonationsToCsv(filterValues));
  };

  return (
    <>
      {errorMessages && (
        <Alert
          className="alert-style"
          variant="danger"
          onClose={() => {
            setErrorMessages('');
          }}
          dismissible
        >
          <p>{errorMessages}</p>
        </Alert>
      )}
      {messageAddedDonation}
      <div className={listStyles.backgroundStyle}>
        <GenericFilter
          principalInputsConfig={principalInputsConfig}
          additionalInputsConfig={additionalInputsConfig}
          onApplyFilter={handleApplyFilter}
          filterValues={filterValues}
          setFilterValues={setFilterValues}
          onResetFilterComplete={handleResetFilterComplete}
          onExport={handleExport}
          data={aideComplementaire.content}
          exportLoading={exportLoading}
          showExportButton={false}
        />
        <div className={`${listStyles.head} d-flex justify-content-between`}>
          <h4>Liste des Aides Complémentaires</h4>
          <Link to="/aide-complementaire/add">
            <button className={btnStyles.addBtnProfile}>
              Ajouter une aide Complémentaire
            </button>
          </Link>
        </div>
        <div className="sub-container">
          {listAideComplementaire}
          <div className="justify-content-center mt-3">
            <CustomPagination
              onPageChange={handlePageChange}
              totalCount={
                aideComplementaire ? aideComplementaire.totalPages : 1
              }
              currentPage={
                aideComplementaire ? aideComplementaire.number + 1 : activePage
              }
              pageSize={aideComplementaire ? aideComplementaire.pageSize : 10}
            />
          </div>
        </div>
      </div>
    </>
  );
}
