import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON>lapse,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  IconButton,
  Paper,
  Switch,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TextField,
} from '@mui/material';
import {
  Delete,
  Edit,
  KeyboardArrowDown,
  KeyboardArrowUp,
} from '@mui/icons-material';
import { makeStyles } from '@mui/styles';
import {
  removeBeneficiaryRequest,
  removeDonorRequest,
  updateMontantBeneficiaryRequest,
  updateStatutValidationBeneficiaryRequest,
} from '../../../containers/AideComplementaire/FicheAideComplementaire/actions';
import { useDispatch } from 'react-redux';
import { Link } from 'react-router-dom';

const useStyles = makeStyles({
  tableHeader: {
    backgroundColor: '#f5f5f5',
    fontWeight: 'bold',
    textAlign: 'center',
  },
  beneficiaryHeader: {
    backgroundColor: '#e0e0e0',
    fontWeight: 'bold',
    textAlign: 'center',
  },
  iconButton: {
    padding: '2px',
  },
  actionIcons: {
    display: 'flex',
    gap: '8px',
    justifyContent: 'center',
  },
  actionCell: {
    width: '100px',
    textAlign: 'center',
  },
  smallIcon: {
    fontSize: '1.5rem',
  },
  beneficiaryRow: {
    background: 'linear-gradient(to bottom, #e0e0e0, #ffffff)', // Dégradé
  },
  tableCell: {
    width: '150px',
    textAlign: 'center',
    verticalAlign: 'middle',
  },
  smallCell: {
    width: '50px',
    textAlign: 'center',
  },
  clickableChip: {
    cursor: 'pointer',
    textDecoration: 'none',
  },
});

function DonorTable({ donorAideComplemenatireDTOList, aideComplementaire }) {
  const classes = useStyles();
  const [open, setOpen] = React.useState({});
  const dispatch = useDispatch();

  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);
  const [deleteDonorDialogOpen, setDeleteDonorDialogOpen] = useState(false);
  const [selectedBeneficiaryId, setSelectedBeneficiaryId] = useState(null);
  const [selectedDonorId, setSelectedDonorId] = useState(null);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [newAmount, setNewAmount] = useState('');

  const aideComplementaireId = aideComplementaire.id;

  const toggleRow = id => {
    setOpen(prevState => ({ ...prevState, [id]: !prevState[id] }));
  };

  const handleSwitchChange = (event, beneficiary) => {
    dispatch(
      updateStatutValidationBeneficiaryRequest(
        aideComplementaireId,
        beneficiary.id,
        { source: 'DonorTable' },
      ),
    );
  };

  const handleDeleteClick = idBeneficiary => {
    setSelectedBeneficiaryId(idBeneficiary);
    setConfirmDialogOpen(true);
  };

  const handleDeleteDonorClick = idDonor => {
    setSelectedDonorId(idDonor);
    setDeleteDonorDialogOpen(true);
  };

  const handleConfirmDonorDelete = () => {
    dispatch(
      removeDonorRequest(aideComplementaireId, selectedDonorId, {
        source: 'DonorTable',
      }),
    );
    setDeleteDonorDialogOpen(false);
  };

  const handleConfirmDelete = () => {
    dispatch(
      removeBeneficiaryRequest(aideComplementaireId, selectedBeneficiaryId, {
        source: 'DonorTable',
      }),
    );
    setConfirmDialogOpen(false);
  };

  const handleCancelDelete = () => {
    setConfirmDialogOpen(false);
    setDeleteDonorDialogOpen(false);
  };

  const handleEditClick = beneficiary => {
    setNewAmount(beneficiary.montantAbeneficier || '');
    setSelectedBeneficiaryId(beneficiary.id);
    setEditDialogOpen(true);
  };

  const handleConfirmEdit = () => {
    dispatch(
      updateMontantBeneficiaryRequest(
        aideComplementaireId,
        selectedBeneficiaryId,
        newAmount,
        { source: 'DonorTable' },
      ),
    );
    setEditDialogOpen(false);
  };

  const handleCancelEdit = () => {
    setEditDialogOpen(false);
  };

  return (
    <>
      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow className={classes.tableHeader}>
              <TableCell className={classes.smallCell} />
              <TableCell className={classes.tableCell}>Code Donateur</TableCell>
              <TableCell className={classes.tableCell}>Nom Complet</TableCell>
              <TableCell className={classes.tableCell}>Montant Total</TableCell>
              <TableCell className={classes.tableCell}>
                Montant par bénéficiaire
              </TableCell>
              <TableCell className={classes.tableCell}>
                Montant Restant
              </TableCell>
              <TableCell className={classes.tableCell}>
                Bénéficiaires Associés
              </TableCell>
              <TableCell className={classes.actionCell}>Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {donorAideComplemenatireDTOList.map(donor => (
              <React.Fragment key={donor.id}>
                <TableRow>
                  <TableCell className={classes.smallCell}>
                    {donor.beneficiaryAideComplemenatireDTOList &&
                    donor.beneficiaryAideComplemenatireDTOList.length > 0 ? (
                      <IconButton
                        className={classes.iconButton}
                        onClick={() => toggleRow(donor.id)}
                      >
                        {open[donor.id] ? (
                          <KeyboardArrowUp className={classes.smallIcon} />
                        ) : (
                          <KeyboardArrowDown className={classes.smallIcon} />
                        )}
                      </IconButton>
                    ) : null}
                  </TableCell>
                  <TableCell className={classes.tableCell}>
                    <Link to={`/donors/fiche/${donor.id}/info`}>
                      {donor.code}
                    </Link>
                  </TableCell>
                  <TableCell
                    className={classes.tableCell}
                  >{`${donor.firstName} ${donor.lastName}`}</TableCell>
                  <TableCell className={classes.tableCell}>
                    {donor.montantTotalDuDonateur || 'N/A'}
                  </TableCell>
                  <TableCell className={classes.tableCell}>
                    {donor.montantPoserDuDonateur || 'N/A'}
                  </TableCell>
                  <TableCell className={classes.tableCell}>
                    {donor.montantRestantDuDonateur !== null &&
                    donor.montantRestantDuDonateur !== undefined
                      ? donor.montantRestantDuDonateur === 0
                        ? 0
                        : donor.montantRestantDuDonateur
                      : 'N/A'}
                  </TableCell>
                  <TableCell className={classes.tableCell}>
                    {donor.beneficiaryAideComplemenatireDTOList
                      ? donor.beneficiaryAideComplemenatireDTOList.length
                      : '0'}
                  </TableCell>
                  <TableCell className={classes.actionCell}>
                    <IconButton
                      className={classes.iconButton}
                      disableRipple
                      style={{ backgroundColor: 'transparent' }}
                      onClick={() => handleDeleteDonorClick(donor.id)}
                    >
                      <Delete className={classes.smallIcon} color="error" />
                    </IconButton>
                  </TableCell>
                </TableRow>

                {/* Beneficiaries Rows */}
                {donor.beneficiaryAideComplemenatireDTOList &&
                  donor.beneficiaryAideComplemenatireDTOList.length > 0 && (
                    <TableRow>
                      <TableCell colSpan={8}>
                        <Collapse
                          in={open[donor.id]}
                          timeout="auto"
                          unmountOnExit
                        >
                          <Table size="small">
                            <TableHead>
                              <TableRow className={classes.beneficiaryHeader}>
                                <TableCell className={classes.tableCell}>
                                  Code Bénéficiaire
                                </TableCell>
                                <TableCell className={classes.tableCell}>
                                  Nom Complet
                                </TableCell>
                                <TableCell className={classes.tableCell}>
                                  Type
                                </TableCell>
                                <TableCell className={classes.tableCell}>
                                  Montant À Bénéficier
                                </TableCell>
                                <TableCell className={classes.tableCell}>
                                  Statut
                                </TableCell>
                                <TableCell className={classes.actionCell}>
                                  Actions
                                </TableCell>
                              </TableRow>
                            </TableHead>
                            <TableBody>
                              {donor.beneficiaryAideComplemenatireDTOList.map(
                                beneficiary => (
                                  <TableRow
                                    key={beneficiary.id}
                                    className={classes.beneficiaryRow}
                                  >
                                    <TableCell className={classes.tableCell}>
                                      <Link
                                        to={`/beneficiaries/fiche/${beneficiary.id}/info`}
                                      >
                                        {beneficiary.code}
                                      </Link>
                                    </TableCell>
                                    <TableCell
                                      className={classes.tableCell}
                                    >{`${beneficiary.firstName} ${beneficiary.lastName}`}</TableCell>
                                    <TableCell className={classes.tableCell}>
                                      {beneficiary.independent ? (
                                        <Chip
                                          label="Indépendant"
                                          color="primary"
                                        />
                                      ) : (
                                        <Link
                                          to={`/beneficiaries/fiche/${beneficiary.id}/family`}
                                          className={classes.clickableChip}
                                        >
                                          <Chip
                                            label="Membre famille"
                                            color="warning"
                                            clickable
                                          />
                                        </Link>
                                      )}
                                    </TableCell>
                                    <TableCell className={classes.tableCell}>
                                      {beneficiary.montantAbeneficier}
                                    </TableCell>
                                    <TableCell className={classes.tableCell}>
                                      {beneficiary.statutValidation ? (
                                        <Chip label="Validé" color="success" />
                                      ) : (
                                        <Chip
                                          label="Non validé"
                                          color="default"
                                        />
                                      )}
                                    </TableCell>
                                    <TableCell className={classes.actionCell}>
                                      <div className={classes.actionIcons}>
                                        <Switch
                                          checked={beneficiary.statutValidation}
                                          onChange={event =>
                                            handleSwitchChange(
                                              event,
                                              beneficiary,
                                            )
                                          }
                                          color="success"
                                        />
                                        {!beneficiary.statutValidation && (
                                          <>
                                            <IconButton
                                              className={classes.iconButton}
                                              disableRipple
                                              style={{
                                                backgroundColor: 'transparent',
                                              }}
                                              onClick={() =>
                                                handleEditClick(beneficiary)
                                              }
                                            >
                                              <Edit
                                                className={classes.smallIcon}
                                                color="primary"
                                              />
                                            </IconButton>
                                            <IconButton
                                              className={classes.iconButton}
                                              disableRipple
                                              style={{
                                                backgroundColor: 'transparent',
                                              }}
                                              onClick={() =>
                                                handleDeleteClick(
                                                  beneficiary.id,
                                                )
                                              }
                                            >
                                              <Delete
                                                className={classes.smallIcon}
                                                color="error"
                                              />
                                            </IconButton>
                                          </>
                                        )}
                                      </div>
                                    </TableCell>
                                  </TableRow>
                                ),
                              )}
                            </TableBody>
                          </Table>
                        </Collapse>
                      </TableCell>
                    </TableRow>
                  )}
              </React.Fragment>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Confirmation Modal */}
      <Dialog open={confirmDialogOpen} onClose={handleCancelDelete}>
        <DialogTitle>Confirmer la suppression</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Êtes-vous sûr de vouloir supprimer ce bénéficiaire ?
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCancelDelete} color="primary">
            Annuler
          </Button>
          <Button onClick={handleConfirmDelete} color="error" autoFocus>
            Supprimer
          </Button>
        </DialogActions>
      </Dialog>

      {/* Delete Donor Modal */}
      <Dialog open={deleteDonorDialogOpen} onClose={handleCancelDelete}>
        <DialogTitle>Confirmer la suppression</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Êtes-vous sûr de vouloir supprimer ce donateur ?
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCancelDelete} color="primary">
            Annuler
          </Button>
          <Button onClick={handleConfirmDonorDelete} color="error" autoFocus>
            Supprimer
          </Button>
        </DialogActions>
      </Dialog>

      {/* Edit Amount Dialog */}
      <Dialog
        open={editDialogOpen}
        onClose={handleCancelEdit}
        sx={{
          '& .MuiDialog-paper': {
            padding: '20px',
            borderRadius: '12px',
            backgroundColor: '#f9f9f9',
          },
        }}
      >
        <DialogTitle
          sx={{
            fontSize: '1.5rem',
            fontWeight: 'bold',
            color: '#333',
            marginBottom: '10px',
          }}
        >
          Modifier le Montant À Bénéficier
        </DialogTitle>
        <DialogContent>
          <DialogContentText
            sx={{ fontSize: '1.1rem', color: '#555', marginBottom: '20px' }}
          >
            Veuillez entrer le nouveau montant à bénéficier:
          </DialogContentText>
          <TextField
            type="number"
            value={newAmount}
            onChange={e => setNewAmount(e.target.value)}
            placeholder="Montant"
            variant="outlined"
            sx={{
              '& .MuiInputBase-input': { fontSize: '1.2rem', padding: '12px' },
              '& .MuiOutlinedInput-root': {
                borderRadius: '8px',
                borderColor: '#ddd',
                boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
              },
              marginBottom: '20px',
              width: '100%',
            }}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCancelEdit} color="primary">
            Annuler
          </Button>
          <Button onClick={handleConfirmEdit} color="error" autoFocus>
            Confirmer
          </Button>
          {/*<Button
            onClick={handleCancelEdit}
            sx={{
              fontSize: '1rem',
              fontWeight: 'bold',
              padding: '10px 20px',
              borderRadius: '8px',
              textTransform: 'none',
              backgroundColor: '#f44336',
              color: '#fff',
              '&:hover': { backgroundColor: '#d32f2f' },
              marginRight: '10px',
            }}
          >
            Annuler
          </Button>
          <Button
            onClick={handleConfirmEdit}
            sx={{
              fontSize: '1rem',
              fontWeight: 'bold',
              padding: '10px 20px',
              borderRadius: '8px',
              textTransform: 'none',
              backgroundColor: '#4caf50',
              color: '#fff',
              '&:hover': { backgroundColor: '#388e3c' },
            }}
          >
            Confirmer
          </Button>
          */}
        </DialogActions>
      </Dialog>
    </>
  );
}

export default DonorTable;
