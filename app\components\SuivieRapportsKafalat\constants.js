export const GET_LIST_RAPPORT = 'SUIVIE_RAPPORT/GET_LIST_RAPPORT';
export const GET_LIST_RAPPORT_SUCCESS =
  'SUIVIE_RAPPORT/GET_LIST_RAPPORT_SUCCESS';
export const GET_LIST_RAPPORT_ERROR = 'SUIVIE_RAPPORT/GET_LIST_RAPPORT_ERROR';

export const VIEW_RAPPORT = 'SUIVIE_RAPPORT/VIEW_RAPPORT';
export const VIEW_RAPPORT_SUCCESS = 'SUIVIE_RAPPORT/VIEW_RAPPORT_SUCCESS';
export const VIEW_RAPPORT_ERROR = 'SUIVIE_RAPPORT/VIEW_RAPPORT_ERROR';
export const VIEW_RAPPORT_RESET = 'SUIVIE_RAPPORT/VIEW_RAPPORT_RESET';

export const NOTIFY_ASSISTANT = 'SUIVIE_RAPPORT/NOTIFY_ASSISTANT_ERROR';
export const NOTIFY_ASSISTANT_SUCCESS =
  'SUIVIE_RAPPORT/NOTIFY_ASSISTANT_SUCCESS';
export const NOTIFY_ASSISTANT_ERROR = 'SUIVIE_RAPPORT/NOTIFY_ASSISTANT_ERROR';
export const NOTIFY_ASSISTANT_RESET = 'SUIVIE_RAPPORT/NOTIFY_ASSISTANT_RESET';

export const GET_LIST_DONOR = 'SUIVIE_RAPPORT/GET_LIST_DONOR';
export const GET_LIST_DONOR_SUCCESS = 'SUIVIE_RAPPORT/GET_LIST_DONOR_SUCCESS';
export const GET_LIST_DONOR_ERROR = 'SUIVIE_RAPPORT/GET_LIST_DONOR_ERROR';
export const GET_LIST_DONOR_RESET = 'SUIVIE_RAPPORT/GET_LIST_DONOR_RESET';

export const VIEW_RAPPORT_WITH_DONOR = 'SUIVIE_RAPPORT/VIEW_RAPPORT_WITH_DONOR';
export const VIEW_RAPPORT_WITH_DONOR_SUCCESS =
  'SUIVIE_RAPPORT/VIEW_RAPPORT_WITH_DONOR_SUCCESS';
export const VIEW_RAPPORT_WITH_DONOR_ERROR =
  'SUIVIE_RAPPORT/VIEW_RAPPORT_WITH_DONOR_ERROR';
export const RESET_VIEW_RAPPORT_WITH_DONOR_SUCCESS =
  'SUIVIE_RAPPORT/RESET_VIEW_RAPPORT_WITH_DONOR_SUCCESS';

export const ADD_AGENDA_RAPPORT_AUTOMATIC =
  'SUIVIE_RAPPORT/ADD_AGENDA_RAPPORT_AUTOMATIC';
export const ADD_AGENDA_RAPPORT_AUTOMATIC_SUCCESS =
  'SUIVIE_RAPPORT/ADD_AGENDA_RAPPORT_AUTOMATIC_SUCCESS';
export const ADD_AGENDA_RAPPORT_AUTOMATIC_ERROR =
  'SUIVIE_RAPPORT/ADD_AGENDA_RAPPORT_AUTOMATIC_ERROR';
export const ADD_AGENDA_RAPPORT_AUTOMATIC_RESET =
  'SUIVIE_RAPPORT/ADD_AGENDA_RAPPORT_AUTOMATIC_RESET';

export const principalInputsConfig = [
  {
    field: 'reportStatus',
    type: 'select',
    placeholder: 'Statut du rapport',
    options: [
      { value: 'RAPPORT_PLANIFIER', label: 'Planifié' },
      { value: 'RAPPORT_A_PREPARE', label: 'À préparer' },
      { value: 'RAPPORT_INITIAL', label: 'Initial' },
      {
        value: 'RAPPORT_VALIDER_ASSISTANCE',
        label: "Validé par l'assistant",
      },
      { value: 'RAPPORT_VALIDER_KAFALAT', label: 'Validé par Kafalat' },
      {
        value: 'RAPPORT_A_COMPLETER_PAR_ASSISTANCE',
        label: "Rapport à compléter par l'assistant",
      },
      {
        value: 'RAPPORT_A_COMPLETER_PAR_KAFALAT',
        label: 'Rapport à compléter par Kafalat',
      },
      { value: 'RAPPORT_FINAL', label: 'Final' },
    ],
    widthStyles: {
      width: 250,
    },
  },
  {
    field: 'beneficiaryCode',
    type: 'text',
    placeholder: 'Code Bénéficiaire',
  },
];

export const additionalInputsConfig = [
  {
    field: 'beneficiaryName',
    type: 'text',
    placeholder: 'Nom Bénéficiaire',
  },
  {
    field: 'numberRapport',
    type: 'number',
    placeholder: 'Numéro du Rapport',
  },
  {
    field: 'plannedDateStart',
    type: 'date',
    placeholder: 'Date de début de planification',
  },
  {
    field: 'plannedDateEnd',
    type: 'date',
    placeholder: 'Date de fin de planification',
  },
  {
    field: 'validationDateStart',
    type: 'date',
    placeholder: 'Date de début de validation',
  },
  {
    field: 'validationDateEnd',
    type: 'date',
    placeholder: 'Date de fin de validation',
  },
];
