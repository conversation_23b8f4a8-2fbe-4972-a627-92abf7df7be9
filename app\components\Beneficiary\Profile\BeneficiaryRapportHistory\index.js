import React, { useEffect, useState, useMemo, useCallback } from 'react';
import moment from 'moment';
import { useHistory, useLocation, useParams, Link } from 'react-router-dom';
import { createStructuredSelector } from 'reselect';
import { useDispatch, useSelector } from 'react-redux';
import { useInjectReducer, useInjectSaga } from 'redux-injectors';
import reducer from 'containers/Common/SubComponents/RapportForm/reducer';
import {
  getAllHistoryRapportBeneficiary,
  getAllRapportBeneficiary,
  rapportViewingReset,
  resetViewRapportWithDonorSuccess,
  viewRapport,
  viewRapportWithDonor,
} from 'containers/Common/SubComponents/RapportForm/actions';
import {
  makeSelectAllHistoryRapportBeneficiary,
  makeSelectAllHistoryRapportBeneficiaryLoading,
  makeSelectSuccess,
  makeSelecRapportWithDonorViewSuccess,
  makeSelecRapportWithDonorViewLoading,
} from 'containers/Common/SubComponents/RapportForm/selectors';
import CustomPagination from 'components/Common/CustomPagination';
import DataTable from 'components/Common/DataTable';
import { rapportSaga } from 'containers/Common/SubComponents/RapportForm/saga';
import list from '../../../../Css/profileList.css';
import GenericFilter from 'containers/Common/Filter/GenericFilter';
import { ARTICLE_ICON } from 'components/Common/ListIcons/ListIcons';
import Modal2 from 'components/Common/Modal2';
import btnStyles from '../../../../Css/button.css';

const key = 'rapportAdd';

const target = 'beneficiary';

const formatDate = date => moment(date).format('DD/MM/YYYY');

const omdbSelector = createStructuredSelector({
  success: makeSelectSuccess,
  historyRapportBeneficiary: makeSelectAllHistoryRapportBeneficiary,
  loadingHistoryRapportBeneficiary: makeSelectAllHistoryRapportBeneficiaryLoading,
  rapportWithDonorViewLoading: makeSelecRapportWithDonorViewLoading,
  successViewRapportWithDonor: makeSelecRapportWithDonorViewSuccess,
});

const listLangues = [
  { id: 1, value: 'Arabe', key: 'ar' },
  { id: 2, value: 'Français', key: 'fr' },
  { id: 3, value: 'Anglais', key: 'en' },
];

export default function BeneficiaryRapportHistory(props) {
  const dispatch = useDispatch();
  const params = useParams();
  const history = useHistory();

  const beneficiaryId = useMemo(() => (params && params.id ? params.id : 0), [
    params,
    params.id,
  ]);
  const {
    error,
    historyRapportBeneficiary,
    loadingHistoryRapportBeneficiary,
    rapportWithDonorViewLoading,
    successViewRapportWithDonor,
  } = useSelector(omdbSelector);
  useInjectReducer({ key, reducer });
  useInjectSaga({ key, saga: rapportSaga });

  const location = useLocation();
  const principalInputsConfig = [
    {
      field: 'donorName',
      type: 'text',
      placeholder: 'Nom complet du donateur',
    },
    {
      field: 'communicatedBy',
      type: 'text',
      placeholder: 'Communiqué par',
    },
  ];
  const additionalInputsConfig = [
    {
      field: 'dateCommunicatedStart',
      type: 'date',
      placeholder: 'Date de début de communication',
    },
    {
      field: 'dateCommunicatedEnd',
      type: 'date',
      placeholder: 'Date de fin de communication',
    },
  ];

  const [filterValues, setFilterValues] = useState({
    communicatedBy: '',
    donorName: '',
    dateCommunicatedStart: '',
    dateCommunicatedEnd: '',
  });

  const handleResetFilterComplete = () => {
    setFilterValues({
      communicatedBy: '',
      donorName: '',
      dateCommunicatedStart: '',
      dateCommunicatedEnd: '',
    });
  };

  const [showSelectLangRapportModal, setShowSelectLangRapportModal] = useState(
    false,
  );
  const [selectedRapportId, setSelectedRapportId] = useState(null);
  const [selectedDonorId, setSelectedDonorId] = useState(null);
  const [chosenLanguage, setChosenLanguage] = useState(null);
  const [errorsLangueInfo, setErrorsLangueInfo] = useState({
    selection: {
      isError: false,
      errorMessage: 'Veuillez choisir une langue',
    },
  });

  const [currentPage, setCurrentPage] = useState(0);

  const handlePageChange = pageNumber => {
    setCurrentPage(pageNumber - 1);
    setFilterValues(prevFilterValues => {
      dispatch(
        getAllHistoryRapportBeneficiary({
          id: beneficiaryId,
          pageNumber: pageNumber - 1,
          filters: prevFilterValues,
        }),
      );
      return prevFilterValues;
    });
  };

  const handleApplyFilter = filters => {
    dispatch(
      getAllHistoryRapportBeneficiary({
        id: beneficiaryId,
        pageNumber: 0,
        filters: filters,
      }),
    );
  };

  useEffect(() => {
    if (location && location.state && location.state.success) {
      setSuccessMessageAddition(location.state.success);
      setShowAlert(true);
    }
  }, [location]);

  useEffect(() => {
    if (beneficiaryId) {
      dispatch(
        getAllHistoryRapportBeneficiary({
          id: beneficiaryId,
          pageNumber: currentPage,
        }),
      );
    }
  }, [beneficiaryId]);

  const handleViewRapport = useCallback(
    rapportId => {
      dispatch(viewRapport(rapportId, target));
    },
    [dispatch],
  );

  const handleViewRapportWithDonor = useCallback((rapportId, donorId) => {
    setSelectedRapportId(rapportId);
    setSelectedDonorId(donorId);
    setShowSelectLangRapportModal(true);
  }, []);

  const handleCloseForSelectLangRapportModal = () => {
    setErrorsLangueInfo({
      selection: {
        isError: false,
        errorMessage: 'Veuiller Choisir une langue',
      },
    });
    setShowSelectLangRapportModal(false);
  };

  const handleChangeLangueSelecttion = e => {
    const value = e.target.value;
    setChosenLanguage(value);
    if (errorsLangueInfo.selection.isError) {
      setErrorsLangueInfo(prev => {
        return { ...prev, selection: { ...prev.selection, isError: false } };
      });
    }
  };

  const handleSubmitViewRapportWithDonor = useCallback(() => {
    if (!chosenLanguage) {
      setErrorsLangueInfo({
        selection: {
          errorMessage: 'Veuillez choisir une langue',
          isError: true,
        },
      });
    } else {
      dispatch(
        viewRapportWithDonor({
          rapportId: selectedRapportId,
          donorId: selectedDonorId,
          language: chosenLanguage,
          target,
        }),
      );
    }
  }, [chosenLanguage, selectedRapportId, selectedDonorId, dispatch]);

  useEffect(() => {
    if (successViewRapportWithDonor) {
      handleCloseForSelectLangRapportModal();
      setTimeout(() => {
        dispatch(resetViewRapportWithDonorSuccess());
        setChosenLanguage(null);
      }, 3200);
    }
  }, [successViewRapportWithDonor]);

  if (error) {
    errorMessage = 'Une erreur est survenue';
  }
  const listRapports =
    (historyRapportBeneficiary &&
      historyRapportBeneficiary.content &&
      historyRapportBeneficiary.content.map(beneRapport => ({
        id: beneRapport.id,
        dateCommunicated: beneRapport.dateCommunicated
          ? formatDate(beneRapport.dateCommunicated)
          : '',
        communicatedBy: beneRapport.communicatedBy
          ? beneRapport.communicatedBy
          : '-',
        donorName: beneRapport.donorName ? beneRapport.donorName : '-',
        action: { id: beneRapport.id, ...beneRapport },
        rapportId: beneRapport.rapportId,
        codeRapport: beneRapport.codeRapport,
        donorId: beneRapport.donorId,
        numberRapport: beneRapport.numberRapport
          ? `${beneRapport.numberRapport} / ${beneRapport.year}`
          : '-',
        year: beneRapport.year ? beneRapport.year : null,
        beneRapport,
      }))) ||
    [];

  const columns = [
    {
      field: 'numberRapport',
      headerName: 'Numéro du Rapport',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
    {
      field: 'dateCommunicated',
      headerName: 'Date de communication',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
    {
      field: 'communicatedBy',
      headerName: 'Communiqué par',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
    {
      field: 'donorName',
      headerName: 'Donateur Destinataire ',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
      renderCell: params => (
        <Link to={`/donors/fiche/${params.row.donorId}/info`}>
          {params.row.donorName}
        </Link>
      ),
    },
    {
      field: 'Rapport',
      headerName: 'Rapport',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
      renderCell: params => (
        <>
          <input
            type="image"
            src={ARTICLE_ICON}
            className="p-2"
            width="40px"
            height="40px"
            title="visualiser"
            onClick={() => {
              handleViewRapportWithDonor(
                params.row.rapportId,
                params.row.donorId,
              );
            }}
          />
        </>
      ),
    },
  ];
  return (
    <div>
      <Modal2
        centered
        className="mt-5"
        title="visualisation"
        show={showSelectLangRapportModal}
        handleClose={handleCloseForSelectLangRapportModal}
      >
        <div className="mb-3">
          <label htmlFor="statutSelect" className="form-label">
            Veuillez sélectionner la langue dans laquelle vous souhaitez
            visualiser le rapport. <span className="text-danger">*</span>
          </label>
          <select
            className={`form-select custom-select ${
              errorsLangueInfo.selection.isError ? 'is-invalid' : ''
            }`}
            onChange={handleChangeLangueSelecttion}
          >
            <option value="">sélectionner la langue</option>
            {listLangues.map(el => (
              <option key={el.key} value={el.key} id={el.id}>
                {el.value}
              </option>
            ))}
          </select>
          {errorsLangueInfo.selection.isError && (
            <div className="text-danger">Ce champ est requis.</div>
          )}
        </div>
        <div className="d-flex justify-content-end px-3 my-1">
          <button
            type="button"
            className={`mx-2 ${btnStyles.cancelBtn}`}
            onClick={handleCloseForSelectLangRapportModal}
          >
            Annuler
          </button>
          <button
            type="button"
            className={`mx-2 ${btnStyles.rejectBtn}`}
            onClick={handleSubmitViewRapportWithDonor}
          >
            {rapportWithDonorViewLoading ? 'loading...' : 'Confirmer'}
          </button>
        </div>
      </Modal2>

      <div className={list.backgroudStyle}>
        <div className={list.global}>
          <div className={list.header}>
            <h4>Liste des historiques des communications</h4>
          </div>
          <div className={list.content}>
            <GenericFilter
              className="col-12"
              principalInputsConfig={principalInputsConfig}
              additionalInputsConfig={additionalInputsConfig}
              onApplyFilter={handleApplyFilter}
              filterValues={filterValues}
              setFilterValues={setFilterValues}
              onResetFilterComplete={handleResetFilterComplete}
            />
            <DataTable
              rows={listRapports}
              loading={loadingHistoryRapportBeneficiary}
              columns={columns}
              fileName={`Liste des historiques rapports du beneficiaire ${
                params && params.id ? params.id : 0
              } , ${new Date().toLocaleString()}`}
            />

            <div className="row justify-content-center my-4">
              {historyRapportBeneficiary && (
                <CustomPagination
                  totalElements={historyRapportBeneficiary.totalElements}
                  totalCount={
                    Math.ceil(
                      historyRapportBeneficiary &&
                        historyRapportBeneficiary.totalElements &&
                        historyRapportBeneficiary.pageable.pageSize &&
                        historyRapportBeneficiary.totalElements /
                          historyRapportBeneficiary.pageable.pageSize,
                    ) || 1
                  }
                  pageSize={
                    (historyRapportBeneficiary &&
                      historyRapportBeneficiary.pageable &&
                      historyRapportBeneficiary.pageable.pageSize &&
                      historyRapportBeneficiary.pageable.pageSize) ||
                    10
                  }
                  currentPage={currentPage + 1 || 1}
                  onPageChange={handlePageChange}
                />
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
