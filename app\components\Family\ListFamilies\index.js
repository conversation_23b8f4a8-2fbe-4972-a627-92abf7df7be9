import React from 'react';
import { useHistory } from 'react-router-dom';
import moment from 'moment';
import { VIEW_ICON } from '../../Common/ListIcons/ListIcons';
import GetBeneficiariesInformationsFromFamily from '../Common/Functions/GetBeneficiariesInformationsFromFamily';
import GetTutor from '../Common/Functions/GetActiveTutor';
import DataTable from 'components/Common/DataTable';
import numberStyle from 'Css/familyNumber.css';
import styled from 'styled-components';

const formatDate = date => moment(date).format('DD/MM/YYYY');

// Styled component for the tags container in the table
const TagsContainer = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  max-width: 200px;
`;

// Styled component for individual tags in the table
const TableTag = styled.div`
  padding: 2px 8px;
  font-size: 11px;
  white-space: nowrap;
  border-radius: 10px;
  background-color: ${props => `#${props.color || 'cccccc'}`};
  color: ${props => {
    const hex = props.color || 'cccccc';
    const r = parseInt(hex.substr(0, 2), 16);
    const g = parseInt(hex.substr(2, 2), 16);
    const b = parseInt(hex.substr(4, 2), 16);
    const brightness = (r * 299 + g * 587 + b * 114) / 1000;
    return brightness > 128 ? '#000000' : '#ffffff';
  }};
`;

export default function ListFamilies(props) {
  let listFamilies = <p>Vide</p>;
  let beneficiariesInfo = null;
  const families = props.families.content;
  const liste1 = props.families;

  const history = useHistory();

  const viewHandler = (id, destination) => {
    if (destination == 'fiche') {
      history.push(`/families/fiche/${id}/members`, { params: id });
    }
  };

  const tutor = null;
  let member = null;

  const getFamilyName = family => {
    let familyName = null;
    if (family && family.familyMembers) {
      let members = family.familyMembers;
      for (let i = 0; i < members.length; i++) {
        if (
          members[i].familyRelationship &&
          members[i].familyRelationship.id === 1
        ) {
          familyName = members[i];
          break;
        }
      }
    }
    return familyName;
  };

  if (props.families) {
    listFamilies = families.map((family, index) => {
      if (family.familyMembers) {
        beneficiariesInfo = GetBeneficiariesInformationsFromFamily(
          family.familyMembers,
        );
        member = family.familyMembers.filter(member => member.tutor);
        const tutor = GetTutor(family);
        const familyPere = getFamilyName(family);
        const tutorName = tutor ? tutor.person.lastName : '---';
        const tutorCIN = tutor ? tutor.person.identityCode : '---';
        const tutorNameArb = tutor ? tutor.person.lastNameAr : '---';
        const familyName = familyPere ? familyPere.person.lastName : '---';
        return {
          id: family.id,
          code: family.code,
          createdAt: formatDate(family.createdAt),
          tutorName: tutorName,
          tutorCIN: tutorCIN,
          familyName: familyName,
          tutorNameArb: tutorNameArb,
          familySize: beneficiariesInfo.familySize,
          count: beneficiariesInfo.count,
          countCandidate: beneficiariesInfo.countCandidate,
          totalGiven: `${beneficiariesInfo.totalGiven} DH`,
          serviceInProgress: beneficiariesInfo.serviceInProgress,
          serviceSuspended: beneficiariesInfo.serviceSuspended,
          servicePending: beneficiariesInfo.servicePending,
          serviceClosed: beneficiariesInfo.serviceClosed,
          tags: family.tags || [],
        };
      }
    });
  }

  const columns = [
    {
      field: 'code',
      headerName: 'Code famille',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
    {
      field: 'createdAt',
      headerName: 'Date inscription',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
    {
      field: 'familyName',
      headerName: 'Nom famille',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
    {
      field: 'tutorName',
      headerName: 'Nom Tuteur',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
    {
      field: 'tutorNameArb',
      headerName: 'نسب الوصي',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
    {
      field: 'tutorCIN',
      headerName: "N° d'identité du tuteur",
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
    {
      field: 'familySize',
      headerName: 'Nombre des membres',
      flex: 1.5,
      headerAlign: 'center',
      align: 'center',
      renderCell: params => {
        const className =
          numberStyle && numberStyle.numberBlue ? numberStyle.numberBlue : '';
        const style =
          numberStyle && numberStyle.numberBlue
            ? {}
            : {
                border: '2px #A4A9B8 solid',
                backgroundColor: '#F1F4F6',
                color: '#A4A9B8',
                fontWeight: 700,
                borderRadius: '5px',
                padding: '1% 4%',
                fontSize: '1.08em',
              };

        return (
          <div className={className} style={style}>
            {params.value}
          </div>
        );
      },
    },
    {
      field: 'count',
      headerName: 'Nombre des bénéficiaires',
      flex: 1.5,
      headerAlign: 'center',
      align: 'center',
      renderCell: params => {
        const className =
          numberStyle && numberStyle.numberGreen ? numberStyle.numberGreen : '';
        const style =
          numberStyle && numberStyle.numberGreen
            ? {}
            : {
                border: '2px #A4A9B8 solid',
                backgroundColor: '#F1F4F6',
                color: '#A4A9B8',
                fontWeight: 700,
                borderRadius: '5px',
                padding: '1% 4%',
                fontSize: '1.08em',
              };

        return (
          <div className={className} style={style}>
            {params.value}
          </div>
        );
      },
    },
    {
      field: 'countCandidate',
      headerName: 'Nombre des candidats',
      flex: 1.5,
      headerAlign: 'center',
      align: 'center',
      renderCell: params => {
        const className =
          numberStyle && numberStyle.numberYellow
            ? numberStyle.numberYellow
            : '';
        const style =
          numberStyle && numberStyle.numberYellow
            ? {}
            : {
                border: '2px #A4A9B8 solid',
                backgroundColor: '#F1F4F6',
                color: '#A4A9B8',
                fontWeight: 700,
                borderRadius: '5px',
                padding: '1% 4%',
                fontSize: '1.08em',
              };

        return (
          <div className={className} style={style}>
            {params.value}
          </div>
        );
      },
    },

    {
      field: 'totalGiven',
      headerName: 'Total reçu',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
    {
      field: 'tags',
      headerName: 'Tags',
      flex: 1.5,
      headerAlign: 'center',
      align: 'center',
      renderCell: params => {
        const tags = params.row.tags || [];
        return (
          <TagsContainer>
            {tags && tags.length > 0 ? (
              tags.map(tag => (
                <TableTag key={tag.id} color={tag.color || 'cccccc'}>
                  {tag.name}
                </TableTag>
              ))
            ) : (
              <span style={{ color: '#999', fontSize: '12px' }}>Aucun tag</span>
            )}
          </TagsContainer>
        );
      },
    },
    /* {
       field: 'status',
       headerName: 'Statut',
       flex: 1,
       headerAlign: 'center',
       align: 'center',
       renderCell: params => (
         <div
           className="align-middle d-flex flex-column p-0 pb-1"
           style={params.rowIndex === 0 ? { borderTop: '0px' } : null}
         >
           {params.row.serviceInProgress > 0 ? (
             <div className={`mt-1 ${tagStyles.tagGreen}`}>
               en cours ({params.row.serviceInProgress})
             </div>
           ) : null}
           {params.row.serviceSuspended > 0 ? (
             <div className={`mt-1 ${tagStyles.tagYellow}`}>
               suspendue ({params.row.serviceSuspended})
             </div>
           ) : null}
           {params.row.servicePending > 0 ? (
             <div className={`mt-1 ${tagStyles.tagRed}`}>
               en attente ({params.row.servicePending})
             </div>
           ) : null}
           {params.row.serviceClosed > 0 ? (
             <div className={`mt-1 ${tagStyles.tagGrey}`}>
               fermée ({params.row.serviceClosed})
             </div>
           ) : null}
         </div>
       ),
     },*/
    {
      field: 'actions',
      type: 'actions',
      headerName: 'Actions',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
      renderCell: params => (
        <input
          type="image"
          onClick={() => viewHandler(params.row.id, 'fiche')}
          className="p-2"
          src={VIEW_ICON}
          width="40px"
          height="40px"
          title="consuler"
        />
      ),
    },
  ];

  return (
    <div>
      <DataTable
        rows={listFamilies}
        columns={columns}
        fileName={`Liste des Familles , ${new Date().toLocaleString()}`}
        totalElements={liste1.totalElements}
        numberOfElements={liste1.numberOfElements}
        pageable={liste1.pageable}
      />
    </div>
  );
}
