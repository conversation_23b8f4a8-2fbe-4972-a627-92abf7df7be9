import React, { useEffect, useState } from 'react';
import {
  Container,
  Grid,
  makeStyles,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
} from '@material-ui/core';
import defaultImage from '../../../../images/user.png';
import PrintHeader from '../../../Common/PrintHeader/PrintHeader';
import { isBeneficiary , isCandidate } from 'containers/Beneficiary/BeneficiaryProfile/statutUtils';

const useStyles = makeStyles(theme => ({
  container: {
    padding: '40px',
    fontFamily: "'Roboto', sans-serif",
    backgroundColor: '#f5f5f5',
    color: '#333',
    direction: 'rtl', // RTL direction
    textAlign: 'right', // Align text to the right
  },
  paper: {
    padding: '40px',
    borderRadius: '15px',
    boxShadow: '0 8px 40px rgba(0, 0, 0, 0.12)',
    backgroundColor: '#ffffff',
    position: 'relative',
    overflow: 'hidden',
    marginBottom: '40px',
    '@media print': {
      '&:not(:first-child)': {
        pageBreakBefore: 'always',
      },
    },
  },
  sectionTitle: {
    marginBottom: '30px',
    color: '#3f51b5',
    textTransform: 'uppercase',
    letterSpacing: '1.5px',
    borderBottom: '2px solid #3f51b5',
    paddingBottom: '5px',
    marginTop: '20px',
  },
  gridItem: {
    marginBottom: '25px',
  },
  tableContainer: {
    marginBottom: '30px',
    pageBreakInside: 'avoid',
  },
  table: {
    minWidth: 650,
    '& th': {
      backgroundColor: '#3f51b5',
      color: '#ffffff',
      direction: 'rtl', // RTL direction for table headers
      textAlign: 'right', // Align text to the right for table
    },
    '& td, & th': {
      border: '1px solid #e0e0e0',
      padding: '14px',
      fontSize: '16px',
      direction: 'rtl', // RTL direction for table headers
      textAlign: 'right', // Align text to the right for table
    },
  },
  logo: {
    width: '60px',
    position: 'absolute',
    top: '20px',
    right: '20px',
  },
  beneficiaryPicture: {
    width: '150px',
    height: '150px',
    borderRadius: '50%',
    border: '5px solid #ffffff',
    boxShadow: '0 4px 10px rgba(0, 0, 0, 0.3)',
    marginBottom: '20px',
  },
  section: {
    marginBottom: '50px',
    pageBreakInside: 'avoid',
  },
  infoItem: {
    display: 'flex',
    alignItems: 'center',
    marginBottom: '15px',
    direction: 'rtl', // RTL direction for info items
    textAlign: 'right', // Align text to the right for info items
  },
  infoLabel: {
    marginLeft: '15px', // Margin for RTL
    fontWeight: 'bold',
    color: '#3f51b5',
    whiteSpace: 'nowrap',
  },
  contactInfo: {
    marginTop: '20px',
  },
  tableTitle: {
    pageBreakInside: 'avoid',
  },
}));

const PrintableBeneficiaryContentAr = React.forwardRef(
  ({ data, family }, ref) => {
    const classes = useStyles();

    if (!data) {
      return null;
    }

    const formatDate = date => new Date(date).toLocaleDateString('fr-FR');

    const personalInfoItems = [
      { label: 'الاسم', value: data.person.firstNameAr },
      { label: ' النسب ', value: data.person.lastNameAr },
      { label: 'البريد الإلكتروني', value: data.person.email ? data.person.email : '------' },
      { label: 'رقم الهاتف', value: data.person.phoneNumber ? data.person.phoneNumber : data.phoneNumberFamily ? data.phoneNumberFamily : '---', },
      { label: 'الجنس', value: data.person.sex == 'Homme' ? 'ذكر' : 'أنثى' },
      { label: 'رقم الهوية', value: data.person.identityCode ? data.person.identityCode : '------' },
        {
          label: 'العنوان',
          value:
            data && data.person && data.person.addressAr
              ? data.person.addressAr
              : data && data.addressFamilyAr
              ? data.addressFamilyAr
              : '---',
        },
        {
          label: 'المدينة',
          value:
            data && data.person && data.person.info && data.person.info.nameAr
              ? data.person.info.nameAr
              : data && data.cityFamily && data.cityFamily.nameAr
              ? data.cityFamily.nameAr
              : '---',
        },
        {
          label: 'الدولة',
          value:
            data && data.person && data.person.info && data.person.info.region && data.person.info.region.country && data.person.info.region.country.nameAr
              ? data.person.info.region.country.nameAr
              : data && data.cityFamily && data.cityFamily.region && data.cityFamily.region.country && data.cityFamily.region.country.nameAr
              ? data.cityFamily.region.country.nameAr
              : '---',
        },
        {
          label: 'الجهة',
          value:
            data && data.person && data.person.info && data.person.info.region && data.person.info.region.nameAr
              ? data.person.info.region.nameAr
              : data && data.cityFamily && data.cityFamily.region && data.cityFamily.region.nameAr
              ? data.cityFamily.region.nameAr
              : '---',
        },
      { label: 'المرحلة الدراسية', value: data.person.schoolLevel.nameAr },
      { label: 'تاريخ الميلاد', value: formatDate(data.person.birthDate) },
      { label: 'تاريخ الإضافة', value: formatDate(data.createdAt) },
      { label: 'رمز المستفيد', value: data.code },
      { label: 'مستقل', value: data.independent ? 'نعم' : 'لا' },
      { label: 'المنطقة', value: data.zone.nameAr },
    ];

    const [nameFiche, setNameFiche] = useState('')

    useEffect(() => {
      if(data && data.statut){
      if(isCandidate(data.statut)){
        // le nom doit etre en arabe
        setNameFiche('بيانات المرشح')
      }else
      {
        setNameFiche('بيانات المستفيد')
      }
    }
    }
    ,[data])

    return (
      <Container
        ref={ref}
        className={classes.container}
        style={{ backgroundColor: '#ffffff' }}
      >
        <Paper className={classes.paper}>
          <PrintHeader headerText={nameFiche} />
          <div className={classes.section}>
            <Typography
              variant="h5"
              gutterBottom
              className={classes.sectionTitle}
            >
              المعلومات الشخصية
            </Typography>
            <Grid container spacing={3}>
              <Grid item xs={12} sm={6}>
                {data.person.pictureBase64 ? (
                  <img
                    src={`data:image/png;base64,${atob(
                      data.person.pictureBase64,
                    )}`}
                    alt="Beneficiary"
                    className={classes.beneficiaryPicture}
                  />
                ) : (
                  <img
                    src={defaultImage}
                    alt="Default Beneficiary"
                    className={classes.beneficiaryPicture}
                  />
                )}
              </Grid>
              <Grid container spacing={3}>
                {personalInfoItems.map((item, index) => (
                  <Grid
                    item
                    xs={12}
                    sm={6}
                    key={index}
                    className={classes.gridItem}
                  >
                    <div className={classes.infoItem}>
                      <Typography
                        variant="subtitle1"
                        className={classes.infoLabel}
                      >
                        {item.label} :
                      </Typography>
                      <Typography variant="body1" className={classes.infoValue}>
                        {item.value}
                      </Typography>
                    </div>
                  </Grid>
                ))}
              </Grid>
            </Grid>
          </div>
        </Paper>
        <Paper className={classes.paper}>
          <PrintHeader headerText={nameFiche} />
          <div className={classes.section}>
            <Typography
              variant="h5"
              gutterBottom
              className={classes.sectionTitle}
            >
              المعلومات الصحية
            </Typography>

            <div className={classes.infoItem}>
              <Typography variant="subtitle1" className={classes.infoLabel}>
                الإعاقات ( التكلفة) :
              </Typography>
              <Typography variant="body1" className={classes.infoValue}>
                {data.handicapped && data.handicapped.length > 0
                  ? data.handicapped
                    .map(
                      handicap =>
                        `${handicap.handicapType.nameAr} ( ${handicap.handicapCost}DH )`,
                    )
                    .join(', ')
                  : '------'}
              </Typography>
            </div>

            <div className={classes.infoItem}>
              <Typography variant="subtitle1" className={classes.infoLabel}>
                الحساسية:
              </Typography>
              <Typography variant="body1" className={classes.infoValue}>
                {data.allergies.length > 0
                  ? data.allergies.map(allergy => allergy.nameAr).join(', ')
                  : '-----'}
              </Typography>
            </div>
            <div className={classes.infoItem}>
            <Typography variant="subtitle1" className={classes.infoLabel}>
              الأمراض :
            </Typography>
            <Typography variant="body1" className={classes.infoValue}>
              {data.diseases.length > 0
                ? data.diseases.map(disease => disease.nameAr).join(', ')
                : '-----'}
            </Typography>
          </div>

            <div className={classes.infoItem}>
              <Typography variant="subtitle1" className={classes.infoLabel}>
                الأمراض المزمنة ( التكلفة) :
              </Typography>
              <Typography variant="body1" className={classes.infoValue}>
                {data.diseaseTreatments.length > 0
                  ? data.diseaseTreatments
                    .map(
                      treatment =>
                        `${treatment.type.nameAr} ( ${treatment.cost}DH )`,
                    )
                    .join(', ')
                  : '------'}
              </Typography>
            </div>
          </div>
          <div className={classes.section}>
            <Typography
              variant="h5"
              gutterBottom
              className={classes.sectionTitle}
            >
              المسار الدراسي
            </Typography>
            <TableContainer component={Paper} className={classes.table}>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>المرحلة الدراسية</TableCell>
                    <TableCell>المستوى الدراسي</TableCell>
                    <TableCell>المدرسة</TableCell>
                    <TableCell>الدرجة</TableCell>
                    <TableCell>التمييز</TableCell>
                    <TableCell>النجاح</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {data.educations.length > 0 ? (
                    data.educations.map((education, index) => (
                      <TableRow key={index}>
                        <TableCell>{education.schoolYear.name}</TableCell>
                        <TableCell>{education.schoolLevel.nameAr}</TableCell>
                        <TableCell>{education.schoolNameAr}</TableCell>
                        <TableCell>{education.mark}</TableCell>
                        <TableCell>{education.honor.nameAr}</TableCell>
                        <TableCell>
                          {education.succeed ? 'نعم' : 'لا'}
                        </TableCell>
                      </TableRow>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell colSpan={7} align="center">
                        لا توجد بيانات دراسية
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </TableContainer>
          </div>

          <div className={classes.section}>
            <Typography
              variant="h5"
              gutterBottom
              className={classes.sectionTitle}
            >
              المنح الدراسية
            </Typography>
            <TableContainer component={Paper} className={classes.table}>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>اسم المنحة</TableCell>
                    <TableCell>قيمة المنحة</TableCell>
                    <TableCell>تاريخ البدء</TableCell>
                    <TableCell>تاريخ الانتهاء</TableCell>
                    <TableCell>الدورية</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {data.scholarshipBeneficiaries.length > 0 ? (
                    data.scholarshipBeneficiaries.map((scholarship, index) => (
                      <TableRow key={index}>
                        <TableCell>{scholarship.scholarship.nameAr}</TableCell>
                        <TableCell> {scholarship.amount} </TableCell>
                        <TableCell>
                          {formatDate(scholarship.startDate)}
                        </TableCell>
                        <TableCell>{formatDate(scholarship.endDate)}</TableCell>
                        <TableCell>{scholarship.periodicity}</TableCell>
                      </TableRow>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell colSpan={5} align="center">
                        لا توجد منح دراسية
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </TableContainer>
          </div>
          {isBeneficiary(data.statut) && (
          <div className={classes.section}>
            <Typography
              variant="h5"
              gutterBottom
              className={classes.sectionTitle}
            >
              المسؤوليات
            </Typography>
            <TableContainer component={Paper} className={classes.table}>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>اسم الخدمة</TableCell>
                    <TableCell>الحالة</TableCell>
                    <TableCell>تاريخ البدء</TableCell>
                    <TableCell>تاريخ الانتهاء</TableCell>
                    <TableCell>الممول</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {data.takenInChargeBeneficiaries &&
                  data.takenInChargeBeneficiaries.length > 0 ? (
                      data.takenInChargeBeneficiaries.map(takeInCharge => (
                        <TableRow key={takeInCharge.id}>
                          <TableCell>
                            {takeInCharge.takenInCharge.service.nameAr}
                          </TableCell>
                          <TableCell>
                            {takeInCharge.takenInCharge.status.nameAr}
                          </TableCell>
                          <TableCell>
                            {formatDate(takeInCharge.takenInCharge.startDate)}
                          </TableCell>
                          <TableCell>
                            {formatDate(takeInCharge.takenInCharge.endDate)}
                          </TableCell>
                          <TableCell>
                            {takeInCharge.takenInCharge.takenInChargeDonors &&
                          takeInCharge.takenInCharge.takenInChargeDonors
                            .length > 0
                              ? takeInCharge.takenInCharge.takenInChargeDonors[0]
                                .donor
                                ? `${takeInCharge.takenInCharge.takenInChargeDonors[0].donor.firstNameAr} ${takeInCharge.takenInCharge.takenInChargeDonors[0].donor.lastNameAr}`
                                : '------'
                              : '------'}
                          </TableCell>
                        </TableRow>
                      ))
                    ) : (
                      <TableRow>
                        <TableCell colSpan={6} align="center">
                        لا توجد بيانات مسؤوليات
                        </TableCell>
                      </TableRow>
                    )}
                </TableBody>
              </Table>
            </TableContainer>
          </div>
          )}
          {family && family.familyMembers && family.familyMembers.length > 0 && (
            <div className={classes.section}>
              <Typography
                variant="h5"
                gutterBottom
                className={classes.sectionTitle}
              >
                المعلومات العائلية
              </Typography>
              <TableContainer component={Paper} className={classes.table}>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>الاسم الأول</TableCell>
                      <TableCell>اللقب</TableCell>
                      <TableCell>العلاقة</TableCell>
                      <TableCell>تاريخ الميلاد</TableCell>
                      <TableCell>وصي</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {family.familyMembers.map((member, index) => (
                      <TableRow key={index}>
                        <TableCell>
                          {member.person ? member.person.firstNameAr : '-'}
                        </TableCell>
                        <TableCell>
                          {member.person ? member.person.lastNameAr : '-'}
                        </TableCell>
                        <TableCell>
                          {member.familyRelationship
                            ? member.familyRelationship.nameAr
                            : '-'}
                        </TableCell>
                        <TableCell>
                          {formatDate(
                            member.person ? member.person.birthDate : null,
                          )}
                        </TableCell>
                        <TableCell>{member.tutor ? 'نعم' : 'لا'}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </div>
          )}
 {data.remarqueAr && (
            <div className={classes.section}>
              <Typography
                variant="h5"
                gutterBottom
                className={classes.sectionTitle}
              >
                ملاحظات
              </Typography>
              <Typography variant="body1">{data.remarqueAr}</Typography>
            </div>
          )}
            
          
        </Paper>
      </Container>
    );
  },
);

export default PrintableBeneficiaryContentAr;
