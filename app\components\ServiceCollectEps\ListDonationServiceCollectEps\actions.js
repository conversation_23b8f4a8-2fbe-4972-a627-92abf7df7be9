import {
    LOAD_Donation_Service_EPS,
    LOAD_Donation_Service_EPS_SUCCESS,
    LOAD_Donation_Service_EPS_ERROR,
} from "./constants";
export function loadDonationServiceCollectEps(id,page, filters) { 
    return {
        type: LOAD_Donation_Service_EPS,
        id,
        page,
        filters,
    };
};

export function donationserviceCollectEpsLoaded(donationServiceCollectEpsList) { 
    return {
        type: LOAD_Donation_Service_EPS_SUCCESS,
        donationServiceCollectEpsList,
    };
};

export function donationserviceCollectEpsLoadingError(error) {
    return {
        type: LOAD_Donation_Service_EPS_ERROR,
        error,
    };
};
 