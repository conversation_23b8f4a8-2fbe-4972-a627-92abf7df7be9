import produce from 'immer';
import {  LOAD_Donation_Service_EPS, LOAD_Donation_Service_EPS_SUCCESS, LOAD_Donation_Service_EPS_ERROR} from "./constants";

export const initialState = {
    donationServiceCollectEpsList: [],
    loading: false, 
    error: null, 
};

const donationServiceCollectEpsReducer = produce((draft, action) => {
    switch (action.type) {
        case LOAD_Donation_Service_EPS:
            draft.loading = true;
            draft.error = false;
            draft.donationServiceCollectEpsList = []; 
            break; 
        case LOAD_Donation_Service_EPS_SUCCESS:
            draft.loading = false;
            draft.error = false;
            draft.donationServiceCollectEpsList = action.donationServiceCollectEpsList;
            break;
        case LOAD_Donation_Service_EPS_ERROR:
            draft.loading = false;
            draft.error = action.error;
            break;

    }    
}, initialState);

export default donationServiceCollectEpsReducer;
