import React, { useEffect } from 'react';
import HomePage from '../../containers/HomePage';
import { createStructuredSelector } from 'reselect';
import { makeSelectDonationsDashboard, makeSelectDonationsDashboardSuccess, makeSelectDonationsDashboardLoading, makeSelectDonationsDashboardError } from '../Dashboard-General/selector';
import { useDispatch, useSelector } from 'react-redux';
import { useInjectReducer, useInjectSaga } from 'redux-injectors';
import generalDashboardReducer  from '../Dashboard-General/reducer';
import generalDashboardSaga from '../Dashboard-General/saga';
import { fetchDonationsDashboard } from '../Dashboard-General/actions';
import { PieCard,pieColors } from '../charts/PieCard';
import  AnimatedLineC<PERSON>  from '../charts/AnimatedLineChart';
import { Grid, Container } from '@mui/material';
import PieChartIcon from '@mui/icons-material/PieChart';
import PaymentIcon from '@mui/icons-material/Payment';
import ShowChartIcon from '@mui/icons-material/ShowChart';
import { AnimatedBarChart } from 'components/charts/AnimatedBarChart';
const donationSelector = createStructuredSelector({
  data: makeSelectDonationsDashboard,
  success: makeSelectDonationsDashboardSuccess,
  loading: makeSelectDonationsDashboardLoading,
  error: makeSelectDonationsDashboardError,
});

const formatNumber = (num) => {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M';
  }
  if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K';
  }
  return num.toString();
};

export default function DashboardDonations() {
  useInjectReducer({ key: 'dashboardAll', reducer: generalDashboardReducer });
  useInjectSaga({ key: 'dashboardAll', saga: generalDashboardSaga });

  const { data, success, loading, error } = useSelector(donationSelector);
  const dispatch = useDispatch();
  useEffect(() => {
    dispatch(fetchDonationsDashboard());
  }, [dispatch]);
  
  useEffect(() => {
    if (success) {
      console.table(data);
      console.log('data.donationbymonth', data.donationByMonth)
    }
  }, [success]);

  const getDonationData = () => {
    if (!data || !data.donationByDonationType) return { data: [], labels: [] };
    
    const { Financière, Nature } = data.donationByDonationType;
    const donationData = [];
    const donationLabels = [];
    
    if (Financière !== undefined) {
      donationData.push(Financière);
      donationLabels.push('Financière');
    }
    if (Nature !== undefined) {
      donationData.push(Nature);
      donationLabels.push('Nature');
    }
    
    return {
      data: donationData,
      labels: donationLabels
    };
  };

  const getChannelData = () => {
    if (!data || !data.donationByChannel) return { data: [], labels: [] };
    
    const { Espèce, Virement, Chèque, ...otherChannels } = data.donationByChannel;
    const channelData = [];
    const channelLabels = [];
    
    if (Espèce) {
      channelData.push(Espèce);
      channelLabels.push('Espèce');
    }
    if (Virement) {
      channelData.push(Virement);
      channelLabels.push('Virement');
    }
    if (Chèque) {
      channelData.push(Chèque);
      channelLabels.push('Chèque');
    }
    
    // Add other channels if they exist
    Object.entries(otherChannels).forEach(([channel, value]) => {
      if (value) {
        channelData.push(value);
        channelLabels.push(channel);
      }
    });
    
    return {
      data: channelData,
      labels: channelLabels
    };
  };

  const donationData = getDonationData();
  const channelData = getChannelData();

  return (
    <>
      <HomePage />
      {!loading && data.donationByDonationType && data.donationByChannel && data.donationByMonth && (
        <>
          <Container maxWidth="xl" sx={{ mt: 2, mb: 4 }}>
            <Grid container spacing={2}>
              <Grid item xs={12} md={6}>
                <PieCard
                  title="Répartition des donations par type"
                  icon={<PieChartIcon color="primary" />}
                  data={donationData.data}
                  labels={donationData.labels}
                  colors={[pieColors.info, pieColors.error, pieColors.primary, pieColors.success, pieColors.warning]}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <PieCard
                  title="Répartition des donations par mode de paiement"
                  icon={<PaymentIcon color="primary" />}
                  data={channelData.data}
                  labels={channelData.labels}
                  colors={[pieColors.info, pieColors.error, pieColors.primary, pieColors.success, pieColors.warning]}
                />
              </Grid>
            </Grid>
            <Grid container spacing={2} sx={{ mt: 1 }}>
              <Grid item xs={12} md={6}>
                <AnimatedLineChart
                  title="Montant total des dons par mois (en DH)"
                  data={data.donationByMonth}
                  icon={<ShowChartIcon color="primary" />}
                  colors={[pieColors.info, pieColors.error, pieColors.primary, pieColors.success, pieColors.warning]}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <AnimatedBarChart
                  title="Répartition des donations par service"
                  data={Object.values(data.donationByService)}
                  labels={Object.keys(data.donationByService)}
                  icon={<ShowChartIcon color="primary" />}
                  colors={[pieColors.info, pieColors.error, pieColors.primary, pieColors.success, pieColors.warning]}
                />
              </Grid>
            </Grid>
          </Container>
        </>
      )}
    </>
  );
} 