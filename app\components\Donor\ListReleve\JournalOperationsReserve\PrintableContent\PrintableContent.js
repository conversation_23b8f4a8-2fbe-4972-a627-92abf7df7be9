import React, { useMemo, useState } from 'react';
import {
  Container,
  Grid,
  makeStyles,
  Paper,
  Typography,
} from '@material-ui/core';
import PrintHeader from 'components/Common/PrintHeader/PrintHeader';

const useStyles = makeStyles(theme => ({
  container: {
    padding: '40px',
    fontFamily: "'Roboto', sans-serif",
    backgroundColor: '#f5f5f5',
    color: '#333',
  },
  paper: {
    padding: '40px',
    borderRadius: '15px',
    boxShadow: '0 8px 40px rgba(0, 0, 0, 0.12)',
    backgroundColor: '#ffffff',
    position: 'relative',
    overflow: 'hidden',
  },
  sectionTitle: {
    marginBottom: '30px',
    color: '#3f51b5',
    textTransform: 'uppercase',
    letterSpacing: '1.5px',
    borderBottom: '2px solid #3f51b5',
    paddingBottom: '5px',
    marginTop: '20px',
  },
  gridItem: {
    marginBottom: '25px',
  },
  tableContainer: {
    marginBottom: '30px',
    pageBreakInside: 'avoid',
  },
  table: {
    minWidth: 650,
    '& th': {
      backgroundColor: '#3f51b5',
      color: '#ffffff',
    },
    '& td, & th': {
      border: '1px solid #e0e0e0',
      padding: '14px',
      fontSize: '16px',
    },
  },
  logo: {
    width: '60px',
    position: 'absolute',
    top: '20px',
    right: '20px',
  },
  donorPicture: {
    width: '150px',
    height: '150px',
    borderRadius: '50%',
    border: '5px solid #ffffff',
    boxShadow: '0 4px 10px rgba(0, 0, 0, 0.3)',
    marginBottom: '20px',
  },
  section: {
    marginTop: '30px',
    pageBreakInside: 'avoid',
  },
  infoItem: {
    display: 'flex',
    alignItems: 'center',
    marginBottom: '15px',
  },
  infoLabel: {
    marginRight: '15px',
    fontWeight: 'bold',
    color: '#3f51b5',
    whiteSpace: 'nowrap',
  },
  contactInfo: {
    marginTop: theme.spacing(3),
  },
  contactTitle: {
    position: 'relative',
    display: 'inline-block',
  },
  titleUnderline: {
    content: '""',
    display: 'block',
    width: '100%',
    height: '2px',
    backgroundColor: 'black',
    position: 'absolute',
    bottom: 0,
    left: 0,
    color: 'black',
  },
  tableTitle: {
    pageBreakInside: 'avoid',
  },
}));

const DataRow = ({ label, value }) => {
  const classes = useStyles();
  return (
    <Grid item xs={12} sm={6} className={classes.gridItem}>
      <div className={classes.infoItem}>
        <Typography variant="subtitle1" className={classes.infoLabel}>
          {label}:
        </Typography>
        <Typography variant="body1">{value ? value : '-'}</Typography>
      </div>
    </Grid>
  );
};

const PrintableContent = React.forwardRef(
  ({ data, total, donor, startDate, endDate }, ref) => {
    const classes = useStyles();

    const formatDate = date => new Date(date).toLocaleDateString('fr-FR');
    const [filteredData, setFilteredData] = useState([]);

    const BodyComponent = ({ data }) => (
      <>
        {data && data.length > 0 ? (
          data.map(el => {
            if (el.typeDonationKafalat !== 'donation') {
              return (
                <tr key={el.id}>
                  <td>{formatDate(el.dateExecution)}</td>
                  <td>
                    <div
                      style={{
                        fontSize: 16,
                        fontWeight: 'bold',
                      }}
                    >
                      {el.type
                        ? el.type
                        : `${el.name ? el.name : el.services.name || '-'}`}
                    </div>

                    {el.beneficiaries && el.beneficiaries.length > 0 ? (
                      <div
                        style={{
                          display: 'flex',
                          flexDirection: 'column',
                          gap: 5,
                        }}
                      >
                        <div
                          style={{
                            fontSize: 16,
                            fontWeight: 'bold',
                          }}
                        >
                          {`Beneficiare(s) d'operation suivante du ${formatDate(
                            el.dateExecution,
                          ).slice(3, 10)} :`}
                        </div>
                        {el.beneficiaries.map(beneficiary => (
                          <div
                            style={{
                              paddingLeft: 5,
                            }}
                          >
                            {`${beneficiary.personFirstName} ${beneficiary.personLastName}`}
                          </div>
                        ))}
                      </div>
                    ) : (
                      <></>
                    )}
                  </td>
                  <td>{el.montantSortie}</td>
                </tr>
              );
            }
          })
        ) : (
          <tr>
            <td colSpan="8">Aucun relevé disponible</td>
          </tr>
        )}
      </>
    );

    return (
      <Container
        style={{ backgroundColor: '#ffffff' }}
        ref={ref}
        className={classes.container}
      >
        <Paper className={classes.paper}>
          <PrintHeader />
          <div className={classes.section}>
            <Typography
              variant="h5"
              gutterBottom
              className={classes.sectionTitle}
            >
              Opérations de prise en charge réservées
            </Typography>
            <Grid
              container
              spacing={3}
              alignItems="center"
              justifyContent="space-between"
            >
              <Grid item xs={6} style={{ textAlign: 'left' }}>
                <Typography variant="subtitle1">
                  <strong>Code:</strong> {donor.code}
                </Typography>
                <Typography variant="subtitle1">
                  <strong>Nom complet:</strong>{' '}
                  {donor.firstName + ' ' + donor.lastName}
                </Typography>
                <Typography variant="subtitle1">
                  <strong>Address:</strong>{' '}
                  {donor.address
                    ? donor.address
                    : donor && donor.city && donor.city.name}
                </Typography>
                <Typography variant="subtitle1">
                  <strong>Email:</strong> {donor.email}
                </Typography>
                <Typography variant="subtitle1">
                  <strong>Téléphone:</strong> {donor.phoneNumber}
                </Typography>
              </Grid>
              <Grid item xs={6} style={{ textAlign: 'right' }}>
                <Typography variant="subtitle1">
                  <strong>Date debut:</strong> {startDate}
                </Typography>
                <Typography variant="subtitle1">
                  <strong>Date fin:</strong> {endDate}
                </Typography>
              </Grid>
            </Grid>

            <div className="d-flex bg-white p-4">
              <table className="table">
                <thead>
                  <tr>
                    <th colSpan={5} className="text-align-center">
                      Opérations de prise en charge exécutées
                    </th>
                  </tr>
                  <tr>
                    <th>Date</th>
                    <th>Operation</th>
                    <th>Debit</th>
                  </tr>
                </thead>
                <tbody>
                  <BodyComponent data={data} />
                </tbody>
                <tfoot>
                  <tr>
                    <td
                      style={{
                        fontWeight: 'bold',
                        fontSize: 16,
                      }}
                    >
                      Total
                    </td>
                    <td></td>
                    <td
                      style={{
                        fontWeight: 'bold',
                        fontSize: 16,
                      }}
                    >{`${total} DH`}</td>
                  </tr>
                </tfoot>
              </table>
            </div>
            <Grid item xs={6} style={{ textAlign: 'left' }}></Grid>
            <Grid item xs={12} style={{ textAlign: 'right' }}>
              <Typography variant="subtitle1">
                <strong>Total Donations:</strong> {donor.totalDonated + ' DH'}
              </Typography>
              <Typography variant="subtitle1">
                <strong>Total Exécuté:</strong> {donor.totalExecuted + ' DH'}
              </Typography>
              <Typography variant="subtitle1">
                <strong>Solde Disponible:</strong>{' '}
                {donor.availableBalance + ' DH'}
              </Typography>
            </Grid>
          </div>
        </Paper>
      </Container>
    );
  },
);

export default PrintableContent;
