import React from 'react';
import table from 'Css/table.css';
import moment from 'moment';
import { Link } from 'react-router-dom';
import { hasRoleAssistant } from '../../../../../../utils/hasAccess';

const formatDate = date => moment(date).format('DD/MM/YYYY');

export default function Details(props) {
  const operations = props.data;
  const isRoleAssistant = hasRoleAssistant();
  let finaloperations = [];
  if (operations) {
    finaloperations = operations.map(operation => {
      const beneficiary = operation[3][0];
      const takenInCharge = operation[4];
      const familyRelationship = operation[3][1];
      const executionDate = formatDate(operation[0].executionDate);
      const { amount } = operation[0];
      let donorName = <span>-</span>;
      const member = `${familyRelationship.name}-${beneficiary.person.firstName} ${beneficiary.person.lastName}`;
      const service = takenInCharge.services
        ? takenInCharge.services.name
        : '-';
      if (takenInCharge.takenInChargeDonors[0].donor.type === 'Physique') {
        donorName = isRoleAssistant ? (
          `${takenInCharge.takenInChargeDonors[0].donor.firstName} ${takenInCharge.takenInChargeDonors[0].donor.lastName}`
        ) : (
          <Link
            to={`/donors/fiche/${takenInCharge.takenInChargeDonors[0].donor.id}/info`}
          >
            {`${takenInCharge.takenInChargeDonors[0].donor.firstName} ${takenInCharge.takenInChargeDonors[0].donor.lastName}`}
          </Link>
        );
      } else if (
        takenInCharge.takenInChargeDonors[0] &&
        takenInCharge.takenInChargeDonors[0].donor.type === 'Moral'
      ) {
        donorName = isRoleAssistant ? (
          takenInCharge.takenInChargeDonors[0].donor.company
        ) : (
          <Link
            to={`/donors/fiche/${takenInCharge.takenInChargeDonors[0].donor.id}/info`}
          >
            {takenInCharge.takenInChargeDonors[0].donor.company}
          </Link>
        );
      } else if (
        takenInCharge.takenInChargeDonors[0] &&
        takenInCharge.takenInChargeDonors[0].donor.type === 'Anonyme'
      ) {
        donorName = isRoleAssistant ? (
          takenInCharge.takenInChargeDonors[0].donor.name
        ) : (
          <Link
            to={`/donors/fiche/${takenInCharge.takenInChargeDonors[0].donor.id}/info`}
          >
            {takenInCharge.takenInChargeDonors[0].donor.name}
          </Link>
        );
      }

      return (
        <tr>
          <td className="col-md-1">{member}</td>
          <td className="col-md-1">{service}</td>
          <td className="col-md-1">{donorName}</td>
          <td className="col-md-1">{executionDate}</td>
          <td className="col-md-1">{amount}</td>
        </tr>
      );
    });
  }
  return (
    <div className={table.global}>
      <table className="table small mx-auto table-borderless">
        <thead>
          <tr>
            <th scope="col">Membre</th>
            <th scope="col">Service</th>
            <th scope="col">Donateur</th>
            <th scope="col">Date d'exécution</th>
            <th scope="col">Montant (DH)</th>
          </tr>
        </thead>
        <tbody>{finaloperations}</tbody>
      </table>
    </div>
  );
}
