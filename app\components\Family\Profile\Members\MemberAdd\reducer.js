import produce from 'immer';
import {
  ADD_FAMILY_MEMBER,
  ADD_FAMILY_MEMBER_ERROR,
  ADD_FAMILY_MEMBER_RESET,
  ADD_FAMILY_MEMBER_SUCCESS,
  ADD_FAMILY_MEMBER_TUTOR_ERROR,
} from './constants';

export const initialState = {
  loading: false,
  error: false,
  success: false,
  member: {},
  tutorError: false,
};

/* eslint-disable no-param-reassign,default-case */
const familyMemberReducer = produce((draft, action) => {
  switch (action.type) {
    case ADD_FAMILY_MEMBER:
      draft.loading = true;
      draft.error = false;
      draft.success = false;
      break;
    case ADD_FAMILY_MEMBER_SUCCESS:
      draft.loading = false;
      draft.error = false;
      draft.success = true;
      draft.member = action.member;
      break;
    case ADD_FAMILY_MEMBER_ERROR:
      draft.loading = false;
      draft.error = action.error;
      break;
    case ADD_FAMILY_MEMBER_TUTOR_ERROR:
      draft.loading = false;
      draft.tutorError = true;
      break;
    case ADD_FAMILY_MEMBER_RESET:
      draft.loading = false;
      draft.error = false;
      draft.success = false;
      draft.member = {};
      break;
  }
}, initialState);

export default familyMemberReducer;
