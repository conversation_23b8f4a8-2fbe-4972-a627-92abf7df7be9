import React, { useEffect, useState, useCallback, useMemo } from 'react';
import moment from 'moment';
import { useHistory, useLocation, useParams } from 'react-router-dom';
import { createStructuredSelector } from 'reselect';
import { useDispatch, useSelector } from 'react-redux';
import { useInjectReducer, useInjectSaga } from 'redux-injectors';
import reducer from 'containers/Common/SubComponents/RapportForm/reducer';
import Modal2 from 'components/Common/Modal2';
import { Alert } from 'react-bootstrap';
import {
  addAgendaRapport,
  deleteRapportById,
  deleteRapportByIdReset,
  getAllRapportBeneficiary,
  viewRapport,
  validateRaportModal,
  validateRaportModalReset,
  completeModalRapport,
  completeModalRapportReset,
  rapportViewingReset,
  addAgendaRapportManuell,
  resetAgendaRapportManuell,
} from 'containers/Common/SubComponents/RapportForm/actions';
import {
  makeSelectAgendaRapport,
  makeSelectAgendaRapportError,
  makeSelectAgendaRapportLoading,
  makeSelectAgendaRapportSuccess,
  makeSelectAllRapportBeneficiary,
  makeSelectAllRapportBeneficiaryLoading,
  makeSelectCompleteRapporError,
  makeSelectCompleteRapporSuccess,
  makeSelectCompleteRapportLoading,
  makeSelectDeleteRapportSuccess,
  makeSelectRapportLoading,
  makeSelectSuccess,
  makeSelectValidateRapportError,
  makeSelectValidateRapportLoading,
  makeSelectValidateRapportSuccess,
  makeSelecRapportViewSuccess,
  makeSelectAgendaRapportManuellSuccess,
  makeSelectAgendaRapportManuellLoading,
  makeSelectAgendaRapportManuellError,
} from 'containers/Common/SubComponents/RapportForm/selectors';
import AccessControl, { isAuthorized } from 'utils/AccessControl';
import CustomPagination from 'components/Common/CustomPagination';
import DataTable from 'components/Common/DataTable';
import {
  ARTICLE_ICON,
  CHECK_ICON,
  DELETE_ICON,
  EDIT_ICON,
  FILE_PLUS_ICON,
  RETWEET_ICON,
} from 'components/Common/ListIcons/ListIcons';
import { rapportSaga } from 'containers/Common/SubComponents/RapportForm/saga';
import RapportFormModel from 'containers/Common/SubComponents/RapportFormModel';
import { FilePlus } from 'components/Common/ListIcons/FilePlus';
import { useHasRole } from 'utils/hasAccess';
import btnStyles from '../../../../Css/button.css';
import list from '../../../../Css/profileList.css';
import {
  getStatusName,
  getStatusStyle,
  isAssistant,
  isAssistantUpdate,
  isKafalatDelete,
  isKafalatUpdate,
  isMarketingDelete,
  isMarketingUpdate,
  isSeviceKafalat,
  isSeviceMerketing,
} from './statutUtils';
import GenericFilter from 'containers/Common/Filter/GenericFilter';

const key = 'rapportAdd';

const target = 'beneficiary';

const formatDate = date => moment(date).format('DD/MM/YYYY');

const omdbSelector = createStructuredSelector({
  success: makeSelectSuccess,
  successAddAgendaRapport: makeSelectAgendaRapportSuccess,
  addAgendaReportLoading: makeSelectAgendaRapportLoading,
  addAgendaReport: makeSelectAgendaRapport,
  addAgendaReportError: makeSelectAgendaRapportError,
  rapportBeneficiary: makeSelectAllRapportBeneficiary,
  successDeleteRapport: makeSelectDeleteRapportSuccess,
  validRapportSuccess: makeSelectValidateRapportSuccess,
  validRapportError: makeSelectValidateRapportError,
  validRapportLoading: makeSelectValidateRapportLoading,
  completeSuccess: makeSelectCompleteRapporSuccess,
  completeError: makeSelectCompleteRapporError,
  completeLoading: makeSelectCompleteRapportLoading,
  listRapportLoading: makeSelectAllRapportBeneficiaryLoading,
  rapportViewLoading: makeSelectRapportLoading,
  successView: makeSelecRapportViewSuccess,
  agendaRapportManuellSuccess: makeSelectAgendaRapportManuellSuccess,
  agendaRapportManuellLoading: makeSelectAgendaRapportManuellLoading,
  agendaRapportManuellError: makeSelectAgendaRapportManuellError,
});

const listReturns = [
  { id: 4, value: 'Assistant' },
  { id: 5, value: 'Service Kafalat' },
];
const listLangues = [
  { id: 1, value: 'Arabe', key: 'ar' },
  { id: 2, value: 'Français', key: 'fr' },
  { id: 3, value: 'Anglais', key: 'en' },
];

export default function BeneficiaryRapport(props) {
  const { setSuccessMessage, setErrorMessage } = props;
  const dispatch = useDispatch();
  const params = useParams();

  const beneficiaryId = useMemo(() => (params && params.id ? params.id : 0), [
    params,
    params.id,
  ]);
  const {
    success,
    error,
    rapportBeneficiary,
    addAgendaReport,
    successAddAgendaRapport,
    addAgendaReportLoading,
    successDeleteRapport,
    validRapportSuccess,
    validRapportError,
    completeSuccess,
    completeError,
    completeLoading,
    validRapportLoading,
    listRapportLoading,
    rapportViewLoading,
    successView,
    agendaRapportManuellSuccess,
    agendaRapportManuellLoading,
    agendaRapportManuellError,
  } = useSelector(omdbSelector);
  useInjectReducer({ key, reducer });
  useInjectSaga({ key, saga: rapportSaga });

  const [show, setShow] = useState(false);

  const location = useLocation();
  const history = useHistory();
  const [successMessageAddition, setSuccessMessageAddition] = useState('');
  const principalInputsConfig = [
    {
      field: 'reportStatus',
      type: 'select',
      placeholder: 'Statut du rapport',
      options: [
        { value: 'RAPPORT_PLANIFIER', label: 'Planifié' },
        { value: 'RAPPORT_A_PREPARE', label: 'À préparer' },
        { value: 'RAPPORT_INITIAL', label: 'Initial' },
        {
          value: 'RAPPORT_VALIDER_ASSISTANCE',
          label: "Validé par l'assistant",
        },
        { value: 'RAPPORT_VALIDER_KAFALAT', label: 'Validé par Kafalat' },
        {
          value: 'RAPPORT_A_COMPLETER_PAR_ASSISTANCE',
          label: "Rapport à compléter par l'assistant",
        },
        {
          value: 'RAPPORT_A_COMPLETER_PAR_KAFALAT',
          label: 'Rapport à compléter par Kafalat',
        },
        { value: 'RAPPORT_FINAL', label: 'Final' },
      ],
      widthStyles: {
        width: 250,
      },
    },
    {
      field: 'numberRapport',
      type: 'number',
      placeholder: 'Numéro du Rapport',
    },
  ];
  const additionalInputsConfig = [
    {
      field: 'plannedDateStart',
      type: 'date',
      placeholder: 'Date de début de planification',
    },
    {
      field: 'plannedDateEnd',
      type: 'date',
      placeholder: 'Date de fin de planification',
    },
    {
      field: 'validationDateStart',
      type: 'date',
      placeholder: 'Date de début de validation',
    },
    {
      field: 'validationDateEnd',
      type: 'date',
      placeholder: 'Date de fin de validation',
    },
  ];

  const [filterValues, setFilterValues] = useState({
    beneficiaryCode: '',
    beneficiaryName: '',
    numberRapport: '',
    reportStatus: '',
    plannedDateStart: '',
    plannedDateEnd: '',
    validationDateStart: '',
    validationDateEnd: '',
  });

  const handleResetFilterComplete = () => {
    setFilterValues({
      beneficiaryCode: '',
      beneficiaryName: '',
      numberRapport: '',
      reportStatus: '',
      plannedDateStart: '',
      plannedDateEnd: '',
      validationDateStart: '',
      validationDateEnd: '',
    });
  };

  const [currentPage, setCurrentPage] = useState(0);

  const handlePageChange = pageNumber => {
    setCurrentPage(pageNumber - 1);
    setFilterValues(prevFilterValues => {
      dispatch(
        getAllRapportBeneficiary({
          id: beneficiaryId,
          pageNumber: pageNumber - 1,
          filters: prevFilterValues,
        }),
      );
      return prevFilterValues;
    });
  };

  const handleApplyFilter = filters => {
    dispatch(
      getAllRapportBeneficiary({
        id: beneficiaryId,
        pageNumber: 0,
        filters: filters,
      }),
    );
  };
  useEffect(() => {
    if (location && location.state && location.state.success) {
      setSuccessMessageAddition(location.state.success);
      setShowAlert(true);
    }
  }, [location]);

  useEffect(() => {
    if (completeSuccess && !completeError) {
      setTimeout(() => {
        dispatch(completeModalRapportReset());
        dispatch(
          getAllRapportBeneficiary({
            id: beneficiaryId,
            pageNumber: currentPage,
          }),
        );
        handleCloseForCompleteRapportModal();
      }, 1200);
    }
  }, [completeSuccess, completeError]);

  useEffect(() => {
    if (beneficiaryId) {
      dispatch(
        getAllRapportBeneficiary({
          id: beneficiaryId,
          pageNumber: currentPage,
        }),
      );
    }
  }, [beneficiaryId]);

  useEffect(() => {
    if (successAddAgendaRapport) {
      dispatch(
        getAllRapportBeneficiary({
          id: beneficiaryId,
          pageNumber: currentPage,
        }),
      );
    }
  }, [beneficiaryId, successAddAgendaRapport]);
  useEffect(() => {
    if (successDeleteRapport) {
      dispatch(deleteRapportByIdReset());

      dispatch(
        getAllRapportBeneficiary({
          id: beneficiaryId,
          pageNumber: currentPage,
        }),
      );
    }
  }, [successDeleteRapport]);

  const handleClose = () => {
    setShow(false);
    setRapportReadOnly({
      title: 'Consulter le rapport',
      isRead: false,
      action: null,
    });
    localStorage.removeItem('modalState');
  };

  const [rapportToEdit, setRapportToEdit] = useState('');
  const [rapportToDelete, setRapportToDelete] = useState('');
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [showAlert, setShowAlert] = useState(false);
  const [rapportToReadOnly, setRapportReadOnly] = useState({
    title: 'Consulter le rapport',
    isRead: false,
    action: null,
  });
  const [details, setDetails] = useState('');
  const [showValidateModal, setShowValidateModal] = useState(false);
  const [showCompleteRapportModal, setShowCompleteRapportModal] = useState(
    false,
  );
  const [showSelectLangRapportModal, setShowSelectLangRapportModal] = useState(
    false,
  );
  const [datePlanned, setDatePlanned] = useState('');
  const [hasPlanifier, setHasPlanifier] = useState(false);
  const [showPlanifiedModal, setShowPlanifiedModal] = useState(false);

  const [reportId, setReportId] = useState(null);
  const [chosenLanguage, setChosenLanguage] = useState(null);

  // const [successMessage, setSuccessMessage] = useState('');
  // const [errorMessage, setErrorMessage] = useState('');

  useEffect(() => {
    if (showAlert) {
      setTimeout(() => {
        setShowAlert(false);
      }, 3000);
    }
  }, [showAlert]);

  useEffect(() => {
    if (validRapportSuccess && !validRapportError) {
      dispatch(validateRaportModalReset());
      setShowValidateModal(false);
      setTimeout(() => {
        dispatch(
          getAllRapportBeneficiary({
            id: beneficiaryId,
            pageNumber: currentPage,
          }),
        );
      }, 1200);
    }
  }, [validRapportSuccess, validRapportError]);

  const hasRoleKafalat = useHasRole('GESTIONNAIRE_KAFALAT');
  const hasRoleMarketing = useHasRole('GESTIONNAIRE_MARKETING');
  const hasRoleAssistant = useHasRole('ASSISTANT');
  const hasRoleAdmin = useHasRole('ADMIN');

  const [chosenReturn, setChosenReturn] = useState(null);
  const [errorsInfo, setErrorsInfo] = useState({
    selection: {
      isError: false,
      errorMessage: 'Veuiller Choisir un destinatainer',
    },
    details: {
      isError: false,
      errorMessage: 'Veuiller renseigner quelque details',
    },
  });

  const [errorsLangueInfo, setErrorsLangueInfo] = useState({
    selection: {
      isError: false,
      errorMessage: 'Veuiller Choisir une langue',
    },
  });

  const [errorsDateInfo, setErrorsDateInfo] = useState({
    datePlanned: {
      isError: false,
      errorMessage: 'Veuiller Choisir une date',
    },
  });

  const [selectedRapportStatus, setSelectedRapportStatus] = useState(null);
  const [selectedRapportId, setSelectedRapportId] = useState(null);
  const [errorValidateMessage, setErrorValidateMessage] = useState('');

  const handleCloseForCompleteRapportModal = () => {
    setChosenReturn(null);
    setDetails('');
    setErrorsInfo({
      selection: {
        isError: false,
        errorMessage: 'Veuiller Choisir un destinatainer',
      },
      details: {
        isError: false,
        errorMessage: 'Veuiller renseigner quelque details',
      },
    });
    setShowCompleteRapportModal(false);
  };

  const handleCloseForValidateModal = () => {
    setShowValidateModal(false);
    setErrorValidateMessage('');
  };

  const handleCloseForSelectLangRapportModal = () => {
    setErrorsLangueInfo({
      selection: {
        isError: false,
        errorMessage: 'Veuiller Choisir une langue',
      },
    });
    setShowSelectLangRapportModal(false);
  };

  const handleCloseForPlanifiedModal = () => {
    setErrorsDateInfo({
      datePlanned: {
        isError: false,
        errorMessage: 'Veuiller Choisir une date',
      },
    });
    setDatePlanned('');
    setShowPlanifiedModal(false);
  };

  const handleDatePlanedChange = event => {
    setDatePlanned(event.target.value);
    if (errorsDateInfo.datePlanned.isError) {
      setErrorsDateInfo(prev => ({
        ...prev,
        datePlanned: {
          ...prev.datePlanned,
          isError: false,
        },
      }));
    }
  };

  const handleValiderRapport = () => {
    dispatch(validateRaportModal({ id: selectedRapportId }));
  };

  const handleChangeSelecttion = e => {
    const { value } = e.target;
    setChosenReturn(value);
    if (errorsInfo.selection.isError) {
      setErrorsInfo(prev => {
        return { ...prev, selection: { ...prev.selection, isError: false } };
      });
    }
  };

  const handleViewRapport = useCallback(
    rapportId => {
      dispatch(viewRapport(rapportId, target));
    },
    [dispatch],
  );

  const handleDetailsChange = event => {
    setDetails(event.target.value);
    if (errorsInfo.details.isError) {
      setErrorsInfo(prev => {
        return {
          ...prev,
          details: {
            ...prev.details,
            isError: false,
          },
        };
      });
    }
  };

  const handleSubmitPlannedRapport = useCallback(() => {
    if (!datePlanned) {
      setErrorsDateInfo({
        datePlanned: {
          errorMessage: 'Veuiller Choisir une date',
          isError: true,
        },
      });
    } else {
      const payload = {
        beneficiaryId: beneficiaryId,
        hasPlanifier: 'true',
        datePlanned: datePlanned,
      };
      dispatch(addAgendaRapportManuell(payload));
    }
  }, [datePlanned, beneficiaryId, dispatch]);

  useEffect(() => {
    if (agendaRapportManuellSuccess) {
      handleCloseForPlanifiedModal();
      setSuccessMessage('Planification enregistrée avec succès.');
      setTimeout(() => {
        dispatch(resetAgendaRapportManuell());
        dispatch(
          getAllRapportBeneficiary({
            id: beneficiaryId,
            pageNumber: currentPage,
          }),
        );
        setDatePlanned(null);
        setSuccessMessage('');
      }, 3200);
    }
  }, [agendaRapportManuellSuccess, beneficiaryId]);

  useEffect(() => {
    if (agendaRapportManuellError) {
      setErrorMessage(
        'Un rapport Kafalat est déjà planifié à cette date. Veuillez en choisir une autre.',
      );
      setTimeout(() => {
        setErrorMessage('');
      }, 3000);
    }
  }, [agendaRapportManuellError]);

  const getStatusAssistance = statut => {
    if (
      hasRoleAssistant &&
      (statut === 'RAPPORT_VALIDER_ASSISTANCE' ||
        statut === 'RAPPORT_VALIDER_KAFALAT' ||
        statut === 'RAPPORT_A_COMPLETER_PAR_KAFALAT')
    ) {
      return 'en cours de validation';
    }
    if (hasRoleAssistant && statut === 'RAPPORT_A_COMPLETER_PAR_ASSISTANCE') {
      return 'à completer';
    }
  };

  const getStatusKafalat = statut => {
    if (hasRoleKafalat && statut === 'RAPPORT_VALIDER_ASSISTANCE') {
      return 'à valider';
    }
    if (hasRoleKafalat && statut === 'RAPPORT_VALIDER_KAFALAT') {
      return 'en cours de validation';
    }
    if (hasRoleKafalat && statut === 'RAPPORT_A_COMPLETER_PAR_KAFALAT') {
      return 'à completer';
    }
  };

  const getStatusMarketing = statut => {
    if (hasRoleMarketing && statut === 'RAPPORT_VALIDER_ASSISTANCE') {
      return 'en cours de validation';
    }
    if (hasRoleMarketing && statut === 'RAPPORT_VALIDER_KAFALAT') {
      return 'à valider';
    }
  };

  if (error) {
    errorMessage = 'Une erreur est survenue';
  }
  const listRapports =
    (rapportBeneficiary &&
      rapportBeneficiary.content &&
      rapportBeneficiary.content.map(beneRapport => ({
        id: beneRapport.id,
        datePlanned: beneRapport.datePlanned
          ? formatDate(beneRapport.datePlanned)
          : '',
        status: beneRapport.status ? beneRapport.status : null,
        year: beneRapport.year ? beneRapport.year : null,
        detailComplete: beneRapport.detailComplete
          ? beneRapport.detailComplete
          : '-',
        modifiedAt: beneRapport.modifiedAt
          ? formatDate(beneRapport.modifiedAt)
          : '-',
        numberRapport: beneRapport.numberRapport
          ? `${beneRapport.numberRapport} / ${beneRapport.year}`
          : '-',
        action: { id: beneRapport.id, ...beneRapport },
        rapportId: beneRapport.rapportId,
        dateValidate: beneRapport.dateValidate
          ? formatDate(beneRapport.dateValidate)
          : '-',
        beneRapport,
      }))) ||
    [];

  const columns = [
    {
      field: 'datePlanned',
      headerName: 'Date de Planification',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
    {
      field: 'status',
      headerName: 'Statut',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
      renderCell: params => {
        const statut = params.row.status;
        const statusName = hasRoleAdmin
          ? getStatusName(statut)
          : hasRoleAssistant
            ? getStatusAssistance(statut) || getStatusName(statut)
            : hasRoleKafalat
              ? getStatusKafalat(statut) || getStatusName(statut)
              : hasRoleMarketing
                ? getStatusMarketing(statut) || getStatusName(statut)
                : getStatusName(statut);
        const statusStyle = getStatusStyle(statut);
        return (
          <span
            className={statusStyle}
            style={{ padding: '5px', borderRadius: '4px' }}
          >
            {statusName}
          </span>
        );
      },
    },
    {
      field: 'numberRapport',
      headerName: 'Numéro du Rapport',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
    {
      field: 'modifiedAt',
      headerName: 'Dernière modification',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
    {
      field: 'dateValidate',
      headerName: 'Date de validation',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
    {
      field: 'detailComplete',
      headerName: 'Détail du complément',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
  ];

  columns.push({
    field: 'actions',
    headerName: 'Actions',
    flex: 1.2,
    headerAlign: 'center',
    align: 'center',
    type: 'actions',
    renderCell: params => (
      <div style={{ display: 'flex', alignItems: 'center' }}>
        {(params.row.status === 'RAPPORT_PLANIFIER' ||
          params.row.status === 'RAPPORT_A_PREPARE') && (
            <input
              type="image"
              src={FILE_PLUS_ICON}
              className="p-2"
              width="40px"
              height="40px"
              title="visualiser"
              onClick={() => {
                history.push('/beneficiaries/add/rapport', {
                  idBeneficiary: beneficiaryId,
                  agendaRapportId: params.row.id,
                });
              }}
            />
          )}
        {params.row.status !== 'RAPPORT_PLANIFIER' &&
          params.row.status !== 'RAPPORT_A_PREPARE' && (
            <>
              <input
                type="image"
                src={ARTICLE_ICON}
                className="p-2"
                width="40px"
                height="40px"
                title="visualiser"
                onClick={() => {
                  setReportId(params.row.rapportId);
                  setShowSelectLangRapportModal(true);
                }}
              />
              {((hasRoleAssistant && isAssistantUpdate(params.row.status)) ||
                (hasRoleKafalat && isKafalatUpdate(params.row.status)) ||
                (hasRoleMarketing && isMarketingUpdate(params.row.status))) && (
                  <>
                    <input
                      type="image"
                      src={EDIT_ICON}
                      className="p-2"
                      width="40px"
                      height="40px"
                      title="modifier"
                      onClick={() => {
                        history.push('/beneficiaries/update/rapport', {
                          rapportId: params.row.rapportId,
                          idBeneficiary: beneficiaryId,
                          isUpdate: true,
                        });
                      }}
                    />
                  </>
                )}
            </>
          )}
        {((hasRoleAssistant && isAssistant(params.row.status)) ||
          (hasRoleKafalat && isSeviceKafalat(params.row.status)) ||
          (hasRoleMarketing && isSeviceMerketing(params.row.status))) && (
            <>
              <input
                type="image"
                src={CHECK_ICON}
                className="p-2"
                width="40px"
                height="40px"
                title="valider"
                onClick={() => {
                  setSelectedRapportId(params.row.rapportId);
                  setShowValidateModal(true);
                }}
              />
            </>
          )}
        {((hasRoleKafalat && isSeviceKafalat(params.row.status)) ||
          (hasRoleMarketing && isSeviceMerketing(params.row.status))) && (
            <>
              <input
                type="image"
                src={RETWEET_ICON}
                className="p-2"
                width="40px"
                height="40px"
                title="à completer"
                onClick={() => {
                  setSelectedRapportStatus(params.row.status);
                  setSelectedRapportId(params.row.rapportId);
                  setShowCompleteRapportModal(true);
                }}
              />
            </>
          )}
        {((hasRoleKafalat && isKafalatDelete(params.row.status)) ||
          (hasRoleMarketing && isMarketingDelete(params.row.status))) && (
            <>
              <input
                type="image"
                src={DELETE_ICON}
                className="p-2"
                width="40px"
                height="40px"
                title="supprimer"
                onClick={() => {
                  setRapportToDelete(params.row.action);
                  setShowDeleteModal(true);
                }}
              />
            </>
          )}
      </div>
    ),
  });

  const handleCloseForDeleteModal = () => setShowDeleteModal(false);

  const handleSubmitCompletion = useCallback(() => {
    if (selectedRapportStatus === 'RAPPORT_VALIDER_KAFALAT') {
      if (!chosenReturn && details.length === 0) {
        setErrorsInfo(prev => ({
          details: {
            ...prev.details,
            isError: true,
          },
          selection: {
            ...prev.selection,
            isError: true,
          },
        }));
      } else if (!chosenReturn) {
        setErrorsInfo(prev => ({
          ...prev,
          selection: {
            ...prev.selection,
            isError: true,
          },
        }));
      } else if (details.length === 0) {
        setErrorsInfo(prev => ({
          ...prev,
          details: {
            ...prev.details,
            isError: true,
          },
        }));
      } else {
        dispatch(
          completeModalRapport({
            rapportId: selectedRapportId,
            beneficiaryId,
            description: details,
            returnId: chosenReturn,
          }),
        );
      }
    } else if (details.length === 0) {
      setErrorsInfo(prev => ({
        selection: prev.selection,
        details: {
          ...prev.details,
          isError: true,
        },
      }));
    } else {
      dispatch(
        completeModalRapport({
          rapportId: selectedRapportId,
          beneficiaryId,
          description: details,
          returnId: null,
        }),
      );
    }
  }, [
    details,
    selectedRapportId,
    beneficiaryId,
    chosenReturn,
    selectedRapportStatus,
  ]);

  const handleChangeLangueSelecttion = e => {
    const { value } = e.target;
    setChosenLanguage(value);
    if (errorsLangueInfo.selection.isError) {
      setErrorsLangueInfo(prev => ({
        ...prev,
        selection: {
          ...prev.selection,
          isError: false,
        },
      }));
    }
  };

  const handleSubmitViewRapport = useCallback(() => {
    if (!chosenLanguage) {
      setErrorsLangueInfo(prev => ({
        ...prev,
        selection: {
          ...prev.selection,
          isError: true,
        },
      }));
    } else {
      handleViewRapport(reportId);
      handleCloseForSelectLangRapportModal();
      dispatch(
        viewRapport({ rapport: reportId, language: chosenLanguage, target }),
      );
    }
  }, [chosenLanguage, reportId, beneficiaryId, history, handleViewRapport]);

  return (
    <div>
      <Modal2
        title="Consulter le rapport"
        size="lg"
        customWidth="modal-90w"
        show={show}
        handleClose={() => {
          setShow(false);
        }}
      >
        <RapportFormModel
          action={
            rapportToEdit ||
            (rapportToReadOnly.action
              ? rapportToReadOnly.action
              : rapportToEdit)
          }
          handleClose={handleClose}
          target={target}
          id={params.id}
        />
      </Modal2>

      <Modal2
        centered
        className="mt-5"
        title="Confirmation de suppression"
        show={showDeleteModal}
        handleClose={handleCloseForDeleteModal}
      >
        <p className="mt-1 mb-5">
          Êtes-vous sûr de vouloir supprimer ce rapport?
        </p>
        <div className="d-flex justify-content-end px-3 my-1">
          <button
            type="button"
            className="mx-2 btn-style outlined"
            onClick={handleCloseForDeleteModal}
          >
            Annuler
          </button>
          <button
            type="submit"
            className="mx-2 btn-style primary"
            onClick={() => {
              dispatch(deleteRapportById({ id: rapportToDelete.id }));
              setRapportToDelete('');
              handleCloseForDeleteModal();
            }}
          >
            Supprimer
          </button>
        </div>
      </Modal2>
      <Modal2
        centered
        className="mt-5"
        title="Demande de complément"
        show={showCompleteRapportModal}
        handleClose={handleCloseForCompleteRapportModal}
      >
        {selectedRapportStatus === 'RAPPORT_VALIDER_KAFALAT' && (
          <div className="mb-3">
            <label htmlFor="statutSelect" className="form-label">
              Renvoyer à <span className="text-danger">*</span>
            </label>
            <select
              className={`form-select custom-select ${errorsInfo.selection.isError ? 'is-invalid' : ''
                }`}
              onChange={handleChangeSelecttion}
            >
              <option value="">Renvoyer le rapport à</option>
              {listReturns.map(el => (
                <option key={el.value} value={el.id} id={el.id}>
                  {el.value}
                </option>
              ))}
            </select>
            {errorsInfo.selection.isError && (
              <div className="text-danger">Ce champ est requis.</div>
            )}
          </div>
        )}
        <div className="mb-3">
          <label>
            Details <span style={{ color: 'red' }}>*</span>
          </label>
          <textarea
            id="detailsTextarea"
            className={`form-control ${errorsInfo.details.isError ? 'is-invalid' : ''
              }`}
            rows="3"
            value={details}
            onChange={handleDetailsChange}
            required
          ></textarea>
          {errorsInfo.details.isError && (
            <div className="text-danger">Ce champ est requis.</div>
          )}
        </div>
        <div className="d-flex justify-content-end px-3 my-1">
          <button
            type="button"
            className="mx-2 btn-style outlined"
            onClick={handleCloseForCompleteRapportModal}
          >
            Annuler
          </button>
          <button
            type="button"
            className={`mx-2 ${btnStyles.rejectBtn}`}
            onClick={handleSubmitCompletion}
          >
            {completeLoading ? 'loading...' : 'Valider'}
          </button>
        </div>
      </Modal2>

      <Modal2
        centered
        className="mt-5"
        title="Valider le rapport"
        show={showValidateModal}
        handleClose={handleCloseForValidateModal}
      >
        {errorValidateMessage && (
          <div className="alert alert-danger" role="alert">
            <p>{errorValidateMessage.split(':')[0]} :</p>
            <ul>
              {errorValidateMessage
                .split(':')[1]
                .split(',')
                .map((item, index) => (
                  <li key={index}>
                    <strong>{item.trim()}</strong>
                  </li>
                ))}
            </ul>
          </div>
        )}
        <p className="mt-1 mb-5">
          Êtes-vous sûr de vouloir valider ce rapport?
        </p>
        <div className="d-flex justify-content-end px-3 my-1">
          <button
            type="button"
            className="mx-2 btn-style outlined"
            onClick={handleCloseForValidateModal}
          >
            Annuler
          </button>
          <button
            type="submit"
            className={`mx-2 ${btnStyles.rejectBtn}`}
            onClick={handleValiderRapport}
          >
            {validRapportLoading ? 'loading...' : 'Valider'}
          </button>
        </div>
      </Modal2>
      <Modal2
        centered
        className="mt-5"
        title="visualisation"
        show={showSelectLangRapportModal}
        handleClose={handleCloseForSelectLangRapportModal}
      >
        <div className="mb-3">
          <label htmlFor="statutSelect" className="form-label">
            Veuillez sélectionner la langue dans laquelle vous souhaitez
            visualiser le rapport. <span className="text-danger">*</span>
          </label>
          <select
            className={`form-select custom-select ${errorsLangueInfo.selection.isError ? 'is-invalid' : ''
              }`}
            onChange={handleChangeLangueSelecttion}
          >
            <option value="">sélectionner la langue</option>
            {listLangues.map(el => (
              <option key={el.key} value={el.key} id={el.id}>
                {el.value}
              </option>
            ))}
          </select>
          {errorsLangueInfo.selection.isError && (
            <div className="text-danger">Ce champ est requis.</div>
          )}
        </div>
        <div className="d-flex justify-content-end px-3 my-1">
          <button
            type="button"
            className={`mx-2 ${btnStyles.cancelBtn}`}
            onClick={handleCloseForSelectLangRapportModal}
          >
            Annuler
          </button>
          <button
            type="button"
            className={`mx-2 ${btnStyles.rejectBtn}`}
            onClick={handleSubmitViewRapport}
          >
            {rapportViewLoading ? 'loading...' : 'Confirmer'}
          </button>
        </div>
      </Modal2>

      <Modal2
        centered
        className="mt-5"
        title="Planifier un rapport Kafalat"
        show={showPlanifiedModal}
        handleClose={handleCloseForPlanifiedModal}
      >
        <div className="mb-3">
          <label htmlFor="statutSelect" className="form-label">
            Veuillez sélectionner la date dans laquelle vous souhaitez planifier
            le rapport. <span className="text-danger">*</span>
          </label>
          <input
            type="date"
            id="datePlanified"
            className={`form-control ${errorsDateInfo.datePlanned.isError ? 'is-invalid' : ''
              }`}
            rows="3"
            value={datePlanned}
            onChange={handleDatePlanedChange}
            required
          />
          {errorsDateInfo.datePlanned.isError && (
            <div className="text-danger">Ce champ est requis.</div>
          )}
        </div>
        <div className="d-flex justify-content-end px-3 my-1">
          <button
            type="button"
            className={`mx-2 ${btnStyles.cancelBtn}`}
            onClick={handleCloseForPlanifiedModal}
          >
            Annuler
          </button>
          <button
            type="button"
            className={`mx-2 ${btnStyles.rejectBtn}`}
            onClick={handleSubmitPlannedRapport}
          >
            {agendaRapportManuellLoading ? 'loading...' : 'Confirmer'}
          </button>
        </div>
      </Modal2>

      {showAlert ? (
        <Alert
          className="alert-style"
          variant="success"
          onClose={() => setShowAlert(false)}
          dismissible
        >
          <p>{successMessageAddition}</p>
        </Alert>
      ) : null}

      {/* {successMessage && (
        <Alert
          variant="success"
          onClose={() => setSuccessMessage('')}
          dismissible
        >
          <p>{successMessage}</p>
        </Alert>
      )}

      {errorMessage && (
        <Alert variant="danger" onClose={() => setErrorMessage('')} dismissible>
          <p>{errorMessage}</p>
        </Alert>
      )} */}

      <div className={list.backgroudStyle}>
        <div className={list.global}>
          <div className={list.header}>
            <h4>Liste des rapports</h4>

            <AccessControl module="BENEFICIARY" functionality="CREATE">
              {/* <button
                className={btnStyles.addBtnProfile}
                onClick={() => {
                  history.push('/beneficiaries/add/rapport', {
                    idBeneficiary: beneficiaryId,
                  });
                }}
              >
                Ajouter un nouveau rapport
              </button> */}

              <button
                className={btnStyles.addBtnProfile}
                // onClick={() => {
                //   const payload = {
                //     beneficiaryId: beneficiaryId,
                //     hasPlanifier: 'true',
                //   };
                //   dispatch(addAgendaRapport(payload));
                // }}
                onClick={() => {
                  setShowPlanifiedModal(true);
                }}
              >
                planifier un nouveau rapport
              </button>
            </AccessControl>
          </div>
          <div className={list.content}>
            <GenericFilter
              className="col-12"
              principalInputsConfig={principalInputsConfig}
              additionalInputsConfig={additionalInputsConfig}
              onApplyFilter={handleApplyFilter}
              filterValues={filterValues}
              setFilterValues={setFilterValues}
              onResetFilterComplete={handleResetFilterComplete}
            />
            <DataTable
              rows={listRapports}
              loading={listRapportLoading}
              columns={columns}
              fileName={`Liste des rapports du beneficiaire ${params && params.id ? params.id : 0
                } , ${new Date().toLocaleString()}`}
            />

            <div className="justify-content-center my-4">
              {rapportBeneficiary && (
                // <CustomPagination
                //   totalCount={Math.ceil(
                //     rapportBeneficiary &&
                //       rapportBeneficiary.content &&
                //       rapportBeneficiary.content.length / pageSize,
                //   )}
                //   pageSize={pageSize}
                //   currentPage={currentPage}
                //   onPageChange={setCurrentPage}
                // />
                <CustomPagination
                  totalElements={rapportBeneficiary.totalElements}
                  totalCount={
                    Math.ceil(
                      rapportBeneficiary &&
                      rapportBeneficiary.totalElements &&
                      rapportBeneficiary.pageable.pageSize &&
                      rapportBeneficiary.totalElements /
                      rapportBeneficiary.pageable.pageSize,
                    ) || 1
                  }
                  pageSize={
                    (rapportBeneficiary &&
                      rapportBeneficiary.pageable &&
                      rapportBeneficiary.pageable.pageSize &&
                      rapportBeneficiary.pageable.pageSize) ||
                    10
                  }
                  currentPage={currentPage + 1 || 1}
                  onPageChange={handlePageChange}
                />
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
