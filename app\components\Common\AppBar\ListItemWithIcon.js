import React from 'react';
import { ListItem, ListItemIcon, ListItemText } from '@mui/material';
import { NavLink } from 'react-router-dom';

const ListItemWithIcon = ({
  icon,
  text,
  to,
  isActive,
  onClick,
  listItemClassName,
}) => (
  <ListItem
    button
    className={listItemClassName}
    component={NavLink}
    to={to}
    onClick={onClick}
  >
    <ListItemIcon className="ml-4">
      {React.cloneElement(icon, { sx: { fontSize: '20px' } })}
    </ListItemIcon>
    <ListItemText primary={text} />
  </ListItem>
);

export default ListItemWithIcon;
