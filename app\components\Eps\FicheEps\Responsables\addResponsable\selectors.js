import { createSelector } from 'reselect';
import { initialState } from './reducer';

const selectOmdb = state => state.responsable || initialState;

const makeSelectSuccess = createSelector(
  selectOmdb,
  omdbState => omdbState.success,
);

const makeSelectLoading = createSelector(
  selectOmdb,
  omdbState => omdbState.loading,
);

const makeSelectError = createSelector(
  selectOmdb,
  omdbState => omdbState.error,
);

const makeSelectResponsable = createSelector(
  selectOmdb,
  omdbState => omdbState.responsable,
);
export {
  selectOmdb,
  makeSelectSuccess,
  makeSelectLoading,
  makeSelectError,
  makeSelectResponsable,
};
