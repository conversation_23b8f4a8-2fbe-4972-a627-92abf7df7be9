import React, { useState, useEffect } from 'react';
import { useHistory } from 'react-router-dom';
import listStyle from 'Css/profileList.css';
import btnStyles from 'Css/button.css';
import { Alert } from 'react-bootstrap';
import AccessControl from 'utils/AccessControl';
import { Link } from 'react-router-dom';
import { Chip } from '@material-ui/core';

import { useDispatch, useSelector } from 'react-redux';
import DataTable from 'components/Common/DataTable';

import Modal2 from 'components/Common/Modal2';
import {
    deleteEps,
    resetEps,
    resetDeleteEps,
} from 'containers/Eps/EpsList/actions';
import {
    DELETE_ICON,
    EDIT_ICON,
    VIEW_ICON,
} from 'components/Common/ListIcons/ListIcons';
import styles from '../../../Css/tag.css';
import { deleteServiceCollectEps } from 'containers/ServiceCollectEps/ListServiceCollectEps/actions';

const ListServiceCollectEps = ({ serviceCollectEpsList }) => {

    const handleCloseForDeleteModal = () => setShowDeleteModal(false);
    const dispatch = useDispatch();
    const history = useHistory();
    const [showAlert, setShowAlert] = useState(false);
    const [message, setMessage] = useState('');

    const [showDeleteModal, setShowDeleteModal] = useState(false);
    const [epsToEdit, setEpsToEdit] = useState('');
    const [epsToDelete, setEpsToDelete] = useState('');


    const viewHandler = id => {
        history.push(`/ServiceCollectEps/fiche/${id}/info`, { params: id });
    };

    const editHandler = id => {
        history.push(`/ServiceCollectEps/edit/${id}`);
    };

    const columns = [
        {
            field: 'code',
            headerName: 'Code',
            flex: 1,
            headerAlign: 'center',
            align: 'center',
        },
        {
            field: 'nom',
            headerName: 'Nom',
            flex: 1,
            headerAlign: 'center',
            align: 'center',
        },
        {
            field: 'eps',
            headerName: 'Eps',
            flex: 1,
            headerAlign: 'center',
            align: 'center',
            renderCell: params => {
                const epsServiceCollect = params.row;
                let eps = <Link to={`/eps/fiche/${epsServiceCollect.eps.id}/info`}>
                    {`${epsServiceCollect.eps.name}`}
                </Link>;

                return eps || '-';
            },
        },

        {
            field: 'Statut',
            headerName: 'Statut',
            flex: 1,
            headerAlign: 'center',
            align: 'center',

            renderCell: (params) => {
                const epsServiceCollect = params.row;
                let statut = '';

                if (epsServiceCollect.isCloture) {
                    statut = 'Cloturé';
                } else {
                    const currentYear = new Date().getFullYear();
                    const currentMonth = new Date().getMonth() + 1;

                    if (epsServiceCollect.annee < currentYear ||
                        (epsServiceCollect.annee === currentYear && epsServiceCollect.mois < currentMonth)) {
                        statut = 'Fermé';
                    } else if (epsServiceCollect.annee > currentYear ||
                        (epsServiceCollect.annee === currentYear && epsServiceCollect.mois > currentMonth)) {
                        statut = 'Planifié';
                    } else {
                        statut = 'Ouvert';
                    }
                }

                // Define background colors
                const statusColors = {
                    'Cloturé': '#FF4D4D',   // Red
                    'Fermé': '#FFA500',     // Orange
                    'Planifié': '#4D94FF', // Blue
                    'Ouvert': '#28A745',    // Green
                };

                return (
                    <div
                        style={{
                            backgroundColor: statusColors[statut] || '#000',
                            color: '#000',
                            padding: '5px 10px',
                            borderRadius: '5px',
                            fontWeight: 'bold',
                            textAlign: 'center'
                        }}
                    >
                        {statut}
                    </div>
                );
            }
        },
        {
            field: 'actions',
            headerName: 'Actions',
            flex: 1,
            headerAlign: 'center',
            align: 'center',
            renderCell: params => (
                <div
                    style={{
                        display: 'flex',
                        flexDirection: 'row',
                        justifyContent: 'space-around',
                        alignItems: 'center',
                    }}
                >
                    <input
                        type="image"
                        onClick={() => viewHandler(params.row.id)}
                        className="p-2"
                        src={VIEW_ICON}
                        width="40px"
                        height="40px"
                        title="consulter"
                    />
                    <AccessControl module="USER" functionality="UPDATE">
                        <input
                            type="image"
                            onClick={() => editHandler(params.row.id)}
                            className="p-2"
                            src={EDIT_ICON}
                            width="40px"
                            height="40px"
                            title="Modifier"
                        />
                    </AccessControl>
                    <input
                        type="image"
                        src={DELETE_ICON}
                        className="p-2"
                        width="40px"
                        height="40px"
                        onClick={() => {
                            setEpsToDelete(params.row.id);
                            setShowDeleteModal(true);
                            setMessage('Le service de collecte a été supprimé avec succès.');
                        }}
                        disabled={params.row.isContainDons === true} // Disable when status is "Actif"
                        style={{
                            opacity: params.row.isContainDons === true ? 0.5 : 1, // Reduce opacity when disabled
                            cursor: params.row.isContainDons === true ? "not-allowed" : "pointer", // Change cursor style
                        }}
                        title={params.row.isContainDons === true ? "Suppression impossible : des donations sont déjà enregistrées pour ce service." : "Supprimer"} // Change title based on status
                    />
                </div>
            ),
        },
    ];
    return (
        <div className="table-container">
            {showAlert ? (
                <Alert
                    className="pb-0"
                    variant="success"
                    onClose={() => setShowAlert(false)}
                    dismissible
                >
                    <p>{message}</p>
                </Alert>
            ) : null}

            <Modal2
                centered
                className="mt-5"
                title="Confirmation de suppression"
                show={showDeleteModal}
                handleClose={handleCloseForDeleteModal}
            >
                <p className="mt-1 mb-5">
                    Êtes-vous sûr de vouloir supprimer ce service de collecte ?
                </p>
                <div className="d-flex justify-content-end px-3 my-1">
                    <button
                        type="button"
                        className={`mx-2 ${btnStyles.cancelBtn}`}
                        onClick={handleCloseForDeleteModal}
                    >
                        Annuler
                    </button>
                    <button
                        type="submit"
                        className={`mx-2 ${btnStyles.addBtn}`}
                        onClick={() => {
                            const epsToDeleteObj = serviceCollectEpsList.find(eps => eps.id === epsToDelete);
                            if (epsToDeleteObj) {
                                dispatch(deleteServiceCollectEps(epsToDeleteObj.id));
                                setEpsToDelete('');
                                handleCloseForDeleteModal();
                            }
                        }}
                    >
                        Confirmer la suppression
                    </button>
                </div>
            </Modal2>
            <div className={listStyle.global}>{/* Any additional UI elements */}</div>
            <div>
                <DataTable
                    rows={serviceCollectEpsList.map(eps => ({
                        ...eps
                    }))}
                    columns={columns}
                    fileName={`Liste des Eps, ${new Date().toLocaleString()}`}
                />
            </div>
            </div>
    );
};

export default ListServiceCollectEps;