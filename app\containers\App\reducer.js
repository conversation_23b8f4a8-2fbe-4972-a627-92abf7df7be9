// reducer.js
import {
  <PERSON>ETCH_CONNECTED_USER_REQUEST,
  FETCH_CONNECTED_USER_SUCCESS,
  FETCH_CONNECTED_USER_FAILURE,
  CALL_DISCONNECT_ACTION,
  CALL_DISCONNECT_ACTION_SUCCESS,
  CALL_DISCONNECT_ACTION_FAIL,
  SET_IMPERSONATED_USER,
  CLEAR_IMPERSONATED_USER,
  START_IMPERSONATION_REQUEST,
  START_IMPERSONATION_SUCCESS,
  START_IMPERSONATION_FAILURE,
  STOP_IMPERSONATION_REQUEST,
  STOP_IMPERSONATION_SUCCESS,
  STOP_IMPERSONATION_FAILURE,
} from './constants';

export const initialState = {
  loading: false,
  error: null,
  connectedUser: null,
  success: false,
  disconnect: false,
  disconnect_loading: false,
  disconnect_error: false,
  isImpersonating: false,
  impersonatedUser: null,
  originalUser: null,
  impersonationLoading: false,
  impersonationError: null,
};

const appReducer = (state = initialState, action) => {
  switch (action.type) {
    case FETCH_CONNECTED_USER_REQUEST:
      return {
        ...state,
        loading: true,
        error: null,
      };
    case FETCH_CONNECTED_USER_SUCCESS:
      return {
        ...state,
        connectedUser: action.payload,
        loading: false,
        error: false,
        success: true,
      };
    case FETCH_CONNECTED_USER_FAILURE:
      return {
        ...state,
        loading: false,
        error: action.payload,
        success: false,
      };
    case CALL_DISCONNECT_ACTION:
      return {
        ...state,
        disconnect_loading: true,
        disconnect: false,
        disconnect_error: false,
      };

    case CALL_DISCONNECT_ACTION_SUCCESS:
      return {
        ...state,
        disconnect_loading: false,
        disconnect: true,
        disconnect_error: false,
      };
    case CALL_DISCONNECT_ACTION_FAIL:
      return {
        ...state,
        disconnect_loading: false,
        disconnect: false,
        disconnect_error: true,
      };

    // Impersonation cases
    case START_IMPERSONATION_REQUEST:
      return {
        ...state,
        impersonationLoading: true,
        impersonationError: null,
      };

    case START_IMPERSONATION_SUCCESS:
      return {
        ...state,
        impersonationLoading: false,
        impersonationError: null,
        isImpersonating: true,
      };

    case START_IMPERSONATION_FAILURE:
      return {
        ...state,
        impersonationLoading: false,
        impersonationError: action.payload,
        isImpersonating: false,
      };

    case STOP_IMPERSONATION_REQUEST:
      return {
        ...state,
        impersonationLoading: true,
        impersonationError: null,
      };

    case STOP_IMPERSONATION_SUCCESS:
      return {
        ...state,
        impersonationLoading: false,
        impersonationError: null,
        isImpersonating: false,
        impersonatedUser: null,
      };

    case STOP_IMPERSONATION_FAILURE:
      return {
        ...state,
        impersonationLoading: false,
        impersonationError: action.payload,
      };

    case SET_IMPERSONATED_USER:
      return {
        ...state,
        isImpersonating: true,
        impersonatedUser: action.payload,
        // Save the original user if not already saved
        originalUser: state.originalUser || state.connectedUser,
        // Update the connectedUser to be the impersonated user
        connectedUser: action.payload,
      };

    case CLEAR_IMPERSONATED_USER:
      return {
        ...state,
        isImpersonating: false,
        impersonatedUser: null,
        originalUser: null,
        // We don't reset connectedUser here as we'll need to fetch the original user
      };

    default:
      return state;
  }
};

export default appReducer;
