import React, { useEffect, useState } from 'react';
import moment from 'moment';
import DocumentForm from 'containers/Common/SubComponents/DocumentForm';
import reducer from 'containers/Common/SubComponents/DocumentForm/reducer';
import { useDispatch, useSelector } from 'react-redux';
import { useInjectReducer, useInjectSaga } from 'redux-injectors';
import { deleteDocumentSaga } from 'containers/Common/SubComponents/DocumentForm/saga';
import { createStructuredSelector } from 'reselect';
import {
  makeSelecDocument,
  makeSelecDocumentDeleteSuccess,
  makeSelecDocumentDownloadSuccess,
  makeSelecDocuments,
  makeSelectDocumentAddDonorSuccess,
  makeSelectError,
  makeSelectSuccess,
} from 'containers/Common/SubComponents/DocumentForm/selectors';
import {
  pushDocumentDonor,
  removeDocumentDonor,
  updateDocumentDonor,
} from 'containers/Donor/DonorProfile/actions';
import { useParams } from 'react-router-dom';
import {
  deleteDocument,
  downloadDocument,
  fetchDataRequest,
  resetDocument,
  viewDocument,
} from 'containers/Common/SubComponents/DocumentForm/actions';
import { Alert } from 'react-bootstrap';

import Modal2 from 'components/Common/Modal2';
import AccessControl, { isAuthorized } from 'utils/AccessControl';
import { makeSelectTagList } from 'containers/tag/selectors';
import { getTags } from 'containers/tag/actions';
import { TagsContainer, Tag, CloseIcon } from 'components/Common/StyledTags';
import tagReducer from 'containers/tag/reducer';
import tagSaga from 'containers/tag/saga';
import CustomPagination from '../../Common/CustomPagination';
import DataTable from '../../Common/DataTable';
import {
  DELETE_ICON,
  DOWNLOAD_ICON,
  EDIT_ICON,
  VIEW_ICON,
} from '../../Common/ListIcons/ListIcons';
import listStyle from '../../../Css/profileList.css';
import btnStyles from '../../../Css/button.css';

const key = 'document';
const keyTag = 'tagList';

const formatDate = date => moment(date).format('DD/MM/YYYY');

const emptyValue = <span>-</span>;

const deleteIcon = require('../../../images/icons/delete.svg');
const viewIcon = require('../../../images/icons/eye.svg');
const editIcon = require('../../../images/icons/edit.svg');
const fileIcon = require('../../../images/icons/file.svg');

const target = 'donor';

const omdbSelector = createStructuredSelector({
  success: makeSelectSuccess,
  error: makeSelectError,
  document: makeSelecDocument,
  successDelete: makeSelecDocumentDeleteSuccess,
  documents: makeSelecDocuments,
  documentDonorAddSuccess: makeSelectDocumentAddDonorSuccess,
  successDownload: makeSelecDocumentDownloadSuccess,
});

const tagSelector = createStructuredSelector({
  tagList: makeSelectTagList,
});

export default function listDocuments(props) {
  const dispatch = useDispatch();

  useInjectReducer({ key, reducer });
  useInjectSaga({ key, saga: deleteDocumentSaga });
  useInjectReducer({ key: keyTag, reducer: tagReducer });
  useInjectSaga({ key: keyTag, saga: tagSaga });
  const params = useParams();

  const {
    success,
    error,
    document,
    successDelete,
    documents,
    documentDonorAddSuccess,
    successDownload,
  } = useSelector(omdbSelector);
  const { tagList } = useSelector(tagSelector);

  const [typeDocumentName, setTypeDocumentName] = useState('');
  const [documentToEdit, setDocumentToEdit] = useState('');
  const [message, setMessage] = useState('');
  let errorMessage = null;
  const [show, setShow] = useState(false);
  const [showAlert, setShowAlert] = useState(false);
  const [documentToDelete, setDocumentToDelete] = useState('');
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [selectedTagId, setSelectedTagId] = useState(null);

  // Define pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const pageSize = 5; // Define page size

  useEffect(() => {
    dispatch(getTags());
  }, [dispatch]);

  const donor = props.data;
  let donorCode;

  if (donor) {
    donorCode = donor.code;
  }

  const handleClose = () => {
    setShow(false);
  };

  const handleShow = () => {
    setShow(true);
  };

  const handleCloseForDeleteModal = () => setShowDeleteModal(false);

  useEffect(
    () =>
      function cleanup() {
        dispatch(resetDocument());
        setDocumentToEdit('');
      },
    [],
  );

  if (error) {
    errorMessage = 'Une erreur est survenue';
  }

  useEffect(() => {
    if (successDelete) {
      setShowAlert(true);
      dispatch(removeDocumentDonor(document));
      setMessage('Document supprimé avec succès');
    }
  }, [successDelete]);

  useEffect(() => {
    if (successDownload) {
      setShowAlert(true);
      setMessage('succès  du téléchargement du document');
    }
  }, [successDownload]);

  useEffect(() => {
    if (documentDonorAddSuccess) {
      setShowAlert(true);
      if (documentToEdit) {
        setMessage('Document modifié avec succès');
      } else {
        setMessage('Document ajouté avec succès');
      }
      setDocumentToEdit('');
    }
  }, [documentDonorAddSuccess]);

  // to close the success message after 4 seconds
  useEffect(() => {
    if (showAlert) {
      setTimeout(() => {
        setShowAlert(false);
      }, 4000);
    }
  }, [showAlert]);

  useEffect(() => {
    dispatch(fetchDataRequest(params.id, target));
  }, []);

  useEffect(() => {
    if (successDelete || documentDonorAddSuccess) {
      dispatch(fetchDataRequest(params.id, target));
    }
  }, [successDelete, documentDonorAddSuccess]);

  let listDocuments = null;
  if (documents) {
    const documentsSorted = [...documents].sort((a, b) =>
      a.id < b.id ? 1 : -1,
    );

    // Filter documents based on selected tag
    const filteredDocuments = selectedTagId
      ? documentsSorted.filter(
        doc => doc.tags && doc.tags.includes(selectedTagId),
      )
      : documentsSorted;

    // Implement pagination logic
    const startIndex = (currentPage - 1) * pageSize;
    const endIndex = Math.min(startIndex + pageSize, filteredDocuments.length);
    const paginatedDocuments = filteredDocuments.slice(startIndex, endIndex);

    listDocuments = paginatedDocuments.map(document => ({
      id: document.id,
      label: document.label || '---',
      type: document.type ? document.type.name : '---',
      documentDate: document.documentDate
        ? formatDate(document.documentDate)
        : '---',
      expiryDate: document.expiryDate ? formatDate(document.expiryDate) : '---',
      comment: document.comment || '---',
      document,
    }));
  }

  const columns = [
    {
      field: 'label',
      headerName: 'Libellé',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
    {
      field: 'type',
      headerName: 'Type Document',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
    {
      field: 'documentDate',
      headerName: 'Date Document',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
    {
      field: 'expiryDate',
      headerName: "Date d'expiration",
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
    {
      field: 'comment',
      headerName: 'Commentaire',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
    {
      field: 'tags',
      headerName: 'Tags',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
      renderCell: params => {
        if (
          !params.row.document.tags ||
          params.row.document.tags.length === 0
        ) {
          return '---';
        }
        return (
          <div style={{ whiteSpace: 'normal', wordBreak: 'break-word',backgroundColor: 'white' }}>
            {params.row.document.tags
              .map(tagId => {
                const tag = tagList.find(t => t.id === tagId);
                return tag ? tag.name : '';
              })
              .filter(name => name !== '')
              .join(', ')}
          </div>
        );
      },
    },
  ];

  const connectedUser = useSelector(state => state.app.connectedUser);
  const isUpdateAuthorized = isAuthorized(connectedUser, 'DONOR', 'UPDATE');
  if (isUpdateAuthorized) {
    // If the user is an admin, add the Actions column
    columns.push({
      field: 'actions',
      type: 'actions',
      headerName: 'Actions',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
      renderCell: rowData => (
        <div className="p-0">
          <input
            type="image"
            src={VIEW_ICON}
            className="p-0 mr-1"
            width="20px"
            height="20px"
            title="visualiser"
            onClick={() => {
              dispatch(viewDocument(rowData.row.document, target));
            }}
          />
          <input
            type="image"
            src={DOWNLOAD_ICON}
            className="p-0 mr-1"
            width="20px"
            height="20px"
            title="télécharger"
            onClick={() => {
              dispatch(downloadDocument(rowData.row.document, target));
            }}
          />
          <input
            type="image"
            className="p-0 mr-1"
            onClick={() => {
              const data = rowData.row.document;
              setDocumentToEdit(rowData.row.document);
              handleShow();
            }}
            src={EDIT_ICON}
            width="20px"
            height="20px"
            title="modifier"
          />
          <input
            type="image"
            src={DELETE_ICON}
            className="p-0"
            width="20px"
            height="20px"
            title="supprimer"
            onClick={() => {
              setDocumentToDelete(rowData.row.document);
              setShowDeleteModal(true);
            }}
          />
        </div>
      ),
    });
  }

  return (
    <div>
      {showAlert ? (
        <Alert
          className="alert-style"
          variant="success"
          onClose={() => setShowAlert(false)}
          dismissible
        >
          <p>{message}</p>
        </Alert>
      ) : null}

      {error && (
        <Alert
          className="alert-style"
          variant="danger"
          onClose={() => setShowAlert(false)}
          dismissible
        >
          <p>{errorMessage}</p>
        </Alert>
      )}

      <div className={listStyle.backgroudStyle}>
        <div className={listStyle.global}>
          <div className={listStyle.header}>
            <h4>Liste des Documents</h4>
            <AccessControl module="DONOR" functionality="UPDATE">
              <button
                className={btnStyles.addBtnProfile}
                onClick={() => {
                  handleShow();
                  setDocumentToEdit('');
                }}
              >
                Ajouter
              </button>
            </AccessControl>
          </div>

          <h5 style={{ marginBottom: '10px' }}>Filtre par tags :</h5>
          <TagsContainer>
            {tagList &&
              tagList.map(tag => (
                <Tag
                  key={tag.id}
                  color={tag.color || 'ffffff'}
                  selected={selectedTagId === tag.id}
                  onClick={() => {
                    setSelectedTagId(selectedTagId === tag.id ? null : tag.id);
                    setCurrentPage(1);
                  }}
                >
                  {tag.name}
                  {selectedTagId === tag.id && <CloseIcon>×</CloseIcon>}
                </Tag>
              ))}
          </TagsContainer>

          <DataTable
            rows={listDocuments}
            columns={columns}
            fileName={`Liste des documents du donateur ${donorCode}, ${new Date().toLocaleString()}`}
          />

          <div className="justify-content-center my-4">
            {documents && (
              <CustomPagination
                totalElements={documents.length}
                totalCount={Math.ceil(documents.length / pageSize)}
                pageSize={pageSize}
                currentPage={currentPage}
                onPageChange={setCurrentPage}
              />
            )}
          </div>
        </div>
      </div>

      <Modal2
        title={documentToEdit ? 'Modifier le document' : 'Ajouter un document'}
        size="lg"
        customWidth="modal-90w"
        show={show}
        handleClose={() => {
          setShow(false);
        }}
      >
        <DocumentForm
          handleClose={handleClose}
          document={documentToEdit}
          button={documentToEdit ? 'Modifier' : 'Ajouter'}
          target={target}
          id={params.id}
        ></DocumentForm>
      </Modal2>

      <Modal2
        centered
        className="mt-5"
        title="Confirmation de suppression"
        show={showDeleteModal}
        handleClose={handleCloseForDeleteModal}
      >
        <p className="mt-1 mb-5">
          Êtes-vous sûr de vouloir supprimer ce document?
        </p>
        <div className="d-flex justify-content-end px-3 my-1">
          <button
            type="button"
            className={`mx-2 btn-style btn-style outlined`}
            onClick={handleCloseForDeleteModal}
          >
            Annuler
          </button>
          <button
            type="submit"
            className={`mx-2 btn-style primary`}
            onClick={() => {
              dispatch(deleteDocument(documentToDelete, target));
              setDocumentToDelete('');
              handleCloseForDeleteModal();
            }}
          >
            Supprimer
          </button>
        </div>
      </Modal2>
    </div>
  );
}
