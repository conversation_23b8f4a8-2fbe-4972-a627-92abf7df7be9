import styled from 'styled-components';

const Wrapper = styled.div`
  .filter-container {
    display: flex;
    flex-direction: column;
    width: 100%;
  }

  .tabs {
    display: flex;
    width: calc(100% + 30px);
  }

  .tab {
    font-family: lexend, sans-serif;
    font-weight: 700;
    font-size: 12px;
    width: calc(100% / 3);
    border: none;
    background: #ffffff;
    color: #435568;
    border-radius: 20px 20px 0 0;
    cursor: pointer;
    transition: background 0.3s ease;
    box-shadow: 3px 0 13px 0 #00565c29;
    margin: 0;
    &:first-child {
      z-index: 4;
    }
    &:nth-child(2) {
      position: relative;
      left: -15px;
      z-index: 3;
    }
    &:nth-child(3) {
      position: relative;
      left: -30px;
      z-index: 2;
    }
  }

  .subFilter-container {
    border-top-left-radius: 0;
    border-top-right-radius: 0;
  }

  .tab.active {
    background: #4f89d7;
    color: #fff;
  }

`;
export default Wrapper;
