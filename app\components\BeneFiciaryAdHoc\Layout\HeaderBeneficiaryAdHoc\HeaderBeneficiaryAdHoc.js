/* eslint-disable jsx-a11y/alt-text */
import React from 'react';
import { NavLink, useParams } from 'react-router-dom';
import profile from '../../../../Css/profileHeader.css';

const HeaderBeneficiaryAdHoc = ({ isPerson }) => {
  const params = useParams();
  const { id } = params;
  return (
    <div className={profile.navBar}>
      {isPerson ? (
        <p>
          <NavLink
            to={`/beneficiaries/ad-hoc/fiche/${id}/info`}
            activeClassName={profile.selected}
          >
            DETAILS BÉNÉFICIAIRE AD-HOC
          </NavLink>
        </p>
      ) : (
        <>
          <p>
            <NavLink
              to={`/beneficiaries/ad-hoc/fiche/${id}/info`}
              activeClassName={profile.selected}
            >
              DETAILS GROUPE AD-HOC
            </NavLink>
          </p>
          <p>
            <NavLink
              to={`/beneficiaries/ad-hoc/fiche/${id}/affectations`}
              activeClassName={profile.selected}
            >
              AFFECTATIONS PERSONNELLES
            </NavLink>
          </p>
        </>
      )}
    </div>
  );
};

export default HeaderBeneficiaryAdHoc;
