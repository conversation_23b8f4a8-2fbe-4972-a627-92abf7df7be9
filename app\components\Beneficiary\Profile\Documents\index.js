import React, { useEffect, useState, useRef } from 'react';
import moment from 'moment';
import styles from 'Css/profileList.css';
import stylesList from 'Css/profileList.css';
import navigationStyle from 'Css/sectionNavigation.css';
import btnStyles from 'Css/button.css';
import reducer from 'containers/Common/SubComponents/DocumentForm/reducer';
import { deleteDocumentSaga } from 'containers/Common/SubComponents/DocumentForm/saga';
import ChevronRightIcon from '@mui/icons-material/ChevronRight';
import ChevronLeftIcon from '@mui/icons-material/ChevronLeft';
import {
  DOWNLOAD_ICON,
  EDIT_ICON,
  VIEW_ICON,
} from 'components/Common/ListIcons/ListIcons';
import { createStructuredSelector } from 'reselect';
import { useDispatch, useSelector } from 'react-redux';
import { useInjectReducer, useInjectSaga } from 'redux-injectors';
import { useLocation, useParams, useHistory } from 'react-router-dom';
import DocumentForm from 'containers/Common/SubComponents/DocumentForm';
import {
  makeSelecDocument,
  makeSelecDocumentDeleteSuccess,
  makeSelecDocumentDownloadSuccess,
  makeSelecDocumentsBenif,
  makeSelectDocumentAddDonorSuccess,
  makeSelectError,
  makeSelectSuccess,
} from 'containers/Common/SubComponents/DocumentForm/selectors';
import {
  deleteDocument,
  downloadDocument,
  fetchDataRequest,
  fetchDataBenifRequest,
  resetFetchDataBenif,
  resetDocument,
  viewDocument,
} from 'containers/Common/SubComponents/DocumentForm/actions';
import Modal2 from 'components/Common/Modal2';
import { removeDocumentBeneficiary } from 'containers/Beneficiary/BeneficiaryProfile/actions';
import { Alert } from 'react-bootstrap';
import { isAuthorized } from 'utils/AccessControl';
import DataTable from '../../../Common/DataTable';
import CustomPagination from '../../../Common/CustomPagination';
import tagStyles from '../../../../Css/tag.css';
import tagReducer from 'containers/tag/reducer';
import tagSaga from 'containers/tag/saga';
import { makeSelectTagList } from 'containers/tag/selectors';
import { getTagsByType } from 'containers/tag/actions';
import styled from 'styled-components';

// Helper function to determine if a color is dark
const isDarkColor = (color) => {
  // Convert hex to RGB
  const hex = color.replace('#', '');
  const r = parseInt(hex.substr(0, 2), 16);
  const g = parseInt(hex.substr(2, 2), 16);
  const b = parseInt(hex.substr(4, 2), 16);

  // Calculate brightness using the formula: (0.299*R + 0.587*G + 0.114*B)
  const brightness = (0.299 * r + 0.587 * g + 0.114 * b) / 255;

  // Return true if the color is dark
  return brightness < 0.5;
};

const TagsContainer = styled.div`
  display: flex;
  flex-wrap: nowrap;
  gap: 10px;
  margin: 20px 0;
  padding: 18px 40px;
  overflow-x: hidden;
  border: 2px solid rgb(33, 108, 184);
  border-radius: 20px;
  background-color: #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  position: relative;
`;

const TagsWrapper = styled.div`
  display: flex;
  gap: 10px;
  overflow-x: hidden;
  scroll-behavior: smooth;
  width: 100%;
  padding: 0 20px;
  margin-right: 20px;
  outline: none;
  -webkit-overflow-scrolling: touch;

  &:focus {
    outline: none;
  }

  &::-webkit-scrollbar {
    display: none;
  }

  -ms-overflow-style: none;
  scrollbar-width: none;
`;

const NavigationArrow = styled.button`
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: white;
  color: white;
  border: 1px solid rgb(33, 108, 184);
  color: rgb(33, 108, 184);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  z-index: 1;
  padding: 0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

  &:hover {
  background-color: rgb(33, 108, 184);
  color: white;
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    pointer-events: none;
  }
`;

const LeftArrow = styled(NavigationArrow)`
  left: 8px;
`;

const RightArrow = styled(NavigationArrow)`
  right: 8px;
`;

const Tag = styled.div`
  background-color: ${props => `#${props.color}`};
  color: ${props => {
    const hex = props.color || 'ffffff';
    const r = parseInt(hex.substr(0, 2), 16);
    const g = parseInt(hex.substr(2, 2), 16);
    const b = parseInt(hex.substr(4, 2), 16);
    const brightness = (r * 299 + g * 587 + b * 114) / 1000;
    return brightness > 128 ? '#000000' : '#ffffff';
  }};
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
  flex-shrink: 0;
  border: 1px solid ${props => props.selected ? '#000000' : '#e0e0e0'};
  display: flex;
  align-items: center;
  gap: 8px;
  opacity: ${props => props.selected ? 1 : props.hasSelected ? 0.5 : 1};

  &:hover {
    transform: scale(1.05);
    border-color: #bdbdbd;
  }
`;

const ResetTag = styled(Tag)`
  background-color: white;
  color: rgb(33, 108, 184);
  border: 1px solid rgb(33, 108, 184);
  border-radius: 50%;
  width: 32px;
  height: 32px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  opacity: 1;
  margin-top: 5px;

  &:hover {
    background-color: rgb(33, 108, 184);
    color: white;
  }
`;

const CloseIcon = styled.span`
  font-size: 16px;
  font-weight: bold;
  margin-left: 4px;
`;

const key = 'document';
const keyTag = 'tagList';

const target = 'beneficiary';

const omdbSelector = createStructuredSelector({
  successDocumentsBenif: makeSelectSuccess,
  error: makeSelectError,
  document: makeSelecDocument,
  documentsBenif: makeSelecDocumentsBenif,
  successDelete: makeSelecDocumentDeleteSuccess,
  successDownload: makeSelecDocumentDownloadSuccess,
  documentDonorAddSuccess: makeSelectDocumentAddDonorSuccess,
});

const tagSelector = createStructuredSelector({
  tagList: makeSelectTagList,
});

const formatDate = date => moment(date).format('DD/MM/YYYY');

export default function Documents(props) {
  const beneficiary = props.data;
  const listDocuments = null;
  let successAlert = null;

  let beneficiaryCode;

  if (beneficiary) {
    beneficiaryCode = beneficiary.code;
  }

  const [hide, setHide] = useState(false);
  const location = useLocation();
  const history = useHistory();

  useEffect(() => {
    if (location && location.state && location.state.isOldBeneficiary) {
      setHide(location.state.isOldBeneficiary);
    } else {
      setHide(false);
    }
  }, [location]);

  const dispatch = useDispatch();
  useInjectReducer({ key, reducer });
  useInjectSaga({ key, saga: deleteDocumentSaga });
  useInjectReducer({ key: keyTag, reducer: tagReducer });
  useInjectSaga({ key: keyTag, saga: tagSaga });
  const params = useParams();

  const {
    successDocumentsBenif,
    error,
    documentsBenif,
    successDelete,
    documents,
    documentDonorAddSuccess,
    successDownload,
  } = useSelector(omdbSelector);
  const { tagList } = useSelector(tagSelector);
  const [typeDocumentName, setTypeDocumentName] = useState('');
  const [documentToEdit, setDocumentToEdit] = useState('');
  const [documentToDelete, setDocumentToDelete] = useState('');
  const [selectedTagIds, setSelectedTagIds] = useState([]);

  const [message, setMessage] = useState('');
  let errorMessage = null;
  const [show, setShow] = useState(false);
  const [showAlert, setShowAlert] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);

  // Define pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const pageSize = 5; // Define page size

  const handleShowForDeleteModal = () => setShowDeleteModal(true);
  const handleCloseForDeleteModal = () => {
    setShowDeleteModal(false);
    dispatch(resetFetchDataBenif());
    console.log('reseted documentsBenif', documentsBenif);
  };
  const handleClose = () => setShow(false);
  const [isCleaned, setIsCleaned] = useState(false);
  const handleShow = () => setShow(true);
  const [normalDocument, setNormalDocument] = useState(true);
  const [activeSection, setActiveSection] = useState('type');

  const [canScrollLeft, setCanScrollLeft] = useState(false);
  const [canScrollRight, setCanScrollRight] = useState(false);
  const tagsWrapperRef = useRef(null);

  useEffect(() => {
    if (location && location.state && location.state.openDocumentId) {
      const documentId = location.state.openDocumentId;
      const foundDocument = documentsBenif.find(doc => doc.id === documentId);

      if (foundDocument) {
        setDocumentToEdit(foundDocument);
        // Set normalDocument based on whether the document has a type
        setNormalDocument(location.state.isNormaleDoc);
        setShow(true);
      }
    }
  }, [location, documentsBenif]);

  useEffect(() => {
    const checkScroll = () => {
      if (tagsWrapperRef.current) {
        const { scrollLeft, scrollWidth, clientWidth } = tagsWrapperRef.current;
        const isAtStart = scrollLeft <= 0;
        const isAtEnd = scrollLeft >= scrollWidth - clientWidth;

        setCanScrollLeft(!isAtStart);
        setCanScrollRight(!isAtEnd);
      }
    };

    checkScroll();

    const handleScroll = () => {
      checkScroll();
    };

    if (tagsWrapperRef.current) {
      tagsWrapperRef.current.addEventListener('scroll', handleScroll);
    }

    window.addEventListener('resize', checkScroll);

    return () => {
      window.removeEventListener('resize', checkScroll);
      if (tagsWrapperRef.current) {
        tagsWrapperRef.current.removeEventListener('scroll', handleScroll);
      }
    };
  }, [tagList, activeSection]);

  const resetScrollState = () => {
    if (tagsWrapperRef.current) {
      tagsWrapperRef.current.scrollLeft = 0;
      setCanScrollLeft(false);
      setCanScrollRight(tagsWrapperRef.current.scrollWidth > tagsWrapperRef.current.clientWidth);
    }
  };

  useEffect(() => {
    resetScrollState();
  }, [activeSection]);

  useEffect(() => {
    if (documentsBenif && documentsBenif.length > 0) {
      console.log('documentsBenif', documentsBenif);
      console.table(documentsBenif);
    }
  }, [documentsBenif]);

  const scrollTags = (direction) => {
    if (tagsWrapperRef.current) {
      const scrollAmount = 200;
      const currentScroll = tagsWrapperRef.current.scrollLeft;
      const newScrollLeft = direction === 'left'
        ? Math.max(0, currentScroll - scrollAmount)
        : currentScroll + scrollAmount;

      tagsWrapperRef.current.scrollTo({
        left: newScrollLeft,
        behavior: 'smooth'
      });
    }
  };

  const scrollToEnd = () => {
    if (tagsWrapperRef.current) {
      tagsWrapperRef.current.scrollTo({
        left: tagsWrapperRef.current.scrollWidth,
        behavior: 'smooth'
      });
    }
  };

  useEffect(() => {
    if (successDocumentsBenif) {
    //test if documentsBenif contains a document with the id of the document to edit
    console.log('checking if document exist ...');
    if (documentsBenif.find(doc => doc.id === location.state.openDocumentId)) {
      console.log('document exist');
      const { state } = location;
      console.log('Location state:', location.state);
      if (location.state.openDocumentId) {
        console.log('Document ID from SuivieDocumentsRenouvler:', location.state.openDocumentId);
        // Fetch documents first
        // Find the document in the documents list
        let foundDocument = null;
        console.log('documentsBenif', Array.isArray(documentsBenif));
        console.log('documentsBenif', documentsBenif);
        if (documentsBenif && Array.isArray(documentsBenif)) {
          foundDocument = documentsBenif.find(doc => doc.id === location.state.openDocumentId);
        }
        if (foundDocument) {
          console.log('Found document content:', foundDocument);
          // Set the document to edit and open the modal
          setDocumentToEdit(foundDocument);
          // Set normalDocument based on the document type
          const isNormalDoc = location.state.isNormaleDoc;
          setNormalDocument(!isNormalDoc);
          // Set active section based on document type
          setActiveSection(isNormalDoc ? 'other' : 'type');
          setShow(true);
        } else {
          console.log('Document not found in the list');
        }
      }
    }
    }
  }, [documentsBenif, location, successDocumentsBenif]);



  useEffect(() => {
    dispatch(getTagsByType('document'));
  }, [dispatch]);

  useEffect(() => {
    dispatch(fetchDataBenifRequest(params.id, target));
  }, [dispatch, params.id]);

  useEffect(
    () =>
      function cleanup() {
        dispatch(resetDocument());
        setDocumentToEdit('');
      },
    [],
  );

  if (error) {
    errorMessage = 'Une erreur est survenue';
  }

  useEffect(() => {
    if (successDelete) {
      setShowAlert(true);
      setMessage('Document supprimé avec succès');
    }
  }, [successDelete]);

  useEffect(() => {
    if (documentDonorAddSuccess) {
      setShowAlert(true);
      if (documentToEdit) {
        if (normalDocument) {
          setMessage('Document modifié avec succès');
          setActiveSection('other');
        } else {
          setMessage('Piece jointe modifié avec succès');
          setActiveSection('type');
        }
      } else if (normalDocument) {
        setMessage(' Document ajouté avec succès');
        setActiveSection('other');
      } else {
        setMessage('Pièce jointe ajoutée avec succès');
        setActiveSection('type');
      }

      setDocumentToEdit('');
      dispatch(resetDocument());
    }
  }, [documentDonorAddSuccess]);

  // to close the success message after 4 seconds
  useEffect(() => {
    if (showAlert) {
      setTimeout(() => {
        setShowAlert(false);
      }, 4000);
    }
  }, [showAlert]);

  useEffect(() => {
    if (successDelete || documentDonorAddSuccess) {
      dispatch(fetchDataBenifRequest(params.id, target));
    }
  }, [successDelete, documentDonorAddSuccess]);

  if (showAlert) {
    successAlert = (
      <Alert className="alert-style" variant="success" onClose={() => setShowAlert(false)} dismissible>
        {message}
      </Alert>
    );
  }

  const getTag = statut => {
    switch (statut) {
      case 'Expiré':
        return tagStyles.tagExpired;
      case 'Non expiré':
        return tagStyles.tagValid;
      default:
        return tagStyles.tagValid;
    }
  };

  let documentsWithType = [];
  let documentsWithoutType = [];
  const listDocumentsWithType = [];
  const listDocumentsWithoutType = [];

  if (documentsBenif) {
    const documentsSorted = [...documentsBenif].sort((a, b) =>
      a.id < b.id ? 1 : -1,
    );

    // Filter documents based on selected tags
    const filteredDocuments = selectedTagIds.length > 0
      ? documentsSorted.filter(doc =>
        doc.tags && doc.tags.some(tag => selectedTagIds.includes(tag.id))
      )
      : documentsSorted;

    // Separate documents into two categories
    documentsWithType = filteredDocuments.filter(
      doc => doc.type !== null && doc.type.id !== null,
    );
    documentsWithoutType = filteredDocuments.filter(
      doc => doc.type && doc.type.id === null,
    );

    // Implement pagination logic for both categories
    const startIndexWithType = (currentPage - 1) * pageSize;
    const endIndexWithType = Math.min(
      startIndexWithType + pageSize,
      documentsWithType.length,
    );
    const paginatedDocumentsWithType = documentsWithType.slice(
      startIndexWithType,
      endIndexWithType,
    );

    const startIndexWithoutType = (currentPage - 1) * pageSize;
    const endIndexWithoutType = Math.min(
      startIndexWithoutType + pageSize,
      documentsWithoutType.length,
    );
    const paginatedDocumentsWithoutType = documentsWithoutType.slice(
      startIndexWithoutType,
      endIndexWithoutType,
    );

    // Map documents with type
    listDocumentsWithType.push(
      ...paginatedDocumentsWithType.map(document => ({
        id: document.id,
        label: document.label || '---',
        code: document.code || '---',
        type: document.type ? document.type.name : '---',
        documentDate: document.documentDate
          ? formatDate(document.documentDate)
          : '---',
        expiryDate: document.expiryDate
          ? formatDate(document.expiryDate)
          : '---',
        comment: document.comment || '---',
        statut:
          document.expiryDate && moment(document.expiryDate).isBefore(moment())
            ? 'Expiré'
            : 'Non expiré',
        document,
      })),
    );

    // Map documents without type (Autre Document)
    listDocumentsWithoutType.push(
      ...paginatedDocumentsWithoutType.map(document => ({
        id: document.id,
        label: document.label || '---',
        documentDate: document.documentDate
          ? formatDate(document.documentDate)
          : '---',
        expiryDate: document.expiryDate
          ? formatDate(document.expiryDate)
          : '---',
        comment: document.comment || '---',
        statut:
          document.expiryDate && moment(document.expiryDate).isBefore(moment())
            ? 'Expiré'
            : 'Non expiré',

        document,
      })),
    );
  }

  // Define columns for Pièce Jointe (with type)
  const columnsWithType = [
    {
      field: 'code',
      headerName: 'Code',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
    {
      field: 'label',
      headerName: 'Objet',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
    {
      field: 'type',
      headerName: 'Type Document',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
    {
      field: 'documentDate',
      headerName: "Date d'ajout",
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
    {
      field: 'expiryDate',
      headerName: "Date d'expiration",
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
    {
      field: 'statut',
      headerName: 'Statut',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
      cellClassName: params => getTag(params.value),
    },
    {
      field: 'comment',
      headerName: 'Commentaire',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
    {
      field: 'tags',
      headerName: 'Tags',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
      renderCell: (params) => {
        if (!params.row.document.tags || params.row.document.tags.length === 0) {
          return <span style={{ color: '#999', fontSize: '12px' }}>Aucun tag</span>;
        }
        return (
          <div style={{
            display: 'flex',
            flexWrap: 'wrap',
            gap: '4px',
            maxWidth: '100%'
          }}>
            {params.row.document.tags.map(tagObj => {
              const tag = tagList.find(t => t.id === tagObj.id);
              if (!tag) return null;
              return (
                <div
                  key={tag.id}
                  style={{
                    backgroundColor: `#${tag.color || 'cccccc'}`,
                    color: isDarkColor(tag.color || 'cccccc') ? 'white' : 'black',
                    padding: '2px 8px',
                    borderRadius: '10px',
                    fontSize: '11px',
                    fontWeight: '500',
                    whiteSpace: 'nowrap',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    maxWidth: '100px'
                  }}
                >
                  {tag.name}
                </div>
              );
            })}
          </div>
        );
      }
    },
  ];
  // Define columns for Autre Document (without type)
  const columnsWithoutType = [
    {
      field: 'label',
      headerName: 'Objet',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
    {
      field: 'documentDate',
      headerName: 'Date Document',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
    {
      field: 'expiryDate',
      headerName: 'Date Expiration',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
    {
      field: 'statut',
      headerName: 'Statut',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
      cellClassName: params => getTag(params.value),
    },
    {
      field: 'comment',
      headerName: 'Commentaire',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
    {
      field: 'tags',
      headerName: 'Tags',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
      renderCell: (params) => {
        if (!params.row.document.tags || params.row.document.tags.length === 0) {
          return <span style={{ color: '#999', fontSize: '12px' }}>Aucun tag</span>;
        }
        return (
          <div style={{
            display: 'flex',
            flexWrap: 'wrap',
            gap: '4px',
            maxWidth: '100%'
          }}>
            {params.row.document.tags.map(tagObj => {
              const tag = tagList.find(t => t.id === tagObj.id);
              if (!tag) return null;
              return (
                <div
                  key={tag.id}
                  style={{
                    backgroundColor: `#${tag.color || 'cccccc'}`,
                    color: isDarkColor(tag.color || 'cccccc') ? 'white' : 'black',
                    padding: '2px 8px',
                    borderRadius: '10px',
                    fontSize: '11px',
                    fontWeight: '500',
                    whiteSpace: 'nowrap',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    maxWidth: '100px'
                  }}
                >
                  {tag.name}
                </div>
              );
            })}
          </div>
        );
      }
    },
  ];

  const connectedUser = useSelector(state => state.app.connectedUser);
  const isUpdateAuthorized = isAuthorized(
    connectedUser,
    'GERER_BENEFICIAIRES_KAFALAT',
    'WRITE',
  );

  if (isUpdateAuthorized && !hide) {
    // Add actions column for Pièce Jointe only
    columnsWithType.push({
      field: 'actions',
      type: 'actions',
      headerName: 'Actions',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
      renderCell: rowData => (
        <div className="p-0">
          <input
            type="image"
            src={VIEW_ICON}
            className="p-0"
            width="20px"
            height="20px"
            title="visualiser"
            onClick={() => {
              dispatch(viewDocument(rowData.row.document, target));
            }}
          />
          <input
            type="image"
            src={DOWNLOAD_ICON}
            className="p-0 mx-2"
            width="20px"
            height="20px"
            title="télécharger"
            onClick={() =>
              dispatch(downloadDocument(rowData.row.document, target))
            }
          />
          <input
            type="image"
            onClick={() => {
              setDocumentToEdit(rowData.row.document);
              setNormalDocument(false);
              setShow(true);
            }}
            src={EDIT_ICON}
            className="p-0"
            width="20px"
            height="20px"
            title="modifier"
          />
        </div>
      ),
    });

    columnsWithoutType.push({
      field: 'actions',
      type: 'actions',
      headerName: 'Actions',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
      renderCell: rowData => (
        <div className="p-0">
          <input
            type="image"
            src={VIEW_ICON}
            className="p-0"
            width="20px"
            height="20px"
            title="visualiser"
            onClick={() => {
              dispatch(viewDocument(rowData.row.document, target));
            }}
          />
          <input
            type="image"
            src={DOWNLOAD_ICON}
            className="p-0 mx-2"
            width="20px"
            height="20px"
            title="télécharger"
            onClick={() =>
              dispatch(downloadDocument(rowData.row.document, target))
            }
          />
          <input
            type="image"
            className="p-0"
            onClick={() => {
              setDocumentToEdit(rowData.row.document);
              setNormalDocument(true);
              setShow(true);
            }}
            src={EDIT_ICON}
            width="20px"
            height="20px"
            title="modifier"
          />
        </div>
      ),
    });
  }

  return (
    <div>
      {successAlert}
      <div className={`pb-5 ${stylesList.backgroudStyle}`}>
        <Modal2
          title={
            documentToEdit ? 'Modifier le document' : 'Ajouter un document'
          }
          size="lg"
          customWidth="modal-90w"
          show={show}
          handleClose={() => {
            setShow(false);
            dispatch(resetFetchDataBenif());
          }}
        >
          <DocumentForm
            handleClose={handleClose}
            document={documentToEdit}
            button={documentToEdit ? 'Modifier' : 'Ajouter'}
            target={target}
            id={params.id}
            normalDocument={normalDocument}
          />
        </Modal2>

        <Modal2
          centered
          className="mt-5"
          title="Confirmation de suppression"
          show={showDeleteModal}
          handleClose={handleCloseForDeleteModal}
        >
          <p className="mt-1 mb-5 px-2">
            Êtes-vous sûr de vouloir supprimer ce document?
          </p>
          <div className="d-flex justify-content-end px-2 my-1 ">
            <button
              type="button"
              className={`${btnStyles.cancelBtn}`}
              onClick={() => {
                handleCloseForDeleteModal();
              }}
            >
              Annuler
            </button>
            <button
              type="submit"
              className={`ml-3 ${btnStyles.addBtn}`}
              onClick={() => {
                dispatch(deleteDocument(documentToDelete, target));
                setDocumentToEdit('');
                handleCloseForDeleteModal();
              }}
            >
              Supprimer
            </button>
          </div>
        </Modal2>

        <div>
          <div className={styles.global}>
            <div className={styles.header}>
              <h4></h4>
              {!hide && (
                <div className="dropdown">
                  <button
                    className={`btn dropdown-toggle mb-3 ${btnStyles.addBtnProfile}`}
                    style={{
                      backgroundColor: '#FFB290',
                      color: 'white',
                      fontWeight: '700',
                    }}
                    type="button"
                    id="dropdownMenuButton"
                    data-toggle="dropdown"
                    aria-haspopup="true"
                    aria-expanded="false"
                  >
                    Ajouter
                  </button>
                  <div
                    className="dropdown-menu"
                    style={{
                      borderRadius: '20px',
                      transform: 'translate(35px, 43px)',
                    }}
                    aria-labelledby="dropdownMenuButton"
                  >
                    <button
                      className="dropdown-item"
                      style={{ width: '86%' }}
                      onClick={() => {
                        setDocumentToEdit('');
                        setShow(true);
                        setNormalDocument(false);
                      }}
                    >
                      Pièce Jointe
                    </button>
                    <button
                      className="dropdown-item"
                      onClick={() => {
                        setDocumentToEdit('');
                        setShow(true);
                        setNormalDocument(true);
                      }}
                    >
                      Document
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>

          <div
            style={{
              marginBottom: '20px',
            }}
            className={navigationStyle.divBtns}
          >
            <button
              className={
                activeSection == 'type'
                  ? navigationStyle.activeBtn
                  : navigationStyle.disabledBtn
              }
              onClick={() => setActiveSection('type')}
            >
              Liste des Pièces Jointes
            </button>

            <button
              className={
                activeSection == 'other'
                  ? navigationStyle.activeBtn
                  : navigationStyle.disabledBtn
              }
              onClick={() => setActiveSection('other')}
            >
              Liste des Documents
            </button>
          </div>

          {activeSection === 'type' && (
            <div>
              <h5 style={{ marginBottom: '10px' }}>Filtre par tags :</h5>
              <TagsContainer>
                <LeftArrow
                  onClick={() => scrollTags('left')}
                  disabled={!canScrollLeft}
                >
                  <ChevronLeftIcon />
                </LeftArrow>
                <TagsWrapper ref={tagsWrapperRef}>
                  {selectedTagIds.length > 0 && (
                    <ResetTag
                      onClick={() => setSelectedTagIds([])}
                    >
                      x
                    </ResetTag>
                  )}
                  {tagList && tagList.map((tag, index) => (
                    <Tag
                      key={tag.id}
                      color={tag.color || 'ffffff'}
                      selected={selectedTagIds.includes(tag.id)}
                      hasSelected={selectedTagIds.length > 0}
                      onClick={() => {
                        setSelectedTagIds(prev => {
                          const newSelectedIds = prev.includes(tag.id)
                            ? prev.filter(id => id !== tag.id)
                            : [...prev, tag.id];

                          // If this is the last tag and it's being selected, scroll to end
                          if (index === tagList.length - 1 && !prev.includes(tag.id)) {
                            setTimeout(scrollToEnd, 100);
                          }

                          return newSelectedIds;
                        });
                        setCurrentPage(1);
                      }}
                    >
                      {tag.name}
                      {selectedTagIds.includes(tag.id) && <CloseIcon>×</CloseIcon>}
                    </Tag>
                  ))}
                </TagsWrapper>
                <RightArrow
                  onClick={() => scrollTags('right')}
                  disabled={!canScrollRight}
                >
                  <ChevronRightIcon />
                </RightArrow>
              </TagsContainer>
              <DataTable
                rows={listDocumentsWithType}
                columns={columnsWithType}
                fileName={`Pièce jointe du bénéficiaire ${beneficiaryCode}`}
              />
              <div className="row justify-content-center my-4">
                <CustomPagination
                  totalElements={documentsWithType.length}
                  currentPage={currentPage}
                  totalCount={Math.ceil(documentsWithType.length / pageSize)}
                  pageSize={pageSize}
                  onPageChange={setCurrentPage}
                />
              </div>
            </div>
          )}
          {activeSection === 'other' && (
            <div>
              <h5 style={{ marginBottom: '10px' }}>Filtre par tags :</h5>
              <TagsContainer>
                <LeftArrow
                  onClick={() => scrollTags('left')}
                  disabled={!canScrollLeft}
                >
                  <ChevronLeftIcon />
                </LeftArrow>
                <TagsWrapper ref={tagsWrapperRef}>
                  {selectedTagIds.length > 0 && (
                    <ResetTag
                      onClick={() => setSelectedTagIds([])}
                    >
                      X
                    </ResetTag>
                  )}
                  {tagList && tagList.map((tag, index) => (
                    <Tag
                      key={tag.id}
                      color={tag.color || 'ffffff'}
                      selected={selectedTagIds.includes(tag.id)}
                      hasSelected={selectedTagIds.length > 0}
                      onClick={() => {
                        setSelectedTagIds(prev => {
                          const newSelectedIds = prev.includes(tag.id)
                            ? prev.filter(id => id !== tag.id)
                            : [...prev, tag.id];

                          // If this is the last tag and it's being selected, scroll to end
                          if (index === tagList.length - 1 && !prev.includes(tag.id)) {
                            setTimeout(scrollToEnd, 100);
                          }

                          return newSelectedIds;
                        });
                        setCurrentPage(1);
                      }}
                    >
                      {tag.name}
                      {selectedTagIds.includes(tag.id) && <CloseIcon>×</CloseIcon>}
                    </Tag>
                  ))}
                </TagsWrapper>
                <RightArrow
                  onClick={() => scrollTags('right')}
                  disabled={!canScrollRight}
                >
                  <ChevronRightIcon />
                </RightArrow>
              </TagsContainer>
              <DataTable
                rows={listDocumentsWithoutType}
                columns={columnsWithoutType}
                fileName={`Autre document du bénéficiaire ${beneficiaryCode}`}
              />
              <div className="row justify-content-center my-4">
                <CustomPagination
                  totalElements={documentsWithoutType.length}
                  currentPage={currentPage}
                  totalCount={Math.ceil(documentsWithoutType.length / pageSize)}
                  pageSize={pageSize}
                  onPageChange={setCurrentPage}
                />
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
