import React from 'react';
import PropTypes from 'prop-types';
import { Box, Typography } from '@mui/material';
import { styled } from '@mui/material/styles';

const CardWrapper = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  padding: theme.spacing(2),
  backgroundColor: '#fff',
  borderRadius: '8px',
  boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
  gap: theme.spacing(3),
  minWidth: '250px',
  border: '1px solid #e0e0e0'
}));

const IconWrapper = styled(Box)(({ bgcolor }) => ({
  width: '50px',
  height: '50px',
  borderRadius: '50%',
  backgroundColor: bgcolor,
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  '& svg': {
    color: '#fff',
    fontSize: '24px'
  }
}));

const ContentWrapper = styled(Box)({
  display: 'flex',
  flexDirection: 'column',
  gap: '4px'
});

const ValueTypography = styled(Typography)({
  fontSize: '24px',
  fontWeight: 600,
  lineHeight: 1.2,
  color: '#333'
});

const TitleTypography = styled(Typography)({
  fontSize: '14px',
  color: '#666',
  fontWeight: 500
});

function ReclamationStatCard({ icon, value, title, iconBgColor }) {
  return (
    <CardWrapper>
      <IconWrapper bgcolor={iconBgColor}>
        {icon}
      </IconWrapper>
      <ContentWrapper>
        <ValueTypography>{value}</ValueTypography>
        <TitleTypography>{title}</TitleTypography>
      </ContentWrapper>
    </CardWrapper>
  );
}

ReclamationStatCard.propTypes = {
  icon: PropTypes.node.isRequired,
  value: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
  title: PropTypes.string.isRequired,
  iconBgColor: PropTypes.string.isRequired
};

export default ReclamationStatCard; 