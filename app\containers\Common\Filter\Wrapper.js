import styled from 'styled-components';
const Wrapper = styled.div`
  border-radius: 20px;
  padding: 15px;
  margin-bottom: 20px;
  width: 100%;
  background-color: #ffffff;
  z-index: 6;
  box-shadow: 0 4px 4px 0 #4f89d71f;
  .initial-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    .form-container {
      display: flex;
      gap: 20px;
      flex-wrap: wrap;
      flex: 1;
    }
  }
  .reset-icon {
    border: 1px solid #435568;
    &:hover {
      background-color: #435568;
      svg path {
        fill: #ffffff;
      }
    }
  }

  .input-style {
    padding: 5px 15px;
    color: #435568;
    border: 1px solid #4f89d7;
    border-radius: 35px;
    min-height: 41px;
    width: 100%;
  }

  .filter-btn {
    width: 121px;
    border-radius: 22px;
    background-color: #435568;
    margin-right: 5px;
  }

  .hidden-form {
    margin-top: 10px;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
  }

  .see-more {
    text-decoration: none;
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    margin-top: 6px;
    gap: 10px;
    i {
      color: #4f89d7;
    }
  }
`;
export default Wrapper;
