import React, { useEffect, useState } from 'react';
import moment from 'moment';
import styles from 'Css/profileList.css';
import stylesList from 'Css/profileList.css';
import { useDispatch } from 'react-redux';
import { useHistory, useLocation } from 'react-router-dom';
import { Alert, Col, OverlayTrigger, Row, Tooltip } from 'react-bootstrap';
import ResetIconSvg from 'images/icons/ResetIconSvg';
import CustomPagination from '../../../Common/CustomPagination';
import {
  reserveAllBudgetLinesRequest,
  reserveAllBudgetLinesReset,
  updateMontantReservedBudgetLineReset,
} from 'containers/AideComplementaire/FicheAideComplementaire/actions';
import BudgetLineTable from '../../../Common/BudgetLineTable';
import btnStyles from '../../../../Css/button.css';
import Modal2 from '../../../Common/Modal2';

const formatDate = date =>
  date
    ? moment(date).isValid()
      ? moment(date).format('DD/MM/YYYY')
      : '-'
    : '-';

export default function BudgetLines(props) {
  const history = useHistory();
  const successAlert = null;

  const dispatch = useDispatch();

  const [currentPage, setCurrentPage] = useState(1);
  const pageSize = 5;

  const [showSuccessMessage, setShowSuccessMessage] = useState(false);
  const [message, setMessage] = useState('');
  const location = useLocation();
  const [showReserveModal, setShowReserveModal] = useState(false);

  // Filter state variables
  const [searchFullName, setSearchFullName] = useState('');
  const [filterStatus, setFilterStatus] = useState('');
  const [filterTypeDonor, setFilterTypeDonor] = useState('');

  const aideComplementaire = props.data;
  const { budgetLines } = props;

  // Filtered and paginated list
   // Filtered and paginated list
   const [filteredList, setFilteredList] = useState([]);

   useEffect(() => {
     const updatedFilteredList = budgetLines.filter(
       line =>
         (!searchFullName ||
           line.fullNameDonor
             .toLowerCase()
             .includes(searchFullName.toLowerCase())) &&
         (!filterStatus || line.status === filterStatus) &&
         (!filterTypeDonor || line.typeDonor === filterTypeDonor),
     );
     setFilteredList(updatedFilteredList);
     console.log({ updatedFilteredList });
   }, [budgetLines, searchFullName, filterStatus, filterTypeDonor]);
 
  const sortedList = [...filteredList].sort((a, b) =>
    a.idDonor < b.idDonor ? 1 : -1,
  );

  const startIndex = (currentPage - 1) * pageSize;
  const endIndex = Math.min(startIndex + pageSize, sortedList.length);
  const paginatedList = sortedList.slice(startIndex, endIndex);

  useEffect(() => {
    if (location.state === 'updateMontantReservedBudgetLineSuccess') {
      setShowSuccessMessage(true);
      setMessage('Montant réservé modifié avec succès !');
      dispatch(updateMontantReservedBudgetLineReset());
      history.replace({ ...location, state: null });
    }
  }, [location.state]);

  useEffect(() => {
    if (location.state === 'reserveAllBudgetLineSuccess') {
      setShowSuccessMessage(true);
      setMessage('Opération effectuée avec succès !');
      dispatch(reserveAllBudgetLinesReset());
      history.replace({ ...location, state: null });
    }
  }, [location.state]);

  useEffect(() => {
    if (showSuccessMessage) {
      const timer = setTimeout(() => {
        handleCloseSuccessMessage();
      }, 4000);
      return () => clearTimeout(timer);
    }
  }, [showSuccessMessage]);

  const handleCloseSuccessMessage = () => {
    setShowSuccessMessage(false);
    history.replace({ ...location, state: null });
  };

  const resetFilters = () => {
    setSearchFullName('');
    setFilterStatus('');
    setFilterTypeDonor('');
    setCurrentPage(1);
  };

  useEffect(() => {
    if (searchFullName || filterStatus || filterTypeDonor) {
      setCurrentPage(1);
    }
  }, [searchFullName, filterStatus, filterTypeDonor]);

  // État pour vérifier si toutes les lignes sont réservées
  const [allReserved, setAllReserved] = useState(
    budgetLines.length > 0 &&
    budgetLines.every(line => line.status === 'RESERVED'),
  );

  console.log({ allReserved });

  useEffect(() => {
    if (budgetLines.length > 0) {
      setAllReserved(budgetLines.every(line => line.status === 'RESERVED'));
    } else {
      setAllReserved(false);
    }
  }, [budgetLines]);

  const handleGlobalReservation = () => {
    const shouldReserveAll = !allReserved;
    const donorAmounts = budgetLines
      .filter(line =>
        shouldReserveAll
          ? line.status === 'DISPONIBLE'
          : line.status === 'RESERVED',
      )
      .reduce((acc, line) => {
        if (acc[line.id]) {
          acc[line.id] = line.montantTotalDuDonateur + acc[line.id];
        }
        else {
          acc[line.id] = line.montantTotalDuDonateur;
        }

        return acc;
      }, {});

    const budgetLineIds = Object.keys(donorAmounts);
    console.log({ donorAmounts });

    if (budgetLineIds.length > 0) {
      dispatch(
        reserveAllBudgetLinesRequest(
          aideComplementaire.id,
          donorAmounts,
          shouldReserveAll ? 'ReserveAll' : 'UnReserveAll',
        ),
      );
    }

    setAllReserved(shouldReserveAll);
  };

  const handleCloseForDeleteModal = () => setShowReserveModal(false);

  return (
    <div>
      <Modal2
        centered
        className="mt-5"
        title={
          allReserved
            ? "Confirmation d'annulation"
            : 'Confirmation de réservation'
        }
        show={showReserveModal}
        handleClose={handleCloseForDeleteModal}
      >
        <p className="mt-1 mb-5">
          {allReserved
            ? 'Êtes-vous sûr de vouloir annuler toutes les réservations ?'
            : 'Êtes-vous sûr de vouloir réserver toutes les lignes disponibles ?'}
        </p>
        <div className="d-flex justify-content-end px-3 my-1">
          <button
            type="button"
            className={`mx-2 btn-style outlined`}
            onClick={handleCloseForDeleteModal}
          >
            Annuler
          </button>
          <button
            type="submit"
            className={`mx-2 btn-style primary`}
            onClick={() => {
              handleGlobalReservation();
              handleCloseForDeleteModal();
            }}
          >
            Confirmer
          </button>
        </div>
      </Modal2>
      {showSuccessMessage && message && (
        <Alert
          className="alert-style"
          variant="success"
          onClose={handleCloseSuccessMessage}
          dismissible
        >
          <p>{message}</p>
        </Alert>
      )}
      {successAlert}
      <div className={`pb-5 pt-4 ${stylesList.backgroudStyle}`}>
        <div>
          <div className={styles.global}>
            <div className={styles.header}>
              <h4>Les lignes de donations</h4>
            </div>

            <div className="filter-section mb-4 p-4 br-20 border bg-light shadow-sm">
              <div className="d-flex align-items-center gap-10">
                <input
                  type="text"
                  placeholder="Recherche par Nom du Donateur"
                  value={searchFullName}
                  onChange={e => setSearchFullName(e.target.value)}
                  className="form-control mb-2"
                  style={{
                    borderColor: '#ced4da',
                    fontSize: '0.875rem',
                    height: '2.5rem',
                  }}
                />
                <select
                  value={filterTypeDonor}
                  onChange={e => setFilterTypeDonor(e.target.value)}
                  className="form-control mb-2"
                  style={{
                    borderColor: '#ced4da',
                    fontSize: '0.875rem',
                    height: '2.5rem',
                  }}
                >
                  <option value="">Types Donateur</option>
                  <option value="Physique">Physique</option>
                  <option value="Moral">Moral</option>
                  <option value="Anonyme">Anonyme</option>
                </select>
                <select
                  value={filterStatus}
                  onChange={e => setFilterStatus(e.target.value)}
                  className="form-control mb-2"
                  style={{
                    borderColor: '#ced4da',
                    fontSize: '0.875rem',
                    height: '2.5rem',
                  }}
                > 
                  <option value="">Statut</option>
                    <option value="DISPONIBLE">Non Réservée</option>
                    <option value="RESERVED">Réservée</option>
                </select>
              </div>
              <div className="d-flex align-items-center justify-content-end gap-10 mt-2">
                <OverlayTrigger
                  placement="bottom"
                  overlay={
                    <Tooltip id="reset-tooltip">Réinitialiser</Tooltip>
                  }
                >
                  <button
                    className={`reset-icon ${btnStyles.iconButton}`}
                    onClick={resetFilters}
                  >
                    <ResetIconSvg />
                  </button>
                </OverlayTrigger>

                {aideComplementaire.statut !== 'executer' &&
                  aideComplementaire.statut !== 'cloturer' && (
                    <OverlayTrigger
                      placement="bottom"
                      overlay={
                        <Tooltip id="tooltip-reservation">
                          {allReserved
                            ? 'Annuler toutes les réservations'
                            : 'Réserver tout'}
                        </Tooltip>
                      }
                    >
                      <button
                        className={`btn br-20 btn-${
                          allReserved ? 'danger' : 'primary'
                        } btn-sm`}
                        onClick={() => {
                          setShowReserveModal(true);
                        }}
                        style={{
                          fontSize: '0.875rem',
                          height: '2.5rem',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                        }}
                      >
                        {allReserved ? 'Annuler toutes' : 'Réserver tout'}
                      </button>
                    </OverlayTrigger>
                  )}
              </div>
            </div>

            <BudgetLineTable
              budgetLines={paginatedList}
              aideComplementaire={aideComplementaire}
            />

            <div className="justify-content-center my-4">
              {filteredList && filteredList.length > 0 && (
                <CustomPagination
                  totalElements={filteredList.length}
                  totalCount={Math.ceil(filteredList.length / pageSize)}
                  pageSize={pageSize}
                  currentPage={currentPage}
                  onPageChange={setCurrentPage}
                />
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
