import React, { useEffect, useState } from 'react';
import stylesList from 'Css/profileList.css';
import navigationStyle from 'Css/sectionNavigation.css';
import { makeSelectSuccessExternalIncome } from 'containers/Family/FamilyProfile/selectors';
import { useDispatch, useSelector } from 'react-redux';
import { createStructuredSelector } from 'reselect';
import {
  Alert,
  Button,
  Form,
  InputGroup,
  ListGroup,
  Modal,
} from 'react-bootstrap';
import {
  processDonorsAndBeneficiariesRequest,
  processDonorsAndBeneficiariesReset,
} from '../../../../containers/AideComplementaire/FicheAideComplementaire/actions';
import { useHistory, useLocation } from 'react-router-dom';
import BeneficiariesWithDonors from '../Beneficiaires/BeneficiariesWithDonors';

const EXTERNAL = 'external';
const INTERNAL = 'internal';

const omdbSelector2 = createStructuredSelector({
  successExternalIncome: makeSelectSuccessExternalIncome,
});

export default function BeneficiariesWithDonorsList(props) {
  const { successExternalIncome } = useSelector(omdbSelector2);
  const dispatch = useDispatch();

  const [section, setSection] = useState(EXTERNAL);
  const [message, setMessage] = useState('');
  const [showAlert, setShowAlert] = useState(false);
  const [showModal, setShowModal] = useState(false);
  const [filterText, setFilterText] = useState('');
  const [selectedBeneficiaries, setSelectedBeneficiaries] = useState([]);
  const [showSuccessMessage, setShowSuccessMessage] = useState(false);

  const history = useHistory();
  const location = useLocation();

  useEffect(() => {
    if (location.state === 'proceesBeneficiariesSuccess') {
      setShowSuccessMessage(true);
      dispatch(processDonorsAndBeneficiariesReset());
      history.replace({ ...location, state: null });
    }
  }, [location.state]);

  useEffect(() => {
    if (showSuccessMessage) {
      const timer = setTimeout(() => {
        handleCloseSuccessMessage();
      }, 4000);
      return () => clearTimeout(timer);
    }
  }, [showSuccessMessage]);

  const handleCloseSuccessMessage = () => {
    setShowSuccessMessage(false);
    history.replace({ ...location, state: null });
  };

  let successAlert = null;
  const aideComplementaire = props.data;
  const beneficiariesForAideComplementaire =
    props.beneficiariesForAideComplementaire;
  let beneficiaries = null;

  /*useEffect(() => {
    if (successExternalIncome) {
      if (successExternalIncome == 'add') {
        setMessage('La source de revenu a été bien ajoutée !');
      } else if (successExternalIncome == 'update') {
        setMessage('La source de revenu a été bien modifiée !');
      } else if (successExternalIncome == 'delete') {
        setMessage('La source de revenu a été bien supprimée !');
      }
      setShowAlert(true);
      dispatch(resetSuccessValues());
    }
  }, [successExternalIncome]);

   */

  useEffect(() => {
    if (showAlert) {
      setTimeout(() => {
        setShowAlert(false);
      }, 4000);
    }
  }, [showAlert]);

  if (showAlert) {
    successAlert = (
      <Alert className="alert-style" variant="success" onClose={() => setShowAlert(false)} dismissible>
        {message}
      </Alert>
    );
  }

  if (aideComplementaire) {
    /* if (section == EXTERNAL) {
       beneficiaries = <BeneficiariesWithDonors data={aideComplementaire} />;
     } else {

     */
    beneficiaries = <BeneficiariesWithDonors data={aideComplementaire} />;
  }

  const filteredBeneficiaries = beneficiariesForAideComplementaire.filter(
    beneficiary => {
      const fullName = `${beneficiary.firstName.toLowerCase()} ${beneficiary.lastName.toLowerCase()}`;
      const filter = filterText.toLowerCase();

      return (
        beneficiary.firstName.toLowerCase().includes(filter) ||
        beneficiary.lastName.toLowerCase().includes(filter) ||
        fullName.includes(filter)
      );
    },
  );

  const handleCheckboxChange = beneficiaryId => {
    setSelectedBeneficiaries(prevSelected =>
      prevSelected.includes(beneficiaryId)
        ? prevSelected.filter(id => id !== beneficiaryId)
        : [...prevSelected, beneficiaryId],
    );
  };

  const handleSelectAll = () => {
    const allBeneficiaryIds = filteredBeneficiaries.map(b => b.id);
    if (allBeneficiaryIds.every(id => selectedBeneficiaries.includes(id))) {
      setSelectedBeneficiaries([]);
    } else {
      setSelectedBeneficiaries(allBeneficiaryIds);
    }
  };

  const handleCloseModal = () => {
    setSelectedBeneficiaries([]);
    setShowModal(false);
  };

  console.log({ selectedBeneficiaries });

  return (
    <div>
      {showSuccessMessage && (
        <Alert
          className="alert-style"
          variant="success"
          onClose={handleCloseSuccessMessage}
          dismissible
        >
          <p>Bénéficiaires ajoutée avec succès !</p>
        </Alert>
      )}
      {successAlert}
      <div className={`pb-5 pt-4 ${stylesList.backgroudStyle}`}>
        <div style={{ textAlign: 'right', marginBottom: '20px' }}>
          <Button
            variant="primary"
            className="align-right-btn"
            onClick={() => setShowModal(true)}
          >
            Ajouter des bénéficiaires
          </Button>
        </div>

        {/*<div className={navigationStyle.divBtns}>
          <button
            className={
              section == EXTERNAL
                ? navigationStyle.activeBtn
                : navigationStyle.disabledBtn
            }
            onClick={() => {
              setSection(EXTERNAL);
            }}
          >
            Bénéficiaires avec donateurs participants
          </button>
          <button
            className={
              section == INTERNAL
                ? navigationStyle.activeBtn
                : navigationStyle.disabledBtn
            }
            onClick={() => {
              setSection(INTERNAL);
            }}
          >
            Bénéficiaires sans donateurs participants
          </button>
        </div>*/}
        <div className={navigationStyle.divIncomes}>{beneficiaries}</div>
      </div>

      {/* Modal for selecting beneficiaries */}
      <Modal show={showModal} onHide={handleCloseModal} size="lg">
        <Modal.Header closeButton>
          <Modal.Title>Ajouter des bénéficiaires</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <InputGroup className="mb-3">
            <Button variant="outline-secondary" onClick={handleSelectAll}>
              {filteredBeneficiaries.length === selectedBeneficiaries.length
                ? 'Désélectionner tout'
                : 'Sélectionner tout'}
            </Button>
            <Form.Control
              type="text"
              placeholder="Filtrer par nom ou prénom"
              value={filterText}
              onChange={e => setFilterText(e.target.value)}
            />
          </InputGroup>
          <div style={{ maxHeight: '500px', overflowY: 'auto' }}>
            <ListGroup className="mt-3">
              {filteredBeneficiaries.map(beneficiary => (
                <ListGroup.Item key={beneficiary.id}>
                  <Form.Check
                    type="checkbox"
                    label={`${beneficiary.firstName} ${beneficiary.lastName}`}
                    onChange={() => handleCheckboxChange(beneficiary.id)}
                    checked={selectedBeneficiaries.includes(beneficiary.id)}
                  />
                </ListGroup.Item>
              ))}
            </ListGroup>
          </div>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={handleCloseModal}>
            Annuler
          </Button>
          <Button
            variant="primary"
            onClick={() => {
              const aideComplementaireId2 = aideComplementaire.id;
              dispatch(
                processDonorsAndBeneficiariesRequest(
                  aideComplementaireId2,
                  selectedBeneficiaries,
                ),
              );
              handleCloseModal();
              //setShowModal(false);
              // You can trigger an action here with the selected beneficiaries
            }}
          >
            Valider
          </Button>
        </Modal.Footer>
      </Modal>
    </div>
  );
}
