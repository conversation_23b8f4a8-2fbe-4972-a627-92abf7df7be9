import React, { useEffect, useRef, useState } from 'react';
import { Link, useHistory, useLocation } from 'react-router-dom';
import PropTypes from 'prop-types';
import moment from 'moment';
import stylesList from 'Css/profileList.css';
import AccessControl from 'utils/AccessControl';
import ReactToPrint from 'react-to-print';
import Alert from 'react-bootstrap/Alert';
import profile from '../../../Css/personalInfo.css';
import btnStyles from '../../../Css/button.css';
import PrintableContent from '../PrintableContent/PrintableContent';
import {PRINT_ICON, WHITE_UPLOAD_PICTURE} from "../../../containers/Common/RequiredElement/Icons";

const formatDate = date => moment(date).format('DD/MM/YYYY');

export default function PersonalInfoPersonne(props) {
  const payload = props.data;

  const location = useLocation();
  const history = useHistory();
  const [showSuccessMessage, setShowSuccessMessage] = useState(false);
  useEffect(() => {
    if (location.state === 'success') {
      setShowSuccessMessage(true);
    }
  }, [location.state]);

  useEffect(() => {
    if (showSuccessMessage) {
      const timer = setTimeout(() => {
        handleCloseSuccessMessage();
      }, 4000);
      return () => clearTimeout(timer);
    }
  }, [showSuccessMessage]);

  const handleCloseSuccessMessage = () => {
    setShowSuccessMessage(false);
    history.replace({ ...location, state: null });
  };

  let content = <p></p>;
  const emptyValue = <span> ----</span>;

  const componentRef = useRef();

  if (payload && payload.person) {
    content = (
      <div className={profile.content}>
        <div className={profile.section1}>
          <div className={`${profile.top} justify-content-between`}>
            <div className={`${profile.label1} ${profile.data1}`}>
              <p>
                <span style={{ fontWeight: 'bold' }}>Nom : </span>
                <span style={{ fontWeight: 'normal' }}>
                  {payload.person.lastName
                    ? payload.person.lastName
                    : emptyValue}
                </span>
              </p>
              <p>
                <span style={{ fontWeight: 'bold' }}>Prénom : </span>
                <span style={{ fontWeight: 'normal' }}>
                  {payload.person.firstName
                    ? payload.person.firstName
                    : emptyValue}
                </span>
              </p>
              <p>
                <span style={{ fontWeight: 'bold' }}>N° identité : </span>
                <span style={{ fontWeight: 'normal' }}>
                  {payload.person.identityCode
                    ? payload.person.identityCode
                    : emptyValue}
                </span>
              </p>
              <p>
                <span style={{ fontWeight: 'bold' }}>Inscrit le : </span>
                <span style={{ fontWeight: 'normal' }}>
                  {payload.createdAt
                    ? formatDate(payload.createdAt)
                    : emptyValue}
                </span>
              </p>
            </div>
            <div className={`${profile.label2} ${profile.data2}`}>
              <p>
                <span style={{ fontWeight: 'bold' }}> النسب : </span>
                <span style={{ fontWeight: 'normal' }}>
                  {payload.person.lastNameAr
                    ? payload.person.lastNameAr
                    : emptyValue}
                </span>
              </p>
              <p>
                <span style={{ fontWeight: 'bold' }}>الإسم : </span>
                <span style={{ fontWeight: 'normal' }}>
                  {payload.person.firstNameAr
                    ? payload.person.firstNameAr
                    : emptyValue}
                </span>
              </p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div>
      {showSuccessMessage && (
        <Alert
          className="alert-style"
          variant="success"
          onClose={handleCloseSuccessMessage}
          dismissible
        >
          <p>personne ajouté avec succès</p>
        </Alert>
      )}
      <div className={stylesList.backgroudStyle}>
        <div className={profile.personalInfo}>
          <div className={profile.header}>
            <h4>Informations personnelles</h4>
            <div className="d-flex align-items-center gap-10">
              <ReactToPrint
                trigger={() => (
                  <button
                    className="btn-style primary"
                  >
                    <img src={PRINT_ICON} width="16px" height="16px" />
                    Imprimer
                  </button>
                )}
                content={() => componentRef.current}
              />
              <div style={{ display: 'none' }}>
                <PrintableContent
                  ref={componentRef}
                  data={payload.person}
                  isPerson={true}
                />
              </div>
              <AccessControl module="DONOR" functionality="UPDATE">
                <Link
                  to={{
                    pathname: `/beneficiaries/edit/ad-hoc/personne/${payload.id}`,
                    state: {
                      redirectTo: 'consultation',
                      id: props.personId ? props.personId : '',
                    },
                  }}
                >
                  <button className="btn-style secondary">
                    <img
                        src={WHITE_UPLOAD_PICTURE}
                        width="16px"
                        height="16spx"
                    />
                    Modifier
                  </button>
                </Link>
              </AccessControl>
            </div>
          </div>
          {content}
        </div>
      </div>
    </div>
  );
}

PersonalInfoPersonne.propTypes = {
  data: PropTypes.object.isRequired,
};
