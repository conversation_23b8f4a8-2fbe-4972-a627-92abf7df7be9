import React, { useEffect, useRef, useState } from 'react';
import { Field, Form, Formik } from 'formik';
import { Switch } from 'formik-material-ui';
import { createStructuredSelector } from 'reselect';
import { useInjectSaga } from 'redux-injectors';
import { useDispatch, useSelector } from 'react-redux';
import styles from 'Css/form.css';
import {
  PROFILE_PICTURE,
  UPLOAD_PICTURE,
} from 'containers/Common/RequiredElement/Icons';
import convertBase64 from 'containers/Common/Scripts/ConvertFileToBase64';
import { CustomTextInput } from 'containers/Common/CustomInputs/CustomTextInput';
import FamilyRelationships from 'containers/Common/SubComponents/FamilyRelationships';
import { CustomTextInputAr } from 'containers/Common/CustomInputs/CustomTextInputAr';
import TypeIdentities from 'containers/Common/SubComponents/TypeIdentities';
import CountryRegionCity from 'containers/Common/SubComponents/CountryRegionCity';
import Professions from 'containers/Common/SubComponents/Professions';
import SchoolLevels from 'containers/Common/SubComponents/SchoolLevels';
import btnStyles from 'Css/button.css';
import { initialValues } from './FormikAttributes/InitialValues';
import getEditValues from './FormikAttributes/EditValues';
import { validationSchema } from './FormikAttributes/ValidationSchema';
import {
  makeSelectError,
  makeSelectLoading,
  makeSelectMember,
  makeSelectSuccess,
  makeSelectTutorError,
} from './selectors';
import { addMemberSaga } from './saga';
import { addMember, resetMember } from './actions';
import { Alert, Modal } from 'react-bootstrap';
import { useHistory } from 'react-router';

import { CustomSelect } from 'containers/Common/CustomInputs/CustomSelect';
import { makeSelectSchoolLevels } from 'containers/Common/SubComponents/SchoolLevels/selectors';
import DeathReason from 'containers/Common/SubComponents/DeathReason';
import { CustomTextArea } from 'containers/Common/CustomInputs/CustomTextArea';
import Cropper from 'react-cropper';
import { Button } from '@mui/material';

const key = 'member2';

const omdbSelector = createStructuredSelector({
  success: makeSelectSuccess,
  error: makeSelectError,
  loading: makeSelectLoading,
  tutorError: makeSelectTutorError,
  memberAdded: makeSelectMember,
});

const omdbSelector2 = createStructuredSelector({
  schoolLevels: makeSelectSchoolLevels,
});

let formikRef = null;

export default function MemberAdd(props) {
  formikRef = useRef();

  useInjectSaga({ key, saga: addMemberSaga });

  const dispatch = useDispatch();

  const { success, error, loading, memberAdded } = useSelector(omdbSelector);
  const [showtNewTutorComponent, setShowNewTutorComponent] = useState(false);
  const [showActuelTutorComponent, setShowActuelTutorComponent] = useState(
    false,
  );
  const [messageDesactivation, setMessageDesactivation] = useState('');
  const { schoolLevels } = useSelector(omdbSelector2);
  const [schoolLevelTypes, setSchoolLevelTypes] = useState([]);

  const [selectedTutor, setSelectedTutor] = useState(null);
  const [showWarning, setShowWarning] = useState(false);

  const [showCropper, setShowCropper] = useState(false);
  const cropperRef = useRef(null);
  const [uploadedPicture, setUploadedPicture] = useState(PROFILE_PICTURE);

  const membere = props.member;
  const familyMembers = props.familyMembers;

  // we should get just the memeber that are not tutor and not deceased and not the actual member that we are editing
  const familyMembersFiltered = familyMembers.filter(
    member =>
    member && // Ensure member exists
      member.tutor === false && // Check if member is not a tutor
      // his birthdate should be more than 18 years
      new Date().getFullYear() -
        new Date(member.person.birthDate).getFullYear() >
        18 &&
      member.person &&
      member.person.deceased === false && // Ensure person exists and is not deceased
      membere &&
      member.id !== membere.id, // Ensure membere exists and member.id is not equal to membere.id
  );

  const { familyId } = props;
  const { handleClose } = props;
  const { setMemberToEdit } = props;

  const [showTutorAlert, setShowTutorAlert] = useState(false);

  const handleFileChange = event => {
    const file = event.target.files[0];
    if (file) {
      const base64 = convertBase64(file);
      base64
        .then(data => {
          setUploadedPicture(data);
          setShowCropper(true);
          setFormPicture(data);
        })
        .catch(error => console.error(error));
      setPictureField(event.target.files[0]);
    }
  };

  const handleSaveCroppedImage = () => {
    if (cropperRef.current) {
      const croppedCanvas = cropperRef.current.cropper.getCroppedCanvas();
      if (croppedCanvas) {
        const croppedImage = croppedCanvas.toDataURL();
        setShowCropper(false);

        const croppedFile = base64ToFile(croppedImage, 'cropped_image.png');
        const base64 = convertBase64(croppedFile);
        base64
          .then(data => {
            setUploadedPicture(data);
            setFormPicture(data);
          })
          .catch(error => console.error(error));
        //props.setPictureField(croppedFile);
        setPictureField(croppedFile);
      } else {
        console.error("Le cropper n'a pas généré de canevas.");
      }
    } else {
      console.error("Le cropper n'est pas correctement initialisé.");
    }
  };

  const base64ToFile = (base64Data, filename = 'cropped_image.png') => {
    const arr = base64Data.split(',');
    const mime = arr[0].match(/:(.*?);/)[1];
    const byteString = atob(arr[1]);

    const byteArray = new Uint8Array(byteString.length);
    for (let i = 0; i < byteString.length; i++) {
      byteArray[i] = byteString.charCodeAt(i);
    }

    return new File([byteArray], filename, { type: mime });
  };

  // do a rest of the form  the id the memeber is null

  const extractUniqueTypes = data => {
    // Use a Set to get unique values of the 'type' field
    const types = [...new Set(data.map(item => item.type))];

    // Map the types to the format { value: ..., label: ... }
    return types.map(type => ({
      value: type,
      label: type.charAt(0).toUpperCase() + type.slice(1), // Capitalize the first letter
    }));
  };

  useEffect(() => {
    if (schoolLevels) {
      setSchoolLevelTypes(extractUniqueTypes(schoolLevels));
    }
  }, [schoolLevels]);

  useEffect(() => {
    if (membere && formikRef.current) {
      const stringified = JSON.stringify(membere);
      const memberData = JSON.parse(stringified.replaceAll('null', '""'));

      const editValues = getEditValues(memberData, initialValues, {
        regions: formikRef.current.values.regions,
        cities: formikRef.current.values.cities,
      });
      formikRef.current.setValues(editValues);
    } else if (formikRef.current) {
      formikRef.current.resetForm();
    }
  }, [membere]);

  // meesaage wwich should inform the user tha he will be directed to the add beneficiary page after finishing the adding of the member
  const [messageRedirection, setMessageRedirection] = useState('');
  useEffect(() => {
    if (props.fromBeneficiaryAdd === true) {
      setMessageRedirection(
        "Après avoir ajouté le membre, vous serez redirigé vers la page de création d'un pré-candidat.",
      );
    } else {
      setMessageRedirection('');
    }
  }, [props.fromBeneficiaryAdd]);

  const history = useHistory();
  useEffect(() => {
    if (success) {
      if (props.fromBeneficiaryAdd === true && memberAdded) {
        history.push({
          pathname: '/beneficiaries/add',
          // we shoudl pass also the family id and the member id
          state: { familyId, familyMemberId: memberAdded.id },
        });
      } else {
        formikRef.current.resetForm();
        handleClose();
        dispatch(resetMember());
      }
    }
  }, [success, props.fromBeneficiaryAdd]);

  useEffect(() => {
    if (props.show === false) {
      formikRef.current.resetForm();
      if (error && !error.response.data.title === 'FunctionalException') {
        dispatch(resetMember());
      }
    }
  }, [props.show]);

  useEffect(() => {
    if (error && error.response.data.title === 'FunctionalException') {
      setShowTutorAlert(true);
    }
  }, [error]);

  const name =
    membere &&
    (membere.familyRelationship.name === 'Père') |
      (membere.familyRelationship.name === 'Mère')
      ? membere.familyRelationship.name
      : null;

  const setFormPicture = data => {
    formikRef.current.setFieldValue('person.uploadedPicture', data);
  };

  const setPictureField = file => {
    formikRef.current.setFieldValue('person.picture', file);
  };

  const saveRegions = data => {
    formikRef.current.setFieldValue('regions', data);
  };

  const saveCities = data => {
    formikRef.current.setFieldValue('cities', data);
  };

  const resetCity = field => {
    formikRef.current.setFieldValue(field, 0);
  };

  useEffect(() => {
    if (
      formikRef.current &&
      formikRef.current.values &&
      formikRef.current.values.tutor === true
    ) {
      formikRef.current.setFieldValue('person.istutor', true);
    } else if (
      formikRef.current &&
      formikRef.current.values &&
      formikRef.current.values.tutor === false
    ) {
      formikRef.current.setFieldValue('person.istutor', false);
    }
  }, [formikRef.current && formikRef.current.values]);

  return (
    <div>
      {showTutorAlert && (
        <Alert
          className="alert-style"
          variant="danger"
          onClose={() => setShowTutorAlert(false)}
          dismissible
        >
          Le tuteur doit être majeur
        </Alert>
      )}
      <Formik
        initialValues={initialValues}
        onSubmit={(values, { setSubmitting }) => {
          let data = {};
          // the birthdate should be more than 18 years
          if (
            formikRef.current.values.tutor === true &&
            formikRef.current.values.person.birthDate
          ) {
            if (
              new Date().getFullYear() -
                new Date(
                  formikRef.current.values.person.birthDate,
                ).getFullYear() <
              18
            ) {
              setShowTutorAlert(true);
              return;
            }
          }
          data = {
            id: membere == null ? null : membere.id,
            personId: membere == null ? null : membere.person.id,
            code: membere == null ? null : membere.code,
            familyId,
            tutor: values.tutor,
            generalComment: values.generalComment,
            newTutorId: values.newTutorId,
            newTutorStartDate: values.newTutorStartDate,
            newTutorEndDate: values.newTutorEndDate,
            hasNewTutor: values.hasNewTutor,
            tutorStartDate: values.tutorStartDate,
            tutorEndDate: values.tutorEndDate,
            familyRelationshipId: values.familyRelationship.id,
            firstName: values.person.firstName,
            lastName: values.person.lastName,
            firstNameAr: values.person.firstNameAr,
            lastNameAr: values.person.lastNameAr,
            sex: values.person.sex,
            email: values.person.email,
            phoneNumber: values.person.phoneNumber,
            address: values.person.address,
            addressAr: values.person.addressAr,
            birthDate: values.person.birthDate,
            educated: values.person.educated,
            deceased: values.person.deceased,
            deathDate: values.person.deathDate,
            deathReason: values.person.deathReason,
            identityCode: values.person.identityCode,
            schoolLevelType: values.person.schoolLevelType,
            schoolName: values.person.schoolName,
            deathReasonId: !values.person.deathReasonSelected.id
              ? null
              : values.person.deathReasonSelected.id,
            schoolLevelId: !values.person.schoolLevel.id
              ? null
              : values.person.schoolLevel.id,
            cityId: values.person.city.id,
            typeIdentityId: !values.person.typeIdentity.id
              ? null
              : values.person.typeIdentity.id,
            professionId: !values.person.profession.id
              ? null
              : values.person.profession.id,

            picture: values.person.picture,
          };
          dispatch(addMember(data));
          setSubmitting(false);
        }}
        innerRef={formikRef}
        validationSchema={validationSchema}
      >
        {props => {
          let tutor = null;
          let person = null;
          let deceased = null;
          let educated = null;
          let picture = null;
          useEffect(() => {
            // Ensure formikRef.current and its values are defined before accessing them
            if (formikRef.current && formikRef.current.values) {
              const {
                tutor,
                tutorEndDate,
                tutorStartDate,
                person,
              } = formikRef.current.values;
              if (
                membere &&
                membere.tutor === true && // Only trigger this logic if props.values.tutor is true
                (tutor === false || // If the tutor is marked as false
                (tutorEndDate &&
                  new Date(tutorEndDate) < new Date() &&
                  new Date(tutorEndDate) > new Date(tutorStartDate)) || // If the tutorEndDate is in the past
                  (person && person.deceased === true)) // If the person exists and is deceased
              ) {
                setShowNewTutorComponent(true);
              } else {
                setShowNewTutorComponent(false); // Reset the state if conditions are not met
              }

              // Handle showing/hiding the actual tutor component based on person.deceased
              if (person && person.deceased === true) {
                setShowActuelTutorComponent(false);
                setMessageDesactivation(
                  "Ne peut pas être tuteur puisqu'il est décédé",
                );
                // also in case of adding a new menber it will 100% a new tutor so hide the actual tutor component
              } else if (
                (membere && membere.tutor === false) ||
                membere == null
              ) {
                setShowActuelTutorComponent(false);
                setMessageDesactivation('Un tuteur actif existe déjà');
              } else {
                setShowActuelTutorComponent(true);
              }
            }
          }, [
            formikRef.current && formikRef.current.values,
            props.values.tutor,
            formikRef.current && formikRef.current.values.tutor,
            formikRef.current && formikRef.current.values.tutorEndDate,
            formikRef.current &&
              formikRef.current.values.person &&
              formikRef.current.values.person.deceased,
          ]);

          useEffect(() => {
            // Ensure formikRef.current and its values are defined
            if (formikRef.current && formikRef.current.values) {
              const {
                newTutorStartDate,
                tutorEndDate,
              } = formikRef.current.values;

              // if it the actual fomik if dead is treu so the dtate of the deat should his end date
              if (
                formikRef.current.values.person.deceased === true &&
                formikRef.current.values.tutor === true &&
                formikRef.current.values.deathDate
              ) {
                formikRef.current.setFieldValue(
                  'tutorEndDate',
                  formikRef.current.values.person.deathDate,
                );
              } else if (
                (newTutorStartDate && !tutorEndDate) ||
                (newTutorStartDate &&
                  new Date(newTutorStartDate) < new Date(tutorEndDate))
              ) {
                formikRef.current.setFieldValue(
                  'tutorEndDate',
                  newTutorStartDate,
                );
              }
            }
          }, [
            formikRef.current &&
              formikRef.current.values &&
              formikRef.current.values.newTutorStartDate,
          ]);

          useEffect(() => {
            if (!showActuelTutorComponent) {
              formikRef.current.setFieldValue('tutor', false);
            }
          }, [showActuelTutorComponent]);

          useEffect(() => {
            if (formikRef.current && formikRef.current.values) {
              if (
                formikRef.current.values.tutor === true &&
                formikRef.current.values.person.deceased === false &&
                formikRef.current.values.tutorEndDate &&
                new Date(formikRef.current.values.tutorEndDate) > new Date()
              ) {
                setShowNewTutorComponent(false);
              }
            }
          }, [formikRef.current && formikRef.current.values]);

          useEffect(() => {
            if (
              membere &&
              membere.tutor === true &&
              formikRef.current &&
              formikRef.current.values &&
              formikRef.current.values.person.deceased === false
            ) {
              setShowNewTutorComponent(false);
              formikRef.current.setFieldValue('tutor', true);
            }
          }, [
            formikRef.current &&
              formikRef.current.values &&
              formikRef.current.values.person.deceased,
          ]);

          // Render the new tutor component if the condition is met

          if (props.values) {
            tutor = props.values.tutor;
            person = props.values.person;
            deceased = props.values.person.deceased;
            educated = props.values.person.educated;
            if (showtNewTutorComponent) {
              // we should maje the attribute hasNewTutor true
              props.values.hasNewTutor = true;
            } else {
              props.values.hasNewTutor = false;
            }
          }
          if (person) {
            picture = props.values.person.uploadedPicture;
          }

          return (
            <div className="container">
              <div className={styles.backgroundForm}>
                <Form>
                  <div>
                    <div className="form-row mb-0">
                      <div className="form-group col-md-6">
                        <div className="d-flex justify-content-center align-content-center">
                          <img
                            src={picture}
                            style={{ border: '3px solid #fafbfd' }}
                            className="rounded-circle row"
                            width="130px"
                            height="130px"
                          />
                          <label htmlFor="upload">
                            <img
                              src={UPLOAD_PICTURE}
                              width="20px"
                              height="20px"
                            />
                          </label>
                          <input
                            type="file"
                            id="upload"
                            hidden
                            className="row"
                            accept="image/*"
                            name="person.picture"
                            onChange={handleFileChange}
                          />
                        </div>
                        {showCropper && (
                          <Modal
                            show={showCropper}
                            onHide={() => setShowCropper(false)}
                            centered
                            backdrop="static"
                          >
                            <Modal.Header closeButton>
                              <Modal.Title>Recadrer l'image</Modal.Title>
                            </Modal.Header>
                            <Modal.Body>
                              <div
                                style={{
                                  position: 'relative',
                                  width: '100%',
                                  height: '400px',
                                  margin: '0 auto',
                                }}
                              >
                                <Cropper
                                  src={picture}
                                  ref={cropperRef}
                                  style={{
                                    width: '100%',
                                    height: '100%',
                                    borderRadius: '20px',
                                  }}
                                  aspectRatio={1}
                                  guides={false}
                                  cropBoxMovable={true}
                                  cropBoxResizable={true}
                                  viewMode={1}
                                  background={false}
                                  autoCropArea={1}
                                  ready={() => {
                                    const cropBox = document.querySelector(
                                      '.cropper-crop-box',
                                    );
                                    const viewBox = document.querySelector(
                                      '.cropper-view-box',
                                    );
                                    if (cropBox) {
                                      cropBox.style.borderRadius = '50%';
                                    }
                                    if (viewBox) {
                                      viewBox.style.borderRadius = '50%';
                                      viewBox.style.overflow = 'hidden';
                                    }
                                  }}
                                />
                              </div>
                            </Modal.Body>
                            <Modal.Footer>
                              <Button
                                variant="secondary"
                                onClick={() => setShowCropper(false)}
                              >
                                Annuler
                              </Button>
                              <Button
                                variant="primary"
                                onClick={handleSaveCroppedImage}
                              >
                                Sauvegarder
                              </Button>
                            </Modal.Footer>
                          </Modal>
                        )}
                      </div>

                      <div
                        className={`form-group col-md-6 ${
                          !tutor ? 'mt-4' : null
                        }`}
                      >
                        <div className="form-row">
                          <div className="form-group mb-2 pl-md-3">
                            {!showActuelTutorComponent && (
                              <p className="text-muted small mt-2 mb-2">
                                {messageDesactivation}
                              </p>
                            )}
                            <label
                              className={`mr-2 pt-1 ${
                                tutor ? styles.underline : ''
                              }`}
                            >
                              Tuteur de famille ?
                            </label>
                            <Field
                              disabled={!showActuelTutorComponent}
                              component={Switch}
                              type="checkbox"
                              name="tutor"
                            />
                          </div>
                        </div>

                        {tutor ? (
                          <div>
                            <div className="form-group col-md-10">
                              <CustomTextInput
                                label="Date début"
                                placeholder="Date début"
                                isRequired
                                type="text"
                                name="tutorStartDate"
                                onFocus={e => {
                                  e.currentTarget.type = 'date';
                                  e.currentTarget.focus();
                                }}
                                formProps={props}
                              />
                            </div>
                            <div className="form-group col-md-10">
                              <CustomTextInput
                                label="Date fin"
                                placeholder="Date fin"
                                min={
                                  props.values.tutorStartDate
                                    ? props.values.tutorStartDate
                                    : null
                                }
                                isRequired
                                type="text"
                                name="tutorEndDate"
                                onFocus={e => {
                                  e.currentTarget.type = 'date';
                                  e.currentTarget.focus();
                                }}
                                formProps={props}
                              />
                            </div>
                          </div>
                        ) : null}
                        {!name && (
                          <div className="col-md-10 mb-0 pb-0">
                            <FamilyRelationships
                              isRequired
                              formProps={props}
                              name="familyRelationship.id"
                            />
                          </div>
                        )}
                      </div>
                    </div>
                    <div
                      className={`d-flex flex-column align-items-center ${!name &&
                        styles.hrWhite}`}
                    >
                      <hr width="75%" />
                    </div>

                    <div className="form-row mt-3">
                      <div className="form-group col-md-6">
                        <CustomTextInput
                          isRequired
                          label="Nom"
                          name="person.lastName"
                          placeholder="Nom"
                          formProps={props}
                        />
                      </div>
                      <div className="form-group col-md-6">
                        <CustomTextInputAr
                          isRequired
                          label=" النسب "
                          name="person.lastNameAr"
                          placeholder="النسب"
                        />
                      </div>
                    </div>
                    <div className="form-row">
                      <div className="form-group col-md-6">
                        <CustomTextInput
                          isRequired
                          label="Prénom"
                          name="person.firstName"
                          placeholder="Prénom"
                          formProps={props}
                        />
                      </div>
                      <div className="col-md-6">
                        <CustomTextInputAr
                          isRequired
                          label=" الإسم "
                          name="person.firstNameAr"
                          placeholder="الإسم"
                        />
                      </div>
                    </div>
                    <div className="form-row">
                      <div className="form-group col-md-6">
                        <CustomTextInput
                          label="Date de naissance"
                          type="date"
                          name="person.birthDate"
                          formProps={props}
                        />
                      </div>
                      <div className="col-md-6 row">
                        <div className="col-md-6 pr-0">
                          <TypeIdentities
                            label="Type d'identité"
                            name="person.typeIdentity.id"
                            isMeta
                            unique
                          />
                        </div>
                        <div className="form-group col-md-6 pr-0 ">
                          <CustomTextInput
                            label="N° identité"
                            name="person.identityCode"
                            placeholder="N° identité"
                            formProps={props}
                          />
                        </div>
                      </div>
                    </div>
                    <CountryRegionCity
                      formikRef={formikRef}
                      fromEducation={false}
                      regions={props.values.regions}
                      cities={props.values.cities}
                      formProps={props}
                      loadRegionsToForm={saveRegions}
                      loadCitiesToForm={saveCities}
                      optionalName="person."
                      data={person}
                      fromEdit={!!membere}
                      resetCity={resetCity}
                      error={
                        props.errors &&
                        props.errors.person &&
                        props.errors.person.region &&
                        props.errors.person.region
                      }
                      errorCity={
                        props.errors &&
                        props.errors.person &&
                        props.errors.person.city &&
                        props.errors.person.city
                      }
                      isRequired={false}
                    >
                      <div className="col-md-6">
                        <Professions
                          label="Profession"
                          name="person.profession.id"
                        />
                      </div>
                    </CountryRegionCity>
                    <div className="form-row">
                      <div className="form-group col-md-6">
                        <CustomTextInput
                          label="Téléphone personnel"
                          name="person.phoneNumber"
                          placeholder="Téléphone personnel"
                          formProps={props}
                        />
                      </div>
                    </div>
                    <div className="form-row">
                      <div className="form-group col-md-6">
                        <CustomTextInput
                          label="Adresse personnelle"
                          placeholder="Adresse personnelle"
                          name="person.address"
                          formProps={props}
                        />
                      </div>
                      <div className="form-group col-md-6">
                        <CustomTextInputAr
                          label=" العنوان الشخصي "
                          placeholder="العنوان الشخصي"
                          name="person.addressAr"
                        />
                      </div>
                    </div>

                    <div className="form-row">
                      <div className="col-md-12">
                        <label className="">Personne Décédée ?</label>
                        <Field
                          component={Switch}
                          type="checkbox"
                          name="person.deceased"
                        />
                      </div>
                    </div>
                    {deceased ? (
                      <div>
                        {/* Date of death and reason for death */}
                        <div className="form-row mt-3">
                          <div className="col-md-6">
                            <CustomTextInput
                              isRequired
                              label="Date du décès"
                              type="date"
                              name="person.deathDate"
                              formProps={props}
                            />
                          </div>
                          <div className="col-md-6">
                            <DeathReason
                              label="Raison du décès"
                              name="person.deathReasonSelected.id"
                              formProps={props.formProps}
                            />
                          </div>
                        </div>

                        {/* Tutor selection */}
                      </div>
                    ) : null}
                    <div className="form-row">
                      <div className="col-md-12">
                        <label className="">Personne Scolarisée ?</label>
                        <Field
                          component={Switch}
                          type="checkbox"
                          name="person.educated"
                        />
                      </div>
                    </div>
                    {educated ? (
                      <div className="form-row mt-3">
                        <div className="col-md-6">
                          <CustomSelect
                            isMeta
                            unique
                            label="Niveau scolaire"
                            name="person.schoolLevelType"
                            isRequired
                          >
                            <option value="">-- Niveau scolaire --</option>
                            {schoolLevelTypes.map(type => (
                              <option key={type.value} value={type.value}>
                                {type.label}
                              </option>
                            ))}
                          </CustomSelect>
                        </div>

                        <div className="col-md-6">
                          <SchoolLevels
                            isRequired
                            label="Année"
                            name="person.schoolLevel.id"
                            educationType={props.values.person.schoolLevelType}
                            formProps={props}
                          />
                        </div>
                      </div>
                    ) : null}
                    <div style={{ marginTop: '20px' }} className="form-row">
                      <div className="col-md-12">
                        <CustomTextArea
                          type="text"
                          label="Commentaire / ملاحظة"
                          name="generalComment"
                          formProps={props.formProps}
                        />
                      </div>
                    </div>
                    {showtNewTutorComponent && (
                      <div className="new-tutor-section p-4 mt-4 border rounded">
                        <h4 className="mb-3">Affecter un nouveau tuteur</h4>
                        <div className="form-row">
                          {/* CustomSelect for choosing a new tutor */}
                          <div className="col-md-12 mb-3">
                            <CustomSelect
                              label="Nouveau tuteur"
                              name="newTutorId"
                              isRequired
                              isMeta
                              unique
                              className="custom-select-input w-100"
                              onChange={e => {
                                const selectedTutorId = e.target.value;
                                props.setFieldValue(
                                  'newTutorId',
                                  selectedTutorId,
                                );

                                const selectedTutor = familyMembersFiltered.find(
                                  member =>
                                    member.id.toString() === selectedTutorId,
                                );

                                if (selectedTutor) {
                                  if (
                                    !selectedTutor.person.birthDate ||
                                    !selectedTutor.person.typeIdentity.id ||
                                    !selectedTutor.person.identityCode
                                  ) {
                                    setShowWarning(true);
                                    props.setFieldValue('newTutorId', '');
                                  } else {
                                    setShowWarning(false);
                                  }
                                }
                              }}
                            >
                              <option value="">
                                -- Sélectionner un membre de la famille --
                              </option>
                              {familyMembersFiltered &&
                              familyMembersFiltered.length > 0 ? (
                                familyMembersFiltered.map(member => (
                                  <option key={member.id} value={member.id}>
                                    {`${member.person.firstName} ${member.person.lastName}`}
                                  </option>
                                ))
                              ) : (
                                <option value="" disabled>
                                  Aucun membre de la famille peut être tuteur,
                                  tous les membres de la famille sont soit
                                  décédés, soit mineurs.
                                </option>
                              )}
                            </CustomSelect>
                          </div>
                        </div>

                        {showWarning && (
                          <div className="alert alert-danger">
                            ⚠️ Les informations du membre sélectionné sont
                            incomplètes (Date de naissance, Type d'identité, N°
                            d'identité). Veuillez les ajouter avant de procéder
                            au changement de tuteur.
                          </div>
                        )}

                        {/* Date fields in the same row */}
                        <div className="form-row">
                          <div className="form-group col-md-6 mb-3">
                            <CustomTextInput
                              label="Date début"
                              min={
                                props.values.tutorEndDate
                                  ? props.values.tutorEndDate
                                  : props.values.tutorStartDate
                              }
                              isRequired
                              placeholder="Date de début"
                              type="Date"
                              name="newTutorStartDate"
                              formProps={props}
                            />
                          </div>
                          <div className="form-group col-md-6 mb-3">
                            <CustomTextInput
                              label="Date fin"
                              placeholder="Date "
                              type="Date"
                              name="newTutorEndDate"
                              formProps={props}
                            />
                          </div>
                        </div>
                      </div>
                    )}
                  </div>

                  <div className="d-flex justify-content-end px-1 mt-4">
                    <button
                      hidden={loading}
                      type="button"
                      className={`mr-2 px-3 btn-style outlined`}
                      onClick={handleClose}
                    >
                      Annuler
                    </button>

                    <button
                      disabled={loading}
                      className={`ml-2 px-4 btn-style primary`}
                      type="submit"
                    >
                      {loading ? (
                        <p>
                          <span
                            className="spinner-border spinner-border-sm"
                            role="status"
                            aria-hidden="true"
                          ></span>
                          <span className="sr-only">Loading...</span>
                        </p>
                      ) : membere ? (
                        'Modifier'
                      ) : (
                        'Ajouter'
                      )}
                    </button>
                  </div>
                  {messageRedirection && (
                    <div className=" text-center mt-2">
                      <div
                        style={{
                          backgroundColor: '#d0e3ff',
                          borderRadius: '5px',
                          marginTop: '30px',
                          color: 'GrayText',
                          padding: '10px',
                          fontWeight: 'bold',
                        }}
                      >
                        {messageRedirection}
                      </div>
                    </div>
                  )}
                </Form>
              </div>
            </div>
          );
        }}
      </Formik>
    </div>
  );
}
