// constants.js
export const FETCH_USERS_REQUEST = 'almobadara/adUser/FETCH_USERS_REQUEST';
export const FETCH_USERS_SUCCESS = 'almobadara/adUser/FETCH_USERS_SUCCESS';
export const FETCH_USERS_FAILURE = 'almobadara/adUser/FETCH_USERS_FAILURE';

  // Add new constant for adding user
export const ADD_USER_REQUEST = 'almobadara/adUser/ADD_USER_REQUEST';
export const ADD_USER_SUCCESS = 'almobadara/adUser/ADD_USER_SUCCESS';
export const ADD_USER_FAILURE = 'almobadara/adUser/ADD_USER_FAILURE';
export const RESET_ADD_USER_SUCCESS = 'almobadara/adUser/RESET_ADD_USER_SUCCESS';
export const RESET_ADD_USER_ERROR = 'almobadara/adUser/RESET_ADD_USER_ERROR ';


export const DELETE_USER_REQUEST = 'almobadara/adUser/DELETE_USER_REQUEST';
export const DELETE_USER_SUCCESS = 'almobadara/adUser/DELETE_USER_SUCCESS';
export const DELETE_USER_FAILURE = 'almobadara/adUser/DELETE_USER_FAILURE';
export const RESET_DELETE_USER_SUCCESS = 'almobadara/adUser/RESET_DELETE_USER_SUCCESS';
// changeUserRole

export const CHANGE_USER_ROLE_REQUEST = 'almobadara/adUser/CHANGE_USER_ROLE_REQUEST';
export const CHANGE_USER_ROLE_SUCCESS = 'almobadara/adUser/CHANGE_USER_ROLE_SUCCESS';
export const CHANGE_USER_ROLE_FAILURE = 'almobadara/adUser/CHANGE_USER_ROLE_FAILURE';
export const RESET_CHANGE_USER_ROLE_SUCCESS = 'almobadara/adUser/RESET_CHANGE_USER_ROLE_SUCCESS';



