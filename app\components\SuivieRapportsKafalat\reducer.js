import produce from 'immer';
import {
  GET_LIST_RAPPORT,
  GET_LIST_RAPPORT_SUCCESS,
  GET_LIST_RAPPORT_ERROR,
  VIEW_RAPPORT,
  VIEW_RAPPORT_SUCCESS,
  VIEW_RAPPORT_ERROR,
  NOTIFY_ASSISTANT,
  NOTIFY_ASSISTANT_SUCCESS,
  NOTIFY_ASSISTANT_ERROR,
  NOTIFY_ASSISTANT_RESET,
  GET_LIST_DONOR,
  GET_LIST_DONOR_SUCCESS,
  GET_LIST_DONOR_ERROR,
  VIEW_RAPPORT_WITH_DONOR,
  RESET_VIEW_RAPPORT_WITH_DONOR_SUCCESS,
  VIEW_RAPPORT_WITH_DONOR_SUCCESS,
  VIEW_RAPPORT_WITH_DONOR_ERROR,
  ADD_AGENDA_RAPPORT_AUTOMATIC,
  ADD_AGENDA_RAPPORT_AUTOMATIC_SUCCESS,
  ADD_AGENDA_RAPPORT_AUTOMATIC_ERROR,
  ADD_AGENDA_RAPPORT_AUTOMATIC_RESET,
} from './constants';

export const initialState = {
  error: false,
  rapports: [],
  loading: false,
  totalElements: 0,
  pageNumber: 0,
  pageSize: 10,

  rapportViewLoading: false,
  successView: false,

  successNotify: false,
  errorNotify: false,
  loadingNotify: false,

  donors: [],
  errorDonor: false,
  loadingDonor: false,

  rapportWithDonorViewLoading: false,
  successViewRapportWithDonor: false,
  errorViewRapportWithDonor: false,

  loadingPlanificationAutomatic: false,
  successPlanificationAutomatic: false,
  errorPlanificationAutomatic: false,
};

const agendaRapportReducer = produce((draft, action) => {
  switch (action.type) {
    case GET_LIST_RAPPORT:
      draft.loading = true;
      draft.error = false;
      break;

    case GET_LIST_RAPPORT_SUCCESS:
      draft.loading = false;
      draft.rapports = action.data.content;
      draft.totalElements = action.data.totalElements;
      draft.pageNumber = action.data.pageable.pageNumber;
      draft.pageSize = action.data.pageable.pageSize;
      draft.error = false;
      break;

    case GET_LIST_RAPPORT_ERROR:
      draft.loading = false;
      draft.error = action.error;
      break;

    // case VIEW_RAPPORT:
    //   draft.error = false;
    //   draft.rapportViewLoading = true;
    //   draft.successView = false;
    //   break;
    // case VIEW_RAPPORT_SUCCESS:
    //   draft.error = false;
    //   draft.successView = true;
    //   draft.rapportViewLoading = false;
    //   draft.rapports = action.data.content;
    //   break;
    // case VIEW_RAPPORT_ERROR:
    //   draft.error = action.error;
    //   draft.rapportViewLoading = false;
    //   break;

    case VIEW_RAPPORT:
      draft.error = false;
      draft.rapportViewLoading = true;
      draft.successView = false;
      break;
    case VIEW_RAPPORT_SUCCESS:
      draft.error = false;
      draft.successView = true;
      draft.rapportViewLoading = false;
      draft.rapports = action.rapports;
      break;
    case VIEW_RAPPORT_ERROR:
      draft.error = action.error;
      draft.rapportViewLoading = false;
      break;
    case NOTIFY_ASSISTANT:
      draft.successNotify = false;
      draft.errorNotify = false;
      draft.loadingNotify = true;
      break;
    case NOTIFY_ASSISTANT_SUCCESS:
      draft.successNotify = action.success;
      draft.errorNotify = false;
      draft.loadingNotify = false;
      break;
    case NOTIFY_ASSISTANT_ERROR:
      draft.successNotify = false;
      draft.errorNotify = action.error;
      draft.loadingNotify = false;
      break;
    case NOTIFY_ASSISTANT_RESET:
      draft.successNotify = false;
      draft.errorNotify = false;
      draft.loadingNotify = false;
      break;

    case GET_LIST_DONOR:
      draft.loadingDonor = true;
      draft.errorDonor = false;
      break;

    case GET_LIST_DONOR_SUCCESS:
      draft.loadingDonor = false;
      draft.donors = action.data;
      draft.errorDonor = false;
      break;

    case GET_LIST_DONOR_ERROR:
      draft.loadingDonor = false;
      draft.errorDonor = action.error;
      break;

    case VIEW_RAPPORT_WITH_DONOR:
      draft.errorViewRapportWithDonor = false;
      draft.rapportWithDonorViewLoading = true;
      draft.successViewRapportWithDonor = false;
      break;
    case VIEW_RAPPORT_WITH_DONOR_SUCCESS:
      draft.errorViewRapportWithDonor = false;
      draft.successViewRapportWithDonor = true;
      draft.rapportWithDonorViewLoading = false;
      break;
    case VIEW_RAPPORT_WITH_DONOR_ERROR:
      draft.errorViewRapportWithDonor = action.error;
      draft.rapportWithDonorViewLoading = false;
      break;
    case RESET_VIEW_RAPPORT_WITH_DONOR_SUCCESS:
      draft.errorViewRapportWithDonor = false;
      draft.rapportWithDonorViewLoading = false;
      draft.successViewRapportWithDonor = false;
      break;

    case ADD_AGENDA_RAPPORT_AUTOMATIC:
      draft.loadingPlanificationAutomatic = true;
      draft.successPlanificationAutomatic = false;
      draft.errorPlanificationAutomatic = false;
      break;

    case ADD_AGENDA_RAPPORT_AUTOMATIC_SUCCESS:
      draft.loadingPlanificationAutomatic = false;
      draft.successPlanificationAutomatic = true;
      draft.errorPlanificationAutomatic = false;
      break;

    case ADD_AGENDA_RAPPORT_AUTOMATIC_ERROR:
      draft.loadingPlanificationAutomatic = false;
      draft.successPlanificationAutomatic = false;
      draft.errorPlanificationAutomatic = action.error;
      break;

    case ADD_AGENDA_RAPPORT_AUTOMATIC_RESET:
      draft.loadingPlanificationAutomatic = false;
      draft.successPlanificationAutomatic = false;
      draft.errorPlanificationAutomatic = false;
      break;

    default:
      break;
  }
}, initialState);

export default agendaRapportReducer;
