import {
  AD<PERSON>_<PERSON><PERSON><PERSON><PERSON>LEMENTAIRE,
  ADD_<PERSON>DECO<PERSON><PERSON>MENTAIRE_ERROR,
  ADD_AIDECOMPLEMENTAIRE_RESET,
  ADD_<PERSON>DECOMPLEMENTAIRE_SUCCESS,
  DELETE_AI<PERSON>COMPLEMENTAIRE,
  <PERSON>LETE_<PERSON><PERSON>COMPLEMENTAIRE_ERROR,
  DELETE_AIDECOMPLEMENTAIRE_RESET,
  DELETE_AIDECO<PERSON><PERSON>MENTAIRE_SUCCESS,
  LOAD_AIDECOMPLEMENTAIRE_TO_ADD,
  LOAD_AIDECOMPLEMENTAIRE_TO_ADD_ERROR,
  LOAD_AIDECOMPLEMENTAIRE_TO_ADD_SUCCESS,
  LOAD_BENEFICIARIES_WITH_TYPEPRISEENCHARGE,
  LOAD_BENEFICIARIES_WITH_TYPEPRISEENCHARGE_ERROR,
  LOAD_BENEFICIARIES_WITH_TYPEPRISEENCHARGE_SUCCESS,
} from './constants';

export function addAideComplementaire(search) {
  return {
    type: AD<PERSON>_<PERSON><PERSON>COMPLEMENTAIRE,
    search,
  };
}

export function aideComplementaireAdded(aideComplementaire) {
  return {
    type: ADD_<PERSON><PERSON><PERSON><PERSON>LEMENTAIRE_SUCCESS,
    aideComplementaire,
  };
}

export function aideComplementaireAddingError(error) {
  return {
    type: ADD_AIDECOMPLEMENTAIRE_ERROR,
    error,
  };
}

export function resetAideComplementaire() {
  return {
    type: ADD_AIDECOMPLEMENTAIRE_RESET,
  };
}

export function deleteAideComplementaire(aideComplementaire) {
  return {
    type: DELETE_AIDECOMPLEMENTAIRE,
    aideComplementaire,
  };
}

export function aideComplementaireDeleted(aideComplementaire) {
  return {
    type: DELETE_AIDECOMPLEMENTAIRE_SUCCESS,
    aideComplementaire,
  };
}

export function aideComplementaireDeletingError(error) {
  return {
    type: DELETE_AIDECOMPLEMENTAIRE_ERROR,
    error,
  };
}

export function loadAideComplementaire(search) {
  return {
    type: LOAD_AIDECOMPLEMENTAIRE_TO_ADD,
    search,
  };
}

export function aideComplementaireLoaded(aideComplementaire) {
  return {
    type: LOAD_AIDECOMPLEMENTAIRE_TO_ADD_SUCCESS,
    aideComplementaire,
  };
}

export function aideComplementaireLoadingError(error) {
  return {
    type: LOAD_AIDECOMPLEMENTAIRE_TO_ADD_ERROR,
    error,
  };
}

// delete reset
export function resetDeleteAideComplementaire() {
  return {
    type: DELETE_AIDECOMPLEMENTAIRE_RESET,
  };
}

/////
export function loadBeneficiariesWithTypePriseEnCharge(idTypePriseEnCharge) {
  return {
    type: LOAD_BENEFICIARIES_WITH_TYPEPRISEENCHARGE,
    idTypePriseEnCharge,
  };
}

export function beneficiariesWithTypePriseEnChargeLoaded(
  beneficiairesWithTypePriseEnCharge,
) {
  return {
    type: LOAD_BENEFICIARIES_WITH_TYPEPRISEENCHARGE_SUCCESS,
    beneficiairesWithTypePriseEnCharge,
  };
}

export function beneficiariesWithTypePriseEnChargeLoadingError(error) {
  return {
    type: LOAD_BENEFICIARIES_WITH_TYPEPRISEENCHARGE_ERROR,
    error,
  };
}
