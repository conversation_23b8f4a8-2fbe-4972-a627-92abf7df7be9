import produce from 'immer';
import {
  FETCH_ASSISTANTS_REQUEST,
  FETCH_ASSISTANTS_SUCCESS,
  FETCH_ASSISTANTS_FAILURE,
  DELETE_ASSISTANT_REQUEST,
  DELETE_ASSISTANT_SUCCESS,
  DELE<PERSON>_ASSISTANT_FAILURE,
  RESET_DELETE_ASSISTANT,
  CHANGE_ZONE_REQUEST,
  CHANGE_ZONE_SUCCESS,
  CHANGE_ZONE_FAILURE,
  CHANGE_ZONE_RESET,
  GET_ALL_ASSISTANTS_REQUEST,
  GET_ALL_ASSISTANTS_SUCCESS,
  GET_ALL_ASSISTANTS_FAILURE,
} from './constants';

export const initialState = {
  loading: false,
  error: false,
  success: false,
  successDelete: false,
  assistants: false,
  changeZoneSuccess: false,
};

const assistantsReducer = produce((draft, action) => {
  switch (action.type) {
    case FETCH_ASSISTANTS_REQUEST:
      draft.loading = true;
      draft.error = false;
      draft.success = false;
      break;
    case FETCH_ASSISTANTS_SUCCESS:
      draft.loading = false;
      draft.error = false;
      draft.success = true;
      draft.assistants = action.assistants;
      break;
    case FETCH_ASSISTANTS_FAILURE:
      draft.loading = false;
      draft.error = action.error;
      break;
    case DELETE_ASSISTANT_REQUEST:
      draft.loading = true;
      draft.error = false;
      draft.success = false;
      draft.successDelete = false;
      break;
    case DELETE_ASSISTANT_SUCCESS:
      draft.loading = false;
      draft.error = false;
      draft.success = false;
      draft.assistants = action.assistants;
      draft.successDelete = true;
      break;
    case DELETE_ASSISTANT_FAILURE:
      draft.loading = false;
      draft.error = action.error;
      break;
    case RESET_DELETE_ASSISTANT:
      draft.loading = false;
      draft.error = false;
      draft.success = false;
      draft.successDelete = false;
      break;
    case CHANGE_ZONE_REQUEST:
      draft.loading = true;
      draft.error = false;
      draft.changeZoneSuccess = false;
      break;
    case CHANGE_ZONE_SUCCESS:
      draft.loading = false;
      draft.error = false;
      draft.changeZoneSuccess = true;
      break;
    case CHANGE_ZONE_FAILURE:
      draft.loading = false;
      draft.error = action.error;
      break;
    case CHANGE_ZONE_RESET:
      draft.loading = false;
      draft.error = false;
      draft.changeZoneSuccess = false;
      break;
    case GET_ALL_ASSISTANTS_REQUEST:
      draft.loading = true;
      draft.error = false;
      break;
    case GET_ALL_ASSISTANTS_SUCCESS:
      draft.loading = false;
      draft.assistants = action.assistants;
      draft.error = false;
      break;
    case GET_ALL_ASSISTANTS_FAILURE:
      draft.loading = false;
      draft.error = action.error;
      break;
    default:
      return draft;
  }
}, initialState);

export default assistantsReducer;
