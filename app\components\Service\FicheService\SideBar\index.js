import React from 'react';
import sideBarStyles from '../../../../Css/sideBar.css';
import tag from '../../../../Css/tag.css';
import { Link } from 'react-router-dom/cjs/react-router-dom.min';

const rightArrow = require('../../../../images/icons/right-arrow.svg');

const SideBar = props => {
  const service = props.data;

  return (
    <>
      <div className={sideBarStyles.sideBar}>
        <div className={`${sideBarStyles.top}`}>
          <h5 className={sideBarStyles.linkDonor} style={{textAlign:'center',marginTop:'10px'}}>{service.name}</h5>
          <p className={sideBarStyles.linkDonor} style={{marginTop:'10px'}}>{service.code}</p>

          <p 
            className={
              service.statutIsActif === true ? tag.tagGreen : tag.tagRed
            }
            style={{marginTop:'10px'}}
          >
            {service.statutIsActif === true ? 'Actif' : 'Inactif'}
          </p>
             
          <p className={sideBarStyles.linkDonor} style={{marginTop:'10px'}}>
              Type de collection : {service.collectionType || '--'}
            </p>
            <p className={sideBarStyles.linkDonor } style={{marginTop:'10px'}}>
              Type de distribution : {service.distributionType || '--'}
            </p>
            {service.eps && service.eps.id && (
          
         <Link
         // to eps
            to={{
              pathname: `/eps/fiche/${service.eps.id}/info`,
            }}
            style={{
              textDecoration: 'underline',
              color: '#007bff',
              fontSize: '16px',
              fontWeight: 'bold',
              display: 'inline-block',
              marginLeft: '14px',
              marginBottom: '10px',
              transition: 'color 0.3s ease, text-decoration-color 0.3s ease',
              textDecorationColor: '#007bff',
              marginTop: '10px',
            }}
          >
          
            Eps : {service.eps.name}
          </Link>

            )}
         
            

          
        </div>
      </div>
    </>
  );
};

export default SideBar;
