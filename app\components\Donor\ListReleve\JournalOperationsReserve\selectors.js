import { createSelector } from 'reselect';
import { initialState } from './reducer';

const selectOmdb = state => state.releveDonor || initialState;

const makeSelectLoading = createSelector(
  selectOmdb,
  omdbState => omdbState.loading,
);

const makeSelectError = createSelector(
  selectOmdb,
  omdbState => omdbState.error,
);

const makeSelectReleveDonor = createSelector(
  selectOmdb,
  releveDoner => releveDoner.releves,
);

const makeSelectCanalDonations = createSelector(
  selectOmdb,
  omdbState => omdbState.canalDonations,
);

export {
  selectOmdb,
  makeSelectLoading,
  makeSelectError,
  makeSelectReleveDonor,
  makeSelectCanalDonations,
};
