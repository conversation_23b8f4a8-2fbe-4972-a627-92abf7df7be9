import { createSelector } from 'reselect';
import { initialState } from './reducer';

const selectOmdb = state => state.member || initialState;

const makeSelectSuccess = createSelector(
  selectOmdb,
  omdbState => omdbState.success,
);

const makeSelectLoading = createSelector(
  selectOmdb,
  omdbState => omdbState.loading,
);

const makeSelectError = createSelector(
  selectOmdb,
  omdbState => omdbState.error,
);

const makeSelectTutorError = createSelector(
  selectOmdb,
  omdbState => omdbState.error && omdbState.error.tutorError,
);

const makeSelectMember = createSelector(
  selectOmdb,
  omdbState => omdbState.member,
);
export {
  selectOmdb,
  makeSelectSuccess,
  makeSelectLoading,
  makeSelectError,
  makeSelectMember,
  makeSelectTutorError,
};
