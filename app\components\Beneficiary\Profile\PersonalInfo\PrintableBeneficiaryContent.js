import React, { useEffect, useState } from 'react';
import {
  Container,
  Grid,
  makeStyles,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
} from '@material-ui/core';
import defaultImage from '../../../../images/user.png';
import PrintHeader from '../../../Common/PrintHeader/PrintHeader';
import {
  isBeneficiary,
  isCandidate,
} from 'containers/Beneficiary/BeneficiaryProfile/statutUtils';

const useStyles = makeStyles(theme => ({
  container: {
    padding: '40px',
    fontFamily: "'Roboto', sans-serif",
    backgroundColor: '#f5f5f5',
    color: '#333',
  },
  paper: {
    padding: '40px',
    borderRadius: '15px',
    boxShadow: '0 8px 40px rgba(0, 0, 0, 0.12)',
    backgroundColor: '#ffffff',
    position: 'relative',
    overflow: 'hidden',
    marginBottom: '40px',
    '@media print': {
      '&:not(:first-child)': {
        pageBreakBefore: 'always',
      },
    },
  },
  sectionTitle: {
    marginBottom: '30px',
    color: '#3f51b5',
    textTransform: 'uppercase',
    letterSpacing: '1.5px',
    borderBottom: '2px solid #3f51b5',
    paddingBottom: '5px',
    marginTop: '20px',
  },
  gridItem: {
    marginBottom: '25px',
  },
  tableContainer: {
    marginBottom: '30px',
    pageBreakInside: 'avoid',
  },
  table: {
    minWidth: 650,
    '& th': {
      backgroundColor: '#3f51b5',
      color: '#ffffff',
    },
    '& td, & th': {
      border: '1px solid #e0e0e0',
      padding: '14px',
      fontSize: '16px',
    },
  },
  beneficiaryPicture: {
    width: '150px',
    height: '150px',
    borderRadius: '50%',
    border: '5px solid #ffffff',
    boxShadow: '0 4px 10px rgba(0, 0, 0, 0.3)',
    marginBottom: '20px',
  },
  section: {
    marginBottom: '50px',
    pageBreakInside: 'avoid',
  },
  infoItem: {
    display: 'flex',
    alignItems: 'center',
    marginBottom: '15px',
  },
  infoLabel: {
    marginRight: '15px',
    fontWeight: 'bold',
    color: '#3f51b5',
    whiteSpace: 'nowrap',
  },
  contactInfo: {
    marginTop: '20px',
  },
  tableTitle: {
    pageBreakInside: 'avoid',
  },
}));

const PrintableBeneficiaryContent = React.forwardRef(
  ({ data, family }, ref) => {
    const classes = useStyles();
    if (!data) {
      return null;
    }

    const formatDate = date => new Date(date).toLocaleDateString('fr-FR');

    const personalInfoItems = [
      { label: 'Prénom', value: data.person.firstName },
      { label: 'Nom ', value: data.person.lastName },
      { label: 'Email', value: data.person.email ? data.person.email : '---' },
      {
        label: 'Numéro de téléphone',
        value: data.person.phoneNumber ? data.person.phoneNumber : data.phoneNumberFamily ? data.phoneNumberFamily : '---',
      },
      { label: 'Sexe', value: data.person.sex },
      {
        label: "Code d'identité",
        value: data.person.identityCode ? data.person.identityCode : '---',
      },
      {
        label: 'Pays',
        value:
          data &&
          data.person &&
          data.person.info &&
          data.person.info.region &&
          data.person.info.region.country &&
          data.person.info.region.country.nameFrench
            ? data.person.info.region.country.nameFrench
            : data &&
              data.cityFamily &&
              data.cityFamily.region &&
              data.cityFamily.region.country &&
              data.cityFamily.region.country.nameFrench
            ? data.cityFamily.region.country.nameFrench
            : '---',
      },
      {
        label: 'Region',
        value:
          data &&
          data.person &&
          data.person.info &&
          data.person.info.region &&
          data.person.info.region.name
            ? data.person.info.region.name
            : data &&
              data.cityFamily &&
              data.cityFamily.region &&
              data.cityFamily.region.name
            ? data.cityFamily.region.name
            : '---',
      },
      {
        label: 'Ville',
        value:
          data && data.person && data.person.info && data.person.info.name
            ? data.person.info.name
            : data && data.cityFamily && data.cityFamily.name
            ? data.cityFamily.name
            : '---',
      },

      {
        label: 'Adresse',
        value: data.person.address
          ? data.person.address
          : data.addressFamily
          ? data.addressFamily
          : '---',
      },
      { label: 'Niveau scolaire', value: data.person.schoolLevel.name },
      { label: 'Date de naissance', value: formatDate(data.person.birthDate) },
      { label: 'Date de création', value: formatDate(data.createdAt) },
      { label: 'Code du bénéficiaire', value: data.code },
      { label: 'Indépendant', value: data.independent ? 'Oui' : 'Non' },
      { label: 'Zone', value: `${data.zone.code} - ${data.zone.name}` },
    ];

    const [nameFiche, setNameFiche] = useState('');

    useEffect(() => {
      if (data && data.statut) {
        if (isCandidate(data.statut)) {
          setNameFiche('Fiche du Pré-Candidat');
        } else {
          setNameFiche('Fiche du Bénéficiaire');
        }
      }
    }, [data.statut]);

    return (
      <Container
        ref={ref}
        className={classes.container}
        style={{ backgroundColor: '#ffffff' }}
      >
        <Paper className={classes.paper}>
          <PrintHeader headerText={nameFiche} />
          <div className={classes.section}>
            <Typography
              variant="h5"
              gutterBottom
              className={classes.sectionTitle}
            >
              Informations Personnelles
            </Typography>
            <Grid container spacing={3}>
              <Grid item xs={12} sm={6}>
                {data.person.pictureBase64 ? (
                  <img
                    src={`data:image/png;base64,${atob(
                      data.person.pictureBase64,
                    )}`}
                    alt="Beneficiary"
                    className={classes.beneficiaryPicture}
                  />
                ) : (
                  <img
                    src={defaultImage}
                    alt="Default Beneficiary"
                    className={classes.beneficiaryPicture}
                  />
                )}
              </Grid>
              <Grid container spacing={3}>
                {personalInfoItems.map((item, index) => (
                  <Grid
                    item
                    xs={12}
                    sm={6}
                    key={index}
                    className={classes.gridItem}
                  >
                    <div className={classes.infoItem}>
                      <Typography
                        variant="subtitle1"
                        className={classes.infoLabel}
                      >
                        {item.label}:
                      </Typography>
                      <Typography variant="body1" className={classes.infoValue}>
                        {item.value}
                      </Typography>
                    </div>
                  </Grid>
                ))}
              </Grid>
            </Grid>
          </div>
        </Paper>
        <Paper className={classes.paper}>
          <PrintHeader headerText={nameFiche} />
          <div className={classes.section}>
            <Typography
              variant="h5"
              gutterBottom
              className={classes.sectionTitle}
            >
              Informations Sanitaires
            </Typography>
            <div className={classes.infoItem}>
              <Typography variant="subtitle1" className={classes.infoLabel}>
                Handicaps (avec leur coût):
              </Typography>
              <Typography variant="body1" className={classes.infoValue}>
                {data.handicapped && data.handicapped.length > 0
                  ? data.handicapped
                      .map(
                        handicap =>
                          `${handicap.handicapType.name} ( ${handicap.handicapCost}DH )`,
                      )
                      .join(', ')
                  : '------'}
              </Typography>
            </div>

            <div className={classes.infoItem}>
              <Typography variant="subtitle1" className={classes.infoLabel}>
                Allergies:
              </Typography>
              <Typography variant="body1" className={classes.infoValue}>
                {data.allergies.length > 0
                  ? data.allergies.map(allergy => allergy.name).join(', ')
                  : '-----'}
              </Typography>
            </div>

            <div className={classes.infoItem}>
              <Typography variant="subtitle1" className={classes.infoLabel}>
                Maladies:
              </Typography>
              <Typography variant="body1" className={classes.infoValue}>
                {data.diseases.length > 0
                  ? data.diseases.map(disease => disease.name).join(', ')
                  : '-----'}
              </Typography>
            </div>

            <div className={classes.infoItem}>
              <Typography variant="subtitle1" className={classes.infoLabel}>
                Traitements de Maladie (avec leur coût):
              </Typography>
              <Typography variant="body1" className={classes.infoValue}>
                {data.diseaseTreatments.length > 0
                  ? data.diseaseTreatments
                      .map(
                        treatment =>
                          `${treatment.type.name} ( ${treatment.cost}DH )`,
                      )
                      .join(', ')
                  : '------'}
              </Typography>
            </div>
          </div>
          <div className={classes.section}>
            <Typography
              variant="h5"
              gutterBottom
              className={classes.sectionTitle}
            >
              Parcours Scolaire
            </Typography>
            <TableContainer component={Paper} className={classes.table}>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Année scolaire</TableCell>
                    <TableCell>Niveau scolaire</TableCell>
                    <TableCell>École</TableCell>
                    <TableCell>Note</TableCell>
                    <TableCell>Mention</TableCell>
                    <TableCell>Réussi</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {data.educations.length > 0 ? (
                    data.educations.map((education, index) => (
                      <TableRow key={index}>
                        <TableCell>{education.schoolYear.name}</TableCell>
                        <TableCell>{education.schoolLevel.name}</TableCell>
                        <TableCell>{education.schoolName}</TableCell>
                        <TableCell>{education.mark}</TableCell>
                        <TableCell>{education.honor.name}</TableCell>
                        <TableCell>
                          {education.succeed ? 'Oui' : 'Non'}
                        </TableCell>
                      </TableRow>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell colSpan={7} align="center">
                        Aucun parcours scolaire trouvé
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </TableContainer>
          </div>
          <div className={classes.section}>
            <Typography
              variant="h5"
              gutterBottom
              className={classes.sectionTitle}
            >
              Bourses
            </Typography>
            <TableContainer component={Paper} className={classes.table}>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Nom de la bourse</TableCell>
                    <TableCell>Montant</TableCell>
                    <TableCell>Début</TableCell>
                    <TableCell>Fin</TableCell>
                    <TableCell>Périodicité</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {data.scholarshipBeneficiaries.length > 0 ? (
                    data.scholarshipBeneficiaries.map(scholarship => (
                      <TableRow key={scholarship.id}>
                        <TableCell>{scholarship.scholarship.name}</TableCell>
                        <TableCell>{scholarship.amount}</TableCell>
                        <TableCell>
                          {formatDate(scholarship.startDate)}
                        </TableCell>
                        <TableCell>{formatDate(scholarship.endDate)}</TableCell>
                        <TableCell>{scholarship.periodicity}</TableCell>
                      </TableRow>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell colSpan={7} align="center">
                        Aucune bourse trouvée
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </TableContainer>
          </div>
          {isBeneficiary(data.statut) && (
            <div className={classes.section}>
              <Typography
                variant="h5"
                gutterBottom
                className={classes.sectionTitle}
              >
                Kafalat
              </Typography>
              <TableContainer component={Paper} className={classes.table}>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Service</TableCell>
                      <TableCell>Statut</TableCell>
                      <TableCell>Date de début</TableCell>
                      <TableCell>Date de fin</TableCell>
                      <TableCell>Donneur</TableCell>{' '}
                      {/* Added column for Donor */}
                    </TableRow>
                  </TableHead>

                  <TableBody>
                    {data.takenInChargeBeneficiaries &&
                    data.takenInChargeBeneficiaries.length > 0 ? (
                      data.takenInChargeBeneficiaries.map(takeInCharge => (
                        <TableRow key={takeInCharge.id}>
                          <TableCell>
                            {takeInCharge.takenInCharge.service.name}
                          </TableCell>
                          <TableCell>
                            {takeInCharge.takenInCharge.status.name}
                          </TableCell>
                          <TableCell>
                            {formatDate(takeInCharge.takenInCharge.startDate)}
                          </TableCell>
                          <TableCell>
                            {formatDate(takeInCharge.takenInCharge.endDate)}
                          </TableCell>
                          <TableCell>
                            {takeInCharge.takenInCharge.takenInChargeDonors &&
                            takeInCharge.takenInCharge.takenInChargeDonors
                              .length > 0
                              ? takeInCharge.takenInCharge
                                  .takenInChargeDonors[0].donor
                                ? `${takeInCharge.takenInCharge.takenInChargeDonors[0].donor.firstName} ${takeInCharge.takenInCharge.takenInChargeDonors[0].donor.lastName}`
                                : 'Donneur anonyme'
                              : 'Pas de donneur'}
                          </TableCell>
                        </TableRow>
                      ))
                    ) : (
                      <TableRow>
                        <TableCell colSpan={5} align="center">
                          Aucune Kafalat trouvée
                        </TableCell>
                      </TableRow>
                    )}
                  </TableBody>
                </Table>
              </TableContainer>
            </div>
          )}
          {family && family.familyMembers && family.familyMembers.length > 0 && (
            <div className={classes.section}>
              <Typography
                variant="h5"
                gutterBottom
                className={classes.sectionTitle}
              >
                Informations Familiales
              </Typography>
              <TableContainer component={Paper} className={classes.table}>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Prénom</TableCell>
                      <TableCell>Nom</TableCell>
                      <TableCell>Relation</TableCell>
                      <TableCell>Date de Naissance</TableCell>
                      <TableCell> Tuteur </TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {family.familyMembers.map((member, index) => (
                      <TableRow key={index}>
                        <TableCell>
                          {member.person ? member.person.firstName : '-'}
                        </TableCell>
                        <TableCell>
                          {member.person ? member.person.lastName : '-'}
                        </TableCell>
                        <TableCell>
                          {member.familyRelationship
                            ? member.familyRelationship.name
                            : '-'}
                        </TableCell>
                        <TableCell>
                          {formatDate(
                            member.person ? member.person.birthDate : null,
                          )}
                        </TableCell>
                        <TableCell> {member.tutor ? 'oui' : 'non'}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </div>
          )}

          {data.remarqueFr && (
            <div className={classes.section}>
              <Typography
                variant="h5"
                gutterBottom
                className={classes.sectionTitle}
              >
                Remarques
              </Typography>
              <Typography variant="body1" className={classes.infoValue}>
                {data.remarqueFr}
              </Typography>
            </div>
          )}
        </Paper>
      </Container>
    );
  },
);

export default PrintableBeneficiaryContent;
