import React, { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';
import { createStructuredSelector } from 'reselect';
import { useSelector } from 'react-redux';
import { makeSelectConnectedUser } from 'containers/App/selectors';
import changelogTxt from './changelog.txt';
import Wrapper from './Wrapper.js';

const omdbSelector = createStructuredSelector({
  connectedUser: makeSelectConnectedUser,
});

function Footer() {
  const [version, setVersion] = useState('');
  const { connectedUser } = useSelector(omdbSelector);

  useEffect(() => {
    const fetchVersion = async () => {
      try {
        const lines = changelogTxt.split('\n');
        const versionPattern = /\[Version (\d+\.\d+\.\d+)\]/;
        for (const line of lines) {
          const match = line.trim().match(versionPattern);
          if (match && match[1]) {
            setVersion(match[1]);
            break;
          }
        }
      } catch (error) {}
    };
    fetchVersion();
  }, []);

  const handleLinkHover = e => {
    e.target.style.color = '#ffcc00';
  };

  const handleLinkLeave = e => {
    e.target.style.color = 'white';
  };

  // Conditionally render the footer content only if connectedUser is not null
  if (!connectedUser) {
    return null; // Return null to hide the footer if connectedUser is null
  }

  return (
    <Wrapper>
      <div className="leftContainerStyle">
        <div className="userInfoStyle">
          <span className="userInfoValueStyle">
            {connectedUser.firstName || connectedUser.lastName
              ? `${connectedUser.firstName || ''} ${connectedUser.lastName ||
                  ''}`
              : 'Utilisateur Inconnu'}
          </span>

          <span className="userInfoValueStyle">
            {' '}
            - {connectedUser.role ? connectedUser.role.name : 'Profile Inconnu'}
          </span>
        </div>
      </div>
      <div className="centerContainerStyle">
        <p className="versionStyle">Version {version || 'N/A'}</p>
      </div>
      <div className="rightContainerStyle">
        <Link to="/changelog" className="linkStyle">
          Consulter le journal des modifications
        </Link>
      </div>
    </Wrapper>
  );
}
export default Footer;
