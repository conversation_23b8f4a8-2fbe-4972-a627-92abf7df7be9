import styled from 'styled-components';

const Wrapper = styled.div`
  .listReleve-container {
    background-color: #ffffff;
    display: flex;
    flex-direction: column;
    padding: 24px;
    gap: 20px;
  }
  .backgroundGray {
    background-color: #e6e9eb;
  }
  .formContainer {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    background-color: #FFFFFF;
    .w-30 {
      width: 30%;
    }
    .withLabelContainer {
      display: flex;
      gap: 10px;
      align-items: center;
      width: 30%;
    }
  }
  .actionBtns {
    display: flex;
    flex-direction: row;
    justify-content: flex-end;
    background-color: #ffffff;
    gap: 15px;
  }
`;
export default Wrapper;


