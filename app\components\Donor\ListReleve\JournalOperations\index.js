import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import { useDispatch, useSelector } from 'react-redux';
import moment from 'moment';
import { useInjectReducer, useInjectSaga } from 'redux-injectors';
import { useHistory, useParams } from 'react-router-dom';
import {
  makeSelectCanalDonations,
  makeSelectError,
  makeSelectLoading,
  makeSelectReleveDonor,
  makeSelectServiceCategories,
  makeSelectServices,
} from './selectors';
import {
  loadCanalDonations,
  loadReleveDonor,
  loadServiceCategories,
  loadServices,
} from './actions';
import reducer from './reducer';
import saga from './saga';
import ReactToPrint from 'react-to-print';
import CustomPagination from 'components/Common/CustomPagination';
import PrintableContent from './PrintableContent/PrintableContent';
import Modal2 from 'components/Common/Modal2';
import eye from 'images/icons/eye.svg';

const formatDate = date => (date ? moment(date).format('DD/MM/YYYY') : '-');

const BodyComponent = ({
  data,
  openModal = ({ list = [], serviceName = '', date = '' }) => {},
}) => (
  <>
    {data && data.length > 0 ? (
      data.map(el => {
        if (el.typeDonationKafalat !== 'donation') {
          return (
            <tr key={el.id}>
              <td>{formatDate(el.dateExecution)}</td>
              <td>{el.montantSortie}</td>
              <td>{el.services.category}</td>
              <td>{el.services.typeCategory}</td>
              <td
                style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                }}
              >
                {el.name ? el.name : el.services.name}
                {el.beneficiaries && el.beneficiaries.length > 0 && (
                  <span
                    onClick={() => {
                      openModal({
                        list: el.beneficiaries,
                        serviceName: el.name
                          ? el.name
                          : el.services && el.services.name
                          ? el.services.name
                          : '-',
                        date: formatDate(el.dateExecution),
                      });
                    }}
                    style={{
                      width: 30,
                      height: 30,
                      borderRadius: 30,
                      display: 'flex',
                      flexDirection: 'column',
                      justifyContent: 'center',
                      alignItems: 'center',
                      backgroundColor: '#F1F1F1',
                    }}
                  >
                    <div
                      style={{
                        color: 'blue',
                        cursor: 'pointer',
                        fontSize: 15,
                      }}
                    >
                      +
                    </div>
                  </span>
                )}
              </td>
            </tr>
          );
        }
      })
    ) : (
      <tr>
        <td colSpan="8">Aucun relevé disponible</td>
      </tr>
    )}
  </>
);

const key = 'releveDonor';
const pageSize = 10;
export default function JournalOperations(props) {
  const history = useHistory();
  const donor = props.data;
  const {
    error,
    loading,
    releves,
    canalDonations,
    serviceCategories,
    services,
  } = useSelector(state => ({
    error: makeSelectError(state),
    loading: makeSelectLoading(state),
    releves: makeSelectReleveDonor(state),
    canalDonations: makeSelectCanalDonations(state),
    serviceCategories: makeSelectServiceCategories(state),
    services: makeSelectServices(state),
  }));

  useInjectReducer({ key, reducer });
  useInjectSaga({ key, saga });

  const dispatch = useDispatch();
  const params = useParams();
  const [activePage, setActivePage] = useState(1);
  const [startDate, setStartDate] = useState(moment().startOf('year'));
  const [endDate, setEndDate] = useState(moment().endOf('year'));
  const [modalVisible, setModalVisible] = useState(false);
  const [beneficiaries, setBeneficiaries] = useState([]);
  const [currentServiceName, setCurrentServiceName] = useState('');
  const [beneficiaryDate, setBeneficiaryDate] = useState('');
  const openModal = ({ list = [], serviceName = '', date = '' }) => {
    setBeneficiaries(list);
    setCurrentServiceName(serviceName);
    setModalVisible(true);
    setBeneficiaryDate(date);
  };
  const closeModal = () => {
    setModalVisible(false);
  };
  useEffect(() => {
    dispatch(loadReleveDonor(params.id));
    dispatch(loadCanalDonations());
    dispatch(loadServiceCategories('search'));
    dispatch(loadServices('search'));
  }, [dispatch, params.id]);

  const handlePageChange = pageNumber => {
    setActivePage(pageNumber);
  };
  const [filteredData, setFilteredData] = useState([]);
  const [serviceCategoryFilter, setServiceCategoryFilter] = useState('');

  const handleDataPaging = useCallback(() => {
    setFilteredData(() => {
      const startIndex = (activePage - 1) * pageSize;
      const endIndex = startIndex + pageSize;
      return releves ? releves.slice(startIndex, endIndex) : [];
    });
  }, [releves, pageSize, activePage]);
  useEffect(() => {
    handleDataPaging();
  }, [releves, pageSize, activePage]);

  const total = useMemo(() => {
    if (filteredData && filteredData.length > 0) {
      return filteredData
        .filter(el => el.typeDonationKafalat !== 'donation')
        .reduce((sum, el) => sum + (el.montantSortie || 0), 0);
    }
    return 0;
  }, [filteredData]);

  const applyFilter = () => {
    if (startDate && serviceCategoryFilter && serviceCategoryFilter !== '') {
      const startIndex = (activePage - 1) * pageSize;
      const endIndex = startIndex + pageSize;
      setFilteredData(() =>
        releves
          .filter(
            el =>
              el.dateExecution &&
              el.dateExecution.slice(0, 10) >=
                moment(startDate).format('YYYY-MM-DD') &&
              el.dateExecution &&
              el.dateExecution.slice(0, 10) <=
                moment(endDate).format('YYYY-MM-DD') &&
              el.services &&
              el.services.category === serviceCategoryFilter,
          )
          .slice(startIndex, endIndex),
      );
    } else {
      const startIndex = (activePage - 1) * pageSize;
      const endIndex = startIndex + pageSize;
      setFilteredData(() =>
        releves
          .filter(
            el =>
              el.dateExecution &&
              el.dateExecution.slice(0, 10) >=
                moment(startDate).format('YYYY-MM-DD') &&
              el.dateExecution &&
              el.dateExecution.slice(0, 10) <=
                moment(endDate).format('YYYY-MM-DD'),
          )
          .slice(startIndex, endIndex),
      );
    }
  };

  const componentRef = useRef();

  return (
    <div className="listReleve-container br-bottoms-20">
      <Modal2
        title={'List des Beneficiares'}
        size="lg"
        customWidth="modal-90w"
        show={modalVisible}
        handleClose={closeModal}
      >
        <p>{`List des beneficiares ${currentServiceName}`}</p>
        <div
          style={{
            gap: 10,
            display: 'flex',
            flexDirection: 'column',
            maxHeight: 400,
            overflowY: 'scroll',
            alignItems: 'center',
          }}
        >
          {beneficiaries &&
            beneficiaries.length > 0 &&
            beneficiaries.map(el => {
              return (
                <div
                  className="border"
                  style={{
                    display: 'flex',
                    flexDirection: 'row',
                    alignItems: 'center',
                    paddingInline: 10,
                    minHeight: 50,
                    width: 300,
                    borderRadius: 10,
                    justifyContent: 'space-between',
                  }}
                >
                  <div
                    style={{
                      display: 'flex',
                      flexDirection: 'row',
                      gap: 5,
                    }}
                  >
                    {/* <div>{el.personFirstName}</div> */}
                    <div>{el.personLastName}</div>
                    <div>{el.montantAffecter} DH</div>
                    <div>{beneficiaryDate} </div>
                  </div>
                  <div>
                    <img
                      src={eye}
                      style={{
                        cursor: 'pointer',
                        color: 'blue',
                      }}
                      width="20px"
                      height="20px"
                      alt="rightArrow"
                      onClick={() => {
                        history.push(`/beneficiaries/fiche/${el.id}/info`);
                        // /beneficiaries/cefhi / 116 / info;
                      }}
                    />
                  </div>
                </div>
              );
            })}
        </div>
      </Modal2>
      <div className="d-flex bg-white">
        <h4>Journal des opérations exécutées</h4>
      </div>
      <div className="formContainer">
        <div className="withLabelContainer">
          <label className="w-30">
            Catégorie:
          </label>
          <select
            value={serviceCategoryFilter}
            onChange={e => setServiceCategoryFilter(e.target.value)}
            className="form-control input-border"
          >
            <option value="">Tous les catégories</option>
            {serviceCategories &&
              serviceCategories.map(service => (
                <option key={service.id} value={service.name}>
                  {service.name}
                </option>
              ))}
          </select>
        </div>
        <div className="withLabelContainer">
          <label className="w-30">
            Date Debut:
          </label>
          <input
            type="date"
            value={startDate ? moment(startDate).format('YYYY-MM-DD') : ''}
            max={
              endDate
                ? moment(endDate)
                    .subtract(1, 'days')
                    .format('YYYY-MM-DD')
                : ''
            }
            onChange={e => setStartDate(e.target.value)}
            className="form-control input-border"
          />
        </div>

        <div className="withLabelContainer">
          <label className="w-30">
            Date Fin :
          </label>
          <input
            type="date"
            value={endDate ? moment(endDate).format('YYYY-MM-DD') : ''}
            min={
              startDate
                ? moment(startDate)
                    .add(1, 'days')
                    .format('YYYY-MM-DD')
                : ''
            }
            onChange={e => setEndDate(e.target.value)}
            className="form-control input-border"
          />
        </div>

      </div>
      <div className="actionBtns">
        <button
            className="btn-style primary"
            onClick={() => {
              if (startDate || endDate) {
                applyFilter();
              }
            }}
        >
          <span>Appliquer</span>
        </button>
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <ReactToPrint
              trigger={() => (
                  <button className="btn-style secondary">Imprimer</button>
              )}
              content={() => componentRef.current}
          />
          <div style={{ display: 'none' }}>
            <PrintableContent
                ref={componentRef}
                data={filteredData}
                total={total}
                donor={donor}
                startDate={moment(startDate).format('DD-MM-YYYY')}
                endDate={moment(endDate).format('DD-MM-YYYY')}
            />
          </div>
        </div>
        <button
          className="btn-style success"
          onClick={() => {
            handleDataPaging();
            setStartDate(moment().startOf('year'));
            setEndDate(moment().endOf('year'));
          }}
        >
          <span>Reset</span>
        </button>
      </div>
      <div className="d-flex flex-column">
        <table className="table table-bordered">
          <thead>
            <tr>
              <th colSpan={5} className="text-align-center">
                Opérations de prise en charge exécutées
              </th>
            </tr>
            <tr>
              {/* <th>Code</th> */}
              <th>Date</th>
              <th>Montant</th>
              <th>Catégorie</th>
              <th>Type de service</th>
              <th>Service</th>
            </tr>
          </thead>
          <tbody>
            <BodyComponent data={filteredData} openModal={openModal} />
          </tbody>
          <tfoot>
            <tr>
              <td>Total</td>
              <td>{`${total} DH`}</td>
              <td></td>
              <td></td>
              <td></td>
            </tr>
          </tfoot>
        </table>
        {releves.length > 0 && (
          <div
              className="d-flex justify-content-center"
          >
            <CustomPagination
                totalElements={releves.length}
                totalCount={
                    releves &&
                    releves.length > 0 &&
                    Math.ceil(releves.length / pageSize)
                }
                pageSize={pageSize}
                currentPage={activePage}
                onPageChange={handlePageChange}
            />
          </div>
        )}
      </div>
    </div>
  );
}
