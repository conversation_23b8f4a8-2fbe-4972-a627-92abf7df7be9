import React, { useEffect, useState } from 'react';
import moment from 'moment';
import { Link, useHistory } from 'react-router-dom';
import listStyle from 'Css/profileList.css';

import {
  makeSelectDeleteSuccess,
  makeSelectDonation,
  makeSelectError,
  makeSelectSuccess,
} from 'containers/Donation/AddDonation/selectors';

import { createStructuredSelector } from 'reselect';
import { useDispatch, useSelector } from 'react-redux';
import { Alert } from 'react-bootstrap';
import { useInjectReducer, useInjectSaga } from 'redux-injectors';
import AccessControl from 'utils/AccessControl';
import DataTable from '../../Common/DataTable';
import { EDIT_ICON, VIEW_ICON } from '../../Common/ListIcons/ListIcons';
import serviceReducer from '../../../containers/Service/ListService/reducer';
import serviceListSaga from '../../../containers/Service/ListService/saga';
import { resetService } from '../../../containers/Service/AddService/actions';
import tagStyles from '../../../Css/tag.css';

const omdbSelector = createStructuredSelector({
  success: makeSelectSuccess,
  donation: makeSelectDonation,
  successDelete: makeSelectDeleteSuccess,
  error: makeSelectError,
});

const keyServiceList = 'serviceList';
export default function ListService(props) {
  let listServices;

  const liste1 = props.listesGlobal;

  const formatDate = date => moment(date).format('DD/MM/YYYY');

  useInjectReducer({
    key: keyServiceList,
    reducer: serviceReducer,
  });
  useInjectSaga({
    key: keyServiceList,
    saga: serviceListSaga,
  });

  const dispatch = useDispatch();
  const { error } = useSelector(omdbSelector);
  const history = useHistory();
  const [show, setShow] = useState(false);
  const [showAlert, setShowAlert] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [message, setMessage] = useState('');
  const [serviceToEdit, setServiceToEdit] = useState('');
  let errorMessage = null;

  useEffect(
    () =>
      function cleanup() {
        dispatch(resetService());
      },
    [],
  );

  if (error) {
    errorMessage = 'Une erreur est survenue';
  }

  const viewHandler = id => {
    history.push(`/services/fiche/${id}/info`, { params: id });
  };

  const handleClose = () => {
    setShow(false);
  };

  const handleShow = () => {
    setShow(true);
  };

  const getTag = statut => {
    if (statut === true) {
      return tagStyles.tagGreen;
    }
    if (statut === false) {
      return tagStyles.tagRed;
    }
    return tagStyles.tagGrey;
  };

  const getStatutText = statut => {
    if (statut === false) {
      return 'Inactif';
    }
    if (statut === true) {
      return 'Actif';
    }
    return '---';
  };

  if (props.service) {
    let { service } = props;

    if (props.profile) {
      service = props.service.service;
    }

    const serviceSorted = [...service].sort((a, b) =>
      a.modifiedAt < b.modifiedAt ? 1 : -1,
    );
    // a voir
    listServices = service.map(service => ({
      id: service.id,
      code: service.code,
      name: service.name || <span>-</span>,
      montantPrevu: service.montantPrevu,
      category: service.category,
      typeCategory: service.typeCategory,
      statutIsActif: service.statutIsActif,
      service: props,
    }));
  }

  const columns = [
    {
      field: 'code',
      headerName: 'Code',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
    {
      field: 'name',
      headerName: 'Nom',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
    {
      field: 'category',
      headerName: 'Catégorie',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
    {
      field: 'statutIsActif',
      headerName: 'Statut',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
      renderCell: params => (
        <div className={getTag(params.row.statutIsActif)}>
          {getStatutText(params.row.statutIsActif)}
        </div>
      ),
    },
    {
      field: 'actions',
      headerName: 'Actions',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
      renderCell: params => (
        <div>
          <input
            type="image"
            onClick={() => viewHandler(params.row.id)}
            className="p-2"
            src={VIEW_ICON}
            width="40px"
            height="40px"
            title="consulter"
          />

          {!props.profile ? (
            <AccessControl module="USER" functionality="UPDATE">
              <Link to={{ pathname: `/services/edit/${params.row.id}` }}>
                <input
                  type="image"
                  src={EDIT_ICON}
                  className="p-2"
                  width="40px"
                  height="40px"
                  title="Modifier"
                  onClick={() => {
                    setServiceToEdit(params.row);
                    handleShow();
                  }}
                />
              </Link>
            </AccessControl>
          ) : (
            <AccessControl module="USER" functionality="UPDATE">
              <input
                type="image"
                src={EDIT_ICON}
                className="p-2"
                width="40px"
                height="40px"
                title="Modifier"
                onClick={() => {
                  setServiceToEdit(params.row);
                  handleShow();
                }}
              />
            </AccessControl>
          )}
        </div>
      ),
    },
  ];

  return (
    <div>
      {showAlert ? (
        <Alert
          className="alert-style"
          variant="success"
          onClose={() => setShowAlert(false)}
          dismissible
        >
          <p>{message}</p>
        </Alert>
      ) : null}

      {error ? (
        <div className="alert alert-danger"> {errorMessage} </div>
      ) : null}

      <div className={` ${!props.profile ? 'p-0' : listStyle.backgroudStyle}`}>
        {props.profile ? (
          <div className={listStyle.global}>
            <div className={listStyle.header}>
              <h4>Liste des Services</h4>
            </div>
          </div>
        ) : null}

        <div>
          <DataTable
            rows={listServices}
            columns={columns}
            fileName={`Liste des Services , ${new Date().toLocaleString()}`}
            totalElements={liste1 ? liste1.totalElements : 0}
            numberOfElements={liste1 ? liste1.numberOfElements : 0}
            pageable={liste1 ? liste1.pageable : 0}
          />
        </div>
      </div>
    </div>
  );
}
