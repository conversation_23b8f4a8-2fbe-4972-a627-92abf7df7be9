import produce from 'immer';
import {
  GET_LIST_OPERATIONS_TAKEN_IN_CHARGE,
  GET_LIST_OPERATIONS_TAKEN_IN_CHARGE_SUCCESS,
  GET_LIST_OPERATIONS_TAKEN_IN_CHARGE_ERROR,
  CHANGE_OPERATION_STATUS,
  CHANGE_OPERATION_STATUS_ERROR,
  CHANGE_OPERATION_STATUS_SUCCESS,
} from './constants';

export const initialState = {
  error: false,
  operationsTakenInCharge: [],
  operationsTakenInChargeLoading: false,
  operationStatusChanged: false,
  operationStatusChangedLoading: false,
  operationStatusChangedError: false,
};

/* eslint-disable default-case, no-param-reassign */
const operationTakenInChargeReducer = produce((draft, action) => {
  switch (action.type) {
    case GET_LIST_OPERATIONS_TAKEN_IN_CHARGE:
      draft.operationsTakenInChargeLoading = true;
      break;
    case GET_LIST_OPERATIONS_TAKEN_IN_CHARGE_SUCCESS:
      draft.operationsTakenInChargeLoading = false;
      draft.operationsTakenInCharge = action.data;
      break;
    case GET_LIST_OPERATIONS_TAKEN_IN_CHARGE_ERROR:
      draft.operationsTakenInChargeLoading = false;
      break;
    case CHANGE_OPERATION_STATUS:
      draft.operationStatusChanged = false;
      draft.operationStatusChangedLoading = true;
      draft.operationStatusChangedError = false;
      break;
    case CHANGE_OPERATION_STATUS_SUCCESS:
      draft.operationStatusChanged = action.success;
      draft.operationStatusChangedLoading = false;
      draft.operationStatusChangedError = false;
      break;
    case CHANGE_OPERATION_STATUS_ERROR:
      draft.operationStatusChanged = false;
      draft.operationStatusChangedLoading = false;
      draft.operationStatusChangedError = action.error;
      break;
  }
}, initialState);

export default operationTakenInChargeReducer;
