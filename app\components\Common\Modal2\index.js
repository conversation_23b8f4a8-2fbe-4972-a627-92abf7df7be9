import React from 'react';
import { Modal } from 'react-bootstrap';
import btnStyles from 'Css/button.css';

const Modal2 = props => (
  <Modal
    size={props.size}
    show={props.show}
    onHide={props.handleClose}
    centered={props.centered}
  >
    <Modal.Header closeButton>
      <Modal.Title>{props.title}</Modal.Title>
    </Modal.Header>
    <Modal.Body>{props.children}</Modal.Body>
    {/* <Modal.Footer>
            </Modal.Footer> */}
  </Modal>
);

export default Modal2;
