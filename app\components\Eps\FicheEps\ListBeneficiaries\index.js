import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useParams, Link } from 'react-router-dom';
import { loadListeBeneficiaires } from 'containers/Eps/FicheEps/actions'; // Action to load beneficiaries
import { createStructuredSelector } from 'reselect';
import DataTable from 'components/Common/DataTable'; // Your DataTable component
import CustomPagination from 'components/Common/CustomPagination'; // Pagination component
import { Alert, Form, Row, Col } from 'react-bootstrap';
import stylesList from 'Css/profileList.css'; // Make sure the path to your styles is correct
import 'bootstrap/dist/css/bootstrap.min.css';
import { makeSelectListeBeneficiaires } from 'containers/Eps/FicheEps/selectors'; // Selector for fetching beneficiaries
import reducer from 'containers/Eps/FicheEps/reducer'; // Reducer for managing state
import saga from 'containers/Eps/FicheEps/saga'; // Saga for handling side effects
import { useInjectReducer } from 'redux-injectors';
import { useInjectSaga } from 'redux-injectors';
import tagStyles from 'Css/tag.css'; // Make sure the path to your styles is correct
import moment from 'moment';
const key = 'epsFiche';

// Selector for accessing the beneficiaries list from the Redux store
const omdbSelector = createStructuredSelector({
  listBeneficiaries: makeSelectListeBeneficiaires,
});
const formatDate = date => moment(date).format('DD/MM/YYYY');

// Mapping status codes to French status names
const statusMap = {
  beneficiary_actif: 'Bénéficiaire actif',
  beneficiary_ancien:"Bénéficiaire ancien",
  beneficiary_enattente: 'Candidat',
  candidat_rejete: 'Candidat rejeté',
  beneficiaire_rejete: 'Bénéficiaire rejeté'
  
};
const getTag = beneficiaryStatutId => {
  switch (beneficiaryStatutId) {
    case 'candidat_initial':
      return tagStyles.tagGrey; // "Candidat initial"

    case 'candidat_valider_assistance':
      return tagStyles.tagGreenLight; // "Candidat validé pour assistance"

    case 'candidat_valider_kafalat':
      return tagStyles.tagGreen; // "Candidat validé pour kafalat"

    case 'candidat_rejete':
      return tagStyles.tagRed; // "Candidat rejeté"

    case 'beneficiaire_rejete':
      return tagStyles.tagRed; // "Bénéficiaire rejeté"

    case 'candidat_a_completer_par_assistance':
      return tagStyles.tagOrange; // "Candidat à compléter par assistance"

    case 'candidat_a_completer_par_kafalat':
      return tagStyles.tagYellow; // "Candidat à compléter par kafalat"

    case 'beneficiary_ancien':
      return tagStyles.tagGrey; // "Bénéficiaire ancien"

    case 'beneficiary_enattente':
      return tagStyles.tagYellow; // "Bénéficiaire en attente"

    case 'beneficiary_actif':
      return tagStyles.tagGreen; // "Bénéficiaire actif"

    case 'candidat_a_updater':
      return tagStyles.tagBlue; // "Candidat à mettre à jour"

    default:
      return tagStyles.tagGrey; // Default style
  }
};
const categories = [
  { id: 1, name: 'Étudiant' },
  { id: 2, name: 'Orphelin' },
  { id: 3, name: 'Veuve' },
  { id: 4, name: 'Handicapé' },
  { id: 5, name: 'Famille' },
  { id: 6, name: 'Enfant en Difficulté' },
];

export default function EpsListBeneficiaires(props) {
  const hasZone = props.hasZone;
  useInjectReducer({ key, reducer });
  useInjectSaga({ key, saga });

  const dispatch = useDispatch();
  const { id } = useParams(); // Get the `id` from the URL to load beneficiaries
  const [currentPage, setCurrentPage] = useState(1);
  const pageSize = 10;
  const [filters, setFilters] = useState({
    name: '',
    status: '',
    category: '',
    type: '',
  });

  // Access the state using the selector
  const { listBeneficiaries } = useSelector(omdbSelector);

  // Fetch the beneficiaries data when the component is mounted
  useEffect(() => {
    if (id) {
      dispatch(loadListeBeneficiaires(id)); // Dispatch the action to load beneficiaries for the given `id`
    }
  }, [dispatch, id]);

  // Function to map status codes to French status names
  const getStatusName = statusCode => {
    return statusMap[statusCode] || statusCode;
  };

  // Filtered data based on the filters state
  const filteredData = (listBeneficiaries || []).filter(beneficiary => {
    const { name, status, category, type } = filters;

    const fullName = beneficiary.fullName
      ? beneficiary.fullName.toLowerCase()
      : '';

    return (
      fullName.includes(name.trim().toLowerCase()) &&
      (status ? beneficiary.status === status : true) &&
      (category ? beneficiary.category === category : true) &&
      (type ? beneficiary.type === type : true)
    );
  });

  // Pagination logic
  const paginatedData = filteredData
    ? filteredData.slice((currentPage - 1) * pageSize, currentPage * pageSize)
    : [];

  // Columns for the DataTable
  const columns = [
    {
      field: 'fullName',
      headerName: 'Nom complet',
      flex: 0.25,
      headerAlign: 'center',
      align: 'center',
      renderCell: params => (
        <Link
          to={`/beneficiaries/fiche/${params.row.id}/info`}
          className={stylesList.linkStyle}
          onMouseOver={e => {
            e.currentTarget.style.color = '#0056b3';
            e.currentTarget.style.textDecorationColor = '#0056b3';
          }}
          onMouseOut={e => {
            e.currentTarget.style.color = '#007bff';
            e.currentTarget.style.textDecorationColor = '#007bff';
          }}
        >
          {params.row.fullName || '-'} {/* Use '-' if fullName is null */}
        </Link>
      ),
    },
    {
      field: 'code',
      headerName: 'Code bénéficiaire',
      flex: 0.25,
      headerAlign: 'center',
      align: 'center',
      renderCell: params => params.row.code || '-', // Use '-' if code is null
    },
    {
      field: 'type',
      headerName: 'Type',
      flex: 0.25,
      headerAlign: 'center',
      align: 'center',
      renderCell: params => params.row.type || '-', // Use '-' if type is null
    },
    {
      field: 'category',
      headerName: 'Catégorie',
      flex: 0.25,
      headerAlign: 'center',
      align: 'center',
      renderCell: params => params.row.category || '-', // Use '-' if category is null
    },
    {
      field: 'status',
      headerName: 'Statut',
      flex: 0.25,
      headerAlign: 'center',
      align: 'center',
      renderCell: params => (
        <span className={getTag(params.row.status)}>
          {getStatusName(params.row.status) || '-'} {/* Use '-' if status is null */}
        </span>
      ),
    },
  ];
  

  const handleResetFilter = () => {
    setFilters({ name: '', status: '', category: '', type: '' });
  };

  return (
    <div className={`pb-5 ${stylesList.backgroundStyle}`}>
      {hasZone ? (
        <div className={stylesList.global}>
          <div className={stylesList.header}>
            <h4>Liste des bénéficiaires</h4>
          </div>

          <Form>
            <Row className="d-flex align-items-center">
              <Col md={2}>
                <Form.Group controlId="filterName">
                  <Form.Label>Nom</Form.Label>
                  <Form.Control
                    type="text"
                    placeholder="Filtrer par nom"
                    value={filters.name}
                    onKeyDown={e => {
                      // If the key pressed is Enter, prevent the default action
                      if (e.key === 'Enter') {
                        e.preventDefault();
                      }
                    }}
                    onChange={e =>
                      setFilters({ ...filters, name: e.target.value })
                    }
                  />
                </Form.Group>
              </Col>

              <Col md={2}>
                <Form.Group controlId="filterStatus">
                  <Form.Label>Statut</Form.Label>
                  <Form.Control
                    as="select"
                    value={filters.status}
                    onChange={e =>
                      setFilters({ ...filters, status: e.target.value })
                    }
                  >
                    <option value="">Tous</option>
                    {Object.keys(statusMap).map(key => (
                      <option key={key} value={key}>
                        {statusMap[key]} {/* Display French status names */}
                      </option>
                    ))}
                  </Form.Control>
                </Form.Group>
              </Col>

              <Col md={2}>
                <Form.Group controlId="filterCategory">
                  <Form.Label>Catégorie</Form.Label>
                  <Form.Control
                    as="select"
                    value={filters.category}
                    onChange={e =>
                      setFilters({ ...filters, category: e.target.value })
                    }
                  >
                    <option value="">Tous</option>
                    {categories.map(category => (
                      <option key={category.id} value={category.code}>
                        {category.name}
                      </option>
                    ))}
                  </Form.Control>
                </Form.Group>
              </Col>

              <Col md={2}>
                <Form.Group controlId="filterType">
                  <Form.Label>Type</Form.Label>
                  <Form.Control
                    as="select"
                    value={filters.type}
                    onChange={e =>
                      setFilters({ ...filters, type: e.target.value })
                    }
                  >
                    <option value="">Tous</option>
                    <option value="Membre de famille">Membre de famille</option>
                    <option value="Indépendant">Indépendant</option>
                  </Form.Control>
                </Form.Group>
              </Col>

              <Col md={1} className="d-flex justify-content-end">
                <button
                  style={{
                    marginTop: '20px',
                    height: '40px',
                    width: '40px',
                  }}
                  title="Réinitialiser les filtres"
                  type="button"
                  className="btn btn-secondary"
                  onClick={handleResetFilter}
                >
                  <i className="fas fa-undo"></i> {/* Reset icon */}
                </button>
              </Col>
            </Row>
          </Form>

          <>
            <DataTable
              rows={paginatedData} // Display paginated data
              columns={columns} // Columns configuration
              fileName={`Liste des bénéficiaires ${new Date().toLocaleString()}`}
            />
            <div className="row justify-content-center my-4">
              <CustomPagination
                totalCount={Math.ceil(filteredData.length / pageSize)} // Total number of pages
                pageSize={pageSize} // Number of records per page
                currentPage={currentPage} // Current page
                onPageChange={page => setCurrentPage(page)} // Function to handle page change
              />
            </div>
          </>
        </div>
      ) : (
        <div className=" text-center mt-2">
          <div
            style={{
              backgroundColor: '#d0e3ff',
              borderRadius: '5px',
              color: 'GrayText',
              padding: '10px',
              fontWeight: 'bold',
            }}
          >
            Cette EPS n'a pas de bénéficiaires car elle n'est pas associée à une
            zone.
          </div>
        </div>
      )}
    </div>
  );
}
