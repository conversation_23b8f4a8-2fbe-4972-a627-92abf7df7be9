import produce from 'immer';
import {
  LOAD_RELEVE_DONOR,
  LOAD_RELEVE_DONOR_ERROR,
  LOAD_RELEVE_DONOR_SUCCESS,
  LOAD_CANAL_DONATIONS,
  LOAD_CANAL_DONATIONS_SUCCESS,
  LOAD_CANAL_DONATIONS_ERROR,
  LOAD_SERVICECATEGORIES_ERROR,
  LOAD_SERVICECATEGORIES_SUCCESS,
  LOAD_SERVICECATEGORIES,
  LOAD_SERVICES,
  LOAD_SERVICES_SUCCESS,
  LOAD_SERVICES_ERROR,
} from './constants';

export const initialState = {
  loading: false,
  error: false,
  success: false,
  releves: [],
  canalDonations: false,
  serviceCategories: false,
  services: false,
};

/* eslint-disable default-case, no-param-reassign */
const donorReducer = produce((draft, action) => {
  switch (action.type) {
    case LOAD_RELEVE_DONOR:
      draft.loading = true;
      draft.error = false;
      draft.releves = false;
      break;
    case LOAD_RELEVE_DONOR_SUCCESS:
      draft.loading = false;
      draft.error = false;
      draft.releves = action.releves;
      break;
    case LOAD_RELEVE_DONOR_ERROR:
      draft.loading = false;
      draft.error = action.error;
      break;
    case LOAD_CANAL_DONATIONS:
      draft.loading = true;
      draft.error = false;
      draft.canalDonations = false;
      break;
    case LOAD_CANAL_DONATIONS_SUCCESS:
      draft.loading = false;
      draft.error = false;
      draft.canalDonations = action.canalDonations;
      break;
    case LOAD_CANAL_DONATIONS_ERROR:
      draft.loading = false;
      draft.error = action.error;
      break;
    case LOAD_SERVICECATEGORIES:
      draft.loading = true;
      draft.error = false;
      draft.serviceCategories = false;
      break;
    case LOAD_SERVICECATEGORIES_SUCCESS:
      draft.loading = false;
      draft.error = false;
      draft.serviceCategories = action.serviceCategories;
      break;
    case LOAD_SERVICECATEGORIES_ERROR:
      draft.loading = false;
      draft.error = action.error;
      break;
    case LOAD_SERVICES:
      draft.error = false;
      draft.services = false;
      break;
    case LOAD_SERVICES_SUCCESS:
      draft.error = false;
      draft.services = action.services;
      break;
    case LOAD_SERVICES_ERROR:
      draft.error = action.error;
      break;
  }
}, initialState);

export default donorReducer;
