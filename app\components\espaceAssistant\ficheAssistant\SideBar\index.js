import React from 'react';
import sideBarStyles from '../../../../Css/sideBar.css';
import tagStyles from '../../../../Css/tag.css';
import moment from 'moment';

const AssistantSideBar = props => {
  const connectedAssistant = props.data;
  const image = require('../../../../images/user.png');

  const getTag = () => {
    return connectedAssistant.status === true
      ? tagStyles.tagGreen // Active
      : tagStyles.tagRed; // Inactive
  };

  const formatDate = date => moment(date).format('DD/MM/YYYY');

  const renderLanguages = () => {
    if (
      connectedAssistant.languageCommunicationDetails &&
      connectedAssistant.languageCommunicationDetails.length > 0
    ) {
      return connectedAssistant.languageCommunicationDetails
        .map(lang => lang.name)
        .join(', ');
    }
    return 'Non spécifié';
  };

  // non spécifié with style
  const NonSpecified = () => {
    return (
      <span style={{ color: '#999', fontStyle: 'italic' }}>Non spécifié</span>
    );
  };

  const topSection = (
    <div className={`${sideBarStyles.top} ${sideBarStyles.section}`}>
      <div className={sideBarStyles.profileHeader}>
        <img
          src={
            connectedAssistant.picture64 == null
              ? image
              : `data:image/png;base64,${atob(connectedAssistant.picture64)}`
          }
          alt="Profile"
          className={`rounded-circle ${sideBarStyles.imgBorder}`}
          style={{
            width: '100px',
            height: '100px',
            objectFit: 'cover',
            borderRadius: '50%',
            border: '2px solid #ddd',
            marginBottom: '20px',
          }}
        />
      </div>
      <div className={sideBarStyles.info}>
        <h5>
          {connectedAssistant.firstName} {connectedAssistant.lastName}
        </h5>
        <p>{connectedAssistant.code}</p>
        {connectedAssistant.status !== undefined && (
          <div className={getTag()}>
            {connectedAssistant.status ? 'Actif' : 'Inactif'}
          </div>
        )}
      </div>
    </div>
  );

  const detailsSection = (
    <div
    style={{marginLeft: '2rem'}}
     className={`${sideBarStyles.data} ${sideBarStyles.section}`}>
      <h5
        style={{
          textAlign: 'center',
          marginBottom: '20px',
          fontSize: '22px',
          fontWeight: '600',
          color: '#333',
        }}
      >
        Détails de l'assistant
      </h5>

      <p>
        <strong>Adresse : </strong>{' '}
        {connectedAssistant.address || NonSpecified()}
      </p>

      <p>
        <strong>Email : </strong> {connectedAssistant.email || NonSpecified()}
      </p>

      <p>
        <strong>Date de naissance : </strong>{' '}
        {connectedAssistant.birthDate
          ? formatDate(connectedAssistant.birthDate)
          : NonSpecified()}
      </p>
      <p>
        <strong>Niveau scolaire :</strong>{' '}
        {connectedAssistant.schoolLevel && connectedAssistant.schoolLevel.name
          ? connectedAssistant.schoolLevel.name
          : NonSpecified()}
      </p>
      <p>
        <strong>Langues de communication :</strong> {renderLanguages()}
      </p>

      


    </div>
  );
  

  return (
    <div className={`${sideBarStyles.sideBar}`}>
      {topSection}
      <hr className={sideBarStyles.hr} />
      <div className={sideBarStyles.text}>{detailsSection}</div>
    </div>
  );
};

export default AssistantSideBar;
