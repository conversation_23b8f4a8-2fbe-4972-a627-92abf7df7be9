import React from 'react';
import { Link } from 'react-router-dom';
import sideBarStyles from '../../../../Css/sideBar.css';

const rightArrow = require('../../../../images/icons/right-arrow.svg');

const SideBar = props => {
  const caisse = props.data;

  let top = null;
  const text = null;

  return (
    <>
      <div className={sideBarStyles.sideBar}>
        {top}
        <hr className={sideBarStyles.hr} />
        <div className={sideBarStyles.text}>
          <div className={`text-center ${sideBarStyles.label}`}>
            <h5 className="mb-3">code de la caisse : </h5>
            <p className={sideBarStyles.linkDonor}>{caisse.code}</p>
            <h5 className="mb-3">Nom de la caisse : </h5>
            <p className={sideBarStyles.linkDonor}>{caisse.name}</p>
            <h5 className="mb-3">Type de la caisse : </h5>
            <p className={sideBarStyles.linkDonor}>
              {caisse.typePriseEnChargeDescription &&
                caisse.typePriseEnChargeDescription.name}
            </p>
            <h5 className="mb-3">Montant de la caisse : </h5>
            <p className={sideBarStyles.linkDonor}>{caisse.amount}</p>
          </div>
        </div>
      </div>
    </>
  );
};

export default SideBar;
