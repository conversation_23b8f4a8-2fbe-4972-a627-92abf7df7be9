import { createSelector } from 'reselect';
import { initialState } from './reducer';

const selectOmdb = state => state.aideComplementaireList || initialState;

const makeSelectAideComplementaire = createSelector(
  selectOmdb,
  omdbState => omdbState.aideComplementaire,
);

const makeSelectLoading = createSelector(
  selectOmdb,
  omdbState => omdbState.loading,
);

const makeSelectError = createSelector(
  selectOmdb,
  omdbState => omdbState.error,
);

const makeSekectExporting = createSelector(
  selectOmdb,
  omdbState => omdbState.exportLoading,
);

const makeSekectDeleteAideComplementaireSuccess = createSelector(
  selectOmdb,
  omdbState => omdbState.deleteAideComplementaireSuccess,
);

const makeSekectDeleteAideComplementaireError = createSelector(
  selectOmdb,
  omdbState => omdbState.deleteAideComplementaireError,
);

export {
  selectOmdb,
  makeSelectAideComplementaire,
  makeSelectLoading,
  makeSelectError,
  makeSekectExporting,
  makeSekectDeleteAideComplementaireSuccess,
  makeSekectDeleteAideComplementaireError,
};
