import React, { useState, useEffect, useRef } from 'react';
import moment from 'moment';
import { Col, OverlayTrigger, Row, Tooltip, Form, Alert } from 'react-bootstrap';
import AutorenewIcon from '@mui/icons-material/Autorenew';
import SearchOutlined from '@mui/icons-material/SearchOutlined';
import { CircularProgress } from '@material-ui/core';
import styles from '../../../../../Css/profileList.css';
import { useDispatch, useSelector } from 'react-redux';
import { createStructuredSelector } from 'reselect';
import { useInjectReducer, useInjectSaga } from 'redux-injectors';
import CustomPagination from '../../../../Common/CustomPagination';
import BeneficiaryWithoutDonorTable from '../../../../Common/BeneficiaryWithoutDonorTable';
import saga from 'containers/AideComplementaire/FicheAideComplementaire/saga';
import reducer from 'containers/AideComplementaire/FicheAideComplementaire/reducer';
import {
  makeSelectPagebleBeneficiariesForAideComplementaire,
  makeSelectLoading2,
  makeSelectValidateAllBeneficiariesLoading,
  makeSelectValidateAllBeneficiariesSuccess,
  makeSelectValidateAllBeneficiariesError,
  makeSelectValidateAllBeneficiariesMessage,
  makeSelectValidateAllBeneficiariesHasFilters
} from 'containers/AideComplementaire/FicheAideComplementaire/selectors';
import {
  getAideBeneficiariesRequest,
  updateStatutValidationBeneficiaryRequest,
  validateAllBeneficiariesRequest,
  validateAllBeneficiariesReset,
  loadAideComplementaire
} from 'containers/AideComplementaire/FicheAideComplementaire/actions';

const key = "aideComplementaireFiche";
const formatDate = date => moment(date).format('DD/MM/YYYY');

const omdbSelector = createStructuredSelector({
  beneficiaries: makeSelectPagebleBeneficiariesForAideComplementaire,
  loading: makeSelectLoading2,
  validateAllLoading: makeSelectValidateAllBeneficiariesLoading,
  validateAllSuccess: makeSelectValidateAllBeneficiariesSuccess,
  validateAllError: makeSelectValidateAllBeneficiariesError,
  validateAllMessage: makeSelectValidateAllBeneficiariesMessage,
  validateAllHasFilters: makeSelectValidateAllBeneficiariesHasFilters,
});

export default function BeneficiariesWithOutDonors({ data: aideComplementaire }) {
  const [activePage, setActivePage] = useState(0);
  const pageSize = 8;
  const [showAlert, setShowAlert] = useState(false);
  const alertTimeoutRef = useRef(null);

  useInjectReducer({ key, reducer });
  useInjectSaga({ key, saga });

  const dispatch = useDispatch();
  const {
    beneficiaries,
    validateAllLoading,
    validateAllSuccess,
    validateAllError,
    validateAllMessage,
    validateAllHasFilters
  } = useSelector(omdbSelector);

  const [filters, setFilters] = useState({
    searchFullName: '',
    category: '',
    type: '',
    relatedToDonor: '',
    validationStatus: ''
  });

  useEffect(() => {
    if (aideComplementaire.id) {
      dispatch(getAideBeneficiariesRequest(aideComplementaire.id, activePage, filters));
    }
  }, [aideComplementaire.id, activePage, dispatch]);

  const handlePageChange = pageNumber => {
    const nextPage = pageNumber - 1;
    if (nextPage >= 0 && nextPage < beneficiaries.totalPages) {
      setActivePage(nextPage);
      dispatch(getAideBeneficiariesRequest(aideComplementaire.id, nextPage));
    }
  };

  const handleFilterChange = (key, value) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  };

  const resetFilters = () => {
    const newFilters = {
      searchFullName: '',
      category: '',
      type: '',
      relatedToDonor: '',
      validationStatus: ''
    };
    setFilters(newFilters);
    applyFilter(newFilters);
  };

  const applyFilter = (newFilters) => {
    if(newFilters){
      dispatch(getAideBeneficiariesRequest(aideComplementaire.id, activePage, newFilters));
    }
    else{
      dispatch(getAideBeneficiariesRequest(aideComplementaire.id, activePage, filters));
    }
  };

  // Add effect to refresh the data after validation is complete
  useEffect(() => {
    if (validateAllSuccess) {
      // Show success message
      setShowAlert(true);

      // Clear any existing timeout
      if (alertTimeoutRef.current) {
        clearTimeout(alertTimeoutRef.current);
      }

      // Set timeout to hide the alert after 5 seconds
      alertTimeoutRef.current = setTimeout(() => {
        setShowAlert(false);
      }, 5000);

      // Reload the aide complementaire data to update the count of validated beneficiaries
      dispatch(loadAideComplementaire(aideComplementaire.id));

      // Refresh the beneficiaries list with current filters after validation
      dispatch(getAideBeneficiariesRequest(aideComplementaire.id, activePage, filters));

      // Reset the validation state
      dispatch(validateAllBeneficiariesReset());
    }

    // Cleanup timeout on unmount
    return () => {
      if (alertTimeoutRef.current) {
        clearTimeout(alertTimeoutRef.current);
      }
    };
  }, [validateAllSuccess, aideComplementaire.id, activePage, filters, dispatch]);

  // Handle validation errors
  useEffect(() => {
    if (validateAllError) {
      alert("Une erreur s'est produite lors de la validation des bénéficiaires");
      // Reset the validation state
      dispatch(validateAllBeneficiariesReset());
    }
  }, [validateAllError, dispatch]);

  const handleValidateAll = async () => {
    if (!beneficiaries || !beneficiaries.content) return;

    // Calculate total amount to be validated
    const totalAmountToValidate = beneficiaries.content.reduce((sum, beneficiary) => {
      if (!beneficiary.statutValidation) {
        return sum + (beneficiary.type !== "Groupe"
          ? (beneficiary.montantAbeneficier || 0)
          : ((beneficiary.montantAbeneficier || 0) * beneficiary.numberOfMembers));
      }
      return sum;
    }, 0);

    // Check if remaining amount is sufficient
    if (totalAmountToValidate > (aideComplementaire.montantPrevu - aideComplementaire.montantAffecte)) {
      alert("Le montant restant n'est pas suffisant pour valider tous les bénéficiaires");
      return;
    }

    // Create filter object for the API call - only include non-empty values
    const apiFilters = {
      category: filters.category,
      type: filters.type,
      withDonor: filters.relatedToDonor === 'true' ? true :
                filters.relatedToDonor === 'false' ? false : null,
      text: filters.searchFullName
    };

    // Call the validateAll API endpoint
    dispatch(validateAllBeneficiariesRequest(aideComplementaire.id, apiFilters));
  };



  return (
    <div className={styles.global}>
      <div className={styles.content}>
        {showAlert && validateAllMessage && (
          <Alert
            variant="success"
            onClose={() => setShowAlert(false)}
            dismissible
            className="mb-3"
          >
            {validateAllMessage}
          </Alert>
        )}

        <div className={styles.header}>
          <Row className="align-items-center">
            <Col>
              <h4>Liste des bénéficiaires</h4>
            </Col>
          </Row>
        </div>

        <div className="filter-section mb-4 p-4 rounded border bg-light shadow-sm">
          <Row className="align-items-center">
            <Col md={4}>
              <input
                type="text"
                placeholder="Recherche par nom bénéficiaire ou donateur"
                value={filters.searchFullName}
                onChange={e => handleFilterChange('searchFullName', e.target.value)}
                className="form-control mb-2"
                style={{ borderColor: '#ced4da', fontSize: '1rem' }}
              />
            </Col>
            <Col md={2}>
              <select
                value={filters.category}
                onChange={e => handleFilterChange('category', e.target.value)}
                className="form-control mb-2"
                style={{ borderColor: '#ced4da', fontSize: '1rem' }}
              >
                <option value="">Catégorie</option>
                <option value="beneficiary_actif">Bénéficiaire Kafalat</option>
                <option value="beneficiary_enattente">Candidat</option>
                <option value="beneficiary_ad_hoc_individual">Bénéficiaire Ad Hoc</option>
              </select>
            </Col>
            <Col md={3}>
              <select
                value={filters.type}
                onChange={e => handleFilterChange('type', e.target.value)}
                className="form-control mb-2"
                style={{ borderColor: '#ced4da', fontSize: '1rem' }}
              >
                <option value="">Type</option>
                <option value="Indépendant">Indépendant</option>
                <option value="Membre Famille">Membre Famille</option>
                <option value="Groupe">Groupe</option>
              </select>
            </Col>
            <Col md={3}>
              <select
                value={filters.relatedToDonor}
                onChange={e => handleFilterChange('relatedToDonor', e.target.value)}
                className="form-control mb-2"
                style={{ borderColor: '#ced4da', fontSize: '1rem' }}
              >
                <option value="">Donateur lié</option>
                <option value="true">Oui</option>
                <option value="false">Non</option>
              </select>
            </Col>
            <Col md={3}>
              <select
                value={filters.validationStatus}
                onChange={e => handleFilterChange('validationStatus', e.target.value)}
                className="form-control mb-2"
                style={{ borderColor: '#ced4da', fontSize: '1rem' }}
              >
                <option value="">Statut Validation</option>
                <option value="true">Validé</option>
                <option value="false">Non Validé</option>
              </select>
            </Col>
            <Col md={1} className="text-end">
              <OverlayTrigger
                placement="bottom"
                overlay={<Tooltip id="reset-tooltip">Réinitialiser</Tooltip>}
              >
                <button
                  className="btn btn-outline-secondary"
                  onClick={resetFilters}
                  style={{ padding: '0.375rem 0.75rem' }}
                >
                  <AutorenewIcon style={{ fontSize: '1.2rem' }} />
                </button>
              </OverlayTrigger>
            </Col>
            <Col md={1} className="text-end">
              <OverlayTrigger
                placement="bottom"
                overlay={<Tooltip id="search-tooltip">Appliquer</Tooltip>}
              >
                <button
                  className="btn btn-primary"
                  onClick={() => applyFilter()}
                  style={{ padding: '0.375rem 0.75rem' }}
                >
                  <SearchOutlined style={{ fontSize: '1.2rem' }} />
                </button>
              </OverlayTrigger>
            </Col>
            <Col md={4} className="text-end">
              <button
                className="btn btn-primary"
                onClick={handleValidateAll}
                disabled={validateAllLoading}
                style={{
                  borderRadius: '20px',
                  padding: '8px 20px',
                  fontSize: '0.9rem',
                  fontWeight: '500'
                }}
              >
                {validateAllLoading ? (
                  <>
                    <CircularProgress size={16} color="inherit" style={{ marginRight: '8px' }} />
                    Validation...
                  </>
                ) : (
                  'Valider tous'
                )}
              </button>
            </Col>
          </Row>
        </div>

        {beneficiaries.content && (
          <BeneficiaryWithoutDonorTable
            beneficiaryAideComplemenatireDTOList={beneficiaries.content}
            aideComplementaire={aideComplementaire}
          />
        )}

        <div className="row justify-content-center my-4">
          {beneficiaries && beneficiaries.totalElements > 0 && (
            <CustomPagination
              totalCount={beneficiaries.totalPages}
              totalElements={beneficiaries.numberOfElements}
              pageSize={pageSize}
              currentPage={activePage + 1}
              onPageChange={handlePageChange}
            />
          )}
        </div>
      </div>
    </div>
  );
}