import React, { useEffect, useState } from 'react';
import moment from 'moment';
import styles from 'Css/profileList.css';
import DataTable from 'components/Common/DataTable';
import stylesTag from 'Css/tag.css';
import CustomPagination from '../../../../Common/CustomPagination';
import btnStyles from "../../../../../Css/button.css";
import ResetIconSvg from "images/icons/ResetIconSvg";

const formatDate = date => (date ? moment(date).format('DD/MM/YYYY') : '-');

export default function BudgetLineTable({ data }) {
  const [gridData, setGridData] = useState([]);
  const [filteredData, setFilteredData] = useState([]);
  const [typeFilter, setTypeFilter] = useState(''); // Filter by type (Entrée/Sortie)
  const [yearFilter, setYearFilter] = useState(''); // Filter by year
  const [monthFilter, setMonthFilter] = useState(''); // Filter by month
  const [startDate, setStartDate] = useState(moment().startOf('year')); // Default start date
  const [endDate, setEndDate] = useState(moment().endOf('year')); // Default end date
  const pageSize = 5; // Define page size
  const [currentPage, setCurrentPage] = useState(1);

  // Handle pagination
  const startIndex = (currentPage - 1) * pageSize;
  const endIndex = Math.min(startIndex + pageSize, filteredData.length);
  const paginatedData = filteredData.slice(startIndex, endIndex);

  const getTheType = status => {
    if (['DISPONIBLE', 'RESERVED', 'REMAINING'].includes(status)) {
      return 'Entrée';
    } else {
      return 'Sortie';
    }
  };

  const getTheDate = (status, createdAt, executionDate) =>
    status === 'Entrée' ? formatDate(createdAt) : formatDate(executionDate);

  useEffect(() => {
    if (data) {
      const formattedData = data.map(line => ({
        id: line.id,
        code: line.code || '-',
        amount: line.amount || '-',
        type: getTheType(line.status) || '-',
        operationDate:
          getTheDate(line.status, line.createdAt, line.executionDate) || '-',
        createdAt: line.createdAt, // Keep original created date for filtering
      }));

      const filtered = formattedData.filter(line => {
        const matchesType = typeFilter ? line.type === typeFilter : true;
        const matchesStartDate = startDate
          ? moment(line.createdAt).isSameOrAfter(moment(startDate))
          : true;
        const matchesEndDate = endDate
          ? moment(line.createdAt).isSameOrBefore(moment(endDate))
          : true;

        return (
          matchesType &&
          matchesStartDate &&
          matchesEndDate
        );
      });

      setFilteredData(filtered);
      setCurrentPage(1); // Reset to first page when data changes
    }
  }, [data, typeFilter, startDate, endDate]);

  const dataGridColumns = [
    {
      field: 'code',
      headerName: 'Code',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
    {
      field: 'amount',
      headerName: 'Montant (DH)',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
    {
      field: 'type',
      headerName: 'Type',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
      renderCell: params => (
        <span
          className={
            params.row.type === 'Entrée' ? stylesTag.tagGreen : stylesTag.tagRed
          }
        >
          {params.row.type}
        </span>
      ),
    },
    {
      field: 'operationDate',
      headerName: 'Date d’Opération',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
  ];

  const resetFilters = () => {
    setMonthFilter('');
    setTypeFilter('');
    setStartDate(moment().startOf('year'));
    setEndDate(moment().endOf('year'));
    setCurrentPage(1);
  };

  return (
    <div className={styles.global}>
      <div className={styles.content}>
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            marginBottom: '20px',
          }}
        >
          <label style={{ marginRight: '10px' , marginTop: '7px' }}>Type :</label>
          <select
            value={typeFilter}
            onChange={e => setTypeFilter(e.target.value)}
            className="form-control"
            style={{ marginRight: '10px', width: '150px' }}
          >
            <option value="">Tous</option>
            <option value="Entrée">Entrée</option>
            <option value="Sortie">Sortie</option>
          </select>

          <label style={{ marginRight: '10px' ,  marginTop: '7px' }}>Date de Début :</label>
          <input
            type="date"
            value={startDate ? moment(startDate).format('YYYY-MM-DD') : ''}
            max={
              endDate
                ? moment(endDate)
                    .subtract(1, 'days')
                    .format('YYYY-MM-DD')
                : ''
            }
            onChange={e => setStartDate(e.target.value)}
            className="form-control"
            style={{ marginRight: '10px', width: '150px' }}
          />

          <label style={{ marginRight: '10px', marginTop: '7px' }}>Date de Fin :</label>
          <input
            type="date"
            value={endDate ? moment(endDate).format('YYYY-MM-DD') : ''}
            min={
              startDate
                ? moment(startDate)
                    .add(1, 'days')
                    .format('YYYY-MM-DD')
                : ''
            }
            onChange={e => setEndDate(e.target.value)}
            className="form-control"
            style={{ marginRight: '10px', width: '150px' }}
          />

          <button
            className={`reset-icon ${btnStyles.iconButton}`}
            onClick={resetFilters}
          >
            <ResetIconSvg />
          </button>
        </div>

        <DataTable
          rows={paginatedData}
          columns={dataGridColumns}
          fileName={`Liste des lignes budgétaires - ${new Date().toLocaleDateString()}`}
        />

        <div className="justify-content-center my-4">
          <CustomPagination
            totalElements={filteredData.length}
            totalCount={Math.ceil(filteredData.length / pageSize)}
            pageSize={pageSize}
            currentPage={currentPage}
            onPageChange={setCurrentPage}
          />
        </div>
      </div>
    </div>
  );
}
