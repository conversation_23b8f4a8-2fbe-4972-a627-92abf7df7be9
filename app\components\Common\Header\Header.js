import React from 'react';
import PropTypes from 'prop-types';
import { NavLink } from 'react-router-dom';
import { useSelector } from 'react-redux';
import styles from './Header.css';
import sofwaLogo from '../../../images/sofwa_logo.png';
import { createStructuredSelector } from 'reselect';
import { makeSelectConnectedUser } from 'containers/AdUser/selectors';

const omdbSelector = createStructuredSelector({
  connectedUser: makeSelectConnectedUser,
});

const Header = () => {
  const { connectedUser } = useSelector(omdbSelector);

  const hasViewAccess = module => {
    if (
      connectedUser &&
      connectedUser.profile &&
      connectedUser.profile.moduleFunctionalities &&
      connectedUser.profile.moduleFunctionalities[module] &&
      connectedUser.profile.moduleFunctionalities[module].includes('VIEW')
    ) {
      return true;
    }
    return false;
  };

  return (
    <div>
      <header className={styles.header}>
        <div className={styles.containerHeader}>
          <a href="#">
            <img src={sofwaLogo}
            style={{
              height: '70px',
              width: '70px',
            }}
             alt="Almobadara Logo" />
          </a>
          <div className={styles.mainNav}>
            <ul>
              <li>
                <NavLink exact to="/" activeClassName={styles.navLinkActive}>
                  ACCUEIL
                </NavLink>
              </li>
              {hasViewAccess('DONOR') && (
                <li>
                  <NavLink to="/donors" activeClassName={styles.navLinkActive}>
                    DONATEURS
                  </NavLink>
                </li>
              )}
              {hasViewAccess('BENEFICIARY') && (
                <li>
                  <NavLink
                    to="/beneficiaries"
                    activeClassName={styles.navLinkActive}
                  >
                    BENEFICIAIRES
                  </NavLink>
                </li>
              )}
              {hasViewAccess('FAMILLE') && (
                <li>
                  <NavLink
                    to="/families"
                    activeClassName={styles.navLinkActive}
                  >
                    FAMILLES
                  </NavLink>
                </li>
              )}
              {hasViewAccess('DONATION') && (
                <li>
                  <NavLink
                    to="/donations"
                    activeClassName={styles.navLinkActive}
                  >
                    DONATIONS
                  </NavLink>
                </li>
              )}
              {hasViewAccess('TAKEINCHARGE') && (
                <li>
                  <NavLink
                    to="/takenInCharges"
                    activeClassName={styles.navLinkActive}
                  >
                    PRISE EN CHARGE
                  </NavLink>
                </li>
              )}
              {hasViewAccess('USER') && (
                <li>
                  <NavLink
                    to="/utilisateur"
                    activeClassName={styles.navLinkActive}
                  >
                    UTILISATEURS
                  </NavLink>
                </li>
              )}
              <li>
                <NavLink to="/audit" activeClassName={styles.navLinkActive}>
                  PISTE AUDIT
                </NavLink>
              </li>
            </ul>
          </div>
        </div>
      </header>
      <div className={styles.afterHeader}></div>
    </div>
  );
};

Header.defaultProps = {
  branding: 'Almobadara',
};

Header.propTypes = {
  branding: PropTypes.string.isRequired,
};

export default Header;
