import { createSelector } from "reselect";
import { initialState } from "./reducer"

// Select raw state
const selectDocumentsRenew = state => state.documentsRenew || initialState;

const makeSelectDocumentsRenew = createSelector(
    selectDocumentsRenew,
    documentsRenew => documentsRenew.documentsRenewList
);

const makeSelectLoading = createSelector(
    selectDocumentsRenew,
    documentsRenew => documentsRenew.loading
);

const makeSelectError = createSelector(
    selectDocumentsRenew,
    documentsRenew => documentsRenew.error
);

const makeSelecDocumentViewSuccess = createSelector(
    selectDocumentsRenew,
    omdbState => documentsRenew.successView,
  );

export {
    selectDocumentsRenew,
    makeSelecDocumentViewSuccess,
    makeSelectDocumentsRenew,
    makeSelectLoading,
    makeSelectError,
};