import React, { useEffect, useRef, useState } from 'react';
import { Link, useLocation, useHistory } from 'react-router-dom';
import PropTypes from 'prop-types';
import moment from 'moment';
import stylesList from 'Css/profileList.css';
import AccessControl from 'utils/AccessControl';
import profile from '../../../../Css/personalInfo.css';
import btnStyles from '../../../../Css/button.css';
import ReactToPrint from 'react-to-print';
import Alert from 'react-bootstrap/Alert';

const formatDate = date => moment(date).format('DD/MM/YYYY');

export default function GeneralInfo({ data }) {
  const assistant = data; // Connected assistant data
  const location = useLocation();
  const history = useHistory();
  const [showSuccessMessage, setShowSuccessMessage] = useState(false);

  useEffect(() => {
    if (location.state === 'success') {
      setShowSuccessMessage(true);
    }
  }, [location.state]);

  useEffect(() => {
    if (showSuccessMessage) {
      const timer = setTimeout(() => {
        handleCloseSuccessMessage();
      }, 4000);
      return () => clearTimeout(timer);
    }
  }, [showSuccessMessage]);

  const handleCloseSuccessMessage = () => {
    setShowSuccessMessage(false);
    history.replace({ ...location, state: null });
  };

  const emptyValue = <span> ----</span>;
  const componentRef = useRef();

  const content = assistant ? (
    <div className={profile.content}>
      {/* SECTION 1 */}
      <div className={profile.section1}>
        <div className={profile.top}>
          <div className={`${profile.label1} ${profile.data1}`}>
            <p>
              <span style={{ fontWeight: 'bold' }}>Nom : </span>
              <span style={{ fontWeight: 'normal' }}>
                {assistant.lastName ? assistant.lastName : emptyValue}
              </span>
            </p>
            <p>
              <span style={{ fontWeight: 'bold' }}>Prénom : </span>
              <span style={{ fontWeight: 'normal' }}>
                {assistant.firstName ? assistant.firstName : emptyValue}
              </span>
            </p>
            <p>
              <span style={{ fontWeight: 'bold' }}>Email : </span>
              <span style={{ fontWeight: 'normal' }}>
                {assistant.email ? assistant.email : emptyValue}
              </span>
            </p>
            <p>
              <span style={{ fontWeight: 'bold' }}>Téléphone : </span>
              <span style={{ fontWeight: 'normal' }}>
                {assistant.phone ? assistant.phone : emptyValue}
              </span>
            </p>
            <p>
              <span style={{ fontWeight: 'bold' }}>Rôle : </span>
              <span style={{ fontWeight: 'normal' }}>
                {assistant.role ? assistant.role : emptyValue}
              </span>
            </p>
            <p>
              <span style={{ fontWeight: 'bold' }}>Date d'inscription : </span>
              <span style={{ fontWeight: 'normal' }}>
                {assistant.createdAt ? formatDate(assistant.createdAt) : emptyValue}
              </span>
            </p>
          </div>
        </div>
      </div>
    </div>
  ) : (
    <p>Aucune donnée disponible</p>
  );

  return (
    <div>
      {showSuccessMessage && (
        <Alert
          className="alert-style"
          variant="success"
          onClose={handleCloseSuccessMessage}
          dismissible
        >
          <p>Assistant connecté modifié avec succès</p>
        </Alert>
      )}
      <div className={stylesList.backgroudStyle}>
        <div className={profile.personalInfo}>
          <div className={profile.header}>
            <h4>Informations générales</h4>
            <div style={{ display: 'flex', alignItems: 'center' }}>
                <Link
                  to={{
                    pathname: `/assistants/edit/${assistant.id}`,
                    state: { redirectTo: 'info' },
                  }}
                >
                  <button className={btnStyles.editBtnProfile}>Modifier</button>
                </Link>
            </div>
          </div>
          {content}
        </div>
      </div>
    </div>
  );
}

GeneralInfo.propTypes = {
  data: PropTypes.object.isRequired,
};
