import React from 'react';
import { Link, useHistory } from 'react-router-dom';
import sideBarStyles from '../../../../Css/sideBar.css';
import tagStyles from '../../../../Css/tag.css';

const rightArrow = require('../../../../images/icons/right-arrow.svg');

const SideBar = props => {
  const history = useHistory();
  const donation = props.data;

  let top = null;
  let donorName = '--';
  let donorType = 'Non identifié';

  let totalDisponible = 0;
  let totalReserve = 0;
  let totalExecute = 0;

  // Function to determine tag style based on donation type
  const getTag = () => {
    switch (donation.type) {
      case 'Financière':
        return tagStyles.tagRed;
      case 'Nature':
        return tagStyles.tagYellow;
      default:
        return tagStyles.tagGrey;
    }
  };

  // Process donor details
  if (donation) {
    if (donation.identifiedDonor) {
      const { donor } = donation;
      switch (donor.type) {
        case 'Physique':
          donorName = (
            <Link to={`/donors/fiche/${donor.id}/info`}>
              <p className={sideBarStyles.linkDonor}>
                {`${donor.firstName} ${donor.lastName}`}
                <img
                  src={rightArrow}
                  width="20px"
                  height="20px"
                  className="ml-3"
                  alt="Right Arrow"
                />
              </p>
            </Link>
          );
          donorType = 'Physique';
          break;
        case 'Moral':
          donorName = (
            <Link to={`/donors/fiche/${donor.id}/info`}>
              <p className={sideBarStyles.linkDonor}>
                {donor.company}
                <img
                  src={rightArrow}
                  width="20px"
                  height="20px"
                  className="ml-3"
                  alt="Right Arrow"
                />
              </p>
            </Link>
          );
          donorType = 'Moral';
          break;
        case 'Anonyme':
          donorName = (
            <Link to={`/donors/fiche/${donor.id}/info`}>
              <p className={sideBarStyles.linkDonor}>
                {donor.name}
                <img
                  src={rightArrow}
                  width="20px"
                  height="20px"
                  className="ml-3"
                  alt="Right Arrow"
                />
              </p>
            </Link>
          );
          donorType = 'Anonyme';
          break;
      }
    } else {
      donorName = donation.unidentifiedDonorName;
    }

    // Calculate donation totals
    if (donation.donor && donation.donor.donations) {
      donation.donor.donations.forEach(don => {
     
          don.budgetLines.forEach(budgetLine => {
              // let make full the totla disponible 
              if(budgetLine.status === 'DISPONIBLE'){
                totalDisponible += budgetLine.amount;
              }
              // let make full the totla reserve
              if(budgetLine.status === 'RESERVED'){
                totalReserve += budgetLine.amount;
              }

              // let make full the tot
              if(budgetLine.status === 'EXECUTED'){
                totalExecute += budgetLine.amount;
              }
               
          });
     
        
        
      });
    }

    // Sidebar top section
    top = (
      <div className={sideBarStyles.topCard}>
        <div className={`${sideBarStyles.top}`}>
          <h5>Donation</h5>
          <p>{donation.code}</p>
          {donation.type && (
            <div className={getTag()}>{`${donation.type}`}</div>
          )}
        </div>
      </div>
    );
  }

  return (
    <>
      <div className={sideBarStyles.sideBar}>
        {top}

        <div className={`mt-5 d-flex flex-column ${sideBarStyles.text}`}>
          <div className={`text-center ${sideBarStyles.label}`}>
            <h5>Donateur {donorType}</h5>
            <p className={sideBarStyles.linkDonor}>{donorName}</p>
            <div className={`text-center ${sideBarStyles.label}`}>
              <h5>Synthèse de la donation</h5>
              <div
                className={sideBarStyles.text}
                style={{
                  display: 'flex',
                  flexDirection: 'column',
                  gap: '12px',
                  marginTop: '20px',
                }}
              >
                {[
                   {
                    label: 'Total disponible',
                    value: totalDisponible,
                    color: '#2e8b57',
                  },
                  
                  {
                    label: 'Total Réservé',
                    value: totalReserve,
                    color: 'darkgoldenrod',
                  },
                 
                  {
                    label: 'Total Exécuté',
                    value: totalExecute,
                    color: '#b22222',
                  },
                ].map((item, idx) => (
                  <div
                    key={idx}
                    style={{
                      display: 'flex',
                      justifyContent: 'space-between',
                      alignItems: 'center',
                      marginBottom: '8px',
                    }}
                  >
                    <div style={{ fontWeight: '600', color: '#555' }}>
                      {item.label}
                    </div>
                    <button
                      style={{
                        fontWeight: '600',
                        padding: '6px 12px',
                        border: 'none',
                        borderRadius: '5px',
                        cursor: 'pointer',
                        backgroundColor: item.color,
                        color: 'white',
                        boxShadow: '0px 2px 4px rgba(0, 0, 0, 0.15)',
                        fontSize: '14px',
                      }}
                    >
                      {item.value} DH
                    </button>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>

        <hr className={`${sideBarStyles.hr} mt-3 mb-3`} />

        {/* Donation Summary */}
      </div>
      <div className=" d-flex justify-content-center">
        <button
          onClick={() => history.push('/donations')}
          className="btn-style secondary w-100"
        >
          Quitter
        </button>
      </div>
    </>
  );
};

export default SideBar;
