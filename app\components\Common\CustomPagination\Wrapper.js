/* customPaginationStyles.css */
import styled from 'styled-components';

const Wrapper = styled.div`
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;

  .MuiPaginationItem-previousNext {
    border: 1px solid #43556880 !important;
  }

  .MuiPaginationItem-root {
    border: none;
    font-weight: 500;
    font-size: 14px;
  }
  .MuiPaginationItem-root.Mui-selected {
    color: #ffffff;
    font-weight: 700;
    background-color: #435568 !important;
  }
`;
export default Wrapper;
