.chartContainer {
  border-radius: 10px; /* Slightly smaller radius */
  padding: 10px; /* Reduced padding for a tighter look */
  background-color: #ffffff; /* White background for a cleaner look */
  box-shadow: 0 1px 5px rgba(0, 0, 0, 0.1); /* Softer shadow */
}

.journalDateInput {
  margin: 0 3px; /* Smaller margin for inputs */
  padding: 8px; /* Reduced padding */
  border-radius: 4px; /* Slightly smaller radius */
  border: 1px solid #4F89D7; /* Primary color border */
  font-size: 14px; /* Smaller font size */
  transition: border-color 0.2s, box-shadow 0.2s; /* Transition for focus */
}

.journalDateInput:focus {
  border-color: #0056b3; /* Darker color on focus */
  box-shadow: 0 0 4px rgba(0, 86, 179, 0.5); /* Focus shadow */
  outline: none; /* Remove default outline */
}

.tooltip {
  position: relative;
  display: inline-block;
  border-bottom: 1px dotted #555; /* Darker dotted line for tooltip */
  background-color: #ffffff; /* Tooltip background */
  padding: 4px; /* Slightly larger padding */
  border-radius: 4px; /* Rounded corners for tooltips */
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1); /* Soft shadow */
}

.kpiContainer {
  display: flex;
  justify-content: space-around;
  margin-bottom: 5px; /* Reduced spacing between KPIs and charts */
}

.kpiItem {
  margin: 0 3px; /* Spacing between KPI items */
  padding: 5px; /* Padding for a more compact appearance */
  border-radius: 10px; /* Slightly smaller rounded corners */
  background-color: #e9f7fe; /* Softer light blue background */
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1); /* Softer shadow */
  transition: transform 0.2s, box-shadow 0.2s; /* Smooth transitions */
}

.kpiItem:hover {
  transform: translateY(-3px); /* Slight lift on hover */
  box-shadow: 0 4px 4px rgba(0, 0, 0, 0.2); /* Enhanced shadow on hover */
}

.kpiHeader {
  display: flex;
  align-items: center;
  margin-bottom: 2px; /* Reduced space below header */
}

.kpiIcon {
  font-size: 1.2rem; /* Slightly larger icon size */
  color: #4F89D7; /* Primary color for icons */
  margin-right: 5px; /* Reduced space */
  margin-bottom: 2px;
  font-weight: 800;
}

.kpiTitle {
  font-size: 1.2rem; /* Smaller title size */
  font-weight: 600;
  color: #022a55; /* Title color */
  margin-top: 5px; /* Reduced space above title */
}

.kpiValue {
  font-size: 1rem; /* Smaller font size for values */
  font-weight: 500;
  color: #28a745; /* Green for positive values */
}

.kpiComparison {
  font-size: 0.9rem; /* Smaller comparison text */
  color: #bda20d; /* Light grey */
}

.kpiDifference {
  font-size: 0.9rem; /* Smaller font size for difference */
  color: #dc3545; /* Red for negative trends */
  font-weight: 600; /* Bold for differences */
}

.kpiLabel {
  font-weight: bold; /* Make the label bold for emphasis */
  margin: 3px 0; /* Reduced spacing above and below the label */
  font-size: 14px; /* Smaller font size */
  color: #555; /* Slightly lighter color for the label text */
}
