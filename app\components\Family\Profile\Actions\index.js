import React, { useEffect, useState } from 'react';
import moment from 'moment';
import stylesList from 'Css/profileList.css';
import btnStyles from 'Css/button.css';
import { useHistory, useLocation, useParams } from 'react-router-dom';

import { createStructuredSelector } from 'reselect';
import { useDispatch, useSelector } from 'react-redux';
import { useInjectReducer, useInjectSaga } from 'redux-injectors';
import reducer from 'containers/Common/SubComponents/ActionForm/reducer';

import Modal2 from 'components/Common/Modal2';
import { Alert } from 'react-bootstrap';

import { meActionSaga } from 'containers/Common/SubComponents/ActionForm/saga';

import {
  deleteAction,
  loadActionData,
  resetAction,
} from 'containers/Common/SubComponents/ActionForm/actions';

import ActionForm from 'containers/Common/SubComponents/ActionForm';
import {
  makeSelecAction,
  makeSelecActionDeleteSuccess,
  makeSelecActions,
  makeSelectActionsAddDonorSuccess,
  makeSelectError,
  makeSelectSuccess,
} from 'containers/Common/SubComponents/ActionForm/selectors';
import { removeActionFamily } from 'containers/Family/FamilyProfile/actions';
import DataTable from 'components/Common/DataTable';
import CustomPagination from 'components/Common/CustomPagination';
import {
  DELETE_ICON,
  EDIT_ICON,
  VIEW_ICON,
} from '../../../Common/ListIcons/ListIcons';
import AccessControl, { isAuthorized } from 'utils/AccessControl';

const key = 'actionData';
const target = 'family';

const formatDate = date => moment(date).format('DD/MM/YYYY');

const omdbSelector = createStructuredSelector({
  success: makeSelectSuccess,
  error: makeSelectError,
  actionData: makeSelecAction,
  successDelete: makeSelecActionDeleteSuccess,
  actionsData: makeSelecActions,
  actionDonorAddSuccess: makeSelectActionsAddDonorSuccess,
});

export default function Actions(props) {
  const dispatch = useDispatch();
  const family = props.data;
  let familyCode;

  if (family) {
    familyCode = family.code;
  }
  useInjectReducer({ key, reducer });
  useInjectSaga({ key, saga: meActionSaga });

  const params = useParams();
  const [show, setShow] = useState(false);
  const [actionToDelete, setActionToDelete] = useState('');
  const [showDeleteModal, setShowDeleteModal] = useState(false);

  const handleClose = () => {
    setShow(false);
    setActionToReadOnly({
      title: "Consulter l'action",
      isRead: false,
      action: null,
    });
    localStorage.removeItem('modalState');
  };

  const handleShow = () => {
    setShow(true);
  };

  const handleCloseForDeleteModal = () => setShowDeleteModal(false);

  const location = useLocation();
  const history = useHistory();

  // Define pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const pageSize = 5; // Define page size

  useEffect(
    () =>
      function cleanup() {
        dispatch(resetAction());
      },
    [],
  );

  const {
    success,
    error,
    actionData,
    successDelete,
    actionsData,
    actionDonorAddSuccess,
  } = useSelector(omdbSelector);
  const [actionToEdit, setActionToEdit] = useState('');
  const [message, setMessage] = useState('');
  const [showAlert, setShowAlert] = useState(false);
  const [actionToReadOnly, setActionToReadOnly] = useState({
    title: "Consulter l'action",
    isRead: false,
    action: null,
  });

  let errorMessage = null;

  useEffect(() => {
    if (successDelete) {
      setShowAlert(true);
      dispatch(removeActionFamily(actionData));
      setMessage('Action supprimé avec succès');
    }
  }, [successDelete]);

  useEffect(() => {
    if (actionDonorAddSuccess) {
      setShowAlert(true);
      if (actionToEdit) {
        setMessage('Action modifiée avec succès');
      } else {
        setMessage('Action ajoutée avec succès');
      }
      setActionToEdit('');
    }
  }, [actionDonorAddSuccess]);

  // to close the success message after 4 seconds
  useEffect(() => {
    if (showAlert) {
      setTimeout(() => {
        setShowAlert(false);
      }, 4000);
    }
  }, [showAlert]);

  // to close the error message after 4 seconds
  useEffect(() => {
    if (error) {
      setTimeout(() => {
        dispatch(resetNote());
      }, 4000);
    }
  }, [error]);

  useEffect(() => {
    dispatch(loadActionData(params.id, target));
  }, []);

  useEffect(() => {
    if (successDelete || actionDonorAddSuccess) {
      dispatch(loadActionData(params.id, target));
    }
  }, [successDelete, actionDonorAddSuccess]);

  useEffect(() => {
    const state = location.state;
    if (state && state.actionDTO && state.actionDTO.actionDTO) {
      localStorage.setItem(
        'modalState',
        JSON.stringify(state.actionDTO.actionDTO),
      );
      history.replace({ ...location, state: null });
    }
  }, [location]);

  useEffect(() => {
    const modalState = localStorage.getItem('modalState');
    if (modalState) {
      const actionDTO = JSON.parse(modalState);
      setActionToReadOnly({
        title: "Consulter l'action",
        isRead: true,
        action: actionDTO,
      });
      handleShow();
    } else {
      setShow(false);
    }
  }, []);

  if (error) {
    errorMessage = 'Une erreur est survenue';
  }

  const emptyValue = <span>-</span>;

  let listActions = null;

  if (actionsData) {
    const actionsSorted = [...actionsData].sort((a, b) =>
      a.createdDate < b.createdDate ? 1 : -1,
    );

    const startIndex = (currentPage - 1) * pageSize;
    const endIndex = Math.min(startIndex + pageSize, actionsSorted.length);
    const paginatedNotes = actionsSorted.slice(startIndex, endIndex);

    listActions = paginatedNotes.map(action => {
      const lastCommentContent =
        action.comments && action.comments.length > 0
          ? action.comments[action.comments.length - 1].content
          : '-';
      return {
        id: action.id,
        // content: action.content,
        content: lastCommentContent,
        subject: action.subject,
        dateEntry: action.dateEntry ? formatDate(action.dateEntry) : emptyValue,
        dateRealize: action.dateRealize ? formatDate(action.dateRealize) : '-',
        deadline: action.deadline ? formatDate(action.deadline) : emptyValue,
        createdBy: action.createdBy
          ? `${action.createdBy.firstName}${' '}${action.createdBy.lastName}`
          : emptyValue,
        affectedTo: action.affectedTo
          ? `${action.affectedTo.firstName}${' '}${action.affectedTo.lastName}`
          : emptyValue,
        actionStatus: action.actionStatus
          ? action.actionStatus.name
          : emptyValue,

        action,
      };
    });
  }

  const columns = [
    {
      field: 'content',
      headerName: 'Commentaire',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
    {
      field: 'subject',
      headerName: 'Objet',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
    {
      field: 'dateEntry',
      headerName: 'Date de saisie',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
    {
      field: 'dateRealize',
      headerName: 'Date de réalisation',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
    {
      field: 'deadline',
      headerName: 'Date limite',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
    {
      field: 'createdBy',
      headerName: 'Affecté par',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
    {
      field: 'affectedTo',
      headerName: 'Affecté à',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
    {
      field: 'actionStatus',
      headerName: 'Statut',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
  ];

  const connectedUser = useSelector(state => state.app.connectedUser);
  const isUpdateAuthorized = isAuthorized(
    connectedUser,
    'GERER_BENEFICIAIRES_KAFALAT',
    'WRITE',
  );
  if (isUpdateAuthorized) {
    columns.push({
      field: 'actions',
      headerName: 'Actions',
      flex: 1.2,
      headerAlign: 'center',
      align: 'center',
      type: 'actions',
      renderCell: params => (
        <div>
          <input
            type="image"
            className="p-0"
            src={VIEW_ICON}
            width="20px"
            height="20px"
            title="consulter"
            onClick={() => {
              setActionToEdit('');
              setActionToReadOnly(prev => ({
                ...prev,
                isRead: true,
                action: params.row.action,
              }));
              handleShow();
            }}
          />
          <input
            type="image"
            src={EDIT_ICON}
            disabled={params.row.action.actionStatus.name === 'Réalisée'}
            style={{
              opacity:
                params.row.action.actionStatus.name === 'Réalisée' ? 0.5 : 1,
            }}
            className="p-0 mx-2"
            width="20px"
            height="20px"
            onClick={() => {
              setActionToEdit(params.row.action);
              handleShow();
            }}
            title="modifier"
          />
          <input
            type="image"
            src={DELETE_ICON}
            disabled={params.row.action.actionStatus.name === 'Réalisée'}
            style={{
              opacity:
                params.row.action.actionStatus.name === 'Réalisée' ? 0.5 : 1,
            }}
            className="p-0"
            width="20px"
            height="20px"
            onClick={() => {
              setActionToDelete(params.row.action);
              setShowDeleteModal(true);
            }}
            title="supprimer"
          />
        </div>
      ),
    });
  }

  return (
    <div>
      <Modal2
        title={
          actionToReadOnly.isRead
            ? actionToReadOnly.title
            : actionToEdit
            ? "Modifier l'action"
            : 'Ajouter une action'
        }
        size="lg"
        customWidth="modal-90w"
        show={show}
        handleClose={() => {
          setShow(false);
          actionToReadOnly.isRead &&
            setActionToReadOnly({
              title: "Consulter l'action",
              isRead: false,
              action: null,
            });
        }}
      >
        <ActionForm
          action={
            actionToEdit
              ? actionToEdit
              : actionToReadOnly.action
              ? actionToReadOnly.action
              : actionToEdit
          }
          handleClose={handleClose}
          button={actionToEdit ? 'Modifier' : 'Ajouter'}
          target={target}
          id={params.id}
          isRead={actionToReadOnly.isRead}
        />
      </Modal2>

      <Modal2
        centered
        className="mt-5"
        title="Confirmation de suppression"
        show={showDeleteModal}
        handleClose={handleCloseForDeleteModal}
      >
        <p className="mt-1 mb-5">
          Êtes-vous sûr de vouloir supprimer cette action?
        </p>
        <div className="d-flex justify-content-end px-3 my-1">
          <button
            type="button"
            className={`mx-2 btn-style outlined`}
            onClick={handleCloseForDeleteModal}
          >
            Annuler
          </button>
          <button
            type="submit"
            className={`mx-2 btn-style primary`}
            onClick={() => {
              dispatch(deleteAction(actionToDelete, target));
              setActionToDelete('');
              handleCloseForDeleteModal();
            }}
          >
            Supprimer
          </button>
        </div>
      </Modal2>

      {showAlert ? (
        <Alert
          className="alert-style"
          variant="success"
          onClose={() => setShowAlert(false)}
          dismissible
        >
          <p>{message}</p>
        </Alert>
      ) : null}

      {error && (
        <Alert
          className="alert-style"
          variant="danger"
          onClose={() => setShowAlert(false)}
          dismissible
        >
          <p>{errorMessage}</p>
        </Alert>
      )}

      <div className={stylesList.backgroudStyle}>
        <div className={stylesList.global}>
          <div className={stylesList.header}>
            <h4>Liste des Actions</h4>
            <AccessControl module="FAMILLE" functionality="UPDATE">
              <button
                className={btnStyles.addBtnProfile}
                onClick={() => {
                  handleShow();
                  setActionToEdit('');
                }}
              >
                Ajouter
              </button>
            </AccessControl>
          </div>
          <div className={stylesList.content}>
            <DataTable
              rows={listActions}
              columns={columns}
              fileName={`Liste des actions de la famille ${familyCode} , ${new Date().toLocaleString()}`}
            />

            <div className="justify-content-center my-4">
              {actionsData && (
                <CustomPagination
                  totalElements={actionsData.length}
                  totalCount={Math.ceil(actionsData.length / pageSize)}
                  pageSize={pageSize}
                  currentPage={currentPage}
                  onPageChange={setCurrentPage}
                />
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
