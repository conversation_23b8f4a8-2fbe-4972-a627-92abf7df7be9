import React, { useState, useEffect } from 'react';
import { useHistory } from 'react-router-dom';
import listStyle from 'Css/profileList.css';
import btnStyles from 'Css/button.css';
import { Alert } from 'react-bootstrap';
import AccessControl from 'utils/AccessControl';
import styled from 'styled-components';

import { useDispatch, useSelector } from 'react-redux';
import DataTable from 'components/Common/DataTable';

import Modal2 from 'components/Common/Modal2';
import {
    deleteEps,
    resetEps,
    resetDeleteEps,
} from 'containers/Eps/EpsList/actions';
import {
    DELETE_ICON,
    EDIT_ICON,
    VIEW_ICON,
} from 'components/Common/ListIcons/ListIcons';
import styles from '../../../Css/tag.css';

// Styled component for the tags container in the table
const TagsContainer = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  max-width: 200px;
`;

// Styled component for individual tags in the table
const TableTag = styled.div`
  padding: 2px 8px;
  font-size: 11px;
  white-space: nowrap;
  border-radius: 10px;
  background-color: ${props => `#${props.color || 'cccccc'}`};
  color: ${props => {
    const hex = props.color || 'cccccc';
    const r = parseInt(hex.substr(0, 2), 16);
    const g = parseInt(hex.substr(2, 2), 16);
    const b = parseInt(hex.substr(4, 2), 16);
    const brightness = (r * 299 + g * 587 + b * 114) / 1000;
    return brightness > 128 ? '#000000' : '#ffffff';
  }};
`;

const ListEps = ({ epsList }) => {

    const handleCloseForDeleteModal = () => setShowDeleteModal(false);
    const dispatch = useDispatch();
    const history = useHistory();
    const [showAlert, setShowAlert] = useState(false);
    const [message, setMessage] = useState('');

    const [showDeleteModal, setShowDeleteModal] = useState(false);
    const [epsToEdit, setEpsToEdit] = useState('');
    const [epsToDelete, setEpsToDelete] = useState('');

    const viewHandler = id => {
        history.push(`/eps/fiche/${id}/info`, { params: id });
    };

    const editHandler = id => {
        history.push(`/eps/editEps/${id}`);
    };

    const formatCityNames = cityDetails => {
        return cityDetails && cityDetails.map(city => city.name).join(', ');
    };

    const columns = [
        {
            field: 'code',
            headerName: 'Code de l\'EPS',
            flex: 1,
            headerAlign: 'center',
            align: 'center',
        },
        {
            field: 'name',
            headerName: 'Nom de l\'établissement',
            flex: 1,
            headerAlign: 'center',
            align: 'center',
        },
        {
            field: 'nameAr',
            headerName: 'اسم المنطقة',
            flex: 1,
            headerAlign: 'center',
            align: 'center',
        },
        {
            field: 'capacity',
            headerName: 'Capacité',
            flex: 1,
            headerAlign: 'center',
            align: 'center',
            renderCell: params => (
                <span>
                    {params.row.capacity ? params.row.capacity : '---'}
                </span>
            ),
        },
        {
            field: 'cityNames',
            headerName: 'Villes associées',
            flex: 1,
            headerAlign: 'center',
            align: 'center',
            renderCell: params => (
                <span>{formatCityNames(params.row.cityDetails) ? formatCityNames(params.row.cityDetails) : '---'}</span>
            ),
        },
        {
            field: 'assistantName',
            headerName: 'Assistant associée',
            flex: 1,
            headerAlign: 'center',
            align: 'center',
            renderCell: params => (
                <span>
                    {params.row.zone && params.row.zone.assistantName ? params.row.zone.assistantName : '---'}
                </span>
            ),
        },
        {
            field: 'beneficiariesCount',
            headerName: 'Nombre de bénéficiaires',
            flex: 1,
            headerAlign: 'center',
            align: 'center',
            renderCell: params => (
                <span>
                    {params.row.zone ? params.row.zone.totalBenecifiaries : '---'}
                </span>
            ),
        },
        {
            field: 'status',
            headerName: 'Statut',
            flex: 1,
            headerAlign: 'center',
            align: 'center',
            renderCell: params => (
                <span className={params.row.status ? styles.tagGreen : styles.tagRed}>
                    {params.row.status ? 'Actif' : 'Inactif'}
                </span>
            ),
        },
        {
            field: 'tags',
            headerName: 'Tags',
            flex: 1.5,
            headerAlign: 'center',
            align: 'center',
            renderCell: params => {
                const tags = params.row.tags || [];
                return (
                    <TagsContainer>
                        {tags && tags.length > 0 ? (
                            tags.map(tag => (
                                <TableTag key={tag.id} color={tag.color || 'cccccc'}>
                                    {tag.name}
                                </TableTag>
                            ))
                        ) : (
                            <span style={{ color: '#999', fontSize: '12px' }}>Aucun tag</span>
                        )}
                    </TagsContainer>
                );
            },
        },

        {
            field: 'actions',
            headerName: 'Actions',
            flex: 1,
            headerAlign: 'center',
            align: 'center',
            renderCell: params => (
                <div
                    style={{
                        display: 'flex',
                        flexDirection: 'row',
                        justifyContent: 'space-around',
                        alignItems: 'center',
                    }}
                >
                    <input
                        type="image"
                        onClick={() => viewHandler(params.row.id)}
                        className="p-2"
                        src={VIEW_ICON}
                        width="40px"
                        height="40px"
                        title="consulter"
                    />
                    <AccessControl module="USER" functionality="UPDATE">
                        <input
                            type="image"
                            onClick={() => editHandler(params.row.id)}
                            className="p-2"
                            src={EDIT_ICON}
                            width="40px"
                            height="40px"
                            title="Modifier"
                        />
                    </AccessControl>
                    <input
                        type="image"
                        src={DELETE_ICON}
                        className="p-2"
                        width="40px"
                        height="40px"
                        onClick={() => {
                            setEpsToDelete(params.row.id);
                            setShowDeleteModal(true);
                            setMessage('EPS supprimé avec succès');
                        }}
                        disabled={params.row.status === true} // Disable when status is "Actif"
                        style={{
                            opacity: params.row.status === true ? 0.5 : 1, // Reduce opacity when disabled
                            cursor: params.row.status === true ? "not-allowed" : "pointer", // Change cursor style
                        }}
                        title={params.row.status === true ? "Suppression impossible : l’EPS est actif" : "Supprimer"} // Change title based on status
                    />
                </div>
            ),
        },
    ];
    return (
        <div className="table-container">
            {showAlert ? (
                <Alert
                    className="pb-0"
                    variant="success"
                    onClose={() => setShowAlert(false)}
                    dismissible
                >
                    <p>{message}</p>
                </Alert>
            ) : null}

            <Modal2
                centered
                className="mt-5"
                title="Confirmation de suppression"
                show={showDeleteModal}
                handleClose={handleCloseForDeleteModal}
            >
                <p className="mt-1 mb-5">
                    Êtes-vous sûr de vouloir supprimer cet EPS?
                </p>
                <div className="d-flex justify-content-end px-3 my-1">
                    <button
                        type="button"
                        className={`mx-2 ${btnStyles.cancelBtn}`}
                        onClick={handleCloseForDeleteModal}
                    >
                        Annuler
                    </button>
                    <button
                        type="submit"
                        className={`mx-2 ${btnStyles.addBtn}`}
                        onClick={() => {
                            const epsToDeleteObj = epsList.find(eps => eps.id === epsToDelete);
                            if (epsToDeleteObj && epsToDeleteObj.status === false) {
                                dispatch(deleteEps(epsToDelete));
                                setEpsToDelete('');
                                handleCloseForDeleteModal();
                            }
                        }}
                    >
                        Confirmer la suppression
                    </button>
                </div>
            </Modal2>
            <div className={listStyle.global}>{/* Any additional UI elements */}</div>

            <div>
                <DataTable
                    rows={epsList.map(eps => ({
                        ...eps,
                        cityNames: formatCityNames(eps.cityDetails), // Map city names for display
                    }))}
                    columns={columns}
                    fileName={`Liste des Eps, ${new Date().toLocaleString()}`}
                />
            </div>
        </div>
    );
};

export default ListEps;