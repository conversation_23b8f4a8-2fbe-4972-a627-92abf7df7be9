import { createSelector } from 'reselect';
import { initialState } from './reducer';

const selectOmdb = state => state.aideComplementaireFiche || initialState;

const makeSelectDonations = createSelector(
  selectOmdb,
  omdbState => omdbState.donation1,
);

const makeSelectLoading = createSelector(
  selectOmdb,
  omdbState => omdbState.loading,
);

const makeSelectLoading2 = createSelector(
  selectOmdb,
  omdbState => omdbState.loading2,
);

const makeSelectSuccess = createSelector(
  selectOmdb,
  omdbState => omdbState.success,
);

const makeSelectGroupSuccess = createSelector(
  selectOmdb,
  omdbState => omdbState.successGroup,
);

const makeSelectError = createSelector(
  selectOmdb,
  omdbState => omdbState.error,
);

const makeSelectAideComplementaire = createSelector(
  selectOmdb,
  omdbState => omdbState.aideComplementaire,
);

const makeSelectBeneficiariesForAideComplementaire = createSelector(
  selectOmdb,
  omdbState => omdbState.beneficiariesForAideComplementaire,
);
const makeSelectPagebleBeneficiariesForAideComplementaire = createSelector(
  selectOmdb,
  omdbState => omdbState.beneficiaries,
);
const makeSelectDonorsForAideComplementaire = createSelector(
  selectOmdb,
  omdbState => omdbState.donorsForAideComplementaire,
);

const makeSelectBudgetLinesForAideComplementaire = createSelector(
  selectOmdb,
  omdbState => omdbState.budgetLines,
);

const makeSelectGroupeForAideComplementaire = createSelector(
  selectOmdb,
  omdbState => omdbState.groupe,
);

const makeSelectProcessBeneficiariesForAideComplementaire = createSelector(
  selectOmdb,
  omdbState => omdbState.proceesBeneficiariesSuccess,
);

const makeSelectProcessDonorsForAideComplementaire = createSelector(
  selectOmdb,
  omdbState => omdbState.proceesDonorsSuccess,
);

const makeSelectRemoveBeneficiaryFromAideComplementaireLoading = createSelector(
  selectOmdb,
  omdbState => omdbState.removeBeneficiaryLoading,
);

const makeSelectRemoveBeneficiaryFromAideComplementaireSuccess = createSelector(
  selectOmdb,
  omdbState => omdbState.removeBeneficiarySuccess,
);

const makeSelectRemoveBeneficiaryFromAideComplementaireError = createSelector(
  selectOmdb,
  omdbState => omdbState.removeBeneficiaryError,
);

const makeSelectUpdateStatutValidationForBeneficiarySuccess = createSelector(
  selectOmdb,
  omdbState => omdbState.updateStatutValidationBeneficiarySuccess,
);

const makeSelectUpdateStatutValidationForBeneficiaryError = createSelector(
  selectOmdb,
  omdbState => omdbState.updateStatutValidationBeneficiaryError,
);

const makeSelectUpdateStatutReservationBudgetLineSuccess = createSelector(
  selectOmdb,
  omdbState => omdbState.updateStatutReservationBudgetLineSuccess,
);

const makeSelectUpdateMontantReservedBudgetLineError = createSelector(
  selectOmdb,
  omdbState => omdbState.updateMontantReservedBudgetLineError,
);

const makeSelectUpdateMontantReservedBudgetLineSuccess = createSelector(
  selectOmdb,
  omdbState => omdbState.updateMontantReservedBudgetLineSuccess,
);

const makeSelectUpdateStatutReservationBudgetLineError = createSelector(
  selectOmdb,
  omdbState => omdbState.updateStatutReservationBudgetLineError,
);

const makeSelectUpdateMontantForBeneficiarySuccess = createSelector(
  selectOmdb,
  omdbState => omdbState.updateMontantBeneficiarySuccess,
);

const makeSelectUpdateMontantForBeneficiaryError = createSelector(
  selectOmdb,
  omdbState => omdbState.updateMontantBeneficiaryError,
);

const makeSelectUpdateMontantForGroupeSuccess = createSelector(
  selectOmdb,
  omdbState => omdbState.updateMontantGroupSuccess,
);

const makeSelectUpdateMontantForGroupeError = createSelector(
  selectOmdb,
  omdbState => omdbState.updateMontantGroupError,
);

const makeSelectReserveAllBudgetLinesSuccess = createSelector(
  selectOmdb,
  omdbState => omdbState.reserveAllBudgetLineSuccess,
);

const makeSelectReserveAllBudgetLinesError = createSelector(
  selectOmdb,
  omdbState => omdbState.reserveAllBudgetLineError,
);

const makeSelectRemoveDonorFromAideComplementaireSuccess = createSelector(
  selectOmdb,
  omdbState => omdbState.removeDonorSuccess,
);

const makeSelectRemoveDonorFromAideComplementaireError = createSelector(
  selectOmdb,
  omdbState => omdbState.removeDonorError,
);

const makeSelectExecuteAideComplementaireSuccess = createSelector(
  selectOmdb,
  omdbState => omdbState.executeAideComplementaireSuccess,
);

const makeSelectExecuteAideComplementaireError = createSelector(
  selectOmdb,
  omdbState => omdbState.executeAideComplementaireError,
);

const makeSelectUnExecuteAideComplementaireSuccess = createSelector(
  selectOmdb,
  omdbState => omdbState.unexecuteAideComplementaireSuccess,
);

const makeSelectUnExecuteAideComplementaireError = createSelector(
  selectOmdb,
  omdbState => omdbState.unexecuteAideComplementaireError,
);

const makeSelectCloseAideComplementaireSuccess = createSelector(
  selectOmdb,
  omdbState => omdbState.closeAideComplementaireSuccess,
);

const makeSelectCloseAideComplementaireError = createSelector(
  selectOmdb,
  omdbState => omdbState.closeAideComplementaireError,
);

const makeSelectValidateAllBeneficiariesLoading = createSelector(
  selectOmdb,
  omdbState => omdbState.validateAllBeneficiariesLoading,
);

const makeSelectValidateAllBeneficiariesSuccess = createSelector(
  selectOmdb,
  omdbState => omdbState.validateAllBeneficiariesSuccess,
);

const makeSelectValidateAllBeneficiariesError = createSelector(
  selectOmdb,
  omdbState => omdbState.validateAllBeneficiariesError,
);

const makeSelectValidateAllBeneficiariesMessage = createSelector(
  selectOmdb,
  omdbState => omdbState.validateAllBeneficiariesMessage,
);

const makeSelectValidateAllBeneficiariesHasFilters = createSelector(
  selectOmdb,
  omdbState => omdbState.validateAllBeneficiariesHasFilters,
);

const makeSelectLastActionMeta = createSelector(
  selectOmdb,
  omdbState => omdbState.lastActionMeta,
);

export {
  selectOmdb,
  makeSelectDonations,
  makeSelectLoading,
  makeSelectError,
  makeSelectAideComplementaire,
  makeSelectBeneficiariesForAideComplementaire,
  makeSelectSuccess,
  makeSelectProcessBeneficiariesForAideComplementaire,
  makeSelectProcessDonorsForAideComplementaire,
  makeSelectDonorsForAideComplementaire,
  makeSelectRemoveBeneficiaryFromAideComplementaireLoading,
  makeSelectRemoveBeneficiaryFromAideComplementaireSuccess,
  makeSelectRemoveBeneficiaryFromAideComplementaireError,
  makeSelectUpdateStatutValidationForBeneficiarySuccess,
  makeSelectUpdateStatutValidationForBeneficiaryError,
  makeSelectUpdateMontantForBeneficiarySuccess,
  makeSelectUpdateMontantForBeneficiaryError,
  makeSelectRemoveDonorFromAideComplementaireSuccess,
  makeSelectRemoveDonorFromAideComplementaireError,
  makeSelectLastActionMeta,
  makeSelectBudgetLinesForAideComplementaire,
  makeSelectUpdateStatutReservationBudgetLineSuccess,
  makeSelectUpdateStatutReservationBudgetLineError,
  makeSelectUpdateMontantReservedBudgetLineError,
  makeSelectUpdateMontantReservedBudgetLineSuccess,
  makeSelectExecuteAideComplementaireSuccess,
  makeSelectPagebleBeneficiariesForAideComplementaire,
  makeSelectUnExecuteAideComplementaireSuccess,
  makeSelectExecuteAideComplementaireError,
  makeSelectUnExecuteAideComplementaireError,
  makeSelectGroupeForAideComplementaire,
  makeSelectUpdateMontantForGroupeSuccess,
  makeSelectUpdateMontantForGroupeError,
  makeSelectGroupSuccess,
  makeSelectReserveAllBudgetLinesSuccess,
  makeSelectReserveAllBudgetLinesError,
  makeSelectCloseAideComplementaireSuccess,
  makeSelectCloseAideComplementaireError,
  makeSelectValidateAllBeneficiariesLoading,
  makeSelectValidateAllBeneficiariesSuccess,
  makeSelectValidateAllBeneficiariesError,
  makeSelectValidateAllBeneficiariesMessage,
  makeSelectValidateAllBeneficiariesHasFilters,
  makeSelectLoading2
};
