import React, { useEffect, useRef, useState } from 'react';
import { Link, useLocation, useHistory } from 'react-router-dom';
import moment from 'moment';
import './styleInfo.css';
import stylesList from 'Css/profileList.css';
import profile from '../../../Css/personalInfo.css';
import tag from '../../../Css/tag.css';
import btnStyles from '../../../Css/button.css';
import PrintableContent from '../PrintableContent/PrintableContent';
import ReactToPrint from 'react-to-print';
import {PRINT_ICON, WHITE_UPLOAD_PICTURE} from "../../../containers/Common/RequiredElement/Icons";

import Alert from 'react-bootstrap/Alert';
const formatDate = date => moment(date).format('DD/MM/YYYY');

export default function PersonalInfo(props) {
  const donor = props.data;
  const componentRef = useRef();
  const location = useLocation();
  const history = useHistory();
  const [showSuccessMessage, setShowSuccessMessage] = useState(false);

  useEffect(() => {
    if (location.state === 'success') {
      setShowSuccessMessage(true);
    }
  }, [location.state]);

  useEffect(() => {
    if (showSuccessMessage) {
      const timer = setTimeout(() => {
        handleCloseSuccessMessage();
      }, 4000);
      return () => clearTimeout(timer);
    }
  }, [showSuccessMessage]);

  const handleCloseSuccessMessage = () => {
    setShowSuccessMessage(false);
    history.replace({ ...location, state: null });
  };

  const handleBeforePrint = () => {
    // Format the current date and time in a more readable way
    const currentDateTime = moment().format('YYYY-MM-DD_HH-mm-ss'); // e.g., 2025-01-23_16-13-13
    const companyName = donor.company;
    document.title = `${companyName} - ${donor.identityCode} - ${currentDateTime}`;
  };

  const handleAfterPrint = () => {
    // Reset the title after printing
    document.title = 'Sofwa Système';
  };

  const backgroundContact = {
    backgroundColor: ' #F7F7F7',
    border: '2px solid white',
    borderRadius: '10px',
  };

  const emptyValue = <span>----</span>;

  const data1 = donor ? (
    <div className={profile.data1}>
      <p>
        <span style={{ fontWeight: 'bold' }}>Nom Entreprise : </span>
        <span style={{ fontWeight: 'normal' }}>
          {donor.company ? donor.company : emptyValue}
          {donor.shortCompany ? `- ${donor.shortCompany}` : ''}
        </span>
      </p>
      <p>
        <span style={{ fontWeight: 'bold' }}>Identifiant : </span>
        <span style={{ fontWeight: 'normal' }}>
          {donor.identityCode ? donor.identityCode : emptyValue}
        </span>
      </p>
      <p>
        <span style={{ fontWeight: 'bold' }}>Secteur Activité : </span>
        <span style={{ fontWeight: 'normal' }}>
          {donor.activitySector && donor.activitySector.name
            ? donor.activitySector.name
            : emptyValue}
        </span>
      </p>
      {donor.comment && (
        <p>
          <span style={{ fontWeight: 'bold' }}>Commentaire : </span>
          <span style={{ fontWeight: 'normal' }}>{donor.comment}</span>
        </p>
      )}
      <p>
        <span style={{ fontWeight: 'bold' }}>Inscrit le : </span>
        <span style={{ fontWeight: 'normal' }}>
          {donor.createdAt ? formatDate(donor.createdAt) : emptyValue}
        </span>
      </p>
    </div>
  ) : null;

  const data2 = donor ? (
    <div className={profile.data1}>
      <p>
        <span style={{ fontWeight: 'bold' }}>Pays : </span>
        <span style={{ fontWeight: 'normal' }}>
          {donor.info ? donor.info.region.country.nameFrench : emptyValue}
        </span>
      </p>
      <p>
        <span style={{ fontWeight: 'bold' }}>Région : </span>
        <span style={{ fontWeight: 'normal' }}>
          {donor.info ? donor.info.region.name : emptyValue}
        </span>
      </p>
      <p>
        <span style={{ fontWeight: 'bold' }}>Ville : </span>
        <span style={{ fontWeight: 'normal' }}>
          {donor.info ? donor.info.name : emptyValue}
        </span>
      </p>
      <p>
        <span style={{ fontWeight: 'bold' }}>Adresse : </span>
        <span style={{ fontWeight: 'normal' }}>
          {donor.address ? donor.address : emptyValue}
        </span>
      </p>
      <p>
        <span style={{ fontWeight: 'bold' }}>العنوان : </span>
        <span style={{ fontWeight: 'normal' }}>
          {donor.addressAr ? donor.addressAr : emptyValue}
        </span>
      </p>
    </div>
  ) : null;

  const contactSection = donor
    ? donor.donorContacts.map(donorContact => (
        <div
          key={donorContact.id}
          className={`${stylesList.backgroudStyle} col-5 m-3`}
          style={backgroundContact}
        >
          <div>
            <h6 style={{ textAlign: 'center', fontWeight: 'bold' }}>
              <span className="value">
                {donorContact.firstName} {donorContact.lastName}
              </span>
            </h6>
            <h6 style={{ textAlign: 'center', fontWeight: 'bold' }}>
              <span className="value">
                {donorContact.firstNameAr || donorContact.lastNameAr ? (
                  <>
                    {donorContact.firstNameAr && (
                      <>
                        {donorContact.firstNameAr}
                        {donorContact.lastNameAr && ' '}
                      </>
                    )}
                    {donorContact.lastNameAr}
                  </>
                ) : (
                  <span style={{ whiteSpace: 'pre' }}> </span>
                )}
              </span>
            </h6>
            <p style={{ textAlign: 'center' }}>
              <span className="value">
                {donorContact.mainContact === true ? (
                  <span className={tag.tagGreen}>Principale</span>
                ) : (
                  <span style={{ whiteSpace: 'pre' }}> </span>
                )}
              </span>
            </p>
            <hr
              className="row mr-5 ml-5 mb-2"
              style={{ color: 'black', backgroundColor: 'black' }}
            ></hr>
            <div
              className={profile.top}
              style={{ display: 'flex', justifyContent: 'center' }}
            >
              <div className={profile.data1}>
                <p>
                  <span style={{ fontWeight: 'bold' }}>Sexe : </span>
                  <span style={{ fontWeight: 'normal' }}>
                    {donorContact.sex ? donorContact.sex : emptyValue}
                  </span>
                </p>
                <p>
                  <span style={{ fontWeight: 'bold' }}>Fonction : </span>
                  <span style={{ fontWeight: 'normal' }}>
                    {donorContact.donorContactFunction &&
                    donorContact.donorContactFunction.name
                      ? donorContact.donorContactFunction.name
                      : emptyValue}
                  </span>
                </p>
                <p>
                  <span style={{ fontWeight: 'bold' }}>Email : </span>
                  <span style={{ fontWeight: 'normal' }}>
                    {donorContact.email ? donorContact.email : emptyValue}
                  </span>
                </p>
                <p>
                  <span style={{ fontWeight: 'bold' }}>Téléphone : </span>
                  <span style={{ fontWeight: 'normal' }}>
                    {donorContact.phoneNumber
                      ? donorContact.phoneNumber
                      : emptyValue}
                  </span>
                </p>
                <p>
                  <span style={{ fontWeight: 'bold' }}>
                    Canal Communication :{' '}
                  </span>
                  <span style={{ fontWeight: 'normal' }}>
                    {donorContact.canalCommunications
                      ? donorContact.canalCommunications.map(canal => (
                          <span key={canal.id}>{canal.name}, </span>
                        ))
                      : emptyValue}
                  </span>
                </p>
                <p>
                  <span style={{ fontWeight: 'bold' }}>
                    Langue communication:{' '}
                  </span>
                  <span style={{ fontWeight: 'normal' }}>
                    {donorContact.languageCommunications
                      ? donorContact.languageCommunications.map(language => (
                          <span key={language.id}>{language.name}, </span>
                        ))
                      : emptyValue}
                  </span>
                </p>
              </div>
            </div>
          </div>
        </div>
      ))
    : null;

  const content = donor ? (
    <div>
      <div className={`${profile.content} ${stylesList.backgroudStyle}`}>
        {/* SECTION 1 */}
        <div className={profile.section1}>
          {/* TOP SECTION */}
          <div className={profile.top}>{data1}</div>
        </div>

        {/* SECTION 2 */}
        <div className={profile.section2}>
          <div className="row">{data2}</div>
          <div className="row">
            <div className={profile.data2}></div>
          </div>
        </div>
      </div>

      <h4 className="mt-3 ml-2">Contacts</h4>
      <div className={`${profile.content} row`}>{contactSection}</div>
    </div>
  ) : (
    <p></p>
  );

  return (
    <div>
      {showSuccessMessage && (
        <Alert
          className="alert-style"
          variant="success"
          onClose={handleCloseSuccessMessage}
          dismissible
        >
          <p>Donateur modifié avec succès</p>
        </Alert>
      )}

      <div className={stylesList.backgroudStyle}>
        <div className={profile.personalInfo}>
          <div className={profile.header}>
            <h4 className="ml-2">Information Morales</h4>

            {donor ? (
              <div className="d-flex align-items-center">
                {/* Print Button */}
                <ReactToPrint
                  trigger={() => (
                    <button className="btn-style primary mr-2">
                    <img src={PRINT_ICON} width="16px" height="16px" />
                      Imprimer
                    </button>
                  )}
                  content={() => componentRef.current}
                  onBeforePrint={handleBeforePrint}
                  onAfterPrint={handleAfterPrint}
                />

                {/* Hidden Printable Content */}
                <div style={{ display: 'none' }}>
                  <PrintableContent ref={componentRef} data={donor} />
                </div>

                {/* Edit Link */}
                <Link
                  to={{
                    pathname:
                      donor.type === 'Moral'
                        ? `/donors/edit/moral/${donor.id}`
                        : `/donors/edit/physique/${donor.id}`,
                    state: { redirectTo: 'consultation' },
                  }}
                >
                  <button className="btn-style secondary mb-2">
                    <img
                      src={WHITE_UPLOAD_PICTURE}
                      width="16px"
                      height="16spx"
                    />
                    Modifier
                  </button>
                </Link>
              </div>
            ) : (
              <button className="btn-style secondary mb-2">
                <img
                  src={WHITE_UPLOAD_PICTURE}
                  width="16px"
                  height="16spx"
                />
                Modifier
              </button>
            )}
          </div>

          {/* Content Section */}
          {content}
        </div>
      </div>
    </div>
  );

}
