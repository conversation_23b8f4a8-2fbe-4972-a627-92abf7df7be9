import React from 'react';


export function LineChart({ data, labels, height = 200 }) {
  const max = Math.max(...data);
  const points = data.map((value, index) => {
    const x = (index / (data.length - 1)) * 100;
    const y = ((max - value) / max) * height;
    return `${x},${y}`;
  }).join(' ');

  return (
    <div style={{ height: `${height}px` }} className="relative">
      <svg width="100%" height="100%" className="overflow-visible">
        <polyline
          points={points}
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          className="text-blue-500"
        />
      </svg>
      <div className="flex justify-between mt-2">
        {labels.map((label, index) => (
          <span key={index} className="text-xs text-gray-500">{label}</span>
        ))}
      </div>
    </div>
  );
}