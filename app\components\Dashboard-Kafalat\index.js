import React, { useEffect } from 'react';
import { Container, Typography, Paper, Box, Grid, CircularProgress } from '@mui/material';
import HomePage from '../../containers/HomePage';
import { PieCard, pieColors } from '../charts/PieCard';
import { createStructuredSelector } from 'reselect';
import { makeSelectKafalatDashboard, makeSelectKafalatDashboardLoading, makeSelectKafalatDashboardError, makeSelectKafalatDashboardSuccess } from '../Dashboard-General/selector';
import { fetchKafalatDashboard } from '../Dashboard-General/actions';
import { useDispatch, useSelector } from 'react-redux';
import { useInjectReducer, useInjectSaga } from 'redux-injectors';
import generalDashboardReducer from '../Dashboard-General/reducer';
import generalDashboardSaga from '../Dashboard-General/saga';
import <PERSON><PERSON><PERSON><PERSON><PERSON> from 'components/charts/AnimatedLineChart';
import Pie<PERSON>hartIcon from '@mui/icons-material/PieChart';
import { AnimatedBar<PERSON><PERSON> } from 'components/charts/AnimatedBarChart';
const stateSelector = createStructuredSelector({
  data: makeSelectKafalatDashboard,
  loading: makeSelectKafalatDashboardLoading,
  error: makeSelectKafalatDashboardError,
  success: makeSelectKafalatDashboardSuccess
});


export default function DashboardKafalat() {
  useInjectReducer({ key: 'dashboardAll', reducer: generalDashboardReducer });
  useInjectSaga({ key: 'dashboardAll', saga: generalDashboardSaga });

  const { data, loading, error, success } = useSelector(stateSelector);

  const dispatch = useDispatch();

  useEffect(() => {
    dispatch(fetchKafalatDashboard());
  }, [dispatch]);

  useEffect(() => {
    if (success) {
      console.log('data from kafalat dashboard : ', data);
    }
  }, [success]);



  return (
    <>
      <HomePage />
      {loading && (
          <Box
            sx={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              zIndex: 1,
              borderRadius: '20px'
            }}
          >
            <CircularProgress sx={{ color: 'black' }} />
          </Box>
        )}
      {success && data && data.kafalatByMonth && data.kafalatByStatus && data.kafalatByService && (
        <Container maxWidth="xl" sx={{ mt: 2, mb: 4 }}>
          <Grid container spacing={2}>
            <Grid item xs={12} md={6}>
              <AnimatedLineChart
                title="Évolution mensuelle des Kafalats"
                data={data.kafalatByMonth}
                icon={<PieChartIcon color="primary" />}
                colors={[pieColors.info, pieColors.error, pieColors.primary, pieColors.success, pieColors.warning]}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <PieCard
                title="Répartition des Kafalats par statut"
                data={Object.values(data.kafalatByStatus)}
                labels={Object.keys(data.kafalatByStatus)}
                icon={<PieChartIcon color="primary" />}
                colors={[pieColors.info, pieColors.error, pieColors.primary, pieColors.success, pieColors.warning]}
              />
            </Grid>
          </Grid>
          <Grid container spacing={2} sx={{ mt: 2 }}>
            <Grid item xs={12} md={12}>
              <AnimatedBarChart
                title="Répartition des Kafalats par service"
                data={Object.values(data.kafalatByService)}
                labels={Object.keys(data.kafalatByService)}
                icon={<PieChartIcon color="primary" />}
                colors={[pieColors.info, pieColors.error, pieColors.primary, pieColors.success, pieColors.warning]}
              />
            </Grid>
          </Grid>
        </Container>
      )}
    </>
  );
} 