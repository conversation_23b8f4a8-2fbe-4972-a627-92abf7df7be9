import React, { useEffect, useState } from 'react';
import { Stack, Typography } from '@mui/material';
import { Alert } from 'react-bootstrap';
import { Link, useHistory, useLocation } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { useInjectReducer, useInjectSaga } from 'redux-injectors';
import { createStructuredSelector } from 'reselect';
import listStyles from 'Css/list.css';
import btnStyles from 'Css/button.css';
import request from 'utils/request';
import CustomPagination from 'components/Common/CustomPagination';
import GenericFilter from 'containers/Common/Filter/GenericFilter';
import ListCandidates from 'components/Beneficiary/ListCandidates';
import { HasSingleAccess } from 'utils/hasAccess';
import ExportIconSvg from 'images/icons/ExportIconSvg';
import PlusIconSVG from 'images/icons/PlusIconSvg';
import saga from './saga';
import reducer from './reducer';
import reducerAdd from '../BeneficiaryAdd/reducer';
import { exportCandidateToCsv, loadCandidats } from './actions';
import {
  makeSekectExporting,
  makeSelectBeneficiaries,
  makeSelectCandidats,
  makeSelectError,
  makeSelectLoading,
} from './selectors';
import {
  deleteBenefiaryReset,
  resetBeneficiary,
} from '../BeneficiaryAdd/actions';
import {
  makeSelectDeleteSuccess,
  makeSelectErrorFromAdd,
  makeSelectSuccess,
} from '../BeneficiaryAdd/selectors';
import Wrapper from './Wrapper';
import { makeSelectTagList } from 'containers/tag/selectors';
import { getTagsByType } from 'containers/tag/actions';
import tagReducer from 'containers/tag/reducer';
import tagSaga from 'containers/tag/saga';

// const key = 'beneficiaryList';
const key2 = 'beneficiaryAdd';
const key = 'candidatList';
const keyTag = 'tagList';

const omdbSelector = createStructuredSelector({
  beneficiaries: makeSelectBeneficiaries,
  error: makeSelectError,
  loading: makeSelectLoading,
  exportLoading: makeSekectExporting,
  candidats: makeSelectCandidats,
});

const omdbSelector2 = createStructuredSelector({
  success: makeSelectSuccess,
  successDelete: makeSelectDeleteSuccess,
  errorDelete: makeSelectErrorFromAdd,
});

const tagSelector = createStructuredSelector({
  tagList: makeSelectTagList,
});

export default function Candidat() {
  const history = useHistory();
  const [activePage, setActivePage] = useState(1);
  // const [itemsCountPerPage, setItemsCountPerPage] = useState(1);
  // const [totalItemsCount, setTotalItemsCount] = useState(1);
  const [showAlert, setShowAlert] = useState(false);
  const [alertMessage, setAlertMessage] = useState('');

  // const [cities, setCities] = useState(null);
  const [services, setServices] = useState(null);
  const [statuses, setStatuses] = useState(null);
  const [errorMessages, setErrorMessages] = useState('');

  const location = useLocation();

  const dispatch = useDispatch();
  const { candidats, error, loading, exportLoading } = useSelector(
    omdbSelector,
  ); 
   useInjectReducer({ key: keyTag, reducer: tagReducer });
  useInjectSaga({ key: keyTag, saga: tagSaga });

  const { success, successDelete, errorDelete } = useSelector(omdbSelector2);
  const { tagList = [] } = useSelector(tagSelector);

  const [activeButton, setActiveButton] = useState('tous candidats');

  const [isCandidateInitial, setIsCandidateInitial] = useState(false);
  const [isValidateAssistant, setIsValidateAssistant] = useState(false);
  const [isValidateKafalat, setIsValidateKafalat] = useState(false);

  useInjectReducer({ key, reducer });
  useInjectSaga({ key, saga });

  useInjectReducer({ key: key2, reducer: reducerAdd });

  // Inject tag reducer and saga

  const principalInputsConfig = [
    {
      field: 'searchByNom',
      type: 'text',
      placeholder: 'Nom du candidat',
      label: 'Nom du candidat',
    },
    {
      field: 'lastNameAr',
      type: 'text',
      placeholder: '   اسم المترشح ',
      label: 'اسم المترشح',
      lang: 'ar',
    },
  ];

  const additionalInputsConfig = [
    {
      field: 'searchByTypeBeneficiaire',
      type: 'select',
      placeholder: 'Type du candidat',
      options: [
        { value: 'true', label: 'Indépendant' },
        { value: 'false', label: 'Membre famille' },
      ],
    },
    /*
    {
      field: 'searchByService',
      type: 'select',
      placeholder: 'Service',
      options:
        services && services.data
          ? services.data.map(service => ({
              value: service.id,
              label: service.name,
            }))
          : [],
    },
    {
      field: 'searchByStatut',
      type: 'select',
      placeholder: 'Statut',
      options:
        statuses && statuses.data
          ? statuses.data.map(statut => ({
              value: statut.id,
              label: statut.name,
            }))
          : [],
    },

     */
    {
      field: 'searchByNumTel',
      type: 'text',
      placeholder: 'Numéro Téléphone',
    },
    {
      field: 'searchByTagId',
      type: 'select',
      placeholder: 'Tag',
      options: tagList && tagList.length > 0 ? tagList.map(tag => ({
        value: tag.id,
        label: tag.name,
      })) : [],
    },
    {
      field: 'minDate',
      type: 'date',
      placeholder: 'Date de naissance minimale',
    },
    {
      field: 'maxDate',
      type: 'date',
      placeholder: 'Date de naissance maximale',
    },
  ];

  const isCandidate = true;

  const [filterValues, setFilterValues] = useState({
    searchByNom: '',
    lastNameAr: '',
    searchByTypeBeneficiaire: '',
    searchByService: '',
    searchByStatut: '',
    searchByNumTel: '',
    searchByTagId: '',
    minDate: '',
    maxDate: '',
  });

  const handleFilterApply = filters => {
    // dispatch(loadBeneficiaries(activePage - 1, null, null, null, filters));
    dispatch(
      loadCandidats(
        0,
        null,
        null,
        null,
        filters,
        isCandidateInitial,
        isValidateAssistant,
        isValidateKafalat,
        isCandidate,
      ),
    );
  };

  const handleResetFilterComplete = () => {
    setFilterValues({
      searchByNom: '',
      lastNameAr: '',
      searchByTypeBeneficiaire: '',
      searchByService: '',
      searchByStatut: '',
      searchByNumTel: '',
      searchByTagId: '',
      minDate: '',
      maxDate: '',
    });
  };

  const handlePageChange = pageNumber => {
    setActivePage(pageNumber - 1);
    setFilterValues(prevFilterValues => {
      dispatch(
        // loadBeneficiaries(pageNumber - 1, null, null, null, prevFilterValues),
        loadCandidats(
          pageNumber - 1,
          null,
          null,
          null,
          prevFilterValues,
          isCandidateInitial,
          isValidateAssistant,
          isValidateKafalat,
          isCandidate,
        ),
      );
      return prevFilterValues;
    });
  };

  const handleExport = () => {
    dispatch(
      exportCandidateToCsv(
        filterValues,
        isCandidateInitial,
        isValidateAssistant,
        isValidateKafalat,
        isCandidate,
      ),
    );
  };

  const loadServices = () => {
    request('/ref/services')
      .then(data => {
        const serviceData = data || [];
        setServices(serviceData);
      })
      .catch(error => alert(error));
  };

  const loadStatuses = () => {
    request('/ref/statuses')
      .then(data => {
        const statusData = data || [];
        setStatuses(statusData);
      })
      .catch(error => alert(error));
  };

  useEffect(() => {
    if (location.state && location.state.showMsg) {
      if (location.state.data) {
        const { code, id } = location.state.data;
        const beneficiaryUrl = `/beneficiaries/fiche/${id}/info`;

        const successMessage =
          location.state.type === 'edit' ? (
            <span className="alert-style">
              Le pré-candidat avec le code {code} a été modifié avec succès.{' '}
              <Link to={beneficiaryUrl} className="alert-link">
                {' '}
                Cliquez ici pour plus de détails.
              </Link>
            </span>
          ) : (
            <span className="alert-style">
              Le pré-candidat avec le code {code} a été ajouté avec succès.{' '}
              <Link to={beneficiaryUrl} className="alert-link" >
                {' '}
                Cliquez ici pour plus de détails.
              </Link>
            </span>
          );

        setAlertMessage(successMessage);
      }
      setShowAlert(true);
      history.replace('/candidats', null);
    }
  }, [location.state, history]);

  useEffect(() => {
    if (success) {
      dispatch(resetBeneficiary());
    }
  }, [success]);

  useEffect(() => {
    if (successDelete) {
      setAlertMessage('Le candidat a été supprimé avec succès.');
      setShowAlert(true);
      dispatch(
        loadCandidats(
          activePage - 1,
          null,
          null,
          null,
          filterValues,
          isCandidateInitial,
          isValidateAssistant,
          isValidateKafalat,
          isCandidate,
        ),
      );
      dispatch(deleteBenefiaryReset());
    }
  }, [successDelete]);

  useEffect(() => {
    if (errorDelete) {
      setErrorMessages(
        "Une erreur s'est produite lors de la suppression du bénéficiaire, ce bénéficiaire est lié à des données.",
      );
      dispatch(deleteBenefiaryReset());
    }
  }, [errorDelete]);

  useEffect(() => {
    if (candidats) {
      setActivePage(candidats.pageable.pageNumber + 1);
      // setItemsCountPerPage(candidats.size);
      // setTotalItemsCount(candidats.totalElements);
    }
  }, [candidats]);

  useEffect(() => {
    if (!services) {
      loadServices();
    }
  }, [services]);

  useEffect(() => {
    if (!statuses) {
      loadStatuses();
    }
  }, [statuses]);

  useEffect(() => {
    // Check if there is an error and if it's a 403 error
    if (error && error.response && error.response.status === 403) {
      setErrorMessages("Votre profil n'a pas accès au module Bénéficiaire.");
    }
  }, [error]);

  useEffect(() => {
    if (showAlert) {
      const timer = setTimeout(() => {
        setShowAlert(false);
      }, 10000);
      return () => clearTimeout(timer);
    }
  }, [showAlert]);

  let listBeneficiaries = (
    <div className="d-flex justify-content-center">
      {loading && (
        <div
          className="spinner-border"
          style={{ width: '5rem', height: '5rem' }}
          role="status"
        >
          <span className="sr-only">Loading...</span>
        </div>
      )}
    </div>
  );
  if (candidats) {
    listBeneficiaries = <ListCandidates candidats={candidats}></ListCandidates>;
  }
  let messageSucces = null;

  if (showAlert) {
    messageSucces = (
      <Alert
        className="alert-style"
        variant="success"
        onClose={() => setShowAlert(false)}
        dismissible
      >
        <p>{alertMessage}</p>
      </Alert>
    );
  }

  const handleButtonClick = status => {
    setActiveButton(status);
    setIsCandidateInitial(false);
    setIsValidateAssistant(false);
    setIsValidateKafalat(false);
    if (status === 'Candidats Initial') {
      setIsCandidateInitial(true);
    } else if (status === 'Candidats Valider par les assistants') {
      setIsValidateAssistant(true);
    } else if (status === 'Candidats Valider par Service Kafalat') {
      setIsValidateKafalat(true);
    }
  };

  useEffect(() => {
    dispatch(
      loadCandidats(
        0,
        null,
        null,
        null,
        filterValues,
        isCandidateInitial,
        isValidateAssistant,
        isValidateKafalat,
        isCandidate,
      ),
    );
  }, [isCandidateInitial, isValidateAssistant, isValidateKafalat]);

  // Load tags for the tag selector
  useEffect(() => {
    dispatch(getTagsByType('beneficiary'));
  }, [dispatch]);

  return (
    <Wrapper>
      {messageSucces}
      {errorMessages && (
        <Alert
          className="pb-0"
          variant="danger"
          onClose={() => {
            setErrorMessages('');
          }}
          dismissible
        >
          <p>{errorMessages}</p>
        </Alert>
      )}
      <div className={listStyles.backgroundStyle}>
        <div className="filter-container">
          <div className="tabs">
            <button
              className={`tab ${
                activeButton === 'tous candidats' ? 'active' : ''
              }`}
              onClick={() => handleButtonClick('tous candidats')}
            >
              Tous les Pré-Candidats
            </button>
            <button
              className={`tab ${
                activeButton === 'Candidats Initial' ? 'active' : ''
              }`}
              onClick={() => handleButtonClick('Candidats Initial')}
            >
              Pré-Candidats Initiaux
            </button>
            <button
              className={`tab ${
                activeButton === 'Candidats Valider par les assistants'
                  ? 'active'
                  : ''
              }`}
              onClick={() =>
                handleButtonClick('Candidats Valider par les assistants')
              }
            >
              Pré-Candidats Validés Par Les Assistants
            </button>
            <button
              className={`tab ${
                activeButton === 'Candidats Valider par Service Kafalat'
                  ? 'active'
                  : ''
              }`}
              onClick={() =>
                handleButtonClick('Candidats Valider par Service Kafalat')
              }
            >
              Pré-Candidats Validés Par Service Kafalat
            </button>
          </div>
        </div>
        <GenericFilter
          className="subFilter-container"
          principalInputsConfig={principalInputsConfig}
          additionalInputsConfig={additionalInputsConfig}
          onApplyFilter={handleFilterApply}
          filterValues={filterValues}
          setFilterValues={setFilterValues}
          onResetFilterComplete={handleResetFilterComplete}
          onExport={handleExport}
          data={candidats.content}
          exportLoading={exportLoading}
          showExportButton={false}
        />

        <Stack direction="row" className="title-section">
          <Typography variant="h6_Lexend_Medium" color="primary.main">
            Liste des Pré-Candidats
          </Typography>
          <Stack direction="row" gap="10px">
            <button
              className={`export-btn ${btnStyles.iconButton}`}
              onClick={handleExport}
              disabled={
                !candidats.content ||
                candidats.content.length === 0 ||
                exportLoading
              }
            >
              {exportLoading ? (
                <i
                  className="fa fa-spinner fa-spin"
                  style={{ color: '#4F89D7' }}
                /> // Display spinner icon if export is loading
              ) : (
                <ExportIconSvg />
              )}
            </button>
            <HasSingleAccess feature="GERER_CANDIDATS" privilege="WRITE">
              <Link
                to={{
                  pathname: '/beneficiaries/add',
                  state: { candidate: true },
                }}
              >
                <button className={`m-0 ${btnStyles.addBtnProfile}`}>
                  <PlusIconSVG />
                  Ajouter un candidat
                </button>
              </Link>
            </HasSingleAccess>
          </Stack>
        </Stack>
        <div className="table-Container">
          {listBeneficiaries}
          <Stack
            direction="row"
            className="justify-content-between align-items-center mt-3"
          >
            <CustomPagination
              totalElements={candidats.totalElements}
              onPageChange={handlePageChange}
              totalCount={candidats ? candidats.totalPages : null}
              currentPage={candidats ? candidats.number + 1 : activePage}
              pageSize={candidats ? candidats.pageSize : 10}
            />
          </Stack>
        </div>
      </div>
    </Wrapper>
  );
}
