import React, { useEffect, useRef, useState } from 'react';
import { Field, Form, Formik } from 'formik';
import * as Yup from 'yup';
import { useInjectReducer, useInjectSaga } from 'redux-injectors';
import { useDispatch, useSelector } from 'react-redux';
import { createStructuredSelector } from 'reselect';
import { useHistory, useLocation, useParams } from 'react-router-dom';
import TagSelector from 'containers/Common/SubComponents/TagSelector';
import { makeSelectTagList } from 'containers/tag/selectors';
import { getTagsByType } from 'containers/tag/actions';
import tagReducer from 'containers/tag/reducer';
import tagSaga from 'containers/tag/saga';

import { Alert } from 'react-bootstrap';
import moment from 'moment';
import { Switch } from 'formik-material-ui';
import {
  addAideComplementaire,
  loadAideComplementaire,
  resetAideComplementaire,
} from './actions';

import {
  makeSelectAideComplementaire,
  makeSelectBeneficiairesWithTypePriseEnCharge,
  makeSelectError,
  makeSelectSuccess,
} from './selectors';
import { makeSelectDonors } from '../../Donor/Donors/selectors';
import { CustomTextInput } from '../../Common/CustomInputs/CustomTextInput';
import aideComplementaireReducer from './reducer';
import aideComplementaireSaga from './saga';
import { CustomSelect } from '../../Common/CustomInputs/CustomSelect';
import { CustomTextArea } from '../../Common/CustomInputs/CustomTextArea';
import serviceReducer from '../../Service/ListService/reducer';
import serviceListSaga from '../../Service/ListService/saga';
import { makeSelectActiveServices } from '../../Service/ListService/selectors';
import { fetchActiveServicesRequest } from '../../Service/ListService/actions';
import { ServiceTypeEnum } from '../../Service/ListService/enum';
import ServiceCategories from 'containers/Common/SubComponents/ServiceCategories';

const keyAideComplementaireAdd = 'aideComplementaireAdd';
const key3 = 'serviceList';
const keyTag = 'tagList';

const formatDate = 'YYYY-MM-DD';

const omdbSelector = createStructuredSelector({
  aideComplementaire: makeSelectAideComplementaire,
  donors: makeSelectDonors,
  success: makeSelectSuccess,
  error: makeSelectError,
  beneficiairesWithTypePriseEnCharge: makeSelectBeneficiairesWithTypePriseEnCharge,
  tagList: makeSelectTagList,
});



const omdbSelector3 = createStructuredSelector({
  activeServices: makeSelectActiveServices,
});

const uploadIcon = require('../../../images/icons/upload.svg');

const backgroundStyle = {
  backgroundColor: 'white',
  border: '2px solid white ',
  borderRadius: '10px',
};

const backgroundStyleDocument = {
  backgroundColor: '#F1F4F6',
  border: '2px solid white ',
  borderRadius: '10px',
};

export default function AddAideComplementaire(globalProps) {
  useInjectReducer({
    key: keyAideComplementaireAdd,
    reducer: aideComplementaireReducer,
  });
  useInjectSaga({
    key: keyAideComplementaireAdd,
    saga: aideComplementaireSaga,
  });

  useInjectReducer({ key: key3, reducer: serviceReducer });
  useInjectSaga({ key: key3, saga: serviceListSaga });

  useInjectReducer({ key: keyTag, reducer: tagReducer });
  useInjectSaga({ key: keyTag, saga: tagSaga });

  const dispatch = useDispatch();

  const {
    success,
    error,
    donors,
    aideComplementaire,
    beneficiairesWithTypePriseEnCharge,
    tagList,
  } = useSelector(omdbSelector);

  const { activeServices } = useSelector(omdbSelector3);
  let activeServicesList = null;


  const [servicesByCategory,setServicesByCategory]=useState();
  useEffect(() => {
    if(servicesByCategory){
      dispatch(fetchActiveServicesRequest(servicesByCategory));
    }
  }, [servicesByCategory]);

  // Load tags when component mounts
  useEffect(() => {
    dispatch(getTagsByType('aideComplementaire'));
  }, [dispatch]);
  const SelectCategoryHandler = value => {
    setServicesByCategory(value.id)
  };

  if (activeServices) {
    activeServicesList = activeServices.map(activeService => (
      <option key={activeService.id} value={activeService.id}>
        {' '}
        {activeService.name}{' '}
      </option>
    ));
  }

  const formikRef = useRef();
  const history = useHistory();
  const params = useParams();
  const location = useLocation();

  const [showAlert, setShowAlert] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [selectedService, setSelectedService] = useState(null);
  const [isStatutPlanifier, setIsStatutPlanifier] = useState(false);
  const [isStatutEnCours, setIsStatutEnCours] = useState(false);
  const [isStatutExecuter, setIsStatutExecuter] = useState(false);


  useEffect(() => {
    if (aideComplementaire) {
      setIsStatutPlanifier(aideComplementaire.statut === 'planifier');
      setIsStatutExecuter(aideComplementaire.statut === 'executer');
      setIsStatutEnCours(aideComplementaire.statut === 'enCours');
      console.log(aideComplementaire.services.serviceCategoryId)
      setServicesByCategory(aideComplementaire.services.serviceCategoryId)
      const newValues = {
        ...formikRef.current.values,
        ...aideComplementaire,
        serviceCategoryId:aideComplementaire.services.serviceCategoryId,
        datePlanification: aideComplementaire.datePlanification
          ? moment(aideComplementaire.datePlanification).format(formatDate)
          : '',
        dateDebut: aideComplementaire.dateDebut
          ? moment(aideComplementaire.dateDebut).format(formatDate)
          : '',
        dateFin: aideComplementaire.dateFin
          ? moment(aideComplementaire.dateFin).format(formatDate)
          : '',
      };

      if (
        JSON.stringify(formikRef.current.values) !== JSON.stringify(newValues)
      ) {
        formikRef.current.setValues(newValues);
      }
    }
  }, [aideComplementaire]);

  const test = formikRef.current;

  /* useEffect(() => {
     if (formikRef.current && formikRef.current.values.typePriseEnChargeId) {
       dispatch(
         loadBeneficiariesWithTypePriseEnCharge(
           formikRef.current.values.typePriseEnChargeId,
         ),
       );
     }
   }, [formikRef.current, formikRef.current.values.typePriseEnChargeId]);

   */

  /* useEffect(() => {
     if (
       formikRef.current &&
       formikRef.current.values &&
       formikRef.current.values.serviceId
     ) {
       const current = formikRef.current.values.montantPrevuParBeneficiary;
       console.log({ current });
     }
   }, [formikRef.current]);

   */

  useEffect(() => {
    if (globalProps.aideComplementaire) {
      console.log('Hello2');
      const aideComplementaireToEdit = globalProps.aideComplementaire;
      const stringified = JSON.stringify(aideComplementaireToEdit);
      const newAideComplementaire = JSON.parse(
        stringified.replaceAll('null', '""'),
      );

      const datePlanification = globalProps.aideComplementaire.datePlanification
        ? moment(globalProps.aideComplementaire.datePlanification).format(
          formatDate,
        )
        : null;

      const dateDebut = globalProps.aideComplementaire.dateDebut
        ? moment(globalProps.aideComplementaire.dateDebut).format(formatDate)
        : null;

      const dateFin = globalProps.aideComplementaire.dateFin
        ? moment(globalProps.aideComplementaire.dateFin).format(formatDate)
        : null;

      const formBody = {
        ...formikRef.current.values,
        ...globalProps.aideComplementaire,
        ...newAideComplementaire,
        datePlanification,
        dateDebut,
        dateFin,
      };

      formikRef.current.setValues(formBody);
    }
  }, [globalProps.aideComplementaire]);

  /* useEffect(() => {
    if (location.state) {
      const { donor } = location.state;
      if (donor) {
        if (formikRef.current) {
          formikRef.current.setFieldValue('donor', donor);
        }
      }
    }

    if (globalProps) {
      if (globalProps.donor) {
        if (formikRef.current) {
          formikRef.current.setFieldValue('donor', globalProps.donor);
        }
      }
    }
  }, []);

   */

  useEffect(
    () =>
      function cleanup() {
        dispatch(resetAideComplementaire());
      },
    [],
  );

  useEffect(() => {
    if (params.idAideComplementaire) {
      formikRef.current.resetForm();
      dispatch(loadAideComplementaire(params.idAideComplementaire));
    }
  }, [params.idAideComplementaire]);

  /* useEffect(() => {
    if (!params.idAideComplementaire) {
      formikRef.current.resetForm();
      dispatch(loadAideComplementaire());
    }
  }, [params.idAideComplementaire]);

   */

  const [
    messageAddedAideComplementaire,
    setMessageAddedAideComplementaire,
  ] = useState('');

  useEffect(() => {
    if (success === true) {
      if (globalProps.profile) {
        globalProps.handleClose();
      } else if (params.idAideComplementaire) {
        const successMessage = params.idAideComplementaire
          ? 'Aide complémentaire modifiée avec succès !'
          : 'Aide complémentaire ajoutée avec succès !';
        setShowAlert(true);
        setMessageAddedAideComplementaire(
          <Alert
            className="alert-style"
            variant="success"
            onClose={() => setShowAlert(false)}
            dismissible
          >
            <p>{successMessage}</p>
          </Alert>,
        );

        if (location.state && location.state.redirectTo) {
          history.push({
            pathname: `/aide-complementaire/fiche/${params.idAideComplementaire}/info`,
            state: 'updateSuccess',
          });
        } else {
          history.push({
            pathname: '/aide-complementaire',
            state: params.idAideComplementaire ? 'updateSuccess' : 'success',
          });
        }
      } else {
        history.push({
          pathname: '/aide-complementaire',
          state: 'success',
        });
      }
      dispatch(resetAideComplementaire());
    }
  }, [
    success,
    params.idAideComplementaire,
    dispatch,
    globalProps,
    history,
    location.state,
  ]);

  useEffect(() => {
    if (success) {
      formikRef.current.resetForm();
      dispatch(resetAideComplementaire());
    }
  }, [success]);

  useEffect(() => {
    if (error) {
      setShowAlert(true);
      setMessageAddedAideComplementaire(
        <Alert
        className="alter-style"

          variant="danger"
          onClose={() => setShowAlert(false)}
          dismissible
        >
          <p>Une erreur est survenue !</p>
        </Alert>,
      );
    }
  }, [error]);

  const handleCancel = () => {
    if (location.state && location.state.redirectTo === 'consultation') {
      history.push({
        pathname: `/aide-complementaire/fiche/${params.idAideComplementaire}/info`,
      });
    } else if (params.idAideComplementaire) {
      history.push('/aide-complementaire');
    } else {
      history.push('/aide-complementaire');
    }
  };

  const [initialized, setInitialized] = useState(false);
  return (
    <div className="mr-5 ml-5 p-3" style={backgroundStyle}>
      {messageAddedAideComplementaire}
      <Formik
        initialValues={{
          serviceId: '',
          slogan:'',
          serviceCategoryId:'',
          name: '',
          montantPrevu: '',
          datePlanification: '',
          dateDebut: '',
          dateFin: '',
          propositionSystem: '',
          priority: '',
          costs: '',
          amountPerBeneficiary: '',
          tags: [],
          commentaire: '',
        }}
        validationSchema={Yup.object({
          dateDebut: Yup.date().required('Date de début est requise'),
          dateFin: Yup.date()
            .required('Date de Fin est requise')
            .test(
              'is-greater',
              'La date de fin doit être supérieure à la date de début',
              function (value) {
                const { dateDebut } = this.parent;
                return value && dateDebut ? value > dateDebut : true;
              },
            ),

          datePlanification: Yup.date().test(
            'is-greater',
            "La date prévue d'exécution doit être supérieure à la date de début",
            function (value) {
              const { dateDebut } = this.parent;
              return value && dateDebut ? value > dateDebut : true;
            },
          ),

          name: Yup.string().required('Nom est requis'),
          serviceId: Yup.string().required('Service est requis'),
          serviceCategoryId:Yup.string().required('Categorie est requis'),

          montantPrevu: Yup.number().required('Montant estimé est requis'),
          costs: Yup.number()
            .required('Les frais sont requis')
            .min(0, 'Le pourcentage des frais doit être supérieur ou égal à 0')
            .max(
              100,
              'Le pourcentage des frais doit être inférieur ou égal à 100',
            ),
          amountPerBeneficiary: Yup.number().required(
            'Montant par bénéficiaire est requis',
          ),
        })}
        innerRef={formikRef}
        onSubmit={(values, { setSubmitting, resetForm }) => {
          setIsSubmitting(true);
          setIsLoading(true);

          const formattedDatePlanification = values.datePlanification
            ? moment(values.datePlanification).format('YYYY-MM-DDTHH:mm:ss')
            : null;

          const formattedDateDebut = values.dateDebut
            ? moment(values.dateDebut).format('YYYY-MM-DDTHH:mm:ss')
            : null;

          const formattedDateFin = values.dateFin
            ? moment(values.dateFin).format('YYYY-MM-DDTHH:mm:ss')
            : null;

          setSubmitting(false);

          const body = {
            ...values,
            datePlanification: formattedDatePlanification,
            dateDebut: formattedDateDebut,
            dateFin: formattedDateFin,
          };
          dispatch(addAideComplementaire(body));
        }}
      >
        {props => {
          const { values, setFieldValue } = props;

          const handleServiceChange = selectedId => {
            const selectedService = activeServices.find(
              service => service.id === Number(selectedId),
            );

            if (selectedService) {
              setFieldValue('priority', selectedService.priority);
              setFieldValue(
                'propositionSystem',
                selectedService.propositionSystem,
              );
              setFieldValue('costs', selectedService.costs);
              setFieldValue(
                'amountPerBeneficiary',
                selectedService.amountPerBeneficiary,
              );
            }
          };

          return (
            <Form
              onKeyDown={e => {
                if (e.key === 'Enter') {
                  e.preventDefault();
                }
              }}
            >
              {globalProps.profile ? null : (
                <h3 className="m-2">
                  {params.idAideComplementaire
                    ? 'Modifier Aide Complémentaire'
                    : 'Ajouter une Aide Complémentaire'}
                </h3>
              )}
              <div className="d-flex justify-content-center mt-5"></div>
              <div>
                <div className="d-flex justify-content-between">
                  <div className="form-group col-md-6 pl-0">
                    <CustomTextInput
                      label="Nom"
                      isRequired
                      name="name"
                      placeholder="Saisir le nom"
                      formProps={props}
                      type="text"
                    />
                  </div>
                  <div className="form-group col-md-6 pl-0">
                    <CustomTextInput
                      label="Slogan"
                      name="slogan"
                      placeholder="Saisir le slogan"
                      formProps={props}
                      type="text"
                    />
                  </div>
                </div>
                <div className="d-flex justify-content-between">
                <div className="form-group col-md-6 pl-0">
                    <ServiceCategories
                      name="serviceCategoryId"
                      value={props.values.serviceCategoryId}
                      label="Catégorie"
                      // formProps={props}
                      forAddAide={"true"}
                      onSelectCanal={SelectCategoryHandler}
                      isRequired
                      disabled={!!params.idAideComplementaire}
                    />
                  </div>
                  <div className="form-group col-md-6 pl-0">
                    <CustomSelect
                      // isMeta={true}
                      unique
                      label="Service"
                      isRequired
                      name="serviceId"
                      formProps={props}
                      onChange={event => {
                        const selectedId = event.target.value;
                        props.setFieldValue('serviceId', selectedId);
                        handleServiceChange(selectedId);
                      }}
                      disabled={!!params.idAideComplementaire}
                    >
                      <option value="">-- Service --</option>
                      {activeServicesList}
                    </CustomSelect>
                  </div>

                </div>
                <div className="d-flex justify-content-between">
                  <div className="form-group col-md-6 pl-0">
                    <CustomTextInput
                      label="Date de début"
                      type="date"
                      isRequired
                      name="dateDebut"
                      formProps={props}
                      disabled={
                        !!params.idAideComplementaire && !isStatutPlanifier
                      }
                    />
                  </div>
                  <div className="form-group col-md-6 pl-0">
                    <CustomTextInput
                      label="Date de fin"
                      type="date"
                      name="dateFin"
                      isRequired
                      formProps={props}
                    />
                  </div>
                </div>
                <div className="d-flex justify-content-between">
                  <div className="form-group col-md-6 pl-0">
                    <CustomTextInput
                      label="Date prévue d'éxécution"
                      type="date"
                      name="datePlanification"
                      formProps={props}
                    />
                  </div>
                  <div className="form-group col-md-6 pl-0">
                    <CustomTextInput
                      label="Montant souhaité pour la campagne (DH)"
                      isRequired
                      name="montantPrevu"
                      placeholder="Saisir le montant souhaité pour la campagne"
                      formProps={props}
                      type="number"
                    />
                  </div>
                </div>
                <div className="d-flex justify-content-between">
                  <div className="form-group col-md-6 pl-0">
                    <CustomTextInput
                      label="Frais de gestion (%)"
                      isRequired
                      name="costs"
                      placeholder="Saisir le pourcentage des frais de gestion"
                      formProps={props}
                      type="number"
                      disabled={
                        !!params.idAideComplementaire &&
                        !isStatutPlanifier &&
                        !isStatutEnCours
                      }
                    />
                  </div>
                  <div className="form-group col-md-6 pl-0">
                    <CustomTextInput
                      label="Montant par bénéficiaire (DH)"
                      isRequired
                      name="amountPerBeneficiary"
                      placeholder="Saisir le montant par bénéficiaire"
                      formProps={props}
                      type="number"
                      disabled={!!params.idAideComplementaire}
                    />
                  </div>
                </div>
                <div className="form-row pt-2">
                  {/* <div
                    className="form-group col-md-6 d-flex align-items-center"
                    style={{ display: 'flex', alignItems: 'center' }}
                  >
                    <label style={{ marginBottom: 0, marginRight: '10px' }}>
                      Proposer une version initiale ?
                    </label>
                    <div style={{ display: 'flex', alignItems: 'center' }}>
                      <span style={{ marginRight: '8px' }}>Non</span>
                      <Field
                        component={Switch}
                        type="checkbox"
                        name="propositionSystem"
                        color="primary"
                        checked={values.propositionSystem}
                        onChange={() => {
                          const newPropositionSystemValue = !values.propositionSystem;
                          setFieldValue(
                            'propositionSystem',
                            newPropositionSystemValue,
                          );

                          if (!newPropositionSystemValue) {
                            setFieldValue('priority', false);
                          }
                        }}
                        disabled={!!params.idAideComplementaire}
                      />
                      <span style={{ marginLeft: '8px' }}>Oui</span>
                    </div>
                  </div>
                  */}
                  <div
                    className="form-group col-md-6 d-flex align-items-center"
                    style={{ display: 'flex', alignItems: 'center' }}
                  >
                    <label style={{ marginBottom: 0, marginRight: '10px' }}>
                      Prioriser les bénéficiaires Kafalt des donateurs ?
                    </label>
                    <div style={{ display: 'flex', alignItems: 'center' }}>
                      <span style={{ marginRight: '8px' }}>Non</span>
                      <Field
                        component={Switch}
                        type="checkbox"
                        name="priority"
                        color="primary"
                        checked={values.priority}
                        disabled={!!params.idAideComplementaire}
                      />
                      <span style={{ marginLeft: '8px' }}>Oui</span>
                    </div>
                  </div>
                </div>
                <div className="form-row">
                  <div className="form-group col-md-7"> 
                    <TagSelector
                      formProps={props}
                      taggableType="aideComplementaire"
                      disabled={false}
                    />
                  </div>
                </div>
                <div className="form-row">
                  <div className="form-group col-md-7">
                    <CustomTextArea
                      label="Commentaire"
                      name="commentaire"
                      placeholder="Saisir un commentaire"
                    />
                  </div>
                </div>
              </div>

              <div className="d-flex gap-10 justify-content-end align-items-center">
                <button
                  className="btn-style primary"
                  type="button"
                  onClick={props.submitForm}
                  disabled={props.isSubmitting || isLoading}
                >
                  {isSubmitting
                    ? 'En cours...'
                    : globalProps.edit || params.idAideComplementaire
                    ? 'Modifier'
                    : 'Enregistrer'}
                </button>
                <button
                  className="btn-style secondary mr-2"
                  type="button"
                  onClick={handleCancel}
                >
                  Annuler
                </button>
              </div>
            </Form>
          );
        }}
      </Formik>
    </div>
  );
}
