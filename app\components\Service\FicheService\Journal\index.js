import React, { useEffect, useState } from 'react';
import navigationStyle from 'Css/sectionNavigation.css';
import { useDispatch, useSelector } from 'react-redux';
import { createStructuredSelector } from 'reselect';
import { Alert } from 'react-bootstrap';
import JournalTable from '../Journal/JournaTable';
import JournalGraph from '../Journal/JournalGraphe';
import serviceReducer from 'containers/Service/ListService/reducer';
import serviceListSaga from 'containers/Service/ListService/saga';

import { makeSelectBudgetLineService } from 'containers/Service/ListService/selectors';
import { loadBudgetLineService } from 'containers/Service/ListService/actions';
import { useInjectReducer, useInjectSaga } from 'redux-injectors';
import stylesList from 'Css/profileList.css';

const JOURNALTABLE = 'journalTable';
const JOUNALGRAPH = 'journalGraph';

const key = 'serviceList';

const omdbSelector = createStructuredSelector({
  budgetLineService: makeSelectBudgetLineService,
});
export default function journal(props) {
  useInjectReducer({ key, reducer: serviceReducer });
  useInjectSaga({ key, saga: serviceListSaga });

  let service = props.data;
  const { budgetLineService } = useSelector(omdbSelector);
  const dispatch = useDispatch();

  const [section, setSection] = useState(JOUNALGRAPH);
  const [message, setMessage] = useState('');
  const [showAlert, setShowAlert] = useState(false);

  let successAlert = null;

  let journal = null;

  useEffect(() => {
    if (service && service.id) {
      dispatch(loadBudgetLineService(service.id));
    }
  }, [service]);

  if (showAlert) {
    successAlert = (
      <Alert className="alert-style" variant="success" onClose={() => setShowAlert(false)} dismissible>
        {message}
      </Alert>
    );
  }

  if (budgetLineService) {
    if (section == JOURNALTABLE) {
      journal = <JournalTable data={budgetLineService} />;
    } else {
      journal = <JournalGraph data={budgetLineService} />;
    }
  }

  return (
    <div>
      {successAlert}
      <div >
          <div className={stylesList.tabs}>
              <button
                  className={`${stylesList.tab} ${section === JOUNALGRAPH ? stylesList.active : ''}`}
                  onClick={() => {setSection(JOUNALGRAPH)}}
              >
                  Statistique des Entrées et Sorties
              </button>
              <button
                  className={`${stylesList.tab} ${section === JOURNALTABLE ? stylesList.active : ''}`}
                  onClick={() => {setSection(JOURNALTABLE)}}
              >
                  Liste des Opérations Entrées et Sorties
              </button>
          </div>
        <div className={navigationStyle.divIncomes}>{journal}</div>
      </div>
    </div>
  );
}
