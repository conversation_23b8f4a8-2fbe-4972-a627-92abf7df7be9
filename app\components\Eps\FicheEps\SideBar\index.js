import React from 'react';
import sideBarStyles from '../../../../Css/sideBar.css';
import tagStyles from '../../../../Css/tag.css';
import moment from 'moment';
import { Link, useHistory } from 'react-router-dom';

const formatDate = (date) => moment(date).format('DD/MM/YYYY');

const SideBar = ({ data: epsData }) => {
  // Calculate total beneficiaries
  const totalBeneficiaries = epsData.zone ? epsData.zone.totalBenecifiaries : 0;

  // Get the EPS status (active or inactive)
  const getStatusTag = (status) => (status ? tagStyles.tagGreen : tagStyles.tagRed);
  const getStatusText = (status) => (status ? 'Actif' : 'Inactif');

  // Get assistant name
  const assistantName =
    epsData.zone && epsData.zone.assistantName
      ? epsData.zone.assistantName
      : 'Aucun assistant assigné';

  return (
    <div className={sideBarStyles.sideBar}>
      <div className="text-center mt-4">
        <h5 className="mb-3"> EPS</h5>
        <div className={sideBarStyles.infoContainer}>
          <p><strong>Nom :</strong> {epsData.name}</p>
          <p><strong>Nom (Arabe) :</strong> {epsData.nameAr}</p>
          <p><strong>Code :</strong> {epsData.code}</p>
          <p className="text-center"><strong>Statut :</strong></p>
          <div className="d-flex justify-content-center">
            <span className={getStatusTag(epsData.status)}>{getStatusText(epsData.status)}</span>
          </div>
          {epsData.creationDate && (
            <p><strong>Créé le :</strong> {formatDate(epsData.creationDate)}</p>
          )}
        </div>
      </div>

      <hr className={sideBarStyles.hr} />

      <div className={`text-center ${sideBarStyles.label}`}>
        <div className={sideBarStyles.infoContainer}>
          <p><strong>Nombre total de bénéficiaires :</strong> {totalBeneficiaries}</p>
          <p><strong>Zone associée :</strong>  {epsData.zone ? <Link to={`/zones/fiche/${epsData.zone.id}/beneficiaries`} >{epsData.zone.name} </Link> : 'Aucune zone associée'}</p>


          <p>
            <strong>Assistant associé :</strong>
            {epsData && epsData.zone && epsData.zone.assistantId ? (
              <Link to={`/assistants/consultAssistant/${epsData.zone.assistantId}/1`}>
                {epsData.zone.assistantName}
              </Link>
            ) : (
              <span>Aucun assistant assigné</span> // Fallback message if data is missing
            )}
          </p>


        </div>
      </div>
    </div>
  );
};

export default SideBar;
