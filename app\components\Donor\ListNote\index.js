import React, { useEffect, useState } from 'react';
import moment from 'moment';
import NoteForm from 'containers/Common/SubComponents/NoteForm';
import {
  makeSelecNote,
  makeSelecNoteDeleteSuccess,
  makeSelecNotes,
  makeSelectError,
  makeSelectNotesAddDonorSuccess,
  makeSelectSuccess,
} from 'containers/Common/SubComponents/NoteForm/selectors';
import {
  deleteNote,
  loadNoteData,
  resetNote,
} from 'containers/Common/SubComponents/NoteForm/actions';
import { useParams } from 'react-router-dom';

import { createStructuredSelector } from 'reselect';
import { useDispatch, useSelector } from 'react-redux';
import { useInjectReducer, useInjectSaga } from 'redux-injectors';
import { deleteNoteSaga } from 'containers/Common/SubComponents/NoteForm/saga';
import reducer from 'containers/Common/SubComponents/NoteForm/reducer';

import { removeNoteDonor } from 'containers/Donor/DonorProfile/actions';
import Modal2 from 'components/Common/Modal2';
import { Alert } from 'react-bootstrap';
import AccessControl, { isAuthorized } from 'utils/AccessControl';
import btnStyles from '../../../Css/button.css';
import list from '../../../Css/profileList.css';
import {
  DELETE_ICON,
  EDIT_ICON,
  VIEW_ICON,
} from '../../Common/ListIcons/ListIcons';
import DataTable from '../../Common/DataTable';
import CustomPagination from '../../Common/CustomPagination';

const key = 'note';

const target = 'donor';

const formatDate = date => moment(date).format('DD/MM/YYYY');

const omdbSelector = createStructuredSelector({
  success: makeSelectSuccess,
  error: makeSelectError,
  note: makeSelecNote,
  successDelete: makeSelecNoteDeleteSuccess,
  notes: makeSelecNotes,
  noteDonorAddSuccess: makeSelectNotesAddDonorSuccess,
});

export default function listNotes(props) {
  const dispatch = useDispatch();

  useInjectReducer({ key, reducer });
  useInjectSaga({ key, saga: deleteNoteSaga });

  const params = useParams();
  const [show, setShow] = useState(false);
  const [noteToDelete, setNoteToDelete] = useState('');
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [noteToReadOnly, setNoteToReadOnly] = useState({
    title: 'Consulter La note',
    isRead: false,
    note: null,
  });
  const handleClose = () => {
    setShow(false);
    noteToReadOnly.isRead &&
      setNoteToReadOnly({
        isRead: false,
        title: 'Consulter La note',
        note: null,
      });
  };

  const handleShow = () => {
    setShow(true);
  };

  const handleCloseForDeleteModal = () => setShowDeleteModal(false);

  useEffect(
    () =>
      function cleanup() {
        dispatch(resetNote());
      },
    [],
  );

  const {
    success,
    error,
    note,
    successDelete,
    notes,
    noteDonorAddSuccess,
  } = useSelector(omdbSelector);
  const [noteToEdit, setNoteToEdit] = useState('');

  const [message, setMessage] = useState('');
  const [showAlert, setShowAlert] = useState(false);

  const [currentPage, setCurrentPage] = useState(1);
  const pageSize = 5;

  const donor = props.data;
  let donorCode;

  if (donor) {
    donorCode = donor.code;
  }

  let errorMessage = null;

  useEffect(() => {
    if (successDelete) {
      setShowAlert(true);
      dispatch(removeNoteDonor(note));
      setMessage('Note supprimé avec succès');
    }
  }, [successDelete]);

  useEffect(() => {
    if (noteDonorAddSuccess) {
      setShowAlert(true);
      if (noteToEdit) {
        setMessage('Note modifiée avec succès');
      } else {
        setMessage('Note ajoutée avec succès');
      }
      setNoteToEdit('');
    }
  }, [noteDonorAddSuccess]);

  useEffect(() => {
    if (showAlert) {
      setTimeout(() => {
        setShowAlert(false);
      }, 4000);
    }
  }, [showAlert]);
  useEffect(() => {
    if (error) {
      setTimeout(() => {
        dispatch(resetNote());
      }, 4000);
    }
  }, [error]);

  useEffect(() => {
    dispatch(loadNoteData(params.id, target));
  }, []);

  useEffect(() => {
    if (successDelete || noteDonorAddSuccess) {
      dispatch(loadNoteData(params.id, target));
    }
  }, [successDelete, noteDonorAddSuccess]);

  if (error) {
    errorMessage = 'Une erreur est survenue';
  }

  const emptyValue = <span>-</span>;

  let listNotes = null;

  if (notes) {
    const notesSorted = [...notes].sort((a, b) =>
      a.createdDate < b.createdDate ? 1 : -1,
    );

    const startIndex = (currentPage - 1) * pageSize;
    const endIndex = Math.min(startIndex + pageSize, notesSorted.length);
    const paginatedNotes = notesSorted.slice(startIndex, endIndex);

    listNotes = paginatedNotes.map(note => ({
      id: note.id,
      content: note.content,
      objet: note.objet,
      createdDate: note.createdDate ? formatDate(note.createdDate) : emptyValue,
      createdBy: note.createdBy
        ? `${note.createdBy.firstName}${' '}${note.createdBy.lastName}`
        : null,
      note,
    }));
  }

  const columns = [
    {
      field: 'content',
      headerName: 'Contenu',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
    {
      field: 'objet',
      headerName: 'Objet',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
    {
      field: 'createdDate',
      headerName: 'Ajoutée le',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
    {
      field: 'createdBy',
      headerName: 'Ajoutée par',
      headerAlign: 'center',
      align: 'center',
      flex: 1,
    },
  ];

  const connectedUser = useSelector(state => state.app.connectedUser);
  const isUpdateAuthorized = isAuthorized(connectedUser, 'DONOR', 'UPDATE');
  if (isUpdateAuthorized) {
    columns.push({
      field: 'actions',
      type: 'actions',
      headerName: 'Actions',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
      renderCell: params => (
        <div>
          <input
            type="image"
            className="p-2"
            src={VIEW_ICON}
            width="40px"
            height="40px"
            onClick={() => {
              setNoteToEdit('');
              setNoteToReadOnly(prev => ({
                ...prev,
                isRead: true,
                note: params.row.note,
              }));
              handleShow();
            }}
            title="consulter"
          />
          <input
            type="image"
            src={EDIT_ICON}
            className="p-2"
            width="40px"
            height="40px"
            onClick={() => {
              const { note } = params.row;
              setNoteToEdit(note);
              setNoteToReadOnly(prev => ({
                ...prev,
                isRead: false,
                note: note,
              }));

              handleShow();
            }}
            title="modifier"
          />
          <input
            type="image"
            src={DELETE_ICON}
            className="p-2"
            width="40px"
            height="40px"
            onClick={() => {
              setNoteToDelete(params.row);
              setShowDeleteModal(true);
            }}
            title="supprimer"
          />
        </div>
      ),
    });
  }

  return (
    <div>
      <Modal2
        title={
          noteToReadOnly.isRead
            ? noteToReadOnly.title
            : noteToEdit
            ? 'Modifier la note'
            : 'Ajouter une note'
        }
        size="lg"
        customWidth="modal-90w"
        show={show}
        handleClose={() => {
          setShow(false);
          setNoteToReadOnly({
            isRead: false,
            title: 'Consulter la note',
            note: null,
          });
        }}
      >
        <NoteForm
          note={
            noteToEdit
              ? noteToEdit
              : noteToReadOnly.note
              ? noteToReadOnly.note
              : noteToEdit
          }
          handleClose={handleClose}
          button={noteToEdit ? 'Modifier' : 'Ajouter'}
          target={target}
          id={params.id}
          isRead={noteToReadOnly.isRead}
        />
      </Modal2>

      <Modal2
        centered
        className="mt-5"
        title="Confirmation de suppression"
        show={showDeleteModal}
        handleClose={handleCloseForDeleteModal}
      >
        <p className="mt-1 mb-5">
          Êtes-vous sûr de vouloir supprimer cette note?
        </p>
        <div className="d-flex justify-content-end px-3 my-1">
          <button
            type="button"
            className={`mx-2 btn-style outlined`}
            onClick={handleCloseForDeleteModal}
          >
            Annuler
          </button>
          <button
            type="submit"
            className={`mx-2 btn-style primary`}
            onClick={() => {
              dispatch(deleteNote(noteToDelete, target));
              setNoteToDelete('');
              handleCloseForDeleteModal();
            }}
          >
            Supprimer
          </button>
        </div>
      </Modal2>

      {showAlert ? (
        <Alert
          className="alert-style"
          variant="success"
          onClose={() => setShowAlert(false)}
          dismissible
        >
          <p>{message}</p>
        </Alert>
      ) : null}

      {error && (
        <Alert
          className="alert-style"
          variant="danger"
          onClose={() => setShowAlert(false)}
          dismissible
        >
          <p>{errorMessage}</p>
        </Alert>
      )}

      <div className={list.backgroudStyle}>
        <div className={list.global}>
          <div className={list.header}>
            <h4>Liste des Notes</h4>
            <AccessControl module="DONOR" functionality="UPDATE">
              <button
                className={btnStyles.addBtnProfile}
                onClick={() => {
                  handleShow();
                  setNoteToEdit('');
                }}
              >
                Ajouter
              </button>
            </AccessControl>
          </div>
          <DataTable
            rows={listNotes}
            columns={columns}
            fileName={`Liste des notes du donateur ${donorCode} , ${new Date().toLocaleString()}`}
          />

          <div className="justify-content-center my-4">
            {notes && (
              <CustomPagination
                totalElements={notes.length}
                totalCount={Math.ceil(notes.length / pageSize)}
                pageSize={pageSize}
                currentPage={currentPage}
                onPageChange={setCurrentPage}
              />
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
