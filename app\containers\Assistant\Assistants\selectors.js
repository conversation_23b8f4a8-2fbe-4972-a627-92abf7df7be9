import { createSelector } from 'reselect';
import { initialState } from './reducer';

const selectOmdb = (state) => state.assistants || initialState; 

const makeSelectAssistants = createSelector(
  selectOmdb,
  (assistantState) => assistantState.assistants,
);

const makeSelectLoading = createSelector(
  selectOmdb,
  (assistantState) => assistantState.loading,
);

const makeSelectError = createSelector(
  selectOmdb,
  (assistantState) => assistantState.error,
);

const makeSelectSuccess = createSelector(
  selectOmdb,
  (assistantState) => assistantState.success,
);

const makeSelectDeleteSuccess = createSelector(
  selectOmdb,
  (assistantState) => assistantState.successDelete,
);

const makeSelectChangeZoneSuccess = createSelector(
  selectOmdb,
  (assistantState) => assistantState.changeZoneSuccess,
);

const makeSelectAssistantsList = createSelector(
  selectOmdb,
  (assistantState) => assistantState.assistants,
);

export { selectOmdb, makeSelectAssistants, makeSelectLoading, makeSelectError, makeSelectSuccess, makeSelectDeleteSuccess, makeSelectChangeZoneSuccess, makeSelectAssistantsList };