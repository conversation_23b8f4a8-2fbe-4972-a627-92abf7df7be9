import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import moment from 'moment';
import { Box, Typography, Paper, Avatar, Chip, IconButton, TextField, Button, Tooltip } from '@mui/material';
import { styled } from '@mui/material/styles';
import PersonIcon from '@mui/icons-material/Person';
import AdminPanelSettingsIcon from '@mui/icons-material/AdminPanelSettings';
import EditIcon from '@mui/icons-material/Edit';
import SaveIcon from '@mui/icons-material/Save';
import CancelIcon from '@mui/icons-material/Cancel';
import LockIcon from '@mui/icons-material/Lock';
import Modal2 from 'components/Common/Modal2';

const ChatContainer = styled(Box)(({ theme }) => ({
  display: 'flex',
  flexDirection: 'column',
  gap: theme.spacing(2),
  padding: theme.spacing(2),
  maxHeight: 'calc(100vh - 300px)', // Adjust based on header height
  minHeight: '200px',
  overflowY: 'auto',
  '&::-webkit-scrollbar': {
    width: '8px',
  },
  '&::-webkit-scrollbar-track': {
    background: '#f1f1f1',
    borderRadius: '4px',
  },
  '&::-webkit-scrollbar-thumb': {
    background: '#888',
    borderRadius: '4px',
  },
  '&::-webkit-scrollbar-thumb:hover': {
    background: '#555',
  },
}));

const MessageBubble = styled(Paper)(({ theme, isAdmin }) => ({
  padding: theme.spacing(2),
  maxWidth: '80%',
  borderRadius: '12px',
  backgroundColor: isAdmin ? "#ffffff" : theme.palette.primary.main,
  color: isAdmin ? "#000000" : "#ffffff",
  position: 'relative',
  '&::before': {
    display: 'none'
  }
}));

const MessageHeader = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  gap: theme.spacing(1),
  marginBottom: theme.spacing(1),
}));

const MessageContent = styled(Typography)(({ theme }) => ({
  whiteSpace: 'pre-wrap',
  wordBreak: 'break-word',
}));

const MessageTime = styled(Typography)(({ theme }) => ({
  fontSize: '0.75rem',
  color: theme.palette.text.secondary,
  marginTop: theme.spacing(1),
}));

const StatusChip = styled(Chip)(({ theme, status }) => ({
  backgroundColor: status === 'envoyé' 
    ? theme.palette.success.main 
    : status === 'résolue'
    ? theme.palette.primary.main
    : theme.palette.grey[500],
  color: 'white',
  fontWeight: 600,
  borderRadius: '16px',
  '& .MuiChip-label': {
    padding: '0 12px',
  },
}));

function ReclamationView({ reclamation, isEditMode: initialEditMode, onUpdateResponse, onCloseReclamation }) {
  const [isEditing, setIsEditing] = useState(initialEditMode || false);
  const [editedResponse, setEditedResponse] = useState('');
  const [showCloseConfirmModal, setShowCloseConfirmModal] = useState(false);

  useEffect(() => {
    if (reclamation) {
      setEditedResponse(reclamation.response || '');
    }
  }, [reclamation]);

  useEffect(() => {
    setIsEditing(initialEditMode || false);
  }, [initialEditMode]);

  if (!reclamation) {
    return <div>Aucune réclamation sélectionnée</div>;
  }

  const handleEditClick = () => {
    setEditedResponse(reclamation.response || '');
    setIsEditing(true);
  };

  const handleSaveClick = () => {
    if (onUpdateResponse) {
      onUpdateResponse(editedResponse, moment().format('YYYY-MM-DDTHH:mm:ss.SSSZ'));
    }
    setIsEditing(false);
  };

  const handleCancelClick = () => {
    setIsEditing(false);
    setEditedResponse(reclamation.response || '');
  };

  const handleCloseClick = () => {
    setShowCloseConfirmModal(true);
  };

  const handleConfirmClose = () => {
    if (onCloseReclamation) {
      onCloseReclamation();
    }
    setShowCloseConfirmModal(false);
  };

  const handleCancelClose = () => {
    setShowCloseConfirmModal(false);
  };

  const getStatusLabel = (status) => {
    switch (status) {
      case 'envoyé':
        return 'Nouveau';
      case 'résolue':
        return 'Traité';
      case 'fermé':
        return 'Fermé';
      default:
        return status;
    }
  };

  return (
    <Box sx={{ 
      display: 'flex', 
      flexDirection: 'column',
      height: '100%'
    }}>
      {/* Fixed Header */}
      <Box sx={{ 
        display: 'flex', 
        justifyContent: 'space-between', 
        alignItems: 'center',
        mb: 3,
        pb: 2,
        borderBottom: '1px solid',
        borderColor: 'divider',
        position: 'sticky',
        top: 0,
        zIndex: 1
      }}>
        <Typography variant="h5" component="h2" sx={{ fontWeight: 600 }}>
        </Typography>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <div>
            status : <StatusChip 
            label={getStatusLabel(reclamation.status)} 
            status={reclamation.status}
            style={{backgroundColor: "#ffffff00",border: reclamation.status === 'envoyé' ? "2px solid rgb(1, 125, 11)" : reclamation.status === 'résolue' ? "2px solid rgb(1, 125, 11)" : "2px solid #808080", color: reclamation.status === 'envoyé' ? "rgb(1, 125, 11)" : reclamation.status === 'résolue' ? "rgb(1, 125, 11)" : "#808080"}}
          />
          </div>
          {reclamation.status === 'résolue' && !isEditing && (
            <Box sx={{ display: 'flex', gap: 1 }}>
              <Tooltip title="Modifier">
                <IconButton 
                  size="small" 
                  sx={{ color: 'primary.main' }}
                  onClick={handleEditClick}
                >
                  <EditIcon />
                </IconButton>
              </Tooltip>
              <Tooltip title="Fermer">
                <IconButton 
                  size="small" 
                  sx={{ color: '#1976d2' }}
                  onClick={handleCloseClick}
                >
                  <LockIcon />
                </IconButton>
              </Tooltip>
            </Box>
          )}
        </Box>
      </Box>

      {/* Scrollable Chat Content */}
      <ChatContainer>
        {/* Donor's message */}
        <Box sx={{ 
          display: 'flex', 
          flexDirection: 'column',
          alignItems: 'flex-start'
        }}>
          <MessageHeader>
            <Avatar sx={{ bgcolor: 'primary.main' }}>
              <PersonIcon />
            </Avatar>
            <Box>
              <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
                {reclamation.donorName || (reclamation.donor ? reclamation.donor.label : 'Donateur')}
              </Typography>
              <Typography variant="caption" color="text.secondary">
                {moment(reclamation.createdAt).format('DD/MM/YYYY HH:mm')}
              </Typography>
            </Box>
          </MessageHeader>
          <MessageBubble>
            <MessageContent>
              {reclamation.description}
            </MessageContent>
          </MessageBubble>
        </Box>

        {/* Admin's response if exists */}
        {reclamation.response && (
          <Box sx={{ 
            display: 'flex', 
            flexDirection: 'column',
            alignItems: 'flex-end'
          }}>
            <MessageHeader>
              <Box sx={{ textAlign: 'right' }}>
                <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
                  {reclamation.respondedBy || 'Administrateur'}
                </Typography>
                <Typography variant="caption" color="text.secondary">
                  {reclamation.respondedAt ? moment(reclamation.respondedAt).format('DD/MM/YYYY HH:mm') : '-'}
                </Typography>
              </Box>
              <Avatar sx={{ bgcolor: 'secondary.main' }}>
                <AdminPanelSettingsIcon />
              </Avatar>
            </MessageHeader>
            {isEditing ? (
              <Box sx={{ width: '100%', display: 'flex', flexDirection: 'column', gap: 1 }}>
                <TextField
                  multiline
                  rows={4}
                  value={editedResponse}
                  onChange={(e) => setEditedResponse(e.target.value)}
                  variant="outlined"
                  fullWidth
                  sx={{ mb: 1 }}
                />
                <Box sx={{ display: 'flex', gap: 1, justifyContent: 'flex-end' }}>
                  <Button
                    variant="outlined"
                    startIcon={<CancelIcon />}
                    onClick={handleCancelClick}
                  >
                    Annuler
                  </Button>
                  <Button
                    variant="contained"
                    startIcon={<SaveIcon />}
                    onClick={handleSaveClick}
                  >
                    Enregistrer
                  </Button>
                </Box>
              </Box>
            ) : (
              <MessageBubble isAdmin>
                <MessageContent>
                  {reclamation.response}
                </MessageContent>
              </MessageBubble>
            )}
          </Box>
        )}
      </ChatContainer>

      {/* Close Confirmation Modal */}
      <Modal2
        title="Confirmer la fermeture"
        size="md"
        show={showCloseConfirmModal}
        centered
        handleClose={handleCancelClose}
        style={{ maxWidth: '400px', margin: '0 auto' }}
      >
        <div style={{ padding: '20px' }}>
          <p style={{ fontSize: '18px' }}>Êtes-vous sûr de vouloir fermer cette réclamation ?</p>
          <div style={{ display: 'flex', justifyContent: 'flex-end', gap: '10px', marginTop: '20px' }}>
            <Button variant="secondary" onClick={handleCancelClose}>
              Annuler
            </Button>
            <Button 
              variant="primary" 
              onClick={handleConfirmClose}
              style={{ backgroundColor: '#C4261E', borderColor: '#CC261E' }}
            >
              Fermer
            </Button>
          </div>
        </div>
      </Modal2>
    </Box>
  );
}

ReclamationView.propTypes = {
  reclamation: PropTypes.object,
  isEditMode: PropTypes.bool,
  onUpdateResponse: PropTypes.func,
  onCloseReclamation: PropTypes.func,
};

export default ReclamationView;
