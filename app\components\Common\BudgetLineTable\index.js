import React, { useState } from 'react';
import {
  <PERSON>ton,
  <PERSON>,
  Dialog,
  <PERSON>alogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  Paper,
  Switch,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TextField,
} from '@mui/material';
import { makeStyles } from '@mui/styles';
import { Link } from 'react-router-dom';
import { updateStatutResarvationBudgetLineRequest } from '../../../containers/AideComplementaire/FicheAideComplementaire/actions';
import { useDispatch } from 'react-redux';
import Tooltip from '@mui/material/Tooltip';
import moment from 'moment/moment';

const useStyles = makeStyles({
  tableHeader: {
    backgroundColor: '#f5f5f5',
    fontWeight: 'bold',
    textAlign: 'center',
  },
  tableCell: {
    textAlign: 'center',
    verticalAlign: 'middle',
  },
  actionCell: {
    width: '100px',
    textAlign: 'center',
  },
  iconButton: {
    padding: '2px',
  },
  smallIcon: {
    fontSize: '1.5rem',
  },
  actionIcons: {
    display: 'flex',
    gap: '8px',
    justifyContent: 'center',
  },
  noBudgetLine: {
    textAlign: 'center',
    margin: '20px 0',
  },
});

const formatDate = date => moment(date).format('DD/MM/YYYY');

const groupBudgetLines = budgetLines => {
  return budgetLines.reduce((acc, line) => {
    const key = line.fullNameDonor;
    if (!acc[key]) {
      acc[key] = {
        donorLines: [],
        totalAmount: 0,
        typeDonor: line.typeDonor,
      };
    }
    acc[key].donorLines.push(line);
    acc[key].totalAmount += line.amount;
    return acc;
  }, {});
};

function BudgetLineTable({ aideComplementaire, budgetLines }) {
  const classes = useStyles();
  const dispatch = useDispatch();

  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [newAmount, setNewAmount] = useState('');
  const [selectedBudgetLine, setSelectedBudgetLine] = useState(null);
  const [error, setError] = useState('');

  const handleSwitchChange = (event, budgetLine) => {
    /*dispatch(
      updateStatutResarvationBudgetLineRequest(
        budgetLine.id,
        aideComplementaire.id,
      ),
    );

     */
    setNewAmount(
      budgetLine.montantReserverDuDonateur
        ? budgetLine.montantReserverDuDonateur
        : budgetLine.montantTotalDuDonateur,
    );
    setSelectedBudgetLine(budgetLine);
    setEditDialogOpen(true);
  };

  const handleEditClick = (event, budgetLine) => {
    if (budgetLine.status === 'DISPONIBLE') {
      setNewAmount(
        budgetLine.montantReserverDuDonateur
          ? budgetLine.montantReserverDuDonateur
          : budgetLine.montantTotalDuDonateur,
      );
      setSelectedBudgetLine(budgetLine);
      setEditDialogOpen(true);
    } else if (budgetLine.status === 'RESERVED') {
      dispatch(
        updateStatutResarvationBudgetLineRequest(
          budgetLine.id,
          aideComplementaire.id,
          null,
          'unReserve',
          budgetLine.isNature
        ),
      );
    }
  };

  const handleConfirmEdit = () => {
    if (parseFloat(newAmount) > selectedBudgetLine.montantTotalDuDonateur) {
      setError('Le montant réservé ne peut pas dépasser le montant total');
    } else {
      /*dispatch(
        updateMontantReservedBudgetLineRequest(
          selectedBudgetLine.id,
          newAmount,
        ),
      );

       */
      dispatch(
        updateStatutResarvationBudgetLineRequest(
          selectedBudgetLine.id,
          aideComplementaire.id,
          newAmount,
          'Reserve',
          selectedBudgetLine.isNature
        ),
      );
      setEditDialogOpen(false);
      setError('');
    }
  };

  const handleCloseDialog = () => {
    setEditDialogOpen(false);
    setError('');
  };
 
  return (
    <>
      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow className={classes.tableHeader}>
              <TableCell className={classes.tableCell}>Donateur</TableCell>
              <TableCell className={classes.tableCell}>Type donateur</TableCell>
              <TableCell className={classes.tableCell}>
                Solde donateur
              </TableCell>
              
              <TableCell className={classes.tableCell}>
                 Type de donation
              </TableCell>
              <TableCell className={classes.tableCell}>
                Montant par bénéficiaire
              </TableCell>
              <TableCell className={classes.tableCell}>Statut</TableCell>
              <TableCell className={classes.tableCell}>
                Montant réservé
              </TableCell>
              {aideComplementaire.statut !== 'executer' &&
                aideComplementaire.statut !== 'cloturer' && (
                  <TableCell className={classes.actionCell}>Actions</TableCell>
                )}
            </TableRow>
          </TableHead>
          <TableBody>
            {budgetLines.map(budgetLine => (
              <TableRow key={`${budgetLine.id}-${budgetLine.fullNameDonor}-${budgetLine.montantTotalDuDonateur}-${budgetLine.isNature}`}>
                <TableCell className={classes.tableCell}>
                  <Link to={`/donors/fiche/${budgetLine.id}/info`}>
                    {budgetLine.fullNameDonor}
                  </Link>
                </TableCell>
                <TableCell className={classes.tableCell}>
                  {budgetLine.typeDonor}
                </TableCell>
                <TableCell className={classes.tableCell}>
                  {budgetLine.montantTotalDuDonateur} Dh
                </TableCell>
                <TableCell className={classes.tableCell}>
                  {budgetLine.isNature?"Nature":"Financiere"} 
                </TableCell>
                <TableCell className={classes.tableCell}>
                  {budgetLine.montantPoserDuDonateur
                    ? `${budgetLine.montantPoserDuDonateur} Dh`
                    : '-'}
                </TableCell> 
                <TableCell className={classes.tableCell}>
                  {budgetLine.status === 'RESERVED' ? (
                    <Chip label="Réservée" color="success" />
                  ) : budgetLine.status === 'EXECUTED' ? (
                    <Chip label="Validé" color="success" />
                  ) : (
                    <Chip label="Non Réservée" color="default" />
                  )}
                </TableCell>
                <TableCell className={classes.tableCell}>
                  {budgetLine.montantReserverDuDonateur != 0.0
                    ? budgetLine.montantReserverDuDonateur
                    : budgetLine.montantTotalDuDonateur}{' '}
                  Dh
                </TableCell>
                
                {aideComplementaire.statut !== 'executer' &&
                  aideComplementaire.statut !== 'cloturer' && (
                    <TableCell className={classes.actionCell}>
                      <div className={classes.actionIcons}>
                        <Tooltip
                          title={
                            budgetLine.status === 'RESERVED'
                              ? 'Annuler la réservation'
                              : 'Réserver'
                          }
                        >
                          <Switch
                            checked={budgetLine.status === 'RESERVED'}
                            onChange={event =>
                              handleEditClick(event, budgetLine)
                            }
                            color="success"
                          />
                        </Tooltip>
                      </div>
                    </TableCell>
                  )}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Edit Dialog */}
      <Dialog
        open={editDialogOpen}
        onClose={handleCloseDialog}
        sx={{
          '& .MuiDialog-paper': {
            padding: '20px',
            borderRadius: '12px',
            backgroundColor: '#f9f9f9',
          },
        }}
      >
        <DialogTitle
          sx={{
            fontSize: '1.5rem',
            fontWeight: 'bold',
            color: '#333',
            marginBottom: '10px',
          }}
        >
          Modifier le Montant
        </DialogTitle>
        <DialogContent>
          <DialogContentText
            sx={{ fontSize: '1.1rem', color: '#555', marginBottom: '20px' }}
          >
            Veuillez entrer le nouveau montant à réserver :
          </DialogContentText>
          <TextField
            type="number"
            value={newAmount}
            onChange={e => setNewAmount(e.target.value)}
            placeholder="Montant"
            fullWidth
            error={!!error}
            helperText={error}
            sx={{
              '& .MuiInputBase-input': { fontSize: '1.2rem', padding: '12px' },
              '& .MuiOutlinedInput-root': {
                borderRadius: '8px',
                borderColor: '#ddd',
                boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
              },
              marginBottom: '20px',
              width: '100%',
            }}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog} color="primary">
            Annuler
          </Button>
          <Button onClick={handleConfirmEdit} color="primary">
            Confirmer
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
}

export default BudgetLineTable;
