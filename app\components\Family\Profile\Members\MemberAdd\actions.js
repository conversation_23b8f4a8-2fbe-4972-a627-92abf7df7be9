import {
  ADD_FAMILY_MEMBER,
  ADD_FAMILY_MEMBER_ERROR,
  ADD_FAMILY_MEMBER_RESET,
  ADD_FAMILY_MEMBER_SUCCESS,
  ADD_FAMILY_MEMBER_TUTOR_ERROR,
} from './constants';

export function addMember(search) {
  return {
    type: ADD_FAMILY_MEMBER,
    search,
  };
}

export function memberAdded(member) {
  return {
    type: ADD_FAMILY_MEMBER_SUCCESS,
    member,
  };
}

export function memberAddingError(error) {
  return {
    type: ADD_FAMILY_MEMBER_ERROR,
    error,
  };
}

export function memberTutorError() {
  return {
    type: ADD_FAMILY_MEMBER_TUTOR_ERROR,
  };
}

export function resetMember() {
  return {
    type: ADD_FAMILY_MEMBER_RESET,
  };
}
