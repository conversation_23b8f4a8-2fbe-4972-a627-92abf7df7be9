export const LOAD_DONATION = 'almobadara/FicheDonation/LOAD_DONATION';
export const LOAD_DONATION_SUCCESS =
  'almobadara/FicheDonation/LOAD_DONATION_SUCCESS';
export const LOAD_DONATION_ERROR =
  'almobadara/FicheDonation/LOAD_DONATION_ERROR';

export const PUSH_DOCUMENT_DONATION =
  'almobadara/FicheDonation/PUSH_DOCUMENT_DONATION';
export const UPDATE_DOCUMENT_DONATION =
  'almobadara/FicheDonation/UPDATE_DOCUMENT_DONATION';
export const REMOVE_DOCUMENT_DONATION =
  'almobadara/FicheDonation/REMOVE_DOCUMENT_DONATION';

export const PUSH_PRODUCT_DONATION =
  'almobadara/FicheDonation/PUSH_PRODUCT_DONATION';
export const UPDATE_PRODUCT_DONATION =
  'almobadara/FicheDonation/UPDATE_PRODUCT_DONATION';
export const REMOVE_PRODUCT_DONATION =
  'almobadara/FicheDonation/REMOVE_PRODUCT_DONATION';

export const PUSH_NOTE_DONATION = 'almobadara/OMDBPage/PUSH_NOTE_DONATION';
export const UPDATE_NOTE_DONATION = 'almobadara/OMDBPage/UPDATE_NOTE_DONATION';
export const REMOVE_NOTE_DONATION = 'almobadara/OMDBPage/REMOVE_NOTE_DONATION';

export const PUSH_ACTION_DONATION = 'PUSH_ACTION_DONATION';
export const UPDATE_ACTION_DONATION = 'UPDATE_ACTION_DONATION';
export const REMOVE_ACTION_DONATION = 'REMOVE_ACTION_DONATION';

export const LOAD_AIDECOMPLEMENTAIRE =
  'almobadara/OMDBPage/LOAD_AIDECOMPLEMENTAIRE';
export const LOAD_AIDECOMPLEMENTAIRE_SUCCESS =
  'almobadara/OMDBPage/LOAD_AIDECOMPLEMENTAIRE_SUCCESS';
export const LOAD_AIDECOMPLEMENTAIRE_ERROR =
  'almobadara/OMDBPage/LOAD_AIDECOMPLEMENTAIRE_ERROR';

export const LOAD_BENEFICIARY_FOR_AIDECOMPLEMENTAIRE =
  'almobadara/OMDBPage/LOAD_BENEFICIARY_FOR_AIDECOMPLEMENTAIRE';
export const LOAD_BENEFICIARY_FOR_AIDECOMPLEMENTAIRE_SUCCESS =
  'almobadara/OMDBPage/LOAD_BENEFICIARY_FOR_AIDECOMPLEMENTAIRE_SUCCESS';
export const LOAD_BENEFICIARY_FOR_AIDECOMPLEMENTAIREE_ERROR =
  'almobadara/OMDBPage/LOAD_BENEFICIARY_FOR_AIDECOMPLEMENTAIREE_ERROR';

export const PROCESS_DONORS_BENEFICIARIES_REQUEST =
  'almobadara/OMDBPage/PROCESS_DONORS_BENEFICIARIES_REQUEST';
export const PROCESS_DONORS_BENEFICIARIES_SUCCESS =
  'almobadara/OMDBPage/PROCESS_DONORS_BENEFICIARIES_SUCCESS';
export const PROCESS_DONORS_BENEFICIARIES_ERROR =
  'almobadara/OMDBPage/PROCESS_DONORS_BENEFICIARIES_ERROR';
export const PROCESS_DONORS_BENEFICIARIES_RESET =
  'almobadara/OMDBPage/PROCESS_DONORS_BENEFICIARIES_RESET';

export const PROCESS_DONORS_REQUEST =
  'almobadara/OMDBPage/PROCESS_DONORS_REQUEST';
export const PROCESS_DONORS_SUCCESS =
  'almobadara/OMDBPage/PROCESS_DONORS_SUCCESS';
export const PROCESS_DONORS_ERROR = 'almobadara/OMDBPage/PROCESS_DONORS_ERROR';
export const PROCESS_DONORS_RESET = 'almobadara/OMDBPage/PROCESS_DONORS_RESET';

export const LOAD_DONOR_FOR_AIDECOMPLEMENTAIRE =
  'almobadara/OMDBPage/LOAD_DONOR_FOR_AIDECOMPLEMENTAIRE';
export const LOAD_DONOR_FOR_AIDECOMPLEMENTAIRE_SUCCESS =
  'almobadara/OMDBPage/LOAD_DONOR_FOR_AIDECOMPLEMENTAIRE_SUCCESS';
export const LOAD_DONOR_FOR_AIDECOMPLEMENTAIREE_ERROR =
  'almobadara/OMDBPage/LOAD_DONOR_FOR_AIDECOMPLEMENTAIREE_ERROR';

export const REMOVE_BENEFICIARY_REQUEST =
  'almobadara/OMDBPage/REMOVE_BENEFICIARY_REQUEST';
export const REMOVE_BENEFICIARY_SUCCESS =
  'almobadara/OMDBPage/REMOVE_BENEFICIARY_SUCCESS';
export const REMOVE_BENEFICIARY_FAILURE =
  'almobadara/OMDBPage/REMOVE_BENEFICIARY_FAILURE';

export const REMOVE_BENEFICIARY_RESET =
  'almobadara/OMDBPage/REMOVE_BENEFICIARY_RESET';

export const UPDATE_STATUT_VALIDATION_BENEFICIARY_REQUEST =
  'almobadara/OMDBPage/UPDATE_STATUT_VALIDATION_BENEFICIARY_REQUEST';
export const UPDATE_STATUT_VALIDATION_BENEFICIARY_SUCCESS =
  'almobadara/OMDBPage/UPDATE_STATUT_VALIDATION_BENEFICIARY_SUCCESS';
export const UPDATE_STATUT_VALIDATION_BENEFICIARY_FAILURE =
  'almobadara/OMDBPage/UPDATE_STATUT_VALIDATION_BENEFICIARY_FAILURE';
export const UPDATE_STATUT_VALIDATION_BENEFICIARY_RESET =
  'almobadara/OMDBPage/UPDATE_STATUT_VALIDATION_BENEFICIARY_RESET';

export const UPDATE_MONTANT_BENEFICIARY_REQUEST =
  'almobadara/OMDBPage/UPDATE_MONTANT_BENEFICIARY_REQUEST';
export const UPDATE_MONTANT_BENEFICIARY_SUCCESS =
  'almobadara/OMDBPage/UPDATE_MONTANT_BENEFICIARY_SUCCESS';
export const UPDATE_MONTANT_BENEFICIARY_FAILURE =
  'almobadara/OMDBPage/UPDATE_MONTANT_BENEFICIARY_FAILURE';
export const UPDATE_MONTANT_BENEFICIARY_RESET =
  'almobadara/OMDBPage/UPDATE_MONTANT_BENEFICIARY_RESET';

export const REMOVE_DONOR_REQUEST = 'almobadara/OMDBPage/REMOVE_DONOR_REQUEST';
export const REMOVE_DONOR_SUCCESS = 'almobadara/OMDBPage/REMOVE_DONOR_SUCCESS';
export const REMOVE_DONOR_FAILURE = 'almobadara/OMDBPage/REMOVE_DONOR_FAILURE';

export const REMOVE_DONOR_RESET = 'almobadara/OMDBPage/REMOVE_DONOR_RESET';

export const FETCH_BUDGET_LINES_REQUEST =
  'almobadara/OMDBPage/FETCH_BUDGET_LINES_REQUEST';
export const FETCH_BUDGET_LINES_SUCCESS =
  'almobadara/OMDBPage/FETCH_BUDGET_LINES_SUCCESS';
export const FETCH_BUDGET_LINES_FAILURE =
  'almobadara/OMDBPage/FETCH_BUDGET_LINES_FAILURE';

export const UPDATE_STATUT_RESERAVATION_BUDGETLINE_REQUEST =
  'almobadara/OMDBPage/UPDATE_STATUT_RESERAVATION_BUDGETLINE_REQUEST';
export const UPDATE_STATUT_RESERAVATION_BUDGETLINE_SUCCESS =
  'almobadara/OMDBPage/UPDATE_STATUT_RESERAVATION_BUDGETLINE_SUCCESS';
export const UPDATE_STATUT_RESERAVATION_BUDGETLINE_FAILURE =
  'almobadara/OMDBPage/UPDATE_STATUT_RESERAVATION_BUDGETLINE_FAILURE';
export const UPDATE_STATUT_RESERAVATION_BUDGETLINE_RESET =
  'almobadara/OMDBPage/UPDATE_STATUT_RESERAVATION_BUDGETLINE_RESET';

export const UPDATE_MONTANT_RESERVED_BUDGETLINE_REQUEST =
  'almobadara/OMDBPage/UPDATE_MONTANT_RESERVED_BUDGETLINE_REQUEST';
export const UPDATE_MONTANT_RESERVED_BUDGETLINE_SUCCESS =
  'almobadara/OMDBPage/UPDATE_MONTANT_RESERVED_BUDGETLINE_SUCCESS';
export const UPDATE_MONTANT_RESERVED_BUDGETLINE_FAILURE =
  'almobadara/OMDBPage/UPDATE_MONTANT_RESERVED_BUDGETLINE_FAILURE';
export const UPDATE_MONTANT_RESERVED_BUDGETLINE_RESET =
  'almobadara/OMDBPage/UPDATE_MONTANT_RESERVED_BUDGETLINE_RESET';

export const EXECUTE_AIDE_COMPLEMENTAIRE_REQUEST =
  'almobadara/OMDBPage/EXECUTE_AIDE_COMPLEMENTAIRE_REQUEST';
export const EXECUTE_AIDE_COMPLEMENTAIRE_SUCCESS =
  'almobadara/OMDBPage/EXECUTE_AIDE_COMPLEMENTAIRE_SUCCESS';
export const EXECUTE_AIDE_COMPLEMENTAIRE_FAILURE =
  'almobadara/OMDBPage/EXECUTE_AIDE_COMPLEMENTAIRE_FAILURE';

export const EXECUTE_AIDE_COMPLEMENTAIRE_RESET =
  'almobadara/OMDBPage/EXECUTE_AIDE_COMPLEMENTAIRE_RESET';


export const UNEXECUTE_AIDE_COMPLEMENTAIRE_REQUEST =
'almobadara/OMDBPage/UNEXECUTE_AIDE_COMPLEMENTAIRE_REQUEST';
export const UNEXECUTE_AIDE_COMPLEMENTAIRE_SUCCESS =
'almobadara/OMDBPage/UNEXECUTE_AIDE_COMPLEMENTAIRE_SUCCESS';
export const UNEXECUTE_AIDE_COMPLEMENTAIRE_FAILURE =
'almobadara/OMDBPage/UNEXECUTE_AIDE_COMPLEMENTAIRE_FAILURE';

export const UNEXECUTE_AIDE_COMPLEMENTAIRE_RESET =
'almobadara/OMDBPage/UNEXECUTE_AIDE_COMPLEMENTAIRE_RESET';

export const GET_GROUPE_BENEFICIARIES_REQUEST =
  'almobadara/OMDBPage/GET_GROUPE_BENEFICIARIES_REQUEST';
export const GET_GROUPE_BENEFICIARIES_SUCCESS =
  'almobadara/OMDBPage/GET_GROUPE_BENEFICIARIES_SUCCESS';
export const GET_GROUPE_BENEFICIARIES_FAILURE =
  'almobadara/OMDBPage/GET_GROUPE_BENEFICIARIES_FAILURE';

export const GET_AIDE_BENEFICIARIES_REQUEST =
  'almobadara/OMDBPage/GET_AIDE_BENEFICIARIES_REQUEST';
export const GET_AIDE_BENEFICIARIES_SUCCESS =
  'almobadara/OMDBPage/GET_AIDE_BENEFICIARIES_SUCCESS';
export const GET_AIDE_BENEFICIARIES_FAILURE =
  'almobadara/OMDBPage/GET_AIDE_BENEFICIARIES_FAILURE';

export const UPDATE_MONTANT_FOR_GROUP_REQUEST =
  'almobadara/OMDBPage/UPDATE_MONTANT_FOR_GROUP_REQUEST';
export const UPDATE_MONTANT_FOR_GROUP_SUCCESS =
  'almobadara/OMDBPage/UPDATE_MONTANT_FOR_GROUP_SUCCESS';
export const UPDATE_MONTANT_FOR_GROUP_FAILURE =
  'almobadara/OMDBPage/UPDATE_MONTANT_FOR_GROUP_FAILURE';
export const UPDATE_MONTANT_FOR_GROUP_RESET =
  'almobadara/OMDBPage/UPDATE_MONTANT_FOR_GROUP_RESET';

export const RESERVE_ALL_BUDGET_LINES_REQUEST =
  'almobadara/OMDBPage/RESERVE_ALL_BUDGET_LINES_REQUEST';
export const RESERVE_ALL_BUDGET_LINES_SUCCESS =
  'almobadara/OMDBPage/RESERVE_ALL_BUDGET_LINES_SUCCESS';
export const RESERVE_ALL_BUDGET_LINES_FAILURE =
  'almobadara/OMDBPage/RESERVE_ALL_BUDGET_LINES_FAILURE';
export const RESERVE_ALL_BUDGET_LINES_RESET =
  'almobadara/OMDBPage/RESERVE_ALL_BUDGET_LINES_RESET';

export const CLOSE_AIDE_COMPLEMENTAIRE_REQUEST =
  'almobadara/OMDBPage/CLOSE_AIDE_COMPLEMENTAIRE_REQUEST';
export const CLOSE_AIDE_COMPLEMENTAIRE_SUCCESS =
  'almobadara/OMDBPage/CLOSE_AIDE_COMPLEMENTAIRE_SUCCESS';
export const CLOSE_AIDE_COMPLEMENTAIRE_FAILURE =
  'almobadara/OMDBPage/CLOSE_AIDE_COMPLEMENTAIRE_FAILURE';

export const CLOSE_AIDE_COMPLEMENTAIRE_RESET =
  'almobadara/OMDBPage/CLOSE_AIDE_COMPLEMENTAIRE_RESET';

export const VALIDATE_ALL_BENEFICIARIES_REQUEST =
  'almobadara/OMDBPage/VALIDATE_ALL_BENEFICIARIES_REQUEST';
export const VALIDATE_ALL_BENEFICIARIES_SUCCESS =
  'almobadara/OMDBPage/VALIDATE_ALL_BENEFICIARIES_SUCCESS';
export const VALIDATE_ALL_BENEFICIARIES_FAILURE =
  'almobadara/OMDBPage/VALIDATE_ALL_BENEFICIARIES_FAILURE';
export const VALIDATE_ALL_BENEFICIARIES_RESET =
  'almobadara/OMDBPage/VALIDATE_ALL_BENEFICIARIES_RESET';
