import { LOAD_DOCS_RENEW, LOAD_DOCS_RENEW_SUCCESS, LOAD_DOCS_RENEW_ERROR,VIEW_DOCUMENT,VIEW_DOCUMENT_SUCCESS,VIEW_DOCUMENT_ERROR } from "./constants";
import produce from 'immer';


export const initialState = {
    documentsRenewList: [],
    document: null,
    loading: false,
    successView:false,
    error: null,
};

const documentsRenewReducer = produce((draft, action) => {
    switch (action.type) {
        case LOAD_DOCS_RENEW:
            draft.loading = true;
            draft.error = false;
            draft.documentsRenewList = []; // Ensure zones is an empty array when loading starts
            break;
        case LOAD_DOCS_RENEW_SUCCESS:
            draft.loading = false;
            draft.error = false;
            draft.documentsRenewList = action.documentsRenewList;
            break;

        case LOAD_DOCS_RENEW_ERROR:
            draft.loading = false;
            draft.error = action.error;
            break;
        case VIEW_DOCUMENT:
            draft.error = false;
            draft.successView = false;
            break;
        case VIEW_DOCUMENT_SUCCESS:
            draft.error = false;
            draft.successView = true;
            draft.documentId = action.documentId;
        case VIEW_DOCUMENT_ERROR:
            draft.error = action.error;
            break;
        default:
            return draft;
    }
}, initialState);

export default documentsRenewReducer;