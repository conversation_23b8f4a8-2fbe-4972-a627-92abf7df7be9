import React, { useEffect, useMemo, useState } from 'react';
import moment from 'moment';
import tagStyles from 'Css/tag.css';
import styles from 'Css/memberCard.css';
import style from 'Css/profileList.css';
import { PROFILE_PICTURE } from 'containers/Common/RequiredElement/Icons';
import { useHistory } from 'react-router';
import { createStructuredSelector } from 'reselect';
import { useDispatch, useSelector } from 'react-redux';
import { useInjectReducer } from 'redux-injectors';
import { Alert } from 'react-bootstrap';
import { setAddMemberSuccess } from 'containers/Family/FamilyProfile/actions';
import AccessControl from 'utils/AccessControl';
import { hasRoleAssistant } from 'utils/hasAccess';
import btnStyles from '../../../../Css/button.css';
import { EDIT_ICON } from '../../../Common/ListIcons/ListIcons';
import Modal2 from '../../../Common/Modal2';
import MemberAdd from './MemberAdd';
import {
  makeSelectError,
  makeSelectMember,
  makeSelectSuccess,
} from './MemberAdd/selectors';
import reducer from './MemberAdd/reducer';
import { resetMember } from './MemberAdd/actions';
import {
  getStatusName,
  getStatusStyle,
} from 'containers/Beneficiary/BeneficiaryProfile/statutUtils';

const formatDate = date => moment(date).format('DD/MM/YYYY');

const key = 'member';

const omdbSelector = createStructuredSelector({
  success: makeSelectSuccess,
  error: makeSelectError,
  member: makeSelectMember,
});

export default function Members(props) {
  const family = props.data;
  const isAssistant = hasRoleAssistant();
  const [show, setShow] = useState(false);
  const [showAlert, setShowAlert] = useState(false);
  const [message, setMessage] = useState('');
  const [memberToEdit, setMemberToEdit] = useState(null);
  const [editedMember, setEditedMember] = useState(null);
  const [alert, setAlert] = useState(null);
  const beneficiary = props.beneficiary;
  const dispatch = useDispatch();
  useInjectReducer({ key, reducer });

  useEffect(
    () =>
      function cleanup() {
        dispatch(resetMember());
      },
    [],
  );

  const { success, error, member } = useSelector(omdbSelector);

  const members = null;
  const code = [];
  let top = null;
  let text = null;
  let sex = null;

  const history = useHistory();
  const fromBeneficiaryAdd = useMemo(() => {
    if (
      history &&
      history.location &&
      history.location.state &&
      history.location.state.from &&
      history.location.state.from === '/beneficiaries/add'
    ) {
      return true;
    }
    return false;
  }, [history]);

  useEffect(() => {
    // we should open the modal if the user comes from the beneficiary add page
    if (fromBeneficiaryAdd) {
      setShow(true);
    } else {
      setShow(false);
    }
  }, [fromBeneficiaryAdd]);

  useEffect(() => {
    if (success) {
      setShowAlert(true);
      if (memberToEdit) {
        dispatch(setAddMemberSuccess(true));
        setAlert(
          <Alert
            className="alert-style"
            variant="success"
            onClose={() => setShowAlert(false)}
            dismissible
          >
            Membre modifié avec succès
          </Alert>,
        );
      } else {
        dispatch(setAddMemberSuccess(true));
        setAlert(
          <Alert
            className="alert-style"
            variant="success"
            onClose={() => setShowAlert(false)}
            dismissible
          >
            Membre ajoutée avec succès
          </Alert>,
        );
      }
      setMemberToEdit(null);
    }
  }, [success]);

  useEffect(() => {
    if (showAlert) {
      const timer = setTimeout(() => {
        setShowAlert(false);
      }, 4000);
      return () => clearTimeout(timer);
    }
  }, [showAlert]);

  useEffect(() => {
    if (error) {
      setAlert(
        <Alert className="alert-style" variant="danger" onClose={() => setShowAlert(false)} dismissible>
          Une erreur est survenue
        </Alert>,
      );
    }
  }, [error]);

  const handleClose = () => {
    setMemberToEdit(null);
    setShow(false);
  };

  if (!family || !family.familyMembers) {
    return (
      <div className="sub-container d-flex justify-content-center align-items-center h-50">
        <p>
          Ce bénéficiaire n'a pas de famille associée. Il est répertorié
          comme indépendant.
        </p>
      </div>
    );
  }

  if (family && family.familyMembers) {
    const members = [...family.familyMembers].sort((a, b) =>
      a.familyRelationship.id > b.familyRelationship.id ? 1 : -1,
    );
    members.map(member => {
      const isSelected =
        !props.isFamilyProfile &&
        props.isFromBeneficiary &&
        props.beneficiary &&
        props.beneficiary.person &&
        member.person &&
        member.person.id === props.beneficiary.person.id;
      top = (
        <div className={styles.top}>
          <img
            src={
              member.person.pictureBase64
                ? `data:image/png;base64,${atob(member.person.pictureBase64)}`
                : PROFILE_PICTURE
            }
            alt="Profile"
            className={`rounded-circle ${styles.imgBorder}`}
            style={{
              width: '100px', // Set a fixed width
              height: '100px', // Set a fixed height
              objectFit: 'cover', // Ensures the image maintains aspect ratio
              borderRadius: '50%', // Ensures it remains circular
              border: '2px solid #ddd', // Optional: to add a border
            }}
          />
          <div className={styles.info}>
            <p>
              {member.person.firstName} {member.person.lastName}
            </p>
            <p>
              {member.person.firstNameAr} {member.person.lastNameAr}
            </p>
            <p className="font-weight-light" style={{ fontSize: '15px' }}>
              {member.code}
            </p>
            {member.tutor ? (
              <div>
                <div className={`mt-1 ${tagStyles.tagTutor}`}>Tuteur</div>
              </div>
            ) : null}
            {member.person.deceased ? (
              <div>
                <div className={`mt-1 ${tagStyles.tagDeceased}`}>
                  {member.person.deceased ? 'Décédé' : null}
                </div>
              </div>
            ) : null}
          </div>
        </div>
      );

      sex = member.person.sex == 'Femme' ? 'e' : '';

      text = (
        <div className={styles.data}>
          {/* Vérification que les propriétés existent avant de les afficher */}
          <div className={styles.row}>
            <div className={styles.label}>Code d'identité :</div>
            <div className={styles.value}>
              {member.person.identityCode || '--'}
            </div>
          </div>
          <div className={styles.row}>
            <div className={styles.label}>Date naissance :</div>
            <div className={styles.value}>
              {member.person.birthDate
                ? formatDate(member.person.birthDate)
                : '--'}
            </div>
          </div>
          <div className={styles.row}>
            <div className={styles.label}>Téléphone :</div>
            <div className={styles.value}>
              {member.person.phoneNumber || '--'}
            </div>
          </div>
          {/* Afficher la profession si elle existe */}
          {member.person.profession && member.person.profession.id && (
            <div className={styles.row}>
              <div className={styles.label}>Profession :</div>
              <div className={styles.value}>
                {member.person.profession.name || '--'}
              </div>
            </div>
          )}
          {/* Affichage des informations liées à la personne décédée */}
          {member.person.deceased && (
            <>
              <div className={styles.row}>
                <div className={styles.label}>Date décès :</div>
                <div className={styles.value}>
                  {member.person.deathDate
                    ? formatDate(member.person.deathDate)
                    : '--'}
                </div>
              </div>
              <div className={styles.row}>
                <div className={styles.label}>Raison décès :</div>
                <div className={styles.value}>
                  {(member.person.deathReasonSelected &&
                    member.person.deathReasonSelected.name) ||
                    '--'}
                </div>
              </div>
            </>
          )}
          {/* Affichage du niveau scolaire et année scolaire */}
          {member.person.educated && props.isFamilyProfile && (
            <>
              <div className={styles.row}>
                <div className={styles.label}>Niveau scolaire :</div>
                <div className={styles.value}>
                  {member.person.schoolLevelType || '--'}
                </div>
              </div>
              <div className={styles.row}>
                <div className={styles.label}>Année scolaire :</div>
                <div className={styles.value}>
                  {(member.person.schoolLevel &&
                    member.person.schoolLevel.name) ||
                    '--'}
                </div>
              </div>
            </>
          )}
          <div className={styles.row}>
            <div className={styles.label}>Commentaire :</div>
            <div className={styles.value}>{member.generalComment || '--'}</div>
          </div>
          {/* Affichage du statut du bénéficiaire */}
          {member.person.beneficiary &&
            member.person.beneficiary.beneficiaryStatut && (
              <div className={styles.row}>
                <div className={styles.label}>Statut :</div>
                <div
                  className={`${styles.value} ${getStatusStyle(
                    member.person.beneficiary.beneficiaryStatut.nameStatut,
                  )}`}
                  style={{
                    color: '#ffffff',
                    fontSize: '14px',
                    padding: '5px 10px',
                    marginBottom: '0px',
                    display: 'inline-block',
                  }}
                >
                  {getStatusName(
                    member.person.beneficiary.beneficiaryStatut.nameStatut,
                  )}
                </div>
              </div>
            )}
        </div>
      );

      code.push(
        <div
          className={`${styles.sideBar} ${
            isSelected ? styles.selectedCard : ''
          }`}
        >
          <div className={styles.relationship}>
            {member.familyRelationship ? member.familyRelationship.name : null}
          </div>

          {props.isFamilyProfile && (
            <input
              type="image"
              src={EDIT_ICON}
              className="p-2 ml-auto"
              disabled={isAssistant && member.canBeUpdatedByAssistant === false}
              title={
                isAssistant && member.canBeUpdatedByAssistant === false
                  ? "Vous n'avez pas le droit de modifier ce membre car il n'appartient pas à votre zone "
                  : null
              }
              style={{
                opacity:
                  isAssistant && member.canBeUpdatedByAssistant === false
                    ? 0.5
                    : 1,
              }}
              width="40px"
              height="40px"
              onClick={() => {
                setMemberToEdit(member);
                setShow(true);
              }}
            />
          )}

          {top}
          <hr className={styles.hr} />
          <div>{text}</div>
          {member.person.beneficiary &&
          props.isFamilyProfile &&
          member.person.beneficiary.archived !== true ? (
            // her also shouls not appear to the assistant when the canBeUpdatedByAssistant is false

            <div
              style={{
                textAlign: 'center',
                margin: '10px 0',
              }}
            >
              <a
                onClick={e => {
                  if (
                    !(isAssistant && member.canBeUpdatedByAssistant === false)
                  ) {
                    history.push(
                      `/beneficiaries/fiche/${member.person.beneficiary.id}/info`,
                    );
                  }
                }}
                style={{
                  fontSize: '16px',
                  fontWeight: 'bold',
                  textDecoration: 'none',
                  color: '#3498db', // Modern blue color
                  padding: '8px 15px',
                  border: '2px solid #3498db', // Border for button-like appearance
                  borderRadius: '35px',
                  backgroundColor: '#fff', // Ensures background is clear
                  display: 'inline-block', // Keeps the link in a button-like block
                  transition: 'background-color 0.3s ease, color 0.3s ease', // Smooth hover effect
                  cursor:
                    isAssistant && member.canBeUpdatedByAssistant === false
                      ? 'not-allowed'
                      : 'pointer', // Disabled cursor
                  opacity:
                    isAssistant && member.canBeUpdatedByAssistant === false
                      ? 0.6
                      : 1, // Reduced opacity when disabled
                }}
                onMouseOver={e => {
                  if (
                    !(isAssistant && member.canBeUpdatedByAssistant === false)
                  ) {
                    e.currentTarget.style.backgroundColor = '#3498db';
                    e.currentTarget.style.color = '#fff'; // Invert colors on hover
                  }
                }}
                onMouseOut={e => {
                  if (
                    !(isAssistant && member.canBeUpdatedByAssistant === false)
                  ) {
                    e.currentTarget.style.backgroundColor = '#fff';
                    e.currentTarget.style.color = '#3498db'; // Revert on mouse out
                  }
                }}
                disabled={
                  isAssistant && member.canBeUpdatedByAssistant === false
                } // Disable the button
                title={
                  isAssistant && member.canBeUpdatedByAssistant === false
                    ? "Vous n'avez pas le droit de consulter ce bénéficiaire car il n'appartient pas à votre zone "
                    : null
                }
              >
                Voir le profil du bénéficiaire
                <span
                  style={{
                    marginLeft: '8px',
                  }}
                >
                  <i className="fas fa-chevron-right"></i>
                </span>
              </a>
            </div>
          ) : null}
        </div>,
      );
    });
  }

  return (
    <div>
      {showAlert && alert}
      {props.isFamilyProfile && (
        <div>
          <div className={style.global}>
            <div className={style.header}>
              <span></span>
              {family && (
                <AccessControl module="FAMILLE" functionality="UPDATE">
                  <button
                    className={btnStyles.addBtnProfile}
                    onClick={() => {
                      setShow(true);
                    }}
                    data-toggle="modal"
                    data-target="#exampleModal"
                  >
                    Ajouter
                  </button>
                </AccessControl>
              )}
            </div>
          </div>
          <Modal2
            title={
              memberToEdit ? "Modification d'un membre" : "Ajout d'un membre"
            }
            size="lg"
            show={show}
            handleClose={() => {
              setMemberToEdit(null);
              setShow(false);
            }}
            customWidth="modal-90w"
          >
            <MemberAdd
              member={memberToEdit}
              show={show}
              handleClose={handleClose}
              familyId={family && family.id}
              setEditedMember={setEditedMember}
              fromBeneficiaryAdd={fromBeneficiaryAdd}
              familyMembers={family.familyMembers}
            />
          </Modal2>
        </div>
      )}

      <div className={styles.global}>{code}</div>
    </div>
  );
}
