import moment from "moment";

const formatDate = (date) => {
    return moment(date).format('DD/MM/YYYY')
}

const CheckIsTutor = (member) => {

    if (member.tutor && member.tutorStartDate) {
        let currentDate = new Date();
        currentDate.setHours(0, 0, 0, 0);

        let tutorStartDate = new Date(member.tutorStartDate);
        tutorStartDate.setHours(0, 0, 0, 0);
        let diff = currentDate.getTime() - tutorStartDate.getTime();
        
        //check if startDate is in past
        if ((diff >= 0)) {
            if (!member.tutorEndDate) { return true; }
            else {
                let tutorEndDate = new Date(member.tutorEndDate);
                tutorEndDate.setHours(0, 0, 0, 0);
                let diff = currentDate.getTime() - tutorEndDate.getTime();
                if ((diff <= 0)) {
                    return true;
                }
            }
        }
    }
    return false;
}

export default CheckIsTutor;