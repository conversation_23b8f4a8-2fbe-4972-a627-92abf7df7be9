import {
  LOAD_CONNECTED_ASSISTANT,
  LOAD_CONNECTED_ASSISTANT_SUCCESS,
  LOAD_CONNECTED_ASSISTANT_ERROR,
  LOAD_SOUS_ZONES,
  LOAD_SOUS_ZONES_SUCCESS,
  LOAD_SOUS_ZONES_ERROR,
  LOAD_RAPPORT_BY_ASSISTANT,
  LOAD_RAPPORT_BY_ASSISTANT_SUCCESS,
  LOAD_RAPPORT_BY_ASSISTANT_ERROR,
  LOAD_ZONES_WITH_SOUS_ZONES,
  LOAD_ZONES_WITH_SOUS_ZONES_SUCCESS,
  LOAD_ZONES_WITH_SOUS_ZONES_ERROR,
} from './constants';

export function loadConnectedAssistant(id) {
  return {
    type: LOAD_CONNECTED_ASSISTANT,
    id,
  };
}

export function connectedAssistantLoaded(connectedAssistant) {
  return {
    type: LOAD_CONNECTED_ASSISTANT_SUCCESS,
    connectedAssistant,
  };
}

export function connectedAssistantLoadingError(error) {
  return {
    type: LOAD_CONNECTED_ASSISTANT_ERROR,
    error,
  };
}

export function loadSousZones(id) {
  return {
    type: LOAD_SOUS_ZONES,
    id,
  };
}

export function sousZonesLoaded(sousZones) {
  return {
    type: LOAD_SOUS_ZONES_SUCCESS,
    sousZones,
  };
}

export function sousZonesLoadingError(error) {
  return {
    type: LOAD_SOUS_ZONES_ERROR,
    error,
  };
}

export function loadRapportByAssistant(id, pageNumber, filters) {
  return {
    type: LOAD_RAPPORT_BY_ASSISTANT,
    id,
    pageNumber,
    filters,
  };
}

export function rapportByAssistantLoaded(rapportByAssistant) {
  return {
    type: LOAD_RAPPORT_BY_ASSISTANT_SUCCESS,
    rapportByAssistant,
  };
}

export function rapportByAssistantLoadingError(error) {
  return {
    type: LOAD_RAPPORT_BY_ASSISTANT_ERROR,
    error,
  };
}

export function loadZonesWithSousZones(assistantId) {
  return {
    type: LOAD_ZONES_WITH_SOUS_ZONES,
    assistantId,
  };
}

export function zonesWithSousZonesLoaded(zones) {
  return {
    type: LOAD_ZONES_WITH_SOUS_ZONES_SUCCESS,
    zones,
  };
}

export function loadZonesWithSousZonesError(error) {
  return {
    type: LOAD_ZONES_WITH_SOUS_ZONES_ERROR,
    error,
  };
}
