import React, { useEffect, useState } from 'react';
import {
  <PERSON><PERSON><PERSON>,
  Bar,
  <PERSON>Axis,
  <PERSON><PERSON><PERSON>s,
  CartesianGrid,
  <PERSON><PERSON><PERSON>,
  Legend,
  ResponsiveContainer,
} from 'recharts';
import moment from 'moment';
import journalStyle from './JournalBarChart.css'; // CSS Module
import styles from 'Css/profileList.css';
import btnStyles from "../../../../../Css/button.css";
import ResetIconSvg from "../../../../../images/icons/ResetIconSvg";
const getEntryType = status =>
  ['DISPONIBLE', 'RESERVED', 'REMAINING'].includes(status)
    ? 'Entrée'
    : 'Sortie';

const getEntryDate = (status, createdAt, executionDate) =>
  ['DISPONIBLE', 'RESERVED', 'REMAINING'].includes(status)
    ? moment(createdAt)
    : moment(executionDate);

const CustomTooltip = ({ active, payload }) => {
  if (active && payload && payload.length) {
    return (
      <div className={journalStyle.tooltip}>
        <p>{`Date: ${payload[0].payload.date}`}</p>
        <p>{`Entrée: ${payload[0].payload.Entrée} DH`}</p>
        <p>{`Sortie: ${payload[0].payload.Sortie} DH`}</p>
      </div>
    );
  }
  return null;
};

const JournalBarChart = ({ data }) => {
  const [graphData, setGraphData] = useState([]);
  const [startDate, setStartDate] = useState(moment().startOf('year')); // Default start date
  const [endDate, setEndDate] = useState(moment().endOf('year')); // Default end date

  useEffect(() => {
    if (data) {
      const formattedData = data.reduce((acc, line) => {
        const type = getEntryType(line.status);
        const operationDate = getEntryDate(
          line.status,
          line.createdAt,
          line.executionDate,
        );

        // Include all entries if start and end dates are set to defaults
        if (operationDate.isBetween(startDate, endDate, null, '[]')) {
          const existingEntry = acc.find(entry =>
            entry.date.isSame(operationDate, 'day'),
          );
          if (existingEntry) {
            existingEntry[type] += line.amount; // Update existing entry
          } else {
            acc.push({
              date: operationDate,
              Entrée: type === 'Entrée' ? line.amount : 0,
              Sortie: type === 'Sortie' ? line.amount : 0,
            });
          }
        }
        return acc;
      }, []);

      const sortedData = formattedData.sort((a, b) => a.date - b.date);
      setGraphData(sortedData);
    }
  }, [data, startDate, endDate]);

  const barData = graphData.map(entry => ({
    date: entry.date.format('DD/MM/YYYY'),
    Entrée: entry.Entrée,
    Sortie: entry.Sortie,
  }));

  // Current year totals
  const currentTotalEntrée = graphData.reduce(
    (total, entry) => total + entry.Entrée,
    0,
  );
  const currentTotalSortie = graphData.reduce(
    (total, entry) => total + entry.Sortie,
    0,
  );

  // Previous year totals using the same month range as current year
  const lastYearStart = startDate.clone().subtract(1, 'year');
  const lastYearEnd = endDate.clone().subtract(1, 'year');

  const previousYearData = data.reduce((acc, line) => {
    const operationDate = getEntryDate(
      line.status,
      line.createdAt,
      line.executionDate,
    );
    if (operationDate.isBetween(lastYearStart, lastYearEnd, null, '[]')) {
      const type = getEntryType(line.status);
      const existingEntry = acc.find(entry =>
        entry.date.isSame(operationDate, 'day'),
      );
      if (existingEntry) {
        existingEntry[type] += line.amount;
      } else {
        acc.push({
          date: operationDate,
          Entrée: type === 'Entrée' ? line.amount : 0,
          Sortie: type === 'Sortie' ? line.amount : 0,
        });
      }
    }
    return acc;
  }, []);

  const previousYearTotalEntrée = previousYearData.reduce(
    (total, entry) => total + entry.Entrée,
    0,
  );
  const previousYearTotalSortie = previousYearData.reduce(
    (total, entry) => total + entry.Sortie,
    0,
  );

  // Calculate total differences
  const totalEntréeDiff = currentTotalEntrée - previousYearTotalEntrée;
  const totalSortieDiff = currentTotalSortie - previousYearTotalSortie;

  const handleReset = () => {
    setStartDate(moment().startOf('year')); // Reset to default start date
    setEndDate(moment().endOf('year')); // Reset to default end date
  };

  return (
    <div className={styles.global}>
      <div className={styles.content}>
      <div
        style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            marginBottom: '20px',
            padding: '5px',
            borderRadius: '5px',
        }}
      >
       <label
        style={{
          marginRight: '10px',
          whiteSpace: 'nowrap',
          marginTop: '10px',
        }}
      >
        Date de Début :{' '}
      </label>
      <input
        id="startDate"
        type="date"
        max={endDate ? endDate.format('YYYY-MM-DD') : null}
        className="form-control input-border"
        value={startDate.format('YYYY-MM-DD')}
        onChange={e => setStartDate(moment(e.target.value))}
        style={{ marginLeft: '10px', marginRight: '20px', width: '150px' }} // Fixed width for the input
      />
      <label
        style={{
          marginRight: '10px',
          whiteSpace: 'nowrap',
          marginTop: '10px',
        }}
      >
        Date de Fin :{' '}
      </label>
      <input
        id="endDate"
        type="date"
        min={startDate ? startDate.format('YYYY-MM-DD') : null}
        className="form-control input-border"
        value={endDate.format('YYYY-MM-DD')}
        onChange={e => setEndDate(moment(e.target.value))}
        style={{ marginLeft: '10px', width: '150px' }}
      />
      <button
        title="Réinitialiser"
        className={`reset-icon ${btnStyles.iconButton}`}
        onClick={handleReset}
      >
        <ResetIconSvg />
      </button>
    </div>
    <div className={`${journalStyle.kpiContainer} `}>
      <div className={journalStyle.kpiItem}>
        <div className={journalStyle.kpiHeader}>
        <i className={`fas fa-arrow-up ${journalStyle.kpiIcon}`}></i>
          <h3 className={journalStyle.kpiTitle}> Opérations d'Entrée </h3>
        </div>
        <p>
          <span className={journalStyle.kpiLabel}>Total de l'Année Actuelle : </span>
          <span className={journalStyle.kpiValue}>
            {currentTotalEntrée.toLocaleString()} DH
          </span>
        </p>
        <p>
          <span className={journalStyle.kpiLabel}>Total de l'Année Précédente : </span>
          <span className={journalStyle.kpiComparison}>
            {previousYearTotalEntrée.toLocaleString()} DH l'année dernière
          </span>
        </p>
        <p>
            <span className={journalStyle.kpiLabel}>Variation  : </span>
            <span
                className={journalStyle.kpiDifference}
                style={{ color: totalEntréeDiff >= 0 ? '#6DD5A0' : '#EF8C5A' }}
            >
                <i className={`fas fa-arrow-${totalEntréeDiff >= 0 ? 'up' : 'down'}`}></i>{' '}
                {Math.abs(totalEntréeDiff).toLocaleString()} DH
            </span>
        </p>
      </div>

      <div className={journalStyle.kpiItem}>
        <div className={journalStyle.kpiHeader}>
        <i className={`fas fa-arrow-down ${journalStyle.kpiIcon}`}></i>
          <h3 className={journalStyle.kpiTitle}> Opérations de Sortie </h3>
        </div>
        <p>
          <span className={journalStyle.kpiLabel}> Total de l'Année Actuelle : </span>
          <span className={journalStyle.kpiValue}>
            {currentTotalSortie.toLocaleString()} DH
          </span>
        </p>
        <p>
          <span className={journalStyle.kpiLabel}>Total de l'Année Précédente : </span>
          <span className={journalStyle.kpiComparison}>
            {previousYearTotalSortie.toLocaleString()} DH l'année dernière
          </span>
        </p>
        <p>
          <span className={journalStyle.kpiLabel}>Variation : </span>
          <span
            className={journalStyle.kpiDifference}
            style={{ color: totalSortieDiff >= 0 ? '#6DD5A0' : '#EF8C5A' }}
          >
            <i className={`fas fa-arrow-${totalSortieDiff >= 0 ? 'up' : 'down'}`}></i>{' '}
            {Math.abs(totalSortieDiff).toLocaleString()} DH
          </span>
        </p>
      </div>
</div>


        <ResponsiveContainer width="100%" height={400}>
          <BarChart
            data={barData}
            margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
          >
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis
              label={{
                value: 'Date',
                position: 'insideRight',
                offset: 2,
                dy: 19,
                style: {
                  textAnchor: 'middle',
                  fontSize: '1.2rem',
                  fontWeight: 'bold',
                },
              }}
              dataKey="date"
            />
            <YAxis
              label={{
                value: 'Montant (DH)',
                angle: -90,
                position: 'insideLeft',
                offset: 2,
                dx: -15,
                style: {
                  textAnchor: 'middle',
                  fontSize: '1.2rem',
                  fontWeight: 'bold',
                },
              }}
              domain={['0', 'dataMax + 100']}
            />
            <Tooltip content={<CustomTooltip />} />
            <Legend />
            <Bar dataKey="Entrée" fill="#28a745" />
            <Bar dataKey="Sortie" fill="#dc3545" />
          </BarChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
};

export default JournalBarChart;
