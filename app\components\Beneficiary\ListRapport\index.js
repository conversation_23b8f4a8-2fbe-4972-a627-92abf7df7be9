import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import { useDispatch, useSelector } from 'react-redux';
import moment from 'moment';
import { useInjectReducer, useInjectSaga } from 'redux-injectors';
import { Link, useHistory, useParams } from 'react-router-dom';
import styles from './styles.css';
import CustomPagination from '../../Common/CustomPagination';
import {
  makeSelectCanalDonations,
  makeSelectError,
  makeSelectLoading,
  makeSelectReleveDonor,
} from './selectors';
import { loadCanalDonations, loadReleveDonor } from './actions';
import reducer from './reducer';
import saga from './saga';
import ReactToPrint from 'react-to-print';
import PrintableContent from './PrintableContent/PrintableContent';
import Modal2 from 'components/Common/Modal2';
import eye from 'images/icons/eye.svg';

const formatDate = date => (date ? moment(date).format('DD/MM/YYYY') : '-');

const sortDataByDate = data => {
  if (!Array.isArray(data)) {
    return data;
  }
  return [...data].sort((a, b) => {
    const dateA = a.receptionDate || a.dateExecution;
    const dateB = b.receptionDate || b.dateExecution;
    return moment(dateA).isBefore(moment(dateB)) ? -1 : 1;
  });
};

const BodyComponent = ({
  data,
  openModal = ({ list = [], serviceName = '', date = '' }) => {},
}) => (
  <>
    {data && data.length > 0 ? (
      data.map(el => {
        if (el.typeDonationKafalat === 'donation') {
          return (
            <tr key={el.id}>
              {/* <td>{el.code}</td> */}
              <td>{formatDate(el.receptionDate)}</td>
              <td>{el.montantEntree}</td>
              <td>{el.type}</td>
              <td>{el.canalDonation ? el.canalDonation.name : '-'}</td>
              <td className={styles.backgroundGray}></td>
              <td>-</td>
              <td>-</td>
              <td>-</td>
            </tr>
          );
        }

        return (
          <tr key={el.id}>
            <td>-</td>
            <td>-</td>
            <td>-</td>
            <td>-</td>
            <td className={styles.backgroundGray}></td>
            <td>{formatDate(el.dateExecution)}</td>
            <td>{el.montantSortie}</td>
            <td
              style={{
                display: 'flex',
                justifyContent: 'space-between',
              }}
            >
              {el.name ? (
                <Link
                  to={{
                    pathname: `/aide-complementaire/fiche/${el.id}/info`,
                  }}
                  style={{
                    color: 'blue',
                    cursor: 'pointer',
                  }}
                >
                  {`${el.name || '-'}`}
                </Link>
              ) : (
                <Link
                  to={{
                    pathname: `/takenInCharges/fiche/${el.id}/planification`,
                  }}
                  style={{
                    color: 'blue',
                    cursor: 'pointer',
                  }}
                >
                  {`${el.services.name || '-'}`}
                </Link>
              )}
              {el.beneficiaries && el.beneficiaries.length > 0 && (
                <span
                  onClick={() => {
                    openModal({
                      list: el.beneficiaries,
                      serviceName: el.name
                        ? el.name
                        : el.services && el.services.name
                        ? el.services.name
                        : '-',
                      date: formatDate(el.dateExecution),
                    });
                  }}
                  style={{
                    width: 30,
                    height: 30,
                    borderRadius: 30,
                    display: 'flex',
                    flexDirection: 'column',
                    justifyContent: 'center',
                    alignItems: 'center',
                    backgroundColor: '#F1F1F1',
                  }}
                >
                  <div
                    style={{
                      color: 'blue',
                      cursor: 'pointer',
                      fontSize: 15,
                    }}
                  >
                    +
                  </div>
                </span>
              )}
            </td>
          </tr>
        );
      })
    ) : (
      <tr>
        <td colSpan="8">Aucun relevé disponible</td>
      </tr>
    )}
  </>
);

const key = 'releveDonor';
const pageSize = 10;
export default function ListReleve(props) {
  const history = useHistory();
  const donor = props.data;
  const { error, loading, releves, canalDonations } = useSelector(state => ({
    error: makeSelectError(state),
    loading: makeSelectLoading(state),
    releves: makeSelectReleveDonor(state),
    canalDonations: makeSelectCanalDonations(state),
  }));

  useInjectReducer({ key, reducer });
  useInjectSaga({ key, saga });

  const dispatch = useDispatch();
  const params = useParams();
  const [activePage, setActivePage] = useState(1);
  const [startDate, setStartDate] = useState(moment().startOf('year'));
  const [endDate, setEndDate] = useState(moment().endOf('year'));
  const [typeFilter, setTypeFilter] = useState('');
  const [modalVisible, setModalVisible] = useState(false);
  const [beneficiaries, setBeneficiaries] = useState([]);
  const [currentServiceName, setCurrentServiceName] = useState('');
  const [beneficiaryDate, setBeneficiaryDate] = useState('');
  const openModal = ({ list = [], serviceName = '', date = '' }) => {
    setBeneficiaries(list);
    setCurrentServiceName(serviceName);
    setBeneficiaryDate(date);
    setModalVisible(true);
  };
  const closeModal = () => {
    setModalVisible(false);
  };

  useEffect(() => {
    dispatch(loadReleveDonor(params.id));
    dispatch(loadCanalDonations());
  }, [dispatch, params.id]);

  const handlePageChange = pageNumber => {
    setActivePage(pageNumber);
  };
  const [filteredData, setFilteredData] = useState([]);
  const handleDataPaging = useCallback(() => {
    setFilteredData(() => {
      const startIndex = (activePage - 1) * pageSize;
      const endIndex = startIndex + pageSize;
      return releves ? releves.slice(startIndex, endIndex) : [];
    });
  }, [releves, pageSize, activePage]);
  useEffect(() => {
    handleDataPaging();
  }, [releves, pageSize, activePage]);

  const totalDonation = useMemo(() => {
    if (filteredData && filteredData.length > 0) {
      const item = filteredData.find(
        el => el.typeDonationKafalat === 'donation',
      );
      return item ? item.totalEntree : 0;
    }
    return 0;
  }, [filteredData]);

  // const totalOther = useMemo(() => {
  //   if (filteredData && filteredData.length > 0) {
  //     const item = filteredData.find(
  //       el => el.typeDonationKafalat !== 'donation',
  //     );
  //     return item ? item.totalSortie : 0;
  //   }
  //   return 0;
  // }, [filteredData]);

  const totalOther = useMemo(() => {
    if (filteredData && filteredData.length > 0) {
      return filteredData
        .filter(el => el.typeDonationKafalat !== 'donation')
        .reduce((sum, el) => sum + (el.montantSortie || 0), 0);
    }
    return 0;
  }, [filteredData]);

  const applyFilter = () => {
    if (startDate && typeFilter && typeFilter !== 'Tous') {
      const startIndex = (activePage - 1) * pageSize;
      const endIndex = startIndex + pageSize;
      setFilteredData(() =>
        releves
          .filter(
            el =>
              el.receptionDate &&
              el.receptionDate.slice(0, 10) >=
                moment(startDate).format('YYYY-MM-DD') &&
              el.receptionDate &&
              el.receptionDate.slice(0, 10) <=
                moment(endDate).format('YYYY-MM-DD') &&
              el.type === typeFilter,
          )
          .slice(startIndex, endIndex),
      );
    } else {
      const startIndex = (activePage - 1) * pageSize;
      const endIndex = startIndex + pageSize;
      setFilteredData(() =>
        releves
          .filter(
            el =>
              el.receptionDate &&
              el.receptionDate.slice(0, 10) >=
                moment(startDate).format('YYYY-MM-DD') &&
              el.receptionDate &&
              el.receptionDate.slice(0, 10) <=
                moment(endDate).format('YYYY-MM-DD'),
          )
          .slice(startIndex, endIndex),
      );
    }
  };
  const componentRef = useRef();

  return (
    <>
      <Modal2
        title={'List des Beneficiares'}
        size="lg"
        customWidth="modal-90w"
        show={modalVisible}
        handleClose={closeModal}
      >
        <p>{`List des beneficiares de service :  ${currentServiceName}`}</p>

        <div
          style={{
            gap: 10,
            display: 'flex',
            flexDirection: 'column',
            maxHeight: 400,
            overflowY: 'scroll',
            alignItems: 'center',
          }}
        >
          {beneficiaries &&
            beneficiaries.length > 0 &&
            beneficiaries.map(el => {
              return (
                <div
                  className="border"
                  style={{
                    display: 'flex',
                    flexDirection: 'row',
                    alignItems: 'center',
                    paddingInline: 10,
                    minHeight: 50,
                    width: 300,
                    borderRadius: 10,
                    justifyContent: 'space-between',
                  }}
                >
                  <div
                    style={{
                      display: 'flex',
                      flexDirection: 'row',
                      gap: 5,
                    }}
                  >
                    <div>{el.personLastName}</div>
                    <div>{el.montantAffecter} DH</div>
                    <div>{beneficiaryDate} </div>
                  </div>
                  <div>
                    <img
                      src={eye}
                      style={{
                        cursor: 'pointer',
                        color: 'blue',
                      }}
                      width="20px"
                      height="20px"
                      alt="rightArrow"
                      onClick={() => {
                        history.push(`/beneficiaries/fiche/${el.id}/info`);
                        // /beneficiaries/cefhi / 116 / info;
                      }}
                    />
                  </div>
                </div>
              );
            })}
        </div>
      </Modal2>

      <div className="d-flex bg-white p-4">
        <h4>Relevé du compte donateur</h4>
      </div>
      <div
        style={{
          display: 'flex',
          flexDirection: 'row',
          justifyContent: 'space-around',
          backgroundColor: '#FFFFFF',
        }}
      >
        <select
          value={typeFilter}
          onChange={e => setTypeFilter(e.target.value)}
          className="form-control"
          style={{ marginRight: '10px', width: '150px' }}
        >
          <option value="">Tous</option>
          <option value="Nature">Nature</option>
          <option value="Financière">Financière</option>
        </select>

        <label style={{ marginRight: '10px', marginTop: '7px' }}>
          Date Debut:
        </label>
        <input
          type="date"
          value={startDate ? moment(startDate).format('YYYY-MM-DD') : ''}
          max={
            endDate
              ? moment(endDate)
                  .subtract(1, 'days')
                  .format('YYYY-MM-DD')
              : ''
          }
          onChange={e => setStartDate(e.target.value)}
          className="form-control"
          style={{ marginRight: '10px', width: '150px' }}
        />

        <label style={{ marginRight: '10px', marginTop: '7px' }}>
          Date Fin :
        </label>
        <input
          type="date"
          value={endDate ? moment(endDate).format('YYYY-MM-DD') : ''}
          min={
            startDate
              ? moment(startDate)
                  .add(1, 'days')
                  .format('YYYY-MM-DD')
              : ''
          }
          onChange={e => setEndDate(e.target.value)}
          className="form-control"
          style={{ marginRight: '10px', width: '150px' }}
        />
        <div
          style={{
            display: 'flex',
            flexDirection: 'row',
            justifyContent: 'space-between',
            gap: 15,
          }}
        >
          <div
            className="btn btn-primary"
            onClick={() => {
              if (startDate || endDate) {
                applyFilter();
              }
            }}
          >
            <span>Appliquer</span>
          </div>
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <ReactToPrint
              trigger={() => (
                <button className="btn btn-secondary">Imprimer</button>
              )}
              content={() => componentRef.current}
            />
            <div style={{ display: 'none' }}>
              <PrintableContent
                ref={componentRef}
                data={filteredData}
                totalDonation={totalDonation}
                totalOther={totalOther}
                donor={donor}
                startDate={moment(startDate).format('DD-MM-YYYY')}
                endDate={moment(endDate).format('DD-MM-YYYY')}
              />
            </div>
          </div>
        </div>
      </div>
      <div
        style={{
          backgroundColor: '#FFFFFF',
          padding: 15,
        }}
      >
        <div
          className="btn btn-success"
          onClick={() => {
            handleDataPaging();
            setTypeFilter('Tous');
            setStartDate(moment().startOf('year'));
            setEndDate(moment().endOf('year'));
          }}
        >
          <span>Reset</span>
        </div>
      </div>
      <div className="d-flex bg-white p-4">
        <table className="table table-bordered">
          <thead>
            <tr>
              <th colSpan={4} className="text-align-center">
                Donations effectuées
              </th>
              <th className={styles.backgroundGray}></th>
              <th colSpan={4} className="text-align-center">
                Opérations de prise en charges exécutées
              </th>
            </tr>
            <tr>
              {/* <th>Code</th> */}
              <th>Date</th>
              <th>Montant</th>
              <th>Type</th>
              <th>Canal</th>
              <th className={styles.backgroundGray}></th>
              <th style={{ width: 115 }}>Date d’exécution</th>
              <th>Montant</th>
              <th colSpan={3}>Service</th>
            </tr>
          </thead>
          <tbody>
            <BodyComponent data={filteredData} openModal={openModal} />
          </tbody>
          <tfoot>
            <tr>
              <td>Total</td>
              <td>{`${totalDonation} DH`}</td>
              <td></td>
              <td></td>
              <td className={styles.backgroundGray}></td>
              <td></td>
              <td>{`${totalOther} DH`}</td>
              <td></td>
            </tr>
          </tfoot>
        </table>
      </div>

      {releves.length > 0 && (
        <div
          className="d-flex justify-content-center bg-white"
          style={{ paddingBottom: 15, marginTop: -10 }}
        >
          <CustomPagination
            totalElements={releves.length}
            totalCount={
              releves &&
              releves.length > 0 &&
              Math.ceil(releves.length / pageSize)
            }
            pageSize={pageSize}
            currentPage={activePage}
            onPageChange={handlePageChange}
          />
        </div>
      )}
    </>
  );
}
