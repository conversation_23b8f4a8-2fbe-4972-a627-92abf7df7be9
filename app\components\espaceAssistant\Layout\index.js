import React from 'react';
import { NavLink, useParams } from 'react-router-dom';
import styles from '../../../Css/profileHeader.css';

const HeaderEspaceAssistant = props => {
  const params = useParams();
  const { connectedAssistant } = props;
  const { loading } = props;

  return (
    <div className={styles.navBar}>
      {connectedAssistant && connectedAssistant.zoneId && !loading ? (
        <>
          <p>
            <NavLink
              exact
              to={`/espace-assistant/fiche/${params.id}/sousZones`}
              activeClassName={styles.selected}
            >
              Sous Zones
            </NavLink>
          </p>
        </>
      ) : (
        <p
          style={{
            fontWeight: 'bold',
            margin: '10px 0',
            textAlign: 'center',
          }}
        >
          {!loading &&
            // Show the message when no zone is assigned
            "L'assistant n'est assigné à aucune zone, donc il n'a pas de sous-zones disponibles."}
        </p>
      )}
    </div>
  );
};

export default HeaderEspaceAssistant;
