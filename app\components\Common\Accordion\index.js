import React, { useState } from 'react';
import ArrowForwardIosIcon from '@mui/icons-material/ArrowForwardIos';
import FormatListBulletedIcon from '@mui/icons-material/FormatListBulleted';
import { entries } from 'lodash';

const Accordion = ({
  title,
  type = 'default',
  oldValue,
  newValue,
  nested = false,
  nestedData = [],
}) => {
  const [isOpen, setIsOpen] = useState(false);

  const toggleAccordion = () => {
    setIsOpen(!isOpen);
  };

  const getStatus = (text) => { 
    const statusMap = {
      candidat_initial: 'Pré-candidat Initial',
      candidat_valider_assistance: "Pré-candidat Validé par l'Assistant",
      candidat_valider_kafalat: 'Pré-candidat Validé par Kafalat',
      candidat_rejete: 'Pré-candidat Rejeté',
      candidat_a_completer_par_assistance: 'Pré-candidat à compléter',
      candidat_a_completer_par_kafalat: 'Pré-candidat à compléter',
      beneficiary_actif: 'Actif',
      beneficiary_enattente: 'Candidat en attente',
      candidat_a_updater: 'Candidat à actualiser',
      beneficiaire_rejete: 'Bénéficiaire Rejeté',
      beneficiary_ancien: 'Bénéficiaire Archivé',
      beneficiary_ad_hoc_individual: 'Personne',
      beneficiary_ad_hoc_group: 'Groupe',
    };
   
    return statusMap[text] || text;
  };
  const deepEqual = (a1, b1) => {
    if (a1 === b1) return true;
    if (Array.isArray(a1) && Array.isArray(b1)) {
      const normalize = str =>
        str
          .map(item => item.trim())
          .sort()
          .join(', ');
      return normalize(a1) === normalize(b1);
    }
    return false;
  };

  const checkIfParentIsDifferent = (newValue, oldValue) => {
    // If either newValue or oldValue is not an object, return false
    if (typeof newValue !== 'object' || typeof oldValue !== 'object') {
      return newValue !== oldValue;  // If they are different, return true
    }

    // Otherwise, recursively check for differences in the nested objects
    if (newValue !== null && oldValue !== null) {
    const allKeys = new Set([...Object.keys(newValue), ...Object.keys(oldValue)]);
    return [...allKeys].some((key) => {
      return checkIfParentIsDifferent(newValue[key], oldValue[key]);  // Recurse into nested objects
    });}
  };

  const formatValue = value => {
    if (Array.isArray(value)) {
      return value.join(', ');
    }
    return getStatus(value);
  };

  const isParentDifferent = checkIfParentIsDifferent(newValue, oldValue);

  const hasNestedAccordion =
    (typeof newValue === 'object' && newValue !== null) ||
    (typeof oldValue === 'object' && oldValue !== null);

  const renderValue = () => {
    if (
      typeof newValue === 'object' &&
      newValue !== null &&
      typeof oldValue === 'object' &&
      oldValue !== null
    ) {
      // Merge keys from both objects to ensure all keys are displayed
      const allKeys = new Set([
        ...Object.keys(oldValue),
        ...Object.keys(newValue),
      ]);
      return (
        <>
          {[...allKeys].map((key, index) => {
            // Remove numbers and ":" from the key
            const cleanedKey = key.replace(/\d+/g, '')  // Remove 'null' and numbers before ":"
            var oldvalue = null;
            var newvalue = '';
            if (typeof oldValue[key] === 'undefined') {
              oldvalue = '-';
            } else {
              oldvalue = oldValue[key];
            }
            if (typeof newValue[key] === 'undefined') {
              newvalue = '-';
            } else {
              newvalue = newValue[key];
            }
            return (
              <Accordion
                title={cleanedKey}
                oldValue={oldvalue} // Show "-" if key is missing
                newValue={newvalue}
                type={type}
              />
            );
          })}
        </>
      );
    }else if ( typeof newValue === 'object' &&
      newValue !== null && oldValue === null) {
      const allKeys = new Set([
        ...Object.keys(newValue),
      ]);
      {[...allKeys].map((key, index) => {
        const cleanedKey = key
          ? key.replace(/\d+/g, '')  // Remove 'null' and numbers before ":"
          : '';
        return (
          <Accordion
            title={cleanedKey}
            newValue={newValue[key]}
            type={type}
          />
        );
      })}}else if ( typeof oldValue === 'object' &&
        oldValue !== null && newValue === null){
        const allKeys = new Set([
          ...Object.keys(oldValue),
        ]);
        {[...allKeys].map((key, index) => {
          const cleanedKey = key
            ? key.replace(/\d+/g, '').trim()  // Remove 'null' and numbers before ":"
            : '';
          return (
            <Accordion
              title={cleanedKey}
              oldValue={oldValue[key]}
              type={type}
            />
          );
        });}
    }

    if (typeof newValue === 'object' && newValue !== null) {
      return (
        <>
          {Object.entries(newValue).map(([key, value], index) => (
            <Accordion title={key} newValue={value} type={type} />
          ))}
        </>
      );
    }

    if (type === 'Consultation' || deepEqual(oldValue, newValue)) {
      return (
        <span style={{ color: 'black', fontWeight: 'bold' }}>
          {formatValue(newValue)}
        </span>
      );
    } else if (type === 'Ajout') {
      return (
        <span style={{ color: 'green', fontWeight: 'bold' }}>
          {formatValue(newValue)}
        </span>
      );
    } else if (type === 'Recherche') {
      return (
        <span style={{ color: 'blue', fontWeight: 'bold' }}>
          {formatValue(newValue)}
        </span>
      );
      return <span style={{ color: 'green', fontWeight: 'bold' }}>{formatValue(newValue)}</span>;
    }else if ( type==='Cycle de Validation') {
            return <span style={{ color: 'green', fontWeight: 'bold' }}>{formatValue(newValue)}</span>;
    }else if (type === 'Recherche') {
      return <span style={{ color: 'blue', fontWeight: 'bold' }}>{formatValue(newValue)}</span>;
    } else if (type === 'Suppression') {
      return (
        <span style={{ color: 'red', fontWeight: 'bold' }}>
          {formatValue(newValue)}
        </span>
      );
    } else {
      return (
        <>
          <span style={{ color: 'red', fontWeight: 'bold' }}>
            {' '}
            Ancienne valeur : {formatValue(oldValue)}
          </span>{' '}
          <br />
          <span style={{ color: 'green', fontWeight: 'bold' }}>
            {' '}
            Nouvelle Valeur : {formatValue(newValue)}
          </span>
        </>
      );
    }
  };

  const titleStyle = {
    ...(type === 'modification' && typeof newValue !== 'object' && !deepEqual(oldValue, newValue) ? { color: 'red', fontWeight: 'bold' } : {}),
    ...(title.startsWith('Service') && newValue === '-'
      ? { textDecoration: 'line-through' }
      : {}),
    ...(type === 'modification' && isParentDifferent && typeof newValue === 'object'
      ? {
          color: 'red',
          fontWeight: 'bold',
        }
      : {}), // Make parent red
  };

  const titlev2 = title.replace(/\d+/g, '');

  return (
    <div style={{ marginBottom: '5px' }}>
      <div
        onClick={toggleAccordion}
        style={{
          cursor: 'pointer',
          ...titleStyle,
          padding: '10px',
          backgroundColor: '#f0f0f0',
          borderRadius: '5px',
          border: '1px solid #ddd',
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
        }}
      >
        <span style={{ display: 'flex', alignItems: 'center' }}>
          {hasNestedAccordion && (
            <FormatListBulletedIcon sx={{ fontSize: '1.2rem', marginRight: '8px' }} />
          )}
          {titlev2}

        </span>

        <ArrowForwardIosIcon
          sx={{
            fontSize: '1rem',
            transform: isOpen ? 'rotate(90deg)' : 'rotate(0deg)',
            transition: 'transform 0.3s ease',
            marginLeft: 'auto',
          }}
        />
      </div>
      <div
        style={{
          overflow: 'auto',
          maxHeight: isOpen ? '500px' : '0',
          opacity: isOpen ? '1' : '0',
          padding: isOpen ? '10px' : '0',
          border: '1px solid #ddd',
          borderTop: 'none',
          borderRadius: '5px',
          transition: 'max-height 0.6s ease, opacity 0.8s ease',
        }}
      >
        {isOpen && (
          <div
            style={{
              padding: '3px',
              borderRadius: '5px',
              backgroundColor: '#fff',
            }}
          >
            {renderValue()}
          </div>
        )}
      </div>
    </div>
  );
};

export default Accordion;
