import { call, put, takeLatest } from 'redux-saga/effects';
import request from 'utils/request';
import {
  changeOperationStatusError,
  changeOperationStatusSuccess,
  getListOperationsTakenInChargeError,
  getListOperationsTakenInChargeSuccess,
} from './actions';
import {
  CHANGE_OPERATION_STATUS,
  GET_LIST_OPERATIONS_TAKEN_IN_CHARGE,
} from './constants';

export function* getListOperationTakenInChargeSaga({ pageNumber, filters }) {
  try {
    console.log({
      sagaProps: pageNumber,
    });

    // Initialisez l'URL de base
    let url = `/takenInCharges/operations?page=${pageNumber}`;

    // Utilisez URLSearchParams pour construire les paramètres de la requête
    const params = new URLSearchParams();

    if (filters) {
      const {
        searchByNameDonor,
        searchByNameDonorAr,
        searchByBeneficiary,
        searchByStatus,
        minPlanningDate,
        maxPlanningDate,
        minExecutionDate,
        maxExecutionDate,
        minClosureDate,
        maxClosureDate,
        searchByServiceId,
        searchByStatusId,
      } = filters;

      if (searchByNameDonor) {
        params.append('searchByNameDonor', searchByNameDonor);
      }
      if (searchByNameDonorAr) {
        params.append('searchByNameDonorAr', searchByNameDonorAr);
      }
      if (searchByBeneficiary) {
        params.append('searchByBeneficiary', searchByBeneficiary);
      }
      if (searchByStatus) {
        params.append('searchByStatusOperation', searchByStatus);
      }
      if (minPlanningDate) {
        params.append('minPlanningDate', minPlanningDate);
      }
      if (maxPlanningDate) {
        params.append('maxPlanningDate', maxPlanningDate);
      }
      if (minExecutionDate) {
        params.append('minExecutionDate', minExecutionDate);
      }
      if (maxExecutionDate) {
        params.append('maxExecutionDate', maxExecutionDate);
      }
      if (minClosureDate) {
        params.append('minClosureDate', minClosureDate);
      }
      if (maxClosureDate) {
        params.append('maxClosureDate', maxClosureDate);
      }
      if (searchByServiceId) {
        params.append('searchByServiceId', searchByServiceId);
      }
      if (searchByStatusId) {
        params.append('searchByStatusId', searchByStatusId);
      }
    }

    // Ajoutez les paramètres à l'URL
    if (params.toString()) {
      url += `&${params.toString()}`;
    }

    const { data } = yield call(request.get, url);

    yield put(getListOperationsTakenInChargeSuccess(data));
  } catch (error) {
    yield put(getListOperationsTakenInChargeError(error));
  }
}

export function* changeOperationStatusSaga({
  ids = [],
  status = '',
  executionDate = '',
}) {
  try {
    let url = `/takenInCharges/processMultiple`;
    let response = null;
    if (status === 'Planifié') {
      response = yield call(request.post, url, {
        ids: ids,
        status: status,
        executionDate: executionDate,
      });
    } else {
      response = yield call(request.post, url, {
        ids: ids,
        status: status,
      });
    }

    if (response && response.data) {
      yield put(changeOperationStatusSuccess({ success: true }));
    } else {
      yield put(changeOperationStatusSuccess({ success: false }));
    }
  } catch (error) {
    yield put(changeOperationStatusError({ error: error }));
  }
}

export default function* operationTakenInChargeSaga() {
  yield takeLatest(
    GET_LIST_OPERATIONS_TAKEN_IN_CHARGE,
    getListOperationTakenInChargeSaga,
  );

  yield takeLatest(CHANGE_OPERATION_STATUS, changeOperationStatusSaga);
}
