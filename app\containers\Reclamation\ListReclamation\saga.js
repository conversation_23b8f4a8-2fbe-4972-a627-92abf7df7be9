import { call, put, takeLatest, select } from 'redux-saga/effects';
import request from 'utils/request';
import {
  FETCH_RECLAMATIONS_REQUEST,
  UPDATE_RECLAMATION_REQUEST,
} from './constants';
import {
  fetchReclamationsSuccess,
  fetchReclamationsFailure,
  updateReclamationSuccess,
  updateReclamationFailure,
  fetchReclamationsRequest,
} from './actions';

// Fetch reclamations
export function* fetchReclamations(action) {
  const { params } = action;
  const requestURL = `/reclamation?page=${params.page || 0}&size=${params.size || 10}`;

  try {
    const response = yield call(request, requestURL);
    console.log('API Response:', response);

    // Extraire les données de la réponse
    let processedResponse;

    // Vérifier si la réponse a une structure avec data
    if (response && response.data) {
      processedResponse = response.data;
      console.log('Extracted data from response:', processedResponse);
    } else {
      // Si la réponse n'a pas de propriété data, utiliser la réponse directement
      processedResponse = response;
    }

    // Si la réponse est un tableau, la formater comme une réponse paginée
    if (Array.isArray(processedResponse)) {
      processedResponse = {
        content: processedResponse,
        totalElements: processedResponse.length,
        totalPages: 1,
        numberOfElements: processedResponse.length,
        pageable: {
          pageNumber: 0,
          pageSize: processedResponse.length,
        },
      };
    }

    yield put(fetchReclamationsSuccess(processedResponse));
  } catch (err) {
    yield put(fetchReclamationsFailure(err.message || 'Une erreur est survenue lors de la récupération des réclamations'));
  }
}

// Update reclamation
export function* updateReclamation(action) {
  const { id, data } = action;
  const requestURL = `/reclamation/update`;

  try {
    // Get current user from state
    const state = yield select();
    const currentUser = state.app.connectedUser;
    const respondedBy = currentUser ? `${currentUser.firstName} ${currentUser.lastName}` : 'Admin';

    console.log('Updating reclamation:', { id, data });
    
    // Get the original reclamation from the state
    const reclamations = state.reclamationList.reclamations;
    const originalReclamation = reclamations.content.find(r => r.id === parseInt(id, 10));

    if (!originalReclamation) {
      throw new Error('Reclamation not found');
    }

    // Create reclamationDTO with all original data and updated fields
    const reclamationDTO = {
      ...originalReclamation,
      status: data.status,
      response: data.response,
      respondedBy: respondedBy
    };

    console.log('Request body:', reclamationDTO);

    const response = yield call(request, requestURL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
      data: reclamationDTO
    });

    console.log('Update response:', response);
    
    // Handle the response
    if (response && typeof response === 'object') {
      const reclamation = response.data || response;
      yield put(updateReclamationSuccess(reclamation));
      
      // Refresh the reclamations list after successful update
      yield put(fetchReclamationsRequest({ page: 0, size: 10 }));
    } else {
      throw new Error('Invalid response format from server');
    }
  } catch (err) {
    console.error('Update error:', err);
    let errorMessage = 'Une erreur est survenue lors de la mise à jour de la réclamation';
    
    if (err.response) {
      if (err.response.data && typeof err.response.data === 'object') {
        errorMessage = err.response.data.message || errorMessage;
      } else if (typeof err.response.data === 'string') {
        errorMessage = err.response.data;
      }
    } else if (err.message) {
      errorMessage = err.message;
    }
    
    yield put(updateReclamationFailure(errorMessage));
  }
}

export default function* reclamationSaga() {
  yield takeLatest(FETCH_RECLAMATIONS_REQUEST, fetchReclamations);
  yield takeLatest(UPDATE_RECLAMATION_REQUEST, updateReclamation);
}
