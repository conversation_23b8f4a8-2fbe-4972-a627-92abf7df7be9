import React, { useEffect, useMemo, useState } from 'react';
import moment from 'moment';
import styles from 'Css/memberCard.css';
import style from 'Css/profileList.css';
import { PROFILE_PICTURE } from 'containers/Common/RequiredElement/Icons';
import { useHistory, useParams } from 'react-router-dom';
import { createStructuredSelector } from 'reselect';
import { useDispatch, useSelector } from 'react-redux';
import { useInjectReducer, useInjectSaga } from 'redux-injectors';
import { Alert } from 'react-bootstrap';
import { setAddMemberSuccess } from 'containers/Family/FamilyProfile/actions';
import btnStyles from 'Css/button.css';
import MemberAdd from 'components/Family/Profile/Members/MemberAdd';
import reducer from './reducer'
import Modal2 from 'components/Common/Modal2';

import saga from './saga'

import sagaAdd from '../addResponsable/saga'
import reducerAdd from '../addResponsable/reducer'
import { EDIT_ICON, DELETE_ICON } from 'components/Common/ListIcons/ListIcons';
import { loadResponsableEps, deleteResponsableEps ,resetDeleteResponsableEps} from './actions';
import {
  makeSelectResponsableEps,
  makeSelectDeleteSuccess,
  makeSelectLoading,
  makeSelectError
} from './selectors'

import { makeSelectSuccess } from '../addResponsable/selectors'

import AddResponsableComp from '../addResponsable';
import Scroller from 'components/Common/scroll/scroller';

const formatDate = date => moment(date).format('DD/MM/YYYY');

const key = 'responsableEps';
const keyAdd = 'responsable'

const stateSelector = createStructuredSelector({
  responsableEps: makeSelectResponsableEps,
  loading: makeSelectLoading,
  deleteSuccess: makeSelectDeleteSuccess,
  error: makeSelectError
});

const stateSelectorAdd = createStructuredSelector({
  success: makeSelectSuccess
})

export default function ResponsableEps(props) {
  const {
    responsableEps,
    loading,
    deleteSuccess,
    error,
  } = useSelector(stateSelector);
  const { success } = useSelector(stateSelectorAdd);
  const params = useParams();

  const [epsId, setEpsId] = useState(null);
  const handleCloseForDeleteModal = () => setShowDeleteModal(false);
  const [show, setShow] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [epsToEdit, setEpsToEdit] = useState('');
  const [epsToDelete, setEpsToDelete] = useState('');
  const [showAlert, setShowAlert] = useState(false);
  const [editResponsable, setEditResponsable] = useState(null);
  const [alert, setAlert] = useState(null);
  const dispatch = useDispatch();
  useInjectReducer({ key, reducer });
  useInjectSaga({ key, saga });

  useEffect(() => {
    setEpsId(params.id)
  }, [params.id])

  useEffect(() => {
    if (epsId) {
      dispatch(loadResponsableEps(epsId));
    }

  }, [dispatch, success, deleteSuccess, epsId])

  const code = [];
  let top = null;
  let text = null;

  useEffect(() => {
    if (success) {
      setShowAlert(true);
      if (editResponsable) {
        setAlert(
          <Alert
            variant="success"
            onClose={() => setShowAlert(false)}
            dismissible
          >
            Le responsable a été modifié avec succès.
          </Alert>,
        );
      } else {
        setAlert(
          <Alert
            variant="success"
            onClose={() => setShowAlert(false)}
            dismissible
          >
            Le responsable a été ajouté avec succès.
          </Alert>,
        );
      }
      setEditResponsable(null);
    }
  }, [success]);


  useEffect(() => {
    if (deleteSuccess) {
      setShowAlert(true);
      setAlert(
        <Alert
          variant="success"
          onClose={() => setShowAlert(false)}
          dismissible
        >
          Le responsable a été supprimé avec succès.
        </Alert>,
      );
    }
  }, [deleteSuccess]);

  useEffect(() => {
    if (showAlert) {
      const timer = setTimeout(() => {
        setShowAlert(false);
      }, 4000);
      dispatch(resetDeleteResponsableEps());
      return () => clearTimeout(timer);
    }
  }, [showAlert]);

  useEffect(() => {
    if (error) {
      setShowAlert(true);
      setAlert(
        <Alert variant="danger" onClose={() => setShowAlert(false)} dismissible>
          Une erreur est survenue lors de la suppression du responsable.
        </Alert>,
      );
    }
  }, [error]);

  const handleClose = () => {
    setShow(false);
    setEditResponsable(null)
  };



  if (responsableEps) {
    const responsables = responsableEps
    responsables.map(member => {
      top = (
        <div className={styles.top}>
          <img
            src={
              member.pictureBase64
                ? `data:image/png;base64,${atob(member.pictureBase64)}`
                : PROFILE_PICTURE
            }
            alt="Profile"
            className={`rounded-circle ${styles.imgBorder}`}
            style={{
              width: '100px', // Set a fixed width
              height: '100px', // Set a fixed height
              objectFit: 'cover', // Ensures the image maintains aspect ratio
              borderRadius: '50%', // Ensures it remains circular
              border: '2px solid #ddd', // Optional: to add a border
            }}
          />
          <div className={styles.info}>
            <p>
              {member.firstName} {member.lastName}
            </p>
            <p>
              {member.firstNameAr} {member.lastNameAr}
            </p>
          </div>
        </div>
      );


      text = (
        <div className={styles.data}>
          {/* Vérification que les propriétés existent avant de les afficher */}
          <div className={styles.row}>
            <div className={styles.label}>Code d'identité :</div>
            <div className={styles.value}>
              {member.identityCode || '--'}
            </div>
          </div>
          <div className={styles.row}>
            <div className={styles.label}>Téléphone :</div>
            <div className={styles.value}>
              {member.phoneNumber || '--'}
            </div>
          </div>
          <div className={styles.row}>
            <div className={styles.label}>Email :</div>
            <div className={styles.value}>
              {member.email || '--'}
            </div>
          </div>
          <div className={styles.row}>
            <div className={styles.label}>Fonction :</div>
            <div className={styles.value}>
              {member.functionContact ? member.functionContact.name : "--"}
            </div>
          </div>
          <div className={styles.row}>
            <div className={styles.label}>Commenataire :</div>
            <div className={styles.value}>
              {member.comment || '--'}
            </div>
          </div>
        </div>
      );

      code.push(
        <div
          className={`${styles.sideBar} style={{ backgroundColor: "white" }} ${false ? styles.selectedCard : ''
            }`} style={{ backgroundColor: "#f1f4f6" }}
        >
          <div className="ml-auto flex space-x-2">
            <input
              type="image"
              src={DELETE_ICON}
              className="p-2"
              width="40px"
              height="40px"
              onClick={() => {
                setShowDeleteModal(true);
                setEpsToDelete(member)
              }}
            />
            <input
              type="image"
              src={EDIT_ICON}
              className="p-2"
              width="40px"
              height="40px"
              onClick={() => {
                setEditResponsable(member)
                setShow(true);
              }}
            />
          </div>

          {top}
          <hr className={styles.hr} />
          {text}
        </div>
      );
    });
  }

  return (
    <Scroller height='70vh'>
      {showAlert && alert}
     
      {(
        <div>

          <div className={style.global}>
            <div className={style.header}>
              <div style={{ width: '100%', display: 'flex', justifyContent: 'end',marginTop:"10px" }}>
                <button
                  className={btnStyles.addBtnProfile}
                  onClick={() => {
                    setShow(true);
                  }}
                  data-toggle="modal"
                  data-target="#exampleModal"
                >
                  Ajouter un responsable
                </button>
              </div>

            </div>
          </div>
          <Modal2
            centered
            className="mt-5"
            title="Confirmation de suppression"
            show={showDeleteModal}
            handleClose={handleCloseForDeleteModal}
          >
            <p className="mt-1 mb-5">
                Êtes-vous sûr de vouloir supprimer ce responsable EPS ?
            </p>
            <div className="d-flex justify-content-end px-3 my-1">
              <button
                type="button"
                className={`mx-2 ${btnStyles.cancelBtn}`}
                onClick={handleCloseForDeleteModal}
              >
                Annuler
              </button>
              <button
                type="submit"
                className={`mx-2 ${btnStyles.addBtn}`}
                onClick={() => {
                  dispatch(deleteResponsableEps(epsToDelete.id));
                  setEpsToDelete(null);
                  handleCloseForDeleteModal();
                }}
              >
                Supprimer
              </button>
            </div>
          </Modal2>
          <Modal2
          
            title={
              editResponsable?"Modification d'un Responsable Eps":"Ajout d'un Responsable Eps"
            }
            size="lg"
            centered
            show={show}
            handleClose={() => {
              setShow(false);
              setEditResponsable(null)
            }}
            customWidth="modal-90w"
          >
            <AddResponsableComp
              member={editResponsable}
              show={show}
              handleClose={handleClose}
              epsId={epsId}
              setEditResponsable={setEditResponsable}
            />
          </Modal2>
        </div>
      )}
       { !loading && code.length!=0?<></>:(
        <div className=" text-center mt-2">
          <div
            style={{
              backgroundColor: '#d0e3ff',
              borderRadius: '5px',
              color: 'GrayText',
              padding: '10px',
              fontWeight: 'bold',
            }}
          >
            Cette EPS n'a pas des Responsables
          </div>
        </div>
      )}

      {code.length!=0?<div className={styles.global}>{code}</div>:<></>}
      
    </Scroller>
  );
}
