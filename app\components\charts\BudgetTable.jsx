import React from 'react';
import { Box, Typography, Paper, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Chip, useTheme, CircularProgress } from '@mui/material';
import { styled } from '@mui/material/styles';
import TocIcon from '@mui/icons-material/Toc';

const StyledTableContainer = styled(TableContainer)(({ theme }) => ({
  borderRadius: '12px',
  border: `1px solid ${theme.palette.grey[300]}`,
  boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
  '& .MuiTableHead-root': {
    backgroundColor: theme.palette.grey[50],
  },
  '& .MuiTableCell-root': {
    borderBottom: `1px solid ${theme.palette.grey[200]}`,
    padding: theme.spacing(2),
  },
  '& .MuiTableRow-root:hover': {
    backgroundColor: theme.palette.grey[50],
  },
}));

const StatusChip = styled(Chip)(({ theme, status }) => ({
  backgroundColor: status === 'disponible' 
    ? theme.palette.success.main 
    : theme.palette.primary.main,
  color: '#ffffff',
  fontWeight: 600,
  fontSize: '0.875rem',
  '& .MuiChip-label': {
    padding: '0 12px',
  },
}));

const AmountCell = styled(TableCell)(({ theme }) => ({
  fontWeight: 600,
  color: theme.palette.primary.main,
  fontSize: '1rem',
}));

const HeaderCell = styled(TableCell)(({ theme }) => ({
  fontWeight: 600,
  fontSize: '0.875rem',
  color: theme.palette.text.primary,
}));

const CategoryCell = styled(TableCell)(({ theme }) => ({
  '& .MuiTypography-root': {
    fontSize: '1rem',
  },
}));

const ServiceCell = styled(TableCell)(({ theme }) => ({
  '& .MuiTypography-root': {
    fontSize: '0.95rem',
  },
}));

const DescriptionCell = styled(TableCell)(({ theme }) => ({
  '& .MuiTypography-root': {
    fontSize: '0.95rem',
  },
}));

const EmptyStateCell = styled(TableCell)(({ theme }) => ({
  textAlign: 'center',
  padding: theme.spacing(4),
  color: theme.palette.text.secondary,
  fontSize: '1.1rem',
}));

const LoadingContainer = styled(Box)(({ theme }) => ({
  display: 'flex',
  justifyContent: 'center',
  alignItems: 'center',
  minHeight: '200px',
  width: '100%',
}));

export function BudgetTable({ columns = [], data = [], loading = false }) {
  const theme = useTheme();

  if (loading) {
    return (
      <StyledTableContainer component={Paper}>
        <LoadingContainer>
          <CircularProgress />
        </LoadingContainer>
      </StyledTableContainer>
    );
  }

  if (!data || data.length === 0) {
    return (
      <StyledTableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              {columns.map((column) => (
                <HeaderCell key={column.field} align={column.align || 'left'}>
                  {column.headerName}
                </HeaderCell>
              ))}
            </TableRow>
          </TableHead>
          <TableBody>
            <TableRow>
              <EmptyStateCell colSpan={columns.length}>
                Aucune donnée trouvée
              </EmptyStateCell>
            </TableRow>
          </TableBody>
        </Table>
      </StyledTableContainer>
    );
  }

  return (
    <StyledTableContainer component={Paper}>
      <Table>
        <TableHead>
          <TableRow>
            {columns.map((column) => (
              <HeaderCell key={column.field} align={column.align || 'left'}>
                {column.headerName}
              </HeaderCell>
            ))}
          </TableRow>
        </TableHead>
        <TableBody>
          {data.map((row, index) => (
            <TableRow key={row.id || index}>
              {columns.map((column) => {
                const value = row[column.field];
                const cellProps = {
                  key: column.field,
                  align: column.align || 'left',
                };

                if (column.renderCell) {
                  return (
                    <TableCell {...cellProps}>
                      {column.renderCell(row)}
                    </TableCell>
                  );
                }

                return (
                  <TableCell {...cellProps}>
                    {value}
                  </TableCell>
                );
              })}
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </StyledTableContainer>
  );
}