import React from 'react';
import { useState, useEffect } from 'react';
import listStyles from 'Css/list.css';
import {
    ARTICLE_ICON,
    DELETE_ICON,
    EDIT_ICON,
    VIEW_ICON,
} from 'components/Common/ListIcons/ListIcons';
import moment from 'moment';
import GenericFilter from 'containers/Common/Filter/GenericFilter';
import DataTable from 'components/Common/DataTable';
import { useDispatch, useSelector } from 'react-redux';
import { useInjectReducer, useInjectSaga } from 'redux-injectors';
import CustomPagination from 'components/Common/CustomPagination';
import documentsRenewReducer from 'components/SuivieDocumentsRenouvler/reducer';
import { Link, useHistory } from 'react-router-dom';
import documentsRenewSaga from 'components/SuivieDocumentsRenouvler/saga';
import { loadDocumentsRenew } from 'components/SuivieDocumentsRenouvler/actions';
import { createStructuredSelector } from 'reselect';
import { makeSelectDocumentsRenew, makeSelectLoading, makeSelectError } from "components/SuivieDocumentsRenouvler/selectors";
import { Padding } from '@mui/icons-material';

const key = 'documentsRenew';

const stateSelector = createStructuredSelector({
    documentsRenewList: makeSelectDocumentsRenew,
    loading: makeSelectLoading,
    error: makeSelectError,
});

export default function SuivieAssistantsDocumentsRenouvler() {

    const {
        documentsRenewList,
        loading,
        error,
    } = useSelector(stateSelector);

    const connectedUser = useSelector(state => state.app.connectedUser);

    const dispatch = useDispatch();
    const history = useHistory();

    const [listTest, setListTest] = useState([])

    useInjectReducer({ key, reducer: documentsRenewReducer });
    useInjectSaga({ key, saga: documentsRenewSaga });

    useEffect(() => {
        console.log('Loading documents renew with zoneIds:', connectedUser);
        dispatch(loadDocumentsRenew(0,connectedUser && connectedUser.zoneIds ? connectedUser.zoneIds : [0],null));

    }, [dispatch]);


    const principalInputsConfig = [
        {
            field: 'searchByName',
            type: 'text',
            placeholder: 'Nom Complet',
            label: 'Nom Complet',
        }
    ];

    const additionalInputsConfig = [
        
        {
            field: 'searchByModule',
            type: 'select',
            placeholder: 'Module',
            options: [
                { value: 'Beneficiaire', label: 'Beneficiaire' },
                { value: 'Famille', label: 'Famille' },
            ],
        },
        {
            field: 'searchByType',
            type: 'text',
            placeholder: 'Type de document',
            label: 'Type de document',
        },
        {
            field: 'searchByExpiryDate',
            type: 'date',
            placeholder: 'Date d\'expiration',
            label: 'Date d\'expiration',
        }
    ];

    const [filterValues, setFilterValues] = useState({
        searchByName: '',
        searchByModule: '',
        searchByType: '',
        searchByExpiryDate: '',
    });

    const handlePageChange = pageNumbers => {
        const nextPage = pageNumbers - 1;
        if (nextPage >= 0 && nextPage < documentsRenewList.totalPages) {
            setActivePage(nextPage);
            setFilterValues(prevFilterValues => {
                dispatch(loadDocumentsRenew(nextPage,connectedUser && connectedUser.zoneIds ? connectedUser.zoneIds : [],prevFilterValues));
                return prevFilterValues;
            });
        }
    };



    useEffect(() => {
        if (documentsRenewList) {
            setActivePage(documentsRenewList.number);
        }
    }, [documentsRenewList]);
    const [activePage, setActivePage] = useState(1);

    const formatDate = date => moment(date).format('DD/MM/YYYY');

    const handleApplyFilter = filters => {
        dispatch(loadDocumentsRenew(0,connectedUser && connectedUser.zoneIds ? connectedUser.zoneIds : [],filters));
        setFilterValues(filters);
    };

    const handleResetFilterComplete = () => {
        setFilterValues({
            searchByName: '',
            searchByModule: '',
            searchByType: '',
            searchByExpiryDate: '',
        });
    };

    const columns = [
        {
            field: 'codeEntity',
            headerName: 'Code Bénéficiaire ou famille',
            flex: 1,
            headerAlign: 'center',
            align: 'center',
        },
        {
            field: 'module',
            headerName: 'Module',
            flex: 1,
            headerAlign: 'center',
            align: 'center',
        },
        {
            field: 'nomEntity',
            headerName: 'Nom du Bénéficiaire ou famille',
            flex: 1,
            headerAlign: 'center',
            align: 'center',
        },
        {
            field: 'labelDocument',
            headerName: 'Objet du document',
            flex: 1,
            headerAlign: 'center',
            align: 'center',
        },
        {
            field: 'typeDocument',
            headerName: 'Type de document',
            flex: 1,
            headerAlign: 'center',
            align: 'center',
        },
        {
            field: 'expiryDate',
            headerName: 'Date d\’Expiration ',
            flex: 1,
            headerAlign: 'center',
            align: 'center',
            renderCell: params => (params.value ? formatDate(params.value) : '-'),
        },
        {
            field: 'status',
            headerName: 'Statut',
            flex: 1,
            headerAlign: 'center',
            align: 'center',
            renderCell: params => {
                const expiryDate = params.row.expiryDate;
                if (!expiryDate) return '-';
                
                const isExpired = moment(expiryDate).isBefore(moment(), 'day');
                const statusText = isExpired ? 'Expiré' : 'Pas encore expiré';
                const statusColor = isExpired ? '#ff0000' : '#00ff00';
                
                return (
                    <span style={{ 
                        color: statusColor,
                        fontWeight: 'bold',
                        padding: '5px 10px',
                        borderRadius: '4px',
                        backgroundColor: `${statusColor}20`
                    }}>
                        {statusText}
                    </span>
                );
            },
        },
        {
            field: 'actions',
            headerName: 'Actions',
            flex: 1,
            headerAlign: 'center',
            align: 'center',
            renderCell: params => (
                <>
                    <input
                        type="image"
                        className="p-2"
                        src={VIEW_ICON}
                        width="40px"
                        height="40px"
                        onClick={() => {                           
                            if(params.row.module === 'Famille'){
                                console.log("Famille")
                                history.push({
                                    pathname: `/families/fiche/${params.row.idEntity}/documents`, 
                                    state: {openDocumentId: params.id,isNormaleDoc: params.row.typeDocument != 'Normal'?false:true},
                                });
                            
                            }
                            if(params.row.module === 'Beneficiaire'){
                                console.log("Beneficiaire")
                                history.push({
                                    pathname: `/beneficiaries/fiche/${params.row.idEntity}/documents`,
                                    state: {openDocumentId: params.id,isNormaleDoc: params.row.typeDocument != 'Normal'?false:true},
                                });
                            }
                        }}
                        title="consulter"
                    />
                </>
            ),
        },
    ];

    return (
        <div className={listStyles.backgroundStyle} >
            <p>
            <br></br>
            </p>
            <GenericFilter
                className="col-12"
                principalInputsConfig={principalInputsConfig}
                additionalInputsConfig={additionalInputsConfig}
                onApplyFilter={handleApplyFilter}
                filterValues={filterValues}
                setFilterValues={setFilterValues}
                onResetFilterComplete={handleResetFilterComplete}
            />
            <div className="sub-container">
            {loading ? (
                <div className="d-flex justify-content-center pb-3">
                    <div
                        className="spinner-border"
                        style={{ width: '5rem', height: '5rem' }}
                        role="status"
                    >
                        <span className="sr-only">Chargement...</span>
                    </div>
                </div>
            ) : documentsRenewList && documentsRenewList.content ? (
                <>
                    <DataTable
                        rows={documentsRenewList.content.map(eps => ({
                            ...eps,
                        }))}
                        columns={columns}
                        fileName={`Liste des Eps, ${new Date().toLocaleString()}`}
                    />
                    {documentsRenewList.totalPages > 0 && (
                       <div className="justify-content-center mt-3">
                            <CustomPagination
                                totalElements={documentsRenewList.totalElements}
                                totalCount={documentsRenewList.totalPages}
                                pageSize={documentsRenewList.pageSize}
                                currentPage={documentsRenewList.number + 1}
                                onPageChange={handlePageChange}
                            />
                        </div>
                    )}
                </>
            ) : (
                <div className={`no-profiles-message ${listStyles.disable}`}>
                    Aucune Document disponible
                </div>
            )}
            </div>
        </div>
    );
}