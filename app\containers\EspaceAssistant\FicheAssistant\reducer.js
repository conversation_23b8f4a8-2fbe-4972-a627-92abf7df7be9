import produce from 'immer';
import {
  LOAD_CONNECTED_ASSISTANT,
  LOAD_CONNECTED_ASSISTANT_SUCCESS,
  LOAD_CONNECTED_ASSISTANT_ERROR,
  LOAD_ZONES_WITH_SOUS_ZONES,
  LOAD_ZONES_WITH_SOUS_ZONES_SUCCESS,
  LOAD_ZONES_WITH_SOUS_ZONES_ERROR,
  LOAD_SOUS_ZONES,
  LOAD_SOUS_ZONES_SUCCESS,
  LOAD_SOUS_ZONES_ERROR,
  LOAD_RAPPORT_BY_ASSISTANT,
  LOAD_RAPPORT_BY_ASSISTANT_SUCCESS,
  LOAD_RAPPORT_BY_ASSISTANT_ERROR,
} from './constants';

export const initialState = {
  loading: false,
  error: false,
  connectedAssistant: false,
  sousZones: [],
  zonesWithSousZones: [],
  zonesLoading: false,
  zonesError: false,
  rapportByAssistant: [],
};

/* eslint-disable default-case, no-param-reassign */
const ficheAssistantReducer = produce((draft, action) => {
  switch (action.type) {
    case LOAD_CONNECTED_ASSISTANT:
      draft.loading = true;
      draft.error = false;
      draft.connectedAssistant = false;
      break;
    case LOAD_CONNECTED_ASSISTANT_SUCCESS:
      draft.loading = false;
      draft.error = false;
      draft.connectedAssistant = action.connectedAssistant;
      break;
    case LOAD_CONNECTED_ASSISTANT_ERROR:
      draft.loading = false;
      draft.error = action.error;
      break;

    case LOAD_SOUS_ZONES:
      draft.loading = true;
      draft.error = false;
      draft.sousZones = [];
      break;
    case LOAD_SOUS_ZONES_SUCCESS:
      draft.loading = false;
      draft.error = false;
      draft.sousZones = action.sousZones;
      break;
    case LOAD_SOUS_ZONES_ERROR:
      draft.loading = false;
      draft.error = action.error;
      break;

    case LOAD_RAPPORT_BY_ASSISTANT:
      draft.loading = true;
      draft.error = false;
      draft.rapportByAssistant = [];
      break;
    case LOAD_RAPPORT_BY_ASSISTANT_SUCCESS:
      draft.loading = false;
      draft.error = false;
      draft.rapportByAssistant = action.rapportByAssistant;
      break;
    case LOAD_RAPPORT_BY_ASSISTANT_ERROR:
      draft.loading = false;
      draft.error = action.error;
      break;

    case LOAD_ZONES_WITH_SOUS_ZONES:
      draft.zonesLoading = true;
      draft.zonesError = false;
      draft.zonesWithSousZones = [];
      break;
    case LOAD_ZONES_WITH_SOUS_ZONES_SUCCESS:
      draft.zonesWithSousZones = action.zones.data || action.zones;
      draft.zonesLoading = false;
      break;
    case LOAD_ZONES_WITH_SOUS_ZONES_ERROR:
      draft.zonesError = action.error;
      draft.zonesLoading = false;
      draft.zonesWithSousZones = [];
      break;
  }
}, initialState);

export default ficheAssistantReducer;
