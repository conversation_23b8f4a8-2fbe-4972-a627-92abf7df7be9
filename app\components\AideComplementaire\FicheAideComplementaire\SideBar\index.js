import React from 'react';
import sideBarStyles from '../../../../Css/sideBar.css';
import { Statut } from '../../enum';
import tagStyles from '../../../../Css/tag.css';
import moment from 'moment';

const formatDate = date => moment(date).format('DD/MM/YYYY');

const SideBar = ({ data: aideComplementaire }) => {
  const totalBeneficiaries =
    aideComplementaire.beneficiaryAideComplemenatireDTOList &&
    aideComplementaire.beneficiaryAideComplemenatireDTOList.length
      ? aideComplementaire.beneficiaryAideComplemenatireDTOList.length
      : 0;

  const totalDonors =
    aideComplementaire.donorAideComplemenatireDTOList &&
    aideComplementaire.donorAideComplemenatireDTOList.length
      ? aideComplementaire.donorAideComplemenatireDTOList.length
      : 0;

  const montantACollecter =
    totalBeneficiaries * aideComplementaire.amountPerBeneficiary;

  const montantDesFrais =
    (aideComplementaire.montantCollecter * aideComplementaire.costs) / 100;
  const montantACollecterAvecFrais = montantACollecter + montantDesFrais;
  const montantCollecterAvecFrais =
    aideComplementaire.montantCollecter - montantDesFrais;
  const montantRestantInclusFrais =
    montantCollecterAvecFrais - aideComplementaire.montantTotalAffecter;

  const getTag = statut => {
    switch (statut) {
      case Statut.PLANIFIER:
        return tagStyles.tagGrey;
      case Statut.EN_COURS:
        return tagStyles.tagGreen;
      case Statut.EN_ATTENTE_D_EXECUTION:
        return tagStyles.tagOrange;
      case Statut.EXECUTER:
        return tagStyles.tagBlue;
      case Statut.CLOTURER:
        return tagStyles.tagYellow;
      default:
        return tagStyles.tagGrey;
    }
  };

  const getStatutText = statut => {
    switch (statut) {
      case Statut.PLANIFIER:
        return 'Planifiée';
      case Statut.EN_COURS:
        return 'En cours';
      case Statut.EN_ATTENTE_D_EXECUTION:
        return "En attente d'exécution";
      case Statut.EXECUTER:
        return 'Exécutée';
      case Statut.CLOTURER:
        return 'Clôturée';
      default:
        return '---';
    }
  };

  return (
    <div className={sideBarStyles.sideBar}>
      <div className={sideBarStyles.topCard}>
        <div className={sideBarStyles.top}>
        <h5 className="mb-3">Aide complémentaire :</h5>
        <p className={sideBarStyles.linkDonor}>{aideComplementaire.code}</p>
        <p className={sideBarStyles.linkDonor}>{aideComplementaire.name}</p>
        <p className={getTag(aideComplementaire.statut)}>
          {getStatutText(aideComplementaire.statut)}
        </p>
        {aideComplementaire.montantExecuter &&
          aideComplementaire.dateExecution && (
            <>
              <p className={sideBarStyles.linkDonor}>
                Montant exécuté : {aideComplementaire.montantExecuter} DH
              </p>
              <p className={sideBarStyles.linkDonor}>
                Date d'exécution :{' '}
                {formatDate(aideComplementaire.dateExecution)}
              </p>
            </>
          )}
        </div>
      </div>

      <div className={`mt-5 ${sideBarStyles.label}`}>
        <h5 className="text-center ">Statistiques</h5>
        <div className="text-center ">
          <p>Nombre total de donateurs participants : {totalDonors}</p>
          <p>
            Nombre total de bénéficiaires validés :{' '}
            {aideComplementaire.nbrBeneficiariesValidated}
          </p>
        </div>

        <div
          className={sideBarStyles.text}
          style={{
            display: 'flex',
            flexDirection: 'column',
            gap: '12px',
            marginTop: '20px',
          }}
        >
          {[
            {
              label: 'Montant souhaité',
              value: aideComplementaire
                ? aideComplementaire.montantPrevu || 0
                : 0,
              color: '#4F89D7',
            },
            {
              label: 'Montant disponible',
              value: aideComplementaire
                ? aideComplementaire.montantCollecter || 0
                : 0,
              color: '#EF8C5A',
            },
            {
              label: 'Montant disponible (incl. frais)',
              value: montantCollecterAvecFrais
                ? montantCollecterAvecFrais || 0
                : 0,
              color: 'darkgoldenrod',
            },
            {
              label: 'Montant validé',
              value: aideComplementaire
                ? aideComplementaire.montantTotalAffecter || 0
                : 0,
              color: '#6DD5A0',
            },
            {
              label: 'Montant restant non affecté',
              value: montantRestantInclusFrais
                ? montantRestantInclusFrais || 0
                : 0,
              color: '#6DD5A0',
            },
          ].map((item, idx) => (
            <div
              key={idx}
              style={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                marginBottom: '12px', // Space between rows
              }}
            >
              {/* Label with more space */}
              <div
                style={{
                  fontWeight: '600',
                  color: '#555',
                }}
              >
                {item.label}
              </div>

              <button
                className={sideBarStyles.itemValueStyle}
                style={{backgroundColor: item.color}}
              >
                {item.value} DH
              </button>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default SideBar;
