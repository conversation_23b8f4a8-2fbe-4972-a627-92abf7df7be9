import styled from 'styled-components';

const Wrapper = styled.div`
  .MuiDataGrid-root {
    border: 0;
    border-radius: 20px 20px 0 0;
  }

  .MuiDataGrid-overlay {
    border-radius: 0 0 20px 20px;
  }

  .MuiDataGrid-root
    .MuiDataGrid-row:not(.MuiDataGrid-row--dynamicHeight)
    > .MuiDataGrid-cell {
    white-space: normal;
  }

  .data-grid-container .MuiDataGrid-root {
    background-color: white;
    border: 2px solid white;
    border-radius: 10px;
    color: #3b3b3b;
    padding: 0 1%;
  }

  .data-grid-container .MuiDataGrid-columnsContainer {
    text-align: center;
  }

  .data-grid-container .MuiDataGrid-header {
    display: flex;
    justify-content: center;
    text-align: center;
    margin: 2% 1% 2% 1%;
  }

  .data-grid-container .MuiDataGrid-cell {
    border-right: 1px solid #ccc;
    border-bottom: 1px solid #ccc;
    text-align: center;
    font-family: 'lexend', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  }

  .data-grid-container .MuiDataGrid-colCellTitle {
    text-align: center;
    font-family: 'lexend', 'Helvetica Neue', Helvetica, Arial, sans-serif;
    display: flex;
    justify-content: center;
    font-weight: bold !important;
  }

  .data-grid-container .MuiDataGrid-cell--selected {
    outline: none !important;
  }

  [data-mui-testid='cell-selected'] {
    outline: none !important;
  }

  .custom-data-grid-footer {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    height: 100%;
    padding-right: 16px;
  }

  .MuiDataGrid-footer {
    position: absolute;
    bottom: 0;
    width: 100%;
    background-color: #f0f0f0;
    padding: 8px 16px;
    box-shadow: 0 -2px 4px rgba(0, 0, 0, 0.1);
  }

  /* Centered cell styling */
  .centeredCell {
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
    text-align: center !important;
  }

  /* Center the content of all cells */
  .MuiDataGrid-cell {
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
    text-align: center !important;
  }

  /* Center the column headers */
  .MuiDataGrid-columnHeader {
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
    text-align: center !important;
  }

  /* Center the header title */
  .MuiDataGrid-columnHeaderTitle {
    text-align: center !important;
    width: 100% !important;
  }

  /* Center the sort icon */
  .MuiDataGrid-sortIcon {
    margin-left: 4px;
  }

  /* Center the column header content */
  .MuiDataGrid-columnHeader .MuiDataGrid-columnHeaderTitleContainer {
    justify-content: center !important;
    text-align: center !important;
  }
`;
export default Wrapper;
