import React from 'react';
import { Link, NavLink } from 'react-router-dom';
import { useParams } from 'react-router-dom';

import styles from '../../../../Css/profileHeader.css';

const HeaderDonation = props => {
  const params = useParams();
  const id = params.id;

  return (
    <div className={styles.navBar}>
      <p>
        <NavLink
          exact
          to={'/donations/fiche/' + params.id + '/info'}
          activeClassName={styles.selected}
        >
          Informations générales
        </NavLink>
      </p>
      {/* {props.isNature ? (
        <p>
          <NavLink
            exact
            to={'/donations/fiche/' + params.id + '/products'}
            activeClassName={styles.selected}
          >
            Produits
          </NavLink>
        </p>
      ) : null} */}
      <p>
        <NavLink
          exact
          to={'/donations/fiche/' + params.id + '/documents'}
          activeClassName={styles.selected}
        >
          Documents
        </NavLink>
      </p>

      <p>
        <NavLink
          exact
          to={'/donations/fiche/' + params.id + '/notes'}
          activeClassName={styles.selected}
        >
          Notes
        </NavLink>
      </p>
      <p>
        <NavLink
          to={`/donations/fiche/${id}/action`}
          activeClassName={styles.selected}
        >
          Actions
        </NavLink>
      </p>
    </div>
  );
};

export default HeaderDonation;
