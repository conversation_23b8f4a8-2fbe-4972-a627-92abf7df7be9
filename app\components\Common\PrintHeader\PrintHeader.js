import React from 'react';
import { Typography } from '@mui/material';
import Logo from '../../../images/almobadara-logo.png';
import { makeStyles } from '@material-ui/core';

const useStyles = makeStyles(theme => ({
  container: {
    display: 'flex',
    alignItems: 'center', // Alignement vertical au centre
    marginBottom: '20px',
  },

  logo: {
    width: '300px',
    marginBottom: '30px',
    marginLeft: '20px',
    marginRight: '180px',
    '@media print': {
      width: '160px',
      marginBottom: '20px',
    },
  },
  headerText: {
    marginBottom: '80px',
    marginTop: '80px',
    textAlign: 'center',
    justifyContent: 'center',
    '@media print': {
      marginBottom: '40px',
      marginTop: '40px',
    },
  },
}));
const PrintHeader = ({ headerText }) => {
  const classes = useStyles();

  return (
    <div className={classes.container}>
      <img src={Logo} alt="Logo" className={classes.logo} />
      <Typography variant="h3" gutterBottom className={classes.headerText}>
        {headerText}
      </Typography>
    </div>
  );
};

export default PrintHeader;
