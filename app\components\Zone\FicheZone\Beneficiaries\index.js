import React, { useState } from 'react';
import moment from 'moment';
import styles from 'Css/profileList.css';
import stylesList from 'Css/profileList.css';
import { useDispatch, useSelector } from 'react-redux';
import AccessControl, { isAuthorized } from 'utils/AccessControl';
import { Link, useHistory } from 'react-router-dom';
import DataTable from '../../../Common/DataTable';
import CustomPagination from '../../../Common/CustomPagination';

const formatDate = date => moment(date).format('DD/MM/YYYY');

export default function Beneficiaries(props) {
  const history = useHistory();
  let listBeneficiaries = [];
  const successAlert = null;

  const dispatch = useDispatch();

  // Define pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const pageSize = 5; // Define page size

  const zone = props.data;

  let ZoneCode;

  if (zone) {
    ZoneCode = zone.code;
  }

  if (zone) {
    const beneficiarySorted = [...zone.beneficiaries].sort((a, b) =>
      a.beneficiaryCreatedAt < b.beneficiaryCreatedAt ? 1 : -1,
    );

    const startIndex = (currentPage - 1) * pageSize;
    const endIndex = Math.min(startIndex + pageSize, beneficiarySorted.length);
    const paginatedZones = beneficiarySorted.slice(startIndex, endIndex);

    listBeneficiaries = paginatedZones.map(beneficiary => ({
      ...beneficiary,
      beneficiaryName: `${beneficiary.beneficiaryName}${' '}${
        beneficiary.beneficiaryLastName
      }`,
      beneficiarId: beneficiary.id,
    }));
  }

  const columns = [
    {
      field: 'beneficiaryCode',
      headerName: 'Code béneficiaire',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
      renderCell: params => (
        <Link to={`/beneficiaries/fiche/${params.row.beneficiarId}/info`}>
          {params.row.beneficiaryCode}
        </Link>
      ),
    },
    {
      field: 'beneficiaryName',
      headerName: 'Nom Complet',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
    // {
    //   field: 'arabicName',
    //   headerName: 'الاسم الكامل',
    //   flex: 1,
    //   headerAlign: 'center',
    //   align: 'center',
    // },
    {
      field: 'beneficiaryBirthDate',
      headerName: 'Date de naissance',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
    {
      field: 'beneficiaryAddress',
      headerName: 'Address',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
    {
      field: 'beneficiaryPhoneNumber',
      headerName: 'Téléphone',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
    // {
    //   field: 'city',
    //   headerName: 'Ville',
    //   flex: 1,
    //   headerAlign: 'center',
    //   align: 'center',
    // },
  ];
  const connectedUser = useSelector(state => state.app.connectedUser);
  const isUpdateAuthorized = isAuthorized(connectedUser, 'DONATION', 'UPDATE');

  return (
    <div>
      {successAlert}
      <div className={`pb-5 ${stylesList.backgroudStyle}`}>
        <div>
          <div className={styles.global}>
            <div className={styles.header}>
              <h4>Liste des Bénéficiaires</h4>
            </div>

            <DataTable
              rows={listBeneficiaries}
              columns={columns}
              fileName={`Liste des notes de la donation ${ZoneCode}, ${new Date().toLocaleString()}`}
            />

            <div className="justify-content-center my-4">
              {zone && (
                <CustomPagination
                  totalCount={Math.ceil(zone.beneficiaries.length / pageSize)}
                  pageSize={pageSize}
                  currentPage={currentPage}
                  onPageChange={setCurrentPage}
                />
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
