import React, { useEffect, useState } from 'react';
import moment from 'moment';
import { Link, useHistory, useParams } from 'react-router-dom';

import { createStructuredSelector } from 'reselect';
import { useDispatch, useSelector } from 'react-redux';
import { useInjectReducer, useInjectSaga } from 'redux-injectors';
import reducer from 'containers/Common/SubComponents/ActionForm/reducer';

import {
  pushActionDonor,
  removeActionDonor,
  updateActionDonor,
} from 'containers/Donor/DonorProfile/actions';
import Modal2 from 'components/Common/Modal2';
import { Alert } from 'react-bootstrap';

import { meActionSaga } from 'containers/Common/SubComponents/ActionForm/saga';
import {
  deleteAction,
  getAllActions,
  resetAction,
} from 'containers/Common/SubComponents/ActionForm/actions';
import ActionForm from 'containers/Common/SubComponents/ActionForm';
import {
  makeSelecAction,
  makeSelecActionDeleteSuccess,
  makeSelecActions,
  makeSelecAllActions,
  makeSelectActionsAddDonorSuccess,
  makeSelectError,
  makeSelectSuccess,
} from 'containers/Common/SubComponents/ActionForm/selectors';
import { isAuthorized } from 'utils/AccessControl';
import btnStyles from '../../Css/button.css';
import list from '../../Css/profileList.css';
import listStyles from '../../Css/list.css';
import {
  DELETE_ICON,
  EDIT_ICON,
  VIEW_ICON,
} from '../Common/ListIcons/ListIcons';
import DataTable from '../Common/DataTable';
import CustomPagination from '../Common/CustomPagination';
import tagStyles from '../../Css/tag.css';
import GenericFilter from '../../containers/Common/Filter/GenericFilter';
import { makeSelectStatuses } from '../../containers/Donor/SubComponents/ActionStatus/selectors';
import omdbSaga from '../../containers/Donor/SubComponents/ActionStatus/saga';
import { loadStatuses } from '../../containers/Donor/SubComponents/ActionStatus/actions';
import statusesReducer from '../../containers/Donor/SubComponents/ActionStatus/reducer';

const key = 'actionData';
const key2 = 'status';

const formatDate = date => moment(date).format('DD/MM/YYYY');

const omdbSelector = createStructuredSelector({
  success: makeSelectSuccess,
  error: makeSelectError,
  actionData: makeSelecAction,
  successDelete: makeSelecActionDeleteSuccess,
  actionsData: makeSelecActions,
  actionDonorAddSuccess: makeSelectActionsAddDonorSuccess,
  actions: makeSelecAllActions,
});

const omdbSelector2 = createStructuredSelector({
  statuses: makeSelectStatuses,
});
export default function Actions(props) {
  const dispatch = useDispatch();
  useInjectReducer({ key, reducer });
  useInjectReducer({ key: key2, reducer: statusesReducer });
  useInjectSaga({ key, saga: meActionSaga });
  useInjectSaga({ key: key2, saga: omdbSaga });
  const history = useHistory();
  const params = useParams();
  const [show, setShow] = useState(false);
  const [actionToDelete, setActionToDelete] = useState('');
  const [actionTarget, setActionTarget] = useState('');
  const [actionEditEntityId, setActionEditEntityId] = useState(null);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [actionToReadOnly, setActionToReadOnly] = useState({
    title: "Consulter L'action",
    isRead: false,
    actionData: null,
  });

  // Define pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const pageSize = 10; // Define page size

  const handleClose = () => {
    setShow(false);
    actionToReadOnly.isRead &&
      setActionToReadOnly({
        isRead: false,
        title: "Consulter L'action",
        actionData: null,
      });
  };

  const handleShow = () => {
    setShow(true);
  };

  const handleCloseForDeleteModal = () => setShowDeleteModal(false);

  useEffect(
    () =>
      function cleanup() {
        dispatch(resetAction());
      },
    [],
  );

  const {
    success,
    error,
    actionData,
    successDelete,
    actionsData,
    actionDonorAddSuccess,
    actions,
  } = useSelector(omdbSelector);
  const { statuses } = useSelector(omdbSelector2);
  const [actionToEdit, setActionToEdit] = useState('');
  const [message, setMessage] = useState('');
  const [showAlert, setShowAlert] = useState(false);

  let errorMessage = null;

  useEffect(() => {
    dispatch(loadStatuses('search'));
  }, []);

  const principalInputsConfig = [
    {
      field: 'searchByValue',
      type: 'text',
      placeholder: 'Recherche',
      label: 'Recherche',
    },
  ];

  const additionalInputsConfig = [
    {
      field: 'searchByModule',
      type: 'select',
      placeholder: 'Module',
      options: [
        { value: 'Donateur', label: 'Donateur' },
        { value: 'Beneficiaire', label: 'Beneficiaire' },
        { value: 'Famille', label: 'Famille' },
        { value: 'Donation', label: 'Donation' },
        { value: 'Prise en charge', label: 'Prise en charge' },
      ],
    },
    {
      field: 'searchByStatus',
      type: 'select',
      placeholder: 'Statut',
      options:
        statuses &&
        statuses.map(status => ({ value: status.id, label: status.name })),
    },
    {
      field: 'searchByUser',
      type: 'text',
      placeholder: 'Utilisateur',
    },
    {
      field: 'minDate',
      type: 'date',
      placeholder: 'Date limite minimale',
    },
    {
      field: 'maxDate',
      type: 'date',
      placeholder: 'Date limite maximale',
    },
  ];

  useEffect(() => {
    if (actions && actions.pageable) {
      setCurrentPage(actions.pageable.pageNumber + 1);
    }
  }, [actions]);

  const [filterValues, setFilterValues] = useState({
    searchByValue: '',
    searchByModule: '',
    searchByStatus: '',
    searchByUser: '',
    minDate: '',
    maxDate: '',
  });

  const handleApplyFilter = filters => {
    dispatch(getAllActions(0, 10, filters));
  };

  const handlePageChange = pageNumber => {
    setCurrentPage(pageNumber - 1);
    dispatch(getAllActions(pageNumber - 1, 10, filterValues));
  };

  const handleResetFilterComplete = () => {
    setFilterValues({
      searchByValue: '',
      searchByOperation: '',
      searchBySubOperation: '',
      searchByUser: '',
      minDate: '',
      maxDate: '',
    });
  };

  const beneficiary = props.data;
  let beneficiaryCode;

  if (beneficiary) {
    beneficiaryCode = beneficiary.code;
  }

  const getTag = status => {
    if (status === 'Abandonnée') {
      return tagStyles.tagRed;
    }
    if (status === 'Réalisée') {
      return tagStyles.tagGreen;
    }
    if (status === 'À effectuer') {
      return tagStyles.tagYellow;
    }
    if (status === 'En cours') {
      return tagStyles.tagBlue;
    }
    return tagStyles.tagGrey;
  };

  let listActions = [];

  useEffect(() => {
    if (successDelete) {
      setShowAlert(true);
      dispatch(removeActionDonor(actionData));
      setMessage('Action supprimé avec succès');
    }
  }, [successDelete]);

  useEffect(() => {
    if (actionDonorAddSuccess) {
      setShowAlert(true);
      if (actionToEdit) {
        dispatch(updateActionDonor(actionData));
        setMessage('Action modifiée avec succès');
      } else {
        dispatch(pushActionDonor(actionData));
        setMessage('Action ajoutée avec succès');
      }
      setActionToEdit('');
      setActionTarget('');
      setActionEditEntityId(null);
    }
  }, [actionDonorAddSuccess]);

  useEffect(() => {
    if (showAlert) {
      setTimeout(() => {
        setShowAlert(false);
      }, 4000);
    }
  }, [showAlert]);

  // to close the error message after 4 seconds
  useEffect(() => {
    if (error) {
      setTimeout(() => {
        dispatch(resetNote());
      }, 4000);
    }
  }, [error]);

  useEffect(() => {
    dispatch(getAllActions(0, 10));
  }, [dispatch]);

  useEffect(() => {
    if (successDelete || actionDonorAddSuccess) {
      dispatch(getAllActions(0, 10));
    }
  }, [successDelete, actionDonorAddSuccess]);

  if (error) {
    errorMessage = 'Une erreur est survenue';
  }

  const emptyValue = <span>-</span>;

  if (actions && actions.content) {
    listActions = actions.content.map(action => ({
      id: action.actionDTO.id,
      content: action.actionDTO.content,
      subject: action.actionDTO.subject,
      dateEntry: action.actionDTO.dateEntry
        ? formatDate(action.actionDTO.dateEntry)
        : emptyValue,
      dateRealize: action.actionDTO.dateRealize
        ? formatDate(action.actionDTO.dateRealize)
        : '-',
      deadline: action.actionDTO.deadline
        ? formatDate(action.actionDTO.deadline)
        : emptyValue,
      createdBy: action.actionDTO.createdBy
        ? `${action.actionDTO.createdBy.firstName}${' '}${
            action.actionDTO.createdBy.lastName
          }`
        : emptyValue,
      affectedTo: action.actionDTO.affectedTo
        ? `${action.actionDTO.affectedTo.firstName}${' '}${
            action.actionDTO.affectedTo.lastName
          }`
        : emptyValue,
      actionStatus: action.actionDTO.actionStatus
        ? action.actionDTO.actionStatus.name
        : emptyValue,

      moduleName: action.moduleName,

      entityType: action.entityType,
      entityId: action.entityId,
      modulepath: action.modulepath,
      actionDTO: action.actionDTO,
      action,
    }));
  }

  const columns = [
    {
      field: 'moduleName',
      headerName: 'Module',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
    {
      field: 'subject',
      headerName: 'Objet',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
      renderCell: params => (
        <Link
          to={{
            pathname: `${params.row.modulepath}/fiche/${params.row.entityId}/action`,
            state: { actionDTO: params.row.action }, // Passer l'action via state
          }}
        >
          {params.row.subject}
        </Link>
      ),
    },
    {
      field: 'dateEntry',
      headerName: 'Date de saisie',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
    {
      field: 'dateRealize',
      headerName: 'Date de réalisation',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
    {
      field: 'deadline',
      headerName: 'Date limite',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
    {
      field: 'createdBy',
      headerName: 'Affecté par',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
    {
      field: 'affectedTo',
      headerName: 'Affecté à',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
    {
      field: 'actionStatus',
      headerName: 'Statut',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
      renderCell: params => (
        <div className={getTag(params.row.actionStatus)}>
          {params.row.actionStatus || '---'}
        </div>
      ),
    },
  ];
  const connectedUser = useSelector(state => state.app.connectedUser);
  const isUpdateAuthorized = isAuthorized(connectedUser, 'USER', 'UPDATE');
  if (isUpdateAuthorized) {
    columns.push({
      field: 'actions',
      headerName: 'Actions',
      flex: 1.2,
      headerAlign: 'center',
      align: 'center',
      type: 'actions',
      renderCell: params => (
        <div>
          <input
            type="image"
            className="p-2"
            src={VIEW_ICON}
            width="40px"
            height="40px"
            /* onClick={() => {
              const entityUrl = `${params.row.modulepath}/fiche/${params.row.entityId}/action`;
              history.push(entityUrl);
            }} */
            onClick={() => {
              setActionToEdit('');
              setActionToReadOnly(prev => ({
                ...prev,
                isRead: true,
                actionData: params.row.actionDTO,
              }));
              handleShow();
            }}
            title="consulter"
          />
          <input
            type="image"
            src={EDIT_ICON}
            className="p-2"
            width="40px"
            height="40px"
            onClick={() => {
              setActionToEdit(params.row.actionDTO);
              setActionTarget(params.row.entityType);
              setActionEditEntityId(params.row.entityId);
              handleShow();
            }}
            title="modifier"
          />
          <input
            type="image"
            src={DELETE_ICON}
            className="p-2"
            width="40px"
            height="40px"
            onClick={() => {
              setActionToDelete(params.row.actionDTO);
              setActionTarget(params.row.entityType);
              setShowDeleteModal(true);
            }}
            title="supprimer"
          />
        </div>
      ),
    });
  }
  return (
    <div>
      <Modal2
        title={
          actionToReadOnly.isRead
            ? actionToReadOnly.title
            : actionToEdit
            ? "Modifier l'action"
            : 'Ajouter une action'
        }
        size="lg"
        customWidth="modal-90w"
        show={show}
        handleClose={() => {
          setShow(false);
          setActionToReadOnly({
            isRead: false,
            title: "Consulter l'action",
            actionData: null,
          });
        }}
      >
        <ActionForm
          action={
            actionToEdit ||
            (actionToReadOnly.actionData
              ? actionToReadOnly.actionData
              : actionToEdit)
          }
          handleClose={handleClose}
          button={actionToEdit ? 'Modifier' : 'Ajouter'}
          target={actionTarget}
          id={actionEditEntityId}
          isRead={actionToReadOnly.isRead}
        />
      </Modal2>

      <Modal2
        centered
        className="mt-5"
        title="Confirmation de suppression"
        show={showDeleteModal}
        handleClose={handleCloseForDeleteModal}
      >
        <p className="mt-1 mb-5">
          Êtes-vous sûr de vouloir supprimer cette action?
        </p>
        <div className="d-flex justify-content-end px-3 my-1">
          <button
            type="button"
            className={`mx-2 btn-style outlined`}
            onClick={handleCloseForDeleteModal}
          >
            Annuler
          </button>
          <button
            type="submit"
            className={`mx-2 btn-style primary`}
            onClick={() => {
              dispatch(deleteAction(actionToDelete, actionTarget));
              setActionToDelete('');
              setActionTarget('');
              handleCloseForDeleteModal();
            }}
          >
            Supprimer
          </button>
        </div>
      </Modal2>

      {showAlert ? (
        <Alert
          className="alert-style"
          variant="success"
          onClose={() => setShowAlert(false)}
          dismissible
        >
          <p>{message}</p>
        </Alert>
      ) : null}

      {error && (
        <Alert
          className="alert-style"
          variant="danger"
          onClose={() => setShowAlert(false)}
          dismissible
        >
          <p>{errorMessage}</p>
        </Alert>
      )}

      <div className={listStyles.backgroudStyle}>
        {/* <div className={list.global}> */}
        <div className={listStyles.header}>
          <h4></h4>
        </div>

        <GenericFilter
          principalInputsConfig={principalInputsConfig}
          additionalInputsConfig={additionalInputsConfig}
          onApplyFilter={handleApplyFilter}
          filterValues={filterValues} // Pass the filterValues state
          setFilterValues={setFilterValues}
          onResetFilterComplete={handleResetFilterComplete} // Pass the function to update filterValues
        />

        <div className={`${list.content} sub-container`}>
          <DataTable
            rows={listActions}
            columns={columns}
            fileName={`Liste de Suivie des Actions, ${new Date().toLocaleString()}`}
            totalElements={actions ? actions.totalElements : null}
            numberOfElements={actions ? actions.numberOfElements : null}
            pageable={actions ? actions.pageable : null}
          />

          <div className="justify-content-center mt-3">
            {actionsData && actions && (
              <CustomPagination
                totalElements={actions.numberOfElements}
                totalCount={actions ? actions.totalPages : null}
                pageSize={
                  actions && actions.pageable ? actions.pageable.pageSize : 10
                }
                currentPage={actions ? actions.number + 1 : currentPage}
                onPageChange={handlePageChange}
              />
            )}
          </div>
        </div>
      </div>
      {/* </div> */}
    </div>
  );
}
