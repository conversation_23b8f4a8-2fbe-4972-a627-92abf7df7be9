import { createSelector } from "reselect";
import { initialState } from "./reducer"

// Select raw state
const selectTag = state => state.tagList || initialState;

// Memoized selector for tags
const makeSelectTags = createSelector(
    selectTag,
    tagState => tagState.tags || []
);

const makeSelectTagList = createSelector(
    selectTag,
    tagState => tagState.tagList || []
);

const makeSelectTypeTags = createSelector(
    selectTag,
    tagState => tagState.typeTags 
);

const makeSelectDeleteSuccess = createSelector(
    selectTag,
    tagState => tagState.deleteSuccess || false
);

const makeSelectSuccess = createSelector(
    selectTag,
    tagState => tagState.success || false
);

const makeSelectLoading = createSelector(
    selectTag,
    tagState => tagState.loading || false
);

const makeSelectError = createSelector(
    selectTag,
    tagState => tagState.error || null
);

export {
    selectTag,
    makeSelectTags,
    makeSelectD<PERSON>teSuc<PERSON>,
    makeSelectTagList,
    makeSelectTypeTags,
    makeSelectLoading,
    makeSelectError,
    makeSelectSuccess,
};