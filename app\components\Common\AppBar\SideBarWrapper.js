import styled from 'styled-components';
import {Drawer} from '@mui/material';

const SideBarWrapper = styled(Drawer)`
  width: 360px;
  flex-shrink: 0;

  &.MuiDrawer-paper {
    width: 300px;
    height: 95vh;
    background-color: white;
    border-radius: 24px;
    color: black;
    top: 10px;
    left: 90px;
  }

    &.MuiListItem-root {
      padding: 8px 16px;
    }

    &.MuiModal-backdrop {
      background-color: #4F89D74C;
    }
  }
  .menuAvatar {
    width: 30px;
    height: 30px;
    background: #4F89D7;
    border: 3px solid #ffffff;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    font-size: 1rem;
    color: #ffffff;
  }

  .sideBarStyle {
    width: 360px;
    flex-shrink: 0;
    & .MuiDrawer-paper {
      width: 300px;
      height: 95vh;
      background-color: white;
      border-radius: 24px;
      color: black;
      top: 10px;
      left: 90px;
    }
    & .MuiListItem-root {
      padding: 8px 16px;
    }
    & .MuiModal-backdrop {
      background-color: #4F89D74C;
    }
  }

  .userStyle {
    font-size: 20px;
    font-weight: 600;
  }

  .menuBackButton {
    cursor: pointer;
    &:hover {
      & svg path {
        fill: #2E90FA;
      }
    }
  }

  .menuItem {
    color: #000000;
    background-color: transparent;
    border:  2px solid transparent;
    border-radius: 12px;
    & span {
      font-size: 14px;
      font-weight: 600;
    }
    &:hover {
      background-color: transparent;
      color: #3B8FEE;
      border: 2px solid #3B8FEE;
      border-radius: 12px;
      & svg path {
        fill: #2E90FA;
      }
    }

    &.active {
      color: #3B8FEE;
      border:  2px solid #3B8FEE;
      border-radius: 12px;
      & svg path {
        fill: #2E90FA;
      }
    }
  }

  .listStyle {
    background-color: transparent;
    color: black;
  }

  .preferenceStyle {
    font-size: 14px;
    color: #000000;
  }

  .subMenuItems {
    width: calc(100% - 10px);
    background-color: transparent;
    margin-left: 10px;
  }

  .logoutBtn {
    color: #FF0A00;
    background-color: #FCE7E7;
    border-radius: 12px;
    & span {
      font-size: 14px;
      font-weight: 600;
    }
    &:hover {
      background-color: #E56C5C;
      color: #FCE7E7;
      & svg path {
        fill: #ffffff;
      }
    }
  }
`;
export default SideBarWrapper;
