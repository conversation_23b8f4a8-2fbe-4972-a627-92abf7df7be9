import React from 'react';
import { Link, NavLink, useParams } from 'react-router-dom';

import styles from '../../../../Css/profileHeader.css';
import AccessControl from 'utils/AccessControl';



const HeaderTakenInCharge = props => {
  const params = useParams();

  return (
    <div className={styles.navBar}>
      <p>
        <NavLink
          exact
          to={`/takenInCharges/fiche/${params.id}/info`}
          activeClassName={styles.selected}
        >
          {' '}
          Détails{' '}
        </NavLink>
      </p>

      <AccessControl module="TAKEINCHARGE" functionality="UPDATE">
  <p>
    <NavLink
      exact
      to={`/takenInCharges/fiche/${params.id}/planification`}
      activeClassName={styles.selected}
    >
      {' '}
      Opérations{' '}
    </NavLink>
  </p>
</AccessControl>

      <p>
        <NavLink
          exact
          to={`/takenInCharges/fiche/${params.id}/notes`}
          activeClassName={styles.selected}
        >
          {' '}
          Notes{' '}
        </NavLink>
      </p>

      <p>
        <NavLink
          exact
          to={`/takenInCharges/fiche/${params.id}/documents`}
          activeClassName={styles.selected}
        >
          {' '}
          Documents{' '}
        </NavLink>
      </p>
      <p>
        <NavLink
          exact
          to={`/takenInCharges/fiche/${params.id}/action`}
          activeClassName={styles.selected}
        >
          Actions
        </NavLink>
      </p>
    </div>
  );
};

export default HeaderTakenInCharge;
