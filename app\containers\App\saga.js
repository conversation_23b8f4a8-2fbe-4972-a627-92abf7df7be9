// saga.js
import { call, put, takeLatest, select } from 'redux-saga/effects';
import request from 'utils/request'; // Assuming request utility is correctly configured
import {
  fetchConnectedUserSuccess,
  fetchConnectedUserFailure,
  CallDisconnectActionSuccess,
  CallDisconnectActionError,
  setImpersonatedUser,
  clearImpersonatedUser,
  startImpersonationSuccess,
  startImpersonationFailure,
  stopImpersonationSuccess,
  stopImpersonationFailure,
  fetchConnectedUserRequest,
} from './actions';
import {
  FETCH_CONNECTED_USER_REQUEST,
  CALL_DISCONNECT_ACTION,
  START_IMPERSONATION_REQUEST,
  STOP_IMPERSONATION_REQUEST,
} from './constants';

// Saga function to fetch connected user
function* fetchConnectedUserSaga(action) {
  try {
    // Normal flow - get the connected user
    // The impersonation token will be sent automatically in the request headers
    // by the request interceptor if it exists in localStorage
    const { data } = yield call(request.get, '/batch/ConnectedUser');
    yield put(fetchConnectedUserSuccess(data));

    // Check if we're in impersonation mode
    const impersonationToken = localStorage.getItem('impersonationToken');
    if (impersonationToken) {
      // If we have a token, we're in impersonation mode
      yield put(setImpersonatedUser(data));
    }
  } catch (error) {
    yield put(fetchConnectedUserFailure(error));
  }
}

function* callDisconnectSagaAction(action) {
  try {
    // If we're impersonating, just remove the token
    const impersonationToken = localStorage.getItem('impersonationToken');
    if (impersonationToken) {
      localStorage.removeItem('impersonationToken');
    }

    yield call(request.get, '/batch/lastSignIn');
    yield put(CallDisconnectActionSuccess(true));
  } catch (error) {
    yield put(CallDisconnectActionError(error));
  }
}

// Saga function to handle impersonation start
function* startImpersonationSaga(action) {
  try {
    // Get the current user before impersonation starts
    const currentState = yield select(state => state.app);
    const originalUser = currentState.connectedUser;

    // Store the original user in localStorage
    if (originalUser) {
      localStorage.setItem('originalUserFirstName', originalUser.firstName || '');
      localStorage.setItem('originalUserLastName', originalUser.lastName || '');
    }

    // Call the API to start impersonation
    const response = yield call(request.post, `/impersonation/impersonate/${action.payload}`);

    // Store the impersonation token in localStorage
    if (response && response.data && response.data.token) {
      localStorage.setItem('impersonationToken', response.data.token);
    }

    // Dispatch success action
    yield put(startImpersonationSuccess(response.data));

    // Set the impersonated user in the store
    if (response.data.user) {
      yield put(setImpersonatedUser(response.data.user));
    } else {
      // If no user data in response, fetch the connected user
      yield put(fetchConnectedUserRequest());
    }

    // Redirect to home page
    window.location.href = '/dashboard-general';
  } catch (error) {
    yield put(startImpersonationFailure(error.message || 'Échec de l\'impersonation. Veuillez réessayer.'));
  }
}

// Saga function to handle impersonation stop
function* stopImpersonationSaga() {
  try {
    // Remove the impersonation token and original user info from localStorage
    localStorage.removeItem('impersonationToken');
    localStorage.removeItem('originalUserFirstName');
    localStorage.removeItem('originalUserLastName');

    // Dispatch success action
    yield put(stopImpersonationSuccess());

    // Clear the impersonated user from the store
    yield put(clearImpersonatedUser());

    // Fetch the original connected user
    yield put(fetchConnectedUserRequest());

    // Redirect to home page
    window.location.href = '/dashboard-general';
  } catch (error) {
    yield put(stopImpersonationFailure(error.message || 'Échec de l\'arrêt de l\'impersonation.'));
  }
}

export default function* appSaga() {
  yield takeLatest(FETCH_CONNECTED_USER_REQUEST, fetchConnectedUserSaga);
  yield takeLatest(CALL_DISCONNECT_ACTION, callDisconnectSagaAction);
  yield takeLatest(START_IMPERSONATION_REQUEST, startImpersonationSaga);
  yield takeLatest(STOP_IMPERSONATION_REQUEST, stopImpersonationSaga);
}
