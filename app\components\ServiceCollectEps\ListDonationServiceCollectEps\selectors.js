import { createSelector } from "reselect";
import { initialState } from "./reducer"

// Select raw state
const selectServiceCollectEps = state => state.donationServiceCollectEps || initialState;

const makeSelectDonationServiceCollectEps = createSelector(
    selectServiceCollectEps,
    serviceCollectEps => serviceCollectEps.donationServiceCollectEpsList
);
 

const makeSelectLoading = createSelector(
    selectServiceCollectEps,
    serviceCollectEps => serviceCollectEps.loading
);

const makeSelectError = createSelector(
    selectServiceCollectEps,
    serviceCollectEps => serviceCollectEps.error
); 

export {
    selectServiceCollectEps,
    makeSelectDonationServiceCollectEps, 
    makeSelectLoading,
    makeSelectError
};

