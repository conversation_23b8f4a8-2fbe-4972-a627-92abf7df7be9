import React, { useEffect, useRef, useState } from 'react';
import { Link, useLocation, useHistory } from 'react-router-dom';
import PropTypes from 'prop-types';
import moment from 'moment';
import stylesList from 'Css/profileList.css';
import AccessControl from 'utils/AccessControl';
import profile from '../../../Css/personalInfo.css';
import btnStyles from '../../../Css/button.css';
import ReactToPrint from 'react-to-print';
import PrintableContent from '../PrintableContent/PrintableContent';
import Alert from 'react-bootstrap/Alert';
import {WHITE_UPLOAD_PICTURE} from "../../../containers/Common/RequiredElement/Icons";

const formatDate = date => moment(date).format('DD/MM/YYYY');

export default function PersonalInfo(props) {
  const donor = props.data;
  const location = useLocation();
  const history = useHistory();
  const [showSuccessMessage, setShowSuccessMessage] = useState(false);
  useEffect(() => {
    if (location.state === 'success') {
      setShowSuccessMessage(true);
    }
  }, [location.state]);

  useEffect(() => {
    if (showSuccessMessage) {
      const timer = setTimeout(() => {
        handleCloseSuccessMessage();
      }, 4000);
      return () => clearTimeout(timer);
    }
  }, [showSuccessMessage]);

  const handleCloseSuccessMessage = () => {
    setShowSuccessMessage(false);
    history.replace({ ...location, state: null });
  };

  let content = <p></p>;
  const emptyValue = <span> -</span>;

  const componentRef = useRef();

  if (props.data) {
    content = (
      <div className={profile.content}>
        {/* SECTION 1 */}
        <div className={profile.section1}>
          {/* TOP SECTION */}
          <div className={profile.top}>
            <div className={`${profile.label1} ${profile.data1}`}>
              <p>
                <span style={{ fontWeight: 'bold' }}>Nom : </span>
                <span style={{ fontWeight: 'normal' }}>
                  {donor.name ? donor.name : emptyValue}
                </span>
              </p>
              <p>
                <span style={{ fontWeight: 'bold' }}>Libellé : </span>
                <span style={{ fontWeight: 'normal' }}>
                  {donor.label ? donor.label : emptyValue}
                </span>
              </p>
              <p>
                <span style={{ fontWeight: 'bold' }}>Inscrit le : </span>
                <span style={{ fontWeight: 'normal' }}>
                  {donor.createdAt ? formatDate(donor.createdAt) : emptyValue}
                </span>
              </p>
              <p>
                <span style={{ fontWeight: 'bold' }}>Commentaire : </span>
                <span style={{ fontWeight: 'normal' }}>
                  {donor.description ? donor.description : emptyValue}
                </span>
              </p>

            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div>
      {showSuccessMessage && (
        <Alert
          className="alert-style"
          variant="success"
          onClose={handleCloseSuccessMessage}
          dismissible
        >
          <p>Donateur modifié avec succès</p>
        </Alert>
      )}
      <div className={stylesList.backgroudStyle}>
        <div className={profile.personalInfo}>
          <div className={profile.header}>
            <h4>Informations personnelles</h4>
            <div style={{ display: 'flex', alignItems: 'center' }}>
              <AccessControl module="DONOR" functionality="UPDATE">
                <div>
                  <div className="dropdown ">
                    <button
                      className={`dropdown-toggle ${btnStyles.addBtnProfile}`}
                      type="button"
                      id="dropdownMenuButton"
                      data-toggle="dropdown"
                      aria-haspopup="true"
                      aria-expanded="false"
                    >
                      Identifier le donateur
                    </button>
                    <div
                      className="dropdown-menu"
                      aria-labelledby="dropdownMenuButton"
                    >
                      <Link className="dropdown-item" 
                      to={{
                        pathname: `/donors/add/physique`,
                        state: { redirectTo: 'identification',anonyme:donor },
                      }}>
                        Donateur Physique
                      </Link>
                      <Link className="dropdown-item" to={{
                        pathname: `/donors/add/moral`,
                        state: { redirectTo: 'identification' ,anonyme:donor },
                      }}>
                        Donateur Moral
                      </Link>
                    </div>
                  </div>
                </div>
                <Link
                  to={{
                    pathname: `/donors/edit/anonyme/${donor.id}`,
                    state: { redirectTo: 'consultation' },
                  }}
                >
                  <button className="btn-style secondary">
                    <img
                      src={WHITE_UPLOAD_PICTURE}
                      width="16px"
                      height="16spx"
                  />
                    Modifier
                  </button>
                </Link>

              </AccessControl>
            </div>
          </div>
          {content}
        </div>
      </div>
    </div>
  );
}

PersonalInfo.propTypes = {
  data: PropTypes.object.isRequired,
};
