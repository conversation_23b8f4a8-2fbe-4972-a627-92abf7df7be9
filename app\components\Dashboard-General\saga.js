// Fetch General Dashboard
import { call, put, takeLatest } from 'redux-saga/effects';
import request from 'utils/request';
import { FETCH_GENERAL_DASHBOARD_REQUEST, FETCH_DONORS_DASHBOARD_REQUEST, FETCH_BENEFICIARIES_DASHBOARD_REQUEST, FETCH_DONATIONS_DASHBOARD_REQUEST, FETCH_KAFALAT_DASHBOARD_REQUEST, FETCH_AIDE_DASHBOARD_REQUEST, FETCH_EPS_DASHBOARD_REQUEST } from './constants';
import { fetchGeneralDashboardSuccess, fetchGeneralDashboardFailure,fetchDonorsDashboardSuccess, fetchDonorsDashboardFailure, fetchBeneficiariesDashboardSuccess, fetchBeneficiariesDashboardFailure, fetchDonationsDashboardSuccess, fetchDonationsDashboardFailure, fetchKafalatDashboardSuccess, fetchKafalatDashboardFailure, fetchAideDashboardSuccess, fetchAideDashboardFailure, fetchEPSDashboardSuccess, fetchEPSDashboardFailure } from './actions';

function* fetchGeneralDashboardSaga() {
  try {
    const { data } = yield call(request.get, '/dashboard/general-statistics');
    console.log('dashboard data from saga : ',data);
    yield put(fetchGeneralDashboardSuccess(data));
  } catch (error) {
    console.log('error from saga : ',error);
    yield put(fetchGeneralDashboardFailure(error));
  }
}

function* fetchDonorsDashboardSaga() {
  try {
    const { data } = yield call(request.get, '/dashboard/donor-statistics');
    yield put(fetchDonorsDashboardSuccess(data));
  } catch (error) {
    yield put(fetchDonorsDashboardFailure(error));
  }
}

function* fetchDonationsDashboardSaga() {
  try {
    const { data } = yield call(request.get, '/dashboard/donation-stats');
    yield put(fetchDonationsDashboardSuccess(data));
  } catch (error) {
    yield put(fetchDonationsDashboardFailure(error));
  }
}
function* fetchBeneficiariesDashboardSaga() {
  try {
    const { data } = yield call(request.get, '/dashboard/beneficiary-statistics');
    console.log('beneficiaries dashboard data from saga : ',data);
    yield put(fetchBeneficiariesDashboardSuccess(data));
  } catch (error) {
    yield put(fetchBeneficiariesDashboardFailure(error));
  }
}

function* fetchKafalatDashboardSaga() {
  try {
    const { data } = yield call(request.get, '/dashboard/kafalat-stats');
    yield put(fetchKafalatDashboardSuccess(data));
  } catch (error) {
    yield put(fetchKafalatDashboardFailure(error));
  }
}

function* fetchAideDashboardSaga() {
  try {
    const { data } = yield call(request.get, '/dashboard/aide-complementaire-statistics');
    yield put(fetchAideDashboardSuccess(data));
  } catch (error) {
    yield put(fetchAideDashboardFailure(error));
  }
}

function* fetchEPSDashboardSaga() {
  try {
    const { data } = yield call(request.get, '/dashboard/eps-stats');
    yield put(fetchEPSDashboardSuccess(data));
  } catch (error) {
    yield put(fetchEPSDashboardFailure(error));
  }
}



export default function* generalDashboardSaga() {
  yield takeLatest(FETCH_GENERAL_DASHBOARD_REQUEST, fetchGeneralDashboardSaga);
  yield takeLatest(FETCH_DONORS_DASHBOARD_REQUEST, fetchDonorsDashboardSaga);
  yield takeLatest(FETCH_BENEFICIARIES_DASHBOARD_REQUEST, fetchBeneficiariesDashboardSaga);
  yield takeLatest(FETCH_DONATIONS_DASHBOARD_REQUEST, fetchDonationsDashboardSaga);
  yield takeLatest(FETCH_KAFALAT_DASHBOARD_REQUEST, fetchKafalatDashboardSaga);
  yield takeLatest(FETCH_AIDE_DASHBOARD_REQUEST, fetchAideDashboardSaga);
  yield takeLatest(FETCH_EPS_DASHBOARD_REQUEST, fetchEPSDashboardSaga);
}