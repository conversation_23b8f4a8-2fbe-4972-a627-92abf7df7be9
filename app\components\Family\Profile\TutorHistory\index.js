import React, { useEffect, useState } from 'react';
import moment from 'moment';
import DataTable from 'components/Common/DataTable';
import CustomPagination from 'components/Common/CustomPagination';
import { useDispatch, useSelector } from 'react-redux';
import { useParams } from 'react-router-dom';
import { useInjectReducer, useInjectSaga } from 'redux-injectors';
import { createStructuredSelector } from 'reselect';
import { loadTutorHistory } from 'containers/Family/FamilyProfile/actions'; // Assurez-vous que ce chemin est correct
import {
  makeSelectError,
  makeSelectTutorHistory,
} from 'containers/Family/FamilyProfile/selectors'; // Assurez-vous que ce chemin est correct
import reducer from 'containers/Family/FamilyProfile/reducer'; // Assurez-vous que ce chemin est correct
import { Alert } from 'react-bootstrap';
import 'bootstrap/dist/css/bootstrap.min.css';
import saga from 'containers/Family/FamilyProfile/saga'; // Assurez-vous que ce chemin est correct
import stylesList from 'Css/profileList.css';
import list from 'Css/profileList.css';

const key = 'familyProfile'; // Changez le nom de la clé ici

const formatDate = date => moment(date).format('DD/MM/YYYY');

const omdbSelector = createStructuredSelector({
  tutorHistory: makeSelectTutorHistory,
  error: makeSelectError,
});

export default function TutorHistory(props) {
  const data = props.data;
  const dispatch = useDispatch();
  useInjectReducer({ key, reducer });
  useInjectSaga({ key, saga });
  const { id } = useParams();
  const [currentPage, setCurrentPage] = useState(1);
  const pageSize = 5;

  const { tutorHistory, error } = useSelector(omdbSelector);

  useEffect(() => {
    if (id) {
      dispatch(loadTutorHistory(id)); // Utilisez l'action appropriée
    }
  }, [dispatch, id]);

  const listHistory = tutorHistory
    ? [...tutorHistory]
        .sort((a, b) => new Date(b.dateDebut) - new Date(a.dateDebut))
        .slice((currentPage - 1) * pageSize, currentPage * pageSize)
        .map(item => ({
          id: item.id,
          date: item.dateDebut ? formatDate(item.dateDebut) : '', // Use dateDebut
          end_date: item.dateFin ? formatDate(item.dateFin) : '', // Use dateFin
          familyMemberName: item.familyMemberName ? item.familyMemberName : '', // Use familyMemberName
          familyMemberRelationship: item.familyMemberRelationship ? item.familyMemberRelationship : '', // Use familyMemberRelationship
        }))
    : [];
  const columns = [
    {
      field: 'familyMemberName',
      headerName: 'Nom du Membre de la Famille',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
    {
      field: 'familyMemberRelationship',
      headerName: 'Relation',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
    {
      field: 'date',
      headerName: 'Date de Début',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
    {
      field: 'end_date',
      headerName: 'Date de Fin',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
  ];

  return (
    <div className={`pb-5 pt-4 ${stylesList.backgroudStyle}`}>
      {error && <Alert className="alert-style" variant="danger">Une erreur est survenue</Alert>}
      <div className={list.backgroundStyle}>
        <div className={list.global}>
          <div className={list.header}>
            <h4>Historique des anciens tuteurs</h4>
          </div>
          <DataTable
            rows={listHistory}
            columns={columns}
            fileName={`Historique des tuteurs ${new Date().toLocaleString()}`}
          />
          {tutorHistory && (
            <div className="justify-content-center my-4">
              <CustomPagination
                totalElements={tutorHistory.length}
                totalCount={Math.ceil(tutorHistory.length / pageSize)}
                pageSize={pageSize}
                currentPage={currentPage}
                onPageChange={page => setCurrentPage(page)}
              />
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
