import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useInjectReducer, useInjectSaga } from 'redux-injectors';
import { createStructuredSelector } from 'reselect';
import { Route, Switch, useHistory, useParams } from 'react-router-dom';
import { Modal } from 'react-bootstrap';
import { CircularProgress } from '@material-ui/core';
import uploadFileIcone from 'images/icons/upload.svg';
import styles from '../../../Css/profileList.css';
import {
  makeSelectAideComplementaire,
  makeSelectBeneficiariesForAideComplementaire,
  makeSelectBudgetLinesForAideComplementaire,
  makeSelectCloseAideComplementaireError,
  makeSelectCloseAideComplementaireSuccess,
  makeSelectDonations,
  makeSelectDonorsForAideComplementaire,
  makeSelectExecuteAideComplementaireError,
  makeSelectExecuteAideComplementaireSuccess,
  makeSelectGroupeForAideComplementaire,
  makeSelectLastActionMeta,
  makeSelectLoading,
  makeSelectProcessBeneficiariesForAideComplementaire,
  makeSelectProcessDonorsForAideComplementaire,
  makeSelectRemoveBeneficiaryFromAideComplementaireError,
  makeSelectRemoveBeneficiaryFromAideComplementaireSuccess,
  makeSelectRemoveDonorFromAideComplementaireError,
  makeSelectRemoveDonorFromAideComplementaireSuccess,
  makeSelectReserveAllBudgetLinesError,
  makeSelectReserveAllBudgetLinesSuccess,
  makeSelectSuccess,
  makeSelectUnExecuteAideComplementaireError,
  makeSelectUnExecuteAideComplementaireSuccess,
  makeSelectUpdateMontantForBeneficiarySuccess,
  makeSelectUpdateMontantForGroupeError,
  makeSelectUpdateMontantForGroupeSuccess,
  makeSelectUpdateMontantReservedBudgetLineSuccess,
  makeSelectUpdateStatutReservationBudgetLineSuccess,
  makeSelectUpdateStatutValidationForBeneficiarySuccess,
} from './selectors';
import detailAideComplementaireSaga from './saga';
import aideComplementaireReducer from './reducer';
import {
  closeAideComplementaireRequest,
  executeAideComplementaireRequest,
  unexecuteAideComplementaireRequest,
  fetchBudgetLinesRequest,
  loadAideComplementaire,
  loadBeneficiariesForAideComplementaire,
} from './actions';
import SideBar from '../../../components/AideComplementaire/FicheAideComplementaire/SideBar';
import HeaderAideComplementaire from '../../../components/AideComplementaire/Layout/HeaderAideComplementaire';
import GeneralInfo from '../../../components/AideComplementaire/FicheAideComplementaire/GeneralInfo';
import Beneficiaires from '../../../components/AideComplementaire/FicheAideComplementaire/Beneficiaires';
import BudgetLines from '../../../components/AideComplementaire/FicheAideComplementaire/BudgetLines';
import reducer from '../../Common/SubComponents/DocumentForm/reducer';
import { deleteDocumentSaga } from '../../Common/SubComponents/DocumentForm/saga';

const target = 'campagne';
const key = 'aideComplementaireFiche';
const key2 = 'document';

const omdbSelector = createStructuredSelector({
  donation: makeSelectDonations,
  loading: makeSelectLoading,
  success: makeSelectSuccess,
  aideComplementaire: makeSelectAideComplementaire,
  beneficiariesForAideComplementaire: makeSelectBeneficiariesForAideComplementaire,
  proceesBeneficiariesSuccess: makeSelectProcessBeneficiariesForAideComplementaire,
  donorsForAideComplementaire: makeSelectDonorsForAideComplementaire,
  proceesDonorsSuccess: makeSelectProcessDonorsForAideComplementaire,
  removeBeneficiarySuccess: makeSelectRemoveBeneficiaryFromAideComplementaireSuccess,
  removeBeneficiaryError: makeSelectRemoveBeneficiaryFromAideComplementaireError,
  updateStatutValidationBeneficiarySuccess: makeSelectUpdateStatutValidationForBeneficiarySuccess,
  updateMontantBeneficiarySuccess: makeSelectUpdateMontantForBeneficiarySuccess,
  removeDonorSuccess: makeSelectRemoveDonorFromAideComplementaireSuccess,
  removeDonorError: makeSelectRemoveDonorFromAideComplementaireError,
  lastActionMeta: makeSelectLastActionMeta,
  budgetLines: makeSelectBudgetLinesForAideComplementaire,
  updateStatutReservationBudgetLineSuccess: makeSelectUpdateStatutReservationBudgetLineSuccess,
  updateMontantReservedBudgetLineSuccess: makeSelectUpdateMontantReservedBudgetLineSuccess,
  executeAideComplementaireSuccess: makeSelectExecuteAideComplementaireSuccess,
  executeAideComplementaireError: makeSelectExecuteAideComplementaireError,
  unexecuteAideComplementaireSuccess:makeSelectUnExecuteAideComplementaireSuccess,
  groupe: makeSelectGroupeForAideComplementaire,
  updateMontantGroupSuccess: makeSelectUpdateMontantForGroupeSuccess,
  updateMontantGroupError: makeSelectUpdateMontantForGroupeError,
  reserveAllBudgetLineSuccess: makeSelectReserveAllBudgetLinesSuccess,
  reserveAllBudgetLineError: makeSelectReserveAllBudgetLinesError,
  closeAideComplementaireSuccess: makeSelectCloseAideComplementaireSuccess,
  closeAideComplementaireError: makeSelectCloseAideComplementaireError,
});

export default function FicheAideComplementaire() {
  const params = useParams();

  useInjectReducer({ key, reducer: aideComplementaireReducer });
  useInjectSaga({ key, saga: detailAideComplementaireSaga });

  useInjectReducer({ key: key2, reducer });
  useInjectSaga({ key: key2, saga: deleteDocumentSaga });

  const {
    donation,
    loading,
    success,
    aideComplementaire,
    beneficiariesForAideComplementaire,
    proceesBeneficiariesSuccess,
    proceesDonorsSuccess,
    donorsForAideComplementaire,
    budgetLines,
    removeBeneficiarySuccess,
    removeBeneficiaryError,
    updateStatutValidationBeneficiarySuccess,
    updateMontantBeneficiarySuccess,
    removeDonorSuccess,
    removeDonorError,
    lastActionMeta,
    updateStatutReservationBudgetLineSuccess,
    updateMontantReservedBudgetLineSuccess,
    executeAideComplementaireSuccess,
    unexecuteAideComplementaireSuccess,
    executeAideComplementaireError,
    groupe,
    updateMontantGroupSuccess,
    updateMontantGroupError,
    reserveAllBudgetLineSuccess,
    reserveAllBudgetLineError,
    closeAideComplementaireSuccess,
    closeAideComplementaireError,
  } = useSelector(omdbSelector);

  const dispatch = useDispatch();
  const history = useHistory();
  const [showExecuteModal, setShowExecuteModal] = useState(false);
  const [showUnExecuteModal, setShowUnExecuteModal] = useState(false);
  const [showClotureModal, setShowClotureModal] = useState(false);
  const [commentaire, setCommentaire] = useState('');
  const [file, setFile] = useState(null);

  const [errorMessage, setErrorMessage] = useState('');

  const montantDesFrais =
    (aideComplementaire.montantCollecter * aideComplementaire.costs) / 100;
  const montantCollecterAvecFrais =
    aideComplementaire.montantCollecter - montantDesFrais;
  const montantRestantInclusFrais =
    montantCollecterAvecFrais - aideComplementaire.montantTotalAffecter;
  const totalBeneficiaries =
    (aideComplementaire.beneficiaryAideComplemenatireDTOList &&
      aideComplementaire.beneficiaryAideComplemenatireDTOList.length) ||
    0;

  const totalDonors =
    (aideComplementaire.donorAideComplemenatireDTOList &&
      aideComplementaire.donorAideComplemenatireDTOList.length) ||
    0;

  useEffect(() => {
    dispatch(loadAideComplementaire(params.id));
  }, []);

  useEffect(() => {
    if (success) {
      let aideComplementaireId = aideComplementaire.id;
      let serviceId = aideComplementaire.serviceId; 
      dispatch(loadBeneficiariesForAideComplementaire(aideComplementaireId));
      // dispatch(loadDonorsForAideComplementaire(aideComplementaireId));
      dispatch(fetchBudgetLinesRequest(serviceId, aideComplementaireId));
    }
  }, [success]);

  useEffect(() => {
    if (proceesBeneficiariesSuccess) {
      let aideComplementaireId = aideComplementaire.id; 
      dispatch(loadAideComplementaire(aideComplementaireId));
      history.push({
        pathname: `/aide-complementaire/fiche/${aideComplementaireId}/beneficiaries`,
        state: 'proceesBeneficiariesSuccess',
      });
    }
  }, [proceesBeneficiariesSuccess]);

  useEffect(() => {
    if (executeAideComplementaireSuccess) {
      let aideComplementaireId = aideComplementaire.id; 
      dispatch(loadAideComplementaire(aideComplementaireId));
      history.push({
        pathname: `/aide-complementaire/fiche/${aideComplementaireId}/info`,
        state: 'executeAideComplementaireSuccess',
      });
    }
  }, [executeAideComplementaireSuccess]);


  useEffect(() => {
    if (unexecuteAideComplementaireSuccess && aideComplementaire.id) {
      let aideComplementaireId = aideComplementaire.id;
     
      dispatch(loadAideComplementaire(aideComplementaireId));
      history.push({
        pathname: `/aide-complementaire/fiche/${aideComplementaireId}/info`,
        state: 'unexecuteAideComplementaireSuccess',
      });
    }
  }, [unexecuteAideComplementaireSuccess]);

  useEffect(() => {
    if (closeAideComplementaireSuccess) {
      let aideComplementaireId = aideComplementaire.id;
     
      dispatch(loadAideComplementaire(aideComplementaireId));
      history.push({
        pathname: `/aide-complementaire/fiche/${aideComplementaireId}/info`,
        state: 'closeAideComplementaireSuccess',
      });
    }
  }, [closeAideComplementaireSuccess]);

  useEffect(() => {
    if (proceesDonorsSuccess) {
      let aideComplementaireId = aideComplementaire.id;
     
      dispatch(loadAideComplementaire(aideComplementaireId));
      history.push({
        pathname: `/aide-complementaire/fiche/${aideComplementaireId}/donateurs`,
        state: 'proceesDonorsSuccess',
      });
    }
  }, [proceesDonorsSuccess]);

  useEffect(() => {
    if (removeBeneficiarySuccess) {
      const aideComplementaireId = aideComplementaire.id;
      let redirectPath = `/aide-complementaire/fiche/${aideComplementaireId}/donateurs`; // Par défaut

      if (lastActionMeta.source === 'BeneficiaryWithoutDonorTable') {
        redirectPath = `/aide-complementaire/fiche/${aideComplementaireId}/beneficiaries`;
      }
     
      dispatch(loadAideComplementaire(aideComplementaireId));
      history.push({
        pathname: redirectPath,
        state: 'removeBeneficiarySuccess',
      });
    }
  }, [removeBeneficiarySuccess]);

  useEffect(() => {
    if (removeDonorSuccess) {
      let aideComplementaireId = aideComplementaire.id;
     
      dispatch(loadAideComplementaire(aideComplementaireId));
      history.push({
        pathname: `/aide-complementaire/fiche/${aideComplementaireId}/donateurs`,
        state: 'removeDonorSuccess',
      });
    }
  }, [removeDonorSuccess]);

  useEffect(() => {
    if (updateStatutReservationBudgetLineSuccess) {
      let aideComplementaireId = aideComplementaire.id;
     
      dispatch(loadAideComplementaire(aideComplementaireId));
      history.push({
        pathname: `/aide-complementaire/fiche/${aideComplementaireId}/collect`,
        state: 'updateStatutReservationBudgetLineSuccess',
      });
    }
  }, [updateStatutReservationBudgetLineSuccess]);

  useEffect(() => {
    if (updateMontantReservedBudgetLineSuccess) {
      const aideComplementaireId = aideComplementaire.id;
      dispatch(loadAideComplementaire(aideComplementaireId));
      history.push({
        pathname: `/aide-complementaire/fiche/${aideComplementaireId}/collect`,
        state: 'updateMontantReservedBudgetLineSuccess',
      });
    }
  }, [updateMontantReservedBudgetLineSuccess]);

  useEffect(() => {
    if (updateMontantGroupSuccess) {
      const aideComplementaireId = aideComplementaire.id;
      dispatch(loadAideComplementaire(aideComplementaireId));
      history.push({
        pathname: `/aide-complementaire/fiche/${aideComplementaireId}/beneficiaries`,
        state: 'updateMontantGroupSuccess',
      });
    }
  }, [updateMontantGroupSuccess]);

  useEffect(() => {
    if (reserveAllBudgetLineSuccess) {
      const aideComplementaireId = aideComplementaire.id;
      dispatch(loadAideComplementaire(aideComplementaireId));
      history.push({
        pathname: `/aide-complementaire/fiche/${aideComplementaireId}/collect`,
        state: 'reserveAllBudgetLineSuccess',
      });
    }
  }, [reserveAllBudgetLineSuccess]);

  useEffect(() => {
    if (updateStatutValidationBeneficiarySuccess) {
      const aideComplementaireId = aideComplementaire.id;

      let redirectPath = `/aide-complementaire/fiche/${aideComplementaireId}/donateurs`; // Par défaut

      if (lastActionMeta.source === 'BeneficiaryWithoutDonorTable') {
        redirectPath = `/aide-complementaire/fiche/${aideComplementaireId}/beneficiaries`;
      }
     
      dispatch(loadAideComplementaire(aideComplementaireId));
      history.push({
        pathname: redirectPath,
        state: 'updateStatutValidationBeneficiarySuccess',
      });
    }
  }, [updateStatutValidationBeneficiarySuccess]);

  useEffect(() => {
    if (updateMontantBeneficiarySuccess) {
      const aideComplementaireId = aideComplementaire.id;
      let redirectPath = `/aide-complementaire/fiche/${aideComplementaireId}/donateurs`; // Par défaut

      if (lastActionMeta.source === 'BeneficiaryWithoutDonorTable') {
        redirectPath = `/aide-complementaire/fiche/${aideComplementaireId}/beneficiaries`;
      }
     
      dispatch(loadAideComplementaire(aideComplementaireId));
      history.push({
        pathname: redirectPath,
        state: 'updateMontantBeneficiarySuccess',
      });
    }
  }, [updateMontantBeneficiarySuccess]);

  const handleExecuteConfirm = () => {
    setErrorMessage('');

    if (aideComplementaire.montantTotalAffecter > montantCollecterAvecFrais) {
      setErrorMessage(
        'Le montant à exécuter ne peut pas être supérieur au montant disponible.',
      );
      return;
    }

    if (aideComplementaire.montantTotalAffecter === 0) {
      setErrorMessage('Le montant à exécuter ne peut pas être nul.');
      return;
    }

    if (montantCollecterAvecFrais === 0) {
      setErrorMessage('Le montant disponible ne peut pas être nul.');
      return;
    }

    if (montantRestantInclusFrais !== 0) {
      setErrorMessage('Il vous reste un montant réservé non affecté.');
      return;
    }

    dispatch(
      executeAideComplementaireRequest(
        aideComplementaire.id,
        aideComplementaire.montantTotalAffecter,
      ),
    );
    setShowExecuteModal(false);
  };

  const handleUnExecuteConfirm = () => {
    setErrorMessage('');



    dispatch(
      unexecuteAideComplementaireRequest(
        aideComplementaire.id,
      ),
    );

    setShowUnExecuteModal(false);
  };

  const handleClotureSubmit = () => {
    console.log('Début de handleClotureSubmit');

    const payload = {
      documentDTO: {
        label: null,
        documentDate: new Date().toISOString(),
        expiryDate: null,
        comment: commentaire,
        file,
        fileUrl: file.name,
        name: "Aucun fichier n'est sélectionné",
        file64: 'file64',
      },
      entityId: aideComplementaire.id,
      entityType: target,
      normalDocument: true,
    };

    console.log({ payload });

    try {
      dispatch(closeAideComplementaireRequest(payload, target));
      console.log('Action dispatchée avec succès');
      setShowClotureModal(false);
      setFile(null);
      setCommentaire('');
    } catch (error) {
      console.error('Erreur lors du dispatch de addDocument :', error);
    }
  };

  console.log({ file });

  return (
    <div>
      <Modal show={loading} centered contentClassName="bg-transparent border-0">
        <div className="d-flex justify-content-center align-items-center">
          <CircularProgress style={{ width: '100px', height: '100px' }} />
        </div>
      </Modal>
      <div className="row">
        <div className="col-3 pr-0 pl-2">
          <SideBar data={aideComplementaire} />
          <div className="d-flex flex-column align-items-stretch w-100">
            <button
              onClick={() => {
                history.push('/aide-complementaire');
              }}
              className="btn-style secondary mb-2 shadow-sm"
            >
              Quitter
            </button>
            {aideComplementaire &&
              aideComplementaire.statut !== 'executer' &&
              aideComplementaire.statut !== 'cloturer' && (
                <button
                  className="btn-style primary mt-2"
                  onClick={() => setShowExecuteModal(true)}
                >
                  Exécuter
                </button>
              )}
            {aideComplementaire && aideComplementaire.statut === 'executer' && (
              <button
                className="btn-style primary mt-2"
                onClick={() => setShowUnExecuteModal(true)}
              >
                Annuler Exécution
              </button>
            )}
            {aideComplementaire && aideComplementaire.statut === 'executer' && (
              <button
                className="btn-style btn-warning mt-2"
                onClick={() => setShowClotureModal(true)}
              >
                Clôturer
              </button>
            )}
          </div>
        </div>
        
        <div className="col-9 pr-2">
          <HeaderAideComplementaire data={aideComplementaire} />
          <div className={styles.backgroudStyle}>
            <Switch>
              <Route exact path="/aide-complementaire/fiche/:id/info">
                <GeneralInfo data={aideComplementaire} />{' '}
              </Route>
              {/* <Route exact path="/aide-complementaire/fiche/:id/donateurs">
                <Donateurs
                  data={aideComplementaire}
                  donorsForAideComplementaire={
                    donorsForAideComplementaire || []
                  }
                />{' '}
              </Route>
              */}
              <Route exact path="/aide-complementaire/fiche/:id/beneficiaries">
                <Beneficiaires
                  data={aideComplementaire}
                  beneficiariesForAideComplementaire={
                    beneficiariesForAideComplementaire || []
                  }
                />{' '}
              </Route>

              <Route exact path="/aide-complementaire/fiche/:id/collect">
                <BudgetLines
                  data={aideComplementaire}
                  budgetLines={budgetLines || []}
                />{' '}
              </Route>

              {/* <Route
                exact
                path="/aide-complementaire/fiche/:id/beneficiariesWithDonors"
              >
                <BeneficiariesWithDonorsList
                  data={aideComplementaire}
                  beneficiariesForAideComplementaire={
                    beneficiariesForAideComplementaire || []
                  }
                />{' '}
              </Route>
              */}

              {/* <Route exact path="/aide-complementaire/fiche/:id/documents">
                {' '}
                <Documents data={donation} />{' '}
              </Route>
              <Route exact path="/aide-complementaire/fiche/:id/notes">
                {' '}
                <Notes data={donation} />{' '}
              </Route>
              <Route exact path="/aide-complementaire/fiche/:id/action">
                {' '}
                <ListActions data={donation} />{' '}
              </Route>
              */}
            </Switch>
          </div>
        </div>
      </div>

      {/* Execution Modal*/}
      <Modal
        show={showExecuteModal}
        centered
        onHide={() => {
          setErrorMessage('');
          setShowExecuteModal(false);
        }}
      >
        <Modal.Header closeButton>
          <Modal.Title>
            <i className="fas fa-tasks"></i> Exécution des détails
          </Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <div className="modal-content-details">
            <div className="info-group mb-4">
              <i className="fas fa-users me-2"></i>{' '}
              <span className="info-text">
                Nombre total de bénéficiaires validés:{' '}
              </span>
              <strong>{aideComplementaire.nbrBeneficiariesValidated}</strong>
            </div>
            <div className="info-group mb-4">
              <i className="fas fa-hand-holding-heart me-2"></i>{' '}
              <span className="info-text">
                Nombre total de donateurs participant:{' '}
              </span>
              <strong>{totalDonors}</strong>
            </div>
            <hr />
            <div className="card bg-light p-4 mb-4">
              <div className="info-group mb-3">
                <i className="fas fa-wallet me-2"></i>{' '}
                <span className="info-text">Montant souhaité: </span>
                <strong>{aideComplementaire.montantPrevu} Dh</strong>
              </div>
              <div className="info-group mb-3">
                <i className="fas fa-coins me-2"></i>{' '}
                <span className="info-text">
                  Montant disponible (incl. frais):{' '}
                </span>
                <strong>{montantCollecterAvecFrais} Dh</strong>
              </div>
              <div className="info-group mb-3">
                <i className="fas fa-check-circle me-2"></i>{' '}
                <span className="info-text">Montant à exécuter: </span>
                <strong>{aideComplementaire.montantTotalAffecter} Dh</strong>
              </div>
              <div className="info-group mb-3">
                <i className="fas fa-feather-alt me-2"></i>{' '}
                <span className="info-text">
                  Montant des frais de gestion:{' '}
                </span>
                <strong>{montantDesFrais} Dh</strong>
              </div>
              <div className="info-group">
                <i className="fas fa-exclamation-circle me-2"></i>{' '}
                <span className="info-text">
                  Montant restant non affecté:{' '}
                </span>
                <strong>{montantRestantInclusFrais} Dh</strong>
              </div>
            </div>
            {errorMessage && (
              <div className="alert alert-danger">{errorMessage}</div>
            )}
          </div>
        </Modal.Body>
        <Modal.Footer>
          <button
            className="btn-style secondary"
            onClick={() => {
              setErrorMessage('');
              setShowExecuteModal(false);
            }}
          >
            Annuler
          </button>
          <button
            className="btn-style success"
            onClick={handleExecuteConfirm}
          >
            Confirmer l'exécution
          </button>
        </Modal.Footer>
      </Modal>

      <Modal
        show={showUnExecuteModal}
        centered
        onHide={() => {
          setErrorMessage('');
          setShowUnExecuteModal(false);
        }}
      >
        <Modal.Header closeButton>
          <Modal.Title>
            Annulation d'Exécution 
          </Modal.Title>
        </Modal.Header>
        <Modal.Body>
          Vous voulez vraiment annuler l'exécution ?
        </Modal.Body>
        <Modal.Footer>
          <button
            className="btn btn-secondary"
            onClick={() => {
              setErrorMessage('');
              setShowUnExecuteModal(false);
            }}
          >
            Annuler
          </button>
          <button className="btn btn-success" onClick={handleUnExecuteConfirm}>
            Confirmer l'Annulation
          </button>
        </Modal.Footer>
      </Modal>

      {/* Modal pour Clôturer */}
      {showClotureModal && (
        <Modal
          show={showClotureModal}
          onHide={() => {
            setShowClotureModal(false);
            setCommentaire('');
            setFile(null);
            setErrorMessage('');
          }}
          centered
        >
          <Modal.Header closeButton>
            <Modal.Title>Clôturer l'aide complémentaire</Modal.Title>
          </Modal.Header>
          <Modal.Body>
            <form>
              {/* Champ pour document */}
              <div className="form-group">
                <label htmlFor="document">
                  Document (Maximum 10 Mo){' '}
                  <span className="text-danger">*</span>
                </label>
                <div className="d-flex align-items-center">
                  <label
                    htmlFor="documentUpload"
                    className="btn-style outlined btn-sm d-flex align-items-center"
                    style={{ cursor: 'pointer' }}
                  >
                    <img
                      src={uploadFileIcone}
                      alt="Upload"
                      width="20px"
                      height="20px"
                      className="mr-2"
                    />
                    Choisir un fichier
                  </label>
                  <input
                    type="file"
                    id="documentUpload"
                    hidden
                    accept=".pdf,.doc,.docx,.png,.jpg,.jpeg"
                    onChange={event => {
                      const selectedFile = event.target.files[0];
                      if (selectedFile) {
                        if (selectedFile.size > 10 * 1024 * 1024) {
                          setErrorMessage(
                            'Le fichier dépasse la taille maximale de 10 MB.',
                          );
                          setFile(null);
                        } else {
                          setErrorMessage('');
                          setFile(selectedFile);
                        }
                      }
                    }}
                  />
                  {file ? (
                    <span className="ml-3 text-success">{file.name}</span>
                  ) : (
                    <span className="ml-3 text-muted">
                      Aucun fichier sélectionné
                    </span>
                  )}
                </div>
                {errorMessage && (
                  <div className="text-danger mt-2">{errorMessage}</div>
                )}
              </div>

              {/* Champ commentaire */}
              <div className="form-group mt-3">
                <label htmlFor="commentaire">Dé</label>
                <textarea
                  id="commentaire"
                  className="form-control"
                  rows="3"
                  placeholder="Ajouter un commentaire (optionnel)"
                  value={commentaire}
                  onChange={e => setCommentaire(e.target.value)}
                ></textarea>
              </div>
            </form>
          </Modal.Body>
          <Modal.Footer>
            <button
              className="btn-style secondary"
              onClick={() => {
                setShowClotureModal(false);
                setCommentaire('');
                setFile(null);
                setErrorMessage('');
              }}
            >
              Annuler
            </button>
            <button
              className="btn-style success"
              onClick={() => {
                if (!file) {
                  setErrorMessage(
                    'Le document est obligatoire pour clôturer.',
                  );
                } else {
                  setErrorMessage('');
                  handleClotureSubmit();
                }
              }}
              // disabled={!file}
            >
              Clôturer
            </button>
          </Modal.Footer>
        </Modal>
      )}
    </div>
  );
}
