import React, { useEffect, useState } from 'react';
import { Container, Typography, Paper, Box, Grid, Pagination, CircularProgress } from '@mui/material';
import HomePage from '../../containers/HomePage';
import { StatCard } from '../charts/StatCard';    
import { useHistory } from 'react-router-dom';
import AccessAlarmsIcon from '@mui/icons-material/AccessAlarms';
import { useTheme } from '@mui/material/styles';
import PersonIcon from '@mui/icons-material/Person';
import MoodIcon from '@mui/icons-material/Mood';
import PaidIcon from '@mui/icons-material/Paid';
import FavoriteIcon from '@mui/icons-material/Favorite';
import TocIcon from '@mui/icons-material/Toc';
import { BudgetTable } from 'components/charts/BudgetTable';
import CustomPagination from 'components/Common/CustomPagination';
import { createStructuredSelector } from 'reselect';
import { fetchGeneralDashboard } from './actions';
import {VIEW_ICON} from '../Common/ListIcons/ListIcons';
import { makeSelectGeneralDashboard, makeSelectGeneralDashboardLoading, makeSelectGeneralDashboardError, makeSelectGeneralDashboardSuccess } from './selector';
import { makeSelectConnectedUser } from 'containers/App/selectors';
import { useDispatch, useSelector } from 'react-redux';
import { useInjectReducer, useInjectSaga } from 'redux-injectors';
//HomePage Imports
import homePageSaga from '../../containers/HomePage/saga';
import HomePageReducer from '../../containers/HomePage/reducer';
import {
  makeSelectActions,
} from '../../containers/HomePage/selectors';

import { getListActions } from '../../containers/HomePage/actions';

//GeneralDashboard Imports
import generalDashboardReducer from './reducer';
import generalDashboardSaga from './saga';
const stateSelector = createStructuredSelector({
  data: makeSelectGeneralDashboard,
  loading: makeSelectGeneralDashboardLoading,
  error: makeSelectGeneralDashboardError,
  success: makeSelectGeneralDashboardSuccess,
});

const stateUserSelector = createStructuredSelector({
  currentUser: makeSelectConnectedUser,
});


const homePageSelector = createStructuredSelector({
  actions: makeSelectActions,
});

export default function DashboardGeneral() {
  useInjectReducer({ key: 'dashboardAll', reducer: generalDashboardReducer });
  useInjectSaga({ key: 'dashboardAll', saga: generalDashboardSaga });
  useInjectReducer({ key: 'homePageContainer', reducer: HomePageReducer });
  useInjectSaga({ key: 'homePageContainer', saga: homePageSaga });

  const { currentUser } = useSelector(stateUserSelector);
  const [formattedString, setFormattedString] = useState('');
  const dispatch = useDispatch();
  const { data, loading, error, success } = useSelector(stateSelector);
  const { actions } = useSelector(homePageSelector);
  const [tableData, setTableData] = useState([]);
  const [activePage, setActivePage] = useState(0);
  const [filteredData, setFilteredData] = useState([]);
  const itemsPerPage = 5;

  useEffect(() => {
    console.log('fetching data and actions');
    dispatch(fetchGeneralDashboard());
    dispatch(getListActions(0));
  }, [dispatch]);

  useEffect(() => {
    if (actions) {
      console.log('actions', actions);
    }
  }, [actions]);

  useEffect(() => {
    if (data && data.totalDonationAmount) {
      console.log('data', data);
      let numString = data.totalDonationAmount.toLocaleString();
      setFormattedString(numString.replace(/\B(?=(\d{3})+(?!\d))/g, ","));
      console.log('formattedString', formattedString);
      }
  }, [data]);

  useEffect(() => {
    if (actions) {
      const filteredActions = actions.filter(action => 
        action.actionDTO.affectedTo && action.actionDTO.affectedTo.id === currentUser.id
      );
      setFilteredData(filteredActions);
      
      const transformedData = filteredActions.map(action => ({
        id: action.id,
        dateEntry: action.actionDTO.dateEntry.split('T')[0],
        deadline: action.actionDTO.deadline.split('T')[0],
        subject: action.actionDTO.subject,
        createdBy: `${action.actionDTO.createdBy.firstName} ${action.actionDTO.createdBy.lastName}`,
        status: action.actionDTO.actionStatus.name || 'Unknown',
        modulepath: action.modulepath,
        entityId: action.entityId,
        moduleName: action.moduleName
      }));
      setTableData(transformedData);
    }
  }, [actions, currentUser]);

  useEffect(() => {
    console.log('tableData', tableData);
  }, [tableData]);

  const history = useHistory();

  const theme = useTheme();

  const statusColors = {
    'actif': '#4CAF50', // green
    'inactif': '#F44336', // red
    'À effectuer': '#FF9800', // orange
    'En cours': '#2196F3', // blue
    'Réalisée': '#9C27B0', // purple
    'Abandonnée': '#607D8B' // grey
  };

  const columns = [
    { field: 'dateEntry', headerName: 'Date de saisie', align: 'center' },
    { field: 'deadline', headerName: 'Date limite', align: 'center' },
    { field: 'subject', headerName: 'Sujet', align: 'center' },
    { field: 'moduleName', headerName: 'Module', align: 'center' },
    { field: 'createdBy', headerName: 'Affecté par', align: 'center' },
    { 
      field: 'status', 
      headerName: 'Statut',
      align: 'center',
      renderCell: (params) => {
        console.log('Status params:', params);
        return (
          <Box
            sx={{
              backgroundColor: statusColors[params.status] || '#607D8B',
              color: 'white',
              padding: '6px 16px',
              borderRadius: '16px',
              fontSize: '0.875rem',
              fontWeight: 500,
              minWidth: '10px',
              height: '32px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
            }}
          >
            <Typography variant="body2" sx={{ fontSize: '0.875rem', fontWeight: 500, color: 'white' }}>
              {params.status}
            </Typography>
          </Box>
        );
      }
    },
    {
      field: 'actions',
      headerName: 'Actions',
      width: 170,
      headerAlign: 'center',
      align: 'center',
      type: 'actions',
      renderCell: params => (
        <div>
          <input
            type="image"
            className="p-0"
            src={VIEW_ICON}
            width="20px"
            height="20px"
            onClick={() => {
            const entityUrl = `${params.modulepath}/fiche/${params.entityId}/action`;
            history.push(entityUrl);
            
            }}
            title="consulter"
          />
        </div>
      ),
    },

  ];

  const handlePageChange = pageNumbers => {
    const nextPage = pageNumbers - 1;
    if (nextPage >= 0 && nextPage < Math.ceil(filteredData.length / itemsPerPage)) {
      setActivePage(nextPage);
    }
  };

  const paginatedData = tableData.slice(
    activePage * itemsPerPage,
    (activePage + 1) * itemsPerPage
  );

  return (
    
    <>
      <HomePage />
      <Container maxWidth="xl" sx={{ mt: 2, mb: 4, position: 'relative' }}>
        {loading && (
          <Box
            sx={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              zIndex: 1,
              borderRadius: '20px'
            }}
          >
            <CircularProgress sx={{ color: 'black' }} />
          </Box>
        )}
        {!loading && actions && (
          <>
          <Grid container spacing={2}>
          <Grid item xs={12} md={6} lg={3}>
            <StatCard icon={<PersonIcon />} value={data.totalDonors} title="Total des donateurs" color="#1d8523" />
          </Grid>
          <Grid item xs={12} md={6} lg={3}>
            <StatCard icon={<MoodIcon />} value={data.totalBeneficiaries} title="Total des bénéficiaires" color="#ff9e00" />
          </Grid>
          <Grid item xs={12} md={6} lg={3}>
            <StatCard icon={<PaidIcon />} value={formattedString+" Dh"} title="Montant total des dons" color="#e43c02" />
          </Grid>
          <Grid item xs={12} md={6} lg={3}>
            <StatCard icon={<FavoriteIcon />} value={data.totalKafalat} title="Nombre total de Kafalats" color="#003eff" />
          </Grid>
        </Grid>
        <div style={{backgroundColor: '#FFFFFF', borderRadius: '20px', padding: '20px', marginTop: '20px', boxShadow: '0px 0px 20px 0px rgba(0, 0, 0, 0.1)'}}>
          <Typography variant="h5" color="primary" gutterBottom sx={{ mb: 4 }}>
            <TocIcon style={{width: '30px', height: '30px', marginRight: '10px', marginBottom: '2px'}}/>
            Mes Actions
          </Typography>
          <div className="table-container">
            <BudgetTable columns={columns} data={paginatedData} />
            <div className="mt-3">
              <CustomPagination
                totalElements={filteredData.length}
                totalCount={Math.ceil(filteredData.length / itemsPerPage)}
                pageSize={itemsPerPage}
                currentPage={activePage + 1}
                onPageChange={handlePageChange}
              />
            </div>
          </div>
        </div>
        </>
      )}
        
      </Container>
    </>
  );
} 