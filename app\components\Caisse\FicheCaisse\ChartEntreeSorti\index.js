import React, { useEffect } from 'react';
import styles from 'Css/profileList.css';
import stylesList from 'Css/profileList.css';
import { useParams } from 'react-router-dom';
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
} from 'recharts';
import { makeSelectCaisseChartDetails, makeSelectError } from './selectors';
import caisseChartReducer from './reducer';
import detailCaisseChartSaga from './saga';
import { useSelector, useDispatch } from 'react-redux';
import { useInjectReducer, useInjectSaga } from 'redux-injectors';
import { createStructuredSelector } from 'reselect';
import { loadCaisseChartDetails } from './actions';
import moment from 'moment';

const formatDate = date =>
  date
    ? moment(date).isValid()
      ? moment(date).format('DD/MM/YYYY')
      : '-'
    : '-';

const CustomTooltip = ({ active, payload, label }) => {
  if (active && payload && payload.length) {
    const caisses = payload[0].payload.caisses;
    return (
      <div
        className="custom-tooltip"
        style={{
          backgroundColor: '#fff',
          padding: '10px',
          border: '1px solid #ccc',
        }}
      >
        <p className="label">
          <strong>{label}</strong>
        </p>
        {caisses && caisses.length > 0 && (
          <div>
            <div>
              <strong>Entrées:</strong>
              <ul>
                {caisses
                  .filter(caisse => caisse.amountBudgetLine)
                  .map((caisse, index) => (
                    <li key={index}>
                      {caisse.fullNameDonor}: {caisse.amountBudgetLine} DH /
                      Date de réception:
                      {formatDate(caisse.dateReceptionBudgetLine)}
                    </li>
                  ))}
              </ul>
            </div>
            <div>
              <strong>Sorties:</strong>
              <ul>
                {caisses
                  .filter(caisse => caisse.montantPrevuAideComplementaire)
                  .map((caisse, index) => (
                    <li key={index}>
                      {caisse.aideComplementaireName}:{' '}
                      {caisse.montantPrevuAideComplementaire} DH / Date
                      d'opération :{formatDate(caisse.dateReceptionBudgetLine)}
                    </li>
                  ))}
              </ul>
            </div>
          </div>
        )}
      </div>
    );
  }
  return null;
};

const key = 'caisseChart';

const omdbSelector = createStructuredSelector({
  caisseChartDetails: makeSelectCaisseChartDetails,
  error: makeSelectError,
});

export default function ChartEntreeSortie(props) {
  const caisse = props.data;
  const params = useParams();

  useInjectReducer({ key, reducer: caisseChartReducer });
  useInjectSaga({ key, saga: detailCaisseChartSaga });

  const { loading, caisseChartDetails, error } = useSelector(omdbSelector);

  const dispatch = useDispatch();

  useEffect(() => {
    dispatch(loadCaisseChartDetails({ id: params.id }));
  }, []);

  const chartData = Array.isArray(caisseChartDetails)
    ? caisseChartDetails.map(monthData => {
        const totalEntrees = monthData.caisses.reduce(
          (sum, caisse) => sum + (caisse.amountBudgetLine || 0),
          0,
        );
        const totalSorties = monthData.caisses.reduce(
          (sum, caisse) => sum + (caisse.montantPrevuAideComplementaire || 0),
          0,
        );
        return {
          month: monthData.monthName,
          entrees: totalEntrees,
          sorties: totalSorties,
          caisses: monthData.caisses,
        };
      })
    : [];

  return (
    <div>
      <div className={`pb-5 ${stylesList.backgroudStyle}`}>
        <div>
          <div className={styles.global}>
            <div className={styles.header}>
              <h4>Évolution des Entrées et Sorties par Mois:</h4>
            </div>
            {caisseChartDetails ? (
              <ResponsiveContainer width="95%" height={600}>
                <LineChart
                  data={chartData}
                  margin={{
                    top: 5,
                    right: 30,
                    left: 20,
                    bottom: 5,
                  }}
                >
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis
                    dataKey="month"
                    angle={-50}
                    textAnchor={'end'}
                    fontSize={14}
                    height={115}
                    fontWeight={'bold'}
                  />
                  <YAxis />
                  <Tooltip content={<CustomTooltip />} />
                  <Legend />
                  <Line
                    type="monotone"
                    dataKey="entrees"
                    name="Entrées"
                    stroke="#82ca9d"
                    activeDot={{ r: 8 }}
                  />
                  <Line
                    type="monotone"
                    dataKey="sorties"
                    name="Sorties"
                    stroke="#ff7300"
                    activeDot={{ r: 8 }}
                  />
                </LineChart>
              </ResponsiveContainer>
            ) : (
              <p>Aucune information</p>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
