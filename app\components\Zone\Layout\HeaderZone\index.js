import React from 'react';
import { Link, NavLink, useParams } from 'react-router-dom';

import styles from '../../../../Css/profileHeader.css';

const HeaderZone = props => {
  const params = useParams();
  return (
    <div className={styles.navBar}>
      {/* <p>
        <NavLink
          exact
          to={'/zones/fiche/' + params.id + '/souszones'}
          activeClassName={styles.selected}
        >
          Sous zones
        </NavLink>
      </p> */}
      <p>
        <NavLink
          exact
          to={`/zones/fiche/${params.id}/beneficiaries`}
          activeClassName={styles.selected}
        >
          Bénéficiaires
        </NavLink>
      </p>

      <p>
        <NavLink
          exact
          to={`/zones/fiche/${params.id}/assistants`}
          activeClassName={styles.selected}
        >
          Historique des assistants
        </NavLink>
      </p>
    </div>
  );
};

export default HeaderZone;
