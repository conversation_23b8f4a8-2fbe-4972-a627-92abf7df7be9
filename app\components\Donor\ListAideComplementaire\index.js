import React, { useState } from 'react';
import { Link, useHistory } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { useEffect } from 'react';
import { Statut } from 'components/AideComplementaire/enum';
import eye from 'images/icons/eye.svg';
import plus from 'images/icons/plus.svg';
import minus from 'images/icons/minus.svg';
import stylesList from 'Css/profileList.css';
import styles from 'Css/profileList.css';
import tagStyles from 'Css/tag.css';
import saga from 'containers/Donor/DonorProfile/saga'
import reducer from 'containers/Donor/DonorProfile/reducer';
import Details from '../OperationsDetail';
import { useInjectReducer, useInjectSaga } from 'redux-injectors';
import { createStructuredSelector } from 'reselect';
import CustomPagination from 'components/Common/CustomPagination';
import { hasRoleAssistant } from 'utils/hasAccess';
import { loadAideComplementaire } from 'containers/Donor/DonorProfile/actions';
import { makeSelectAideComplementaire, makeSelectError, makeSelectLoading } from 'containers/Donor/DonorProfile/selectors';
const key = 'donorProfil';
const omdbSelector = createStructuredSelector({
    aideComplementaire: makeSelectAideComplementaire,
    error: makeSelectError,
    loading: makeSelectLoading,
});
export default function aideComplementaire(props) {
    const donor = props.data;
    const [currentPage, setCurrentPage] = useState(1);
    const [expandedRows, setExpandedRows] = useState({}); // State to track expanded rows
    const pageSize = 5;
    const history = useHistory();
    const viewHandler = id => {
        history.push(`/aide-complementaire/fiche/${id}/info`, { params: id });
    };
    const getTag = statut => {
        switch (statut) {
            case Statut.PLANIFIER:
                return tagStyles.tagGrey;
            case Statut.EN_COURS:
                return tagStyles.tagGreen;
            case Statut.EN_ATTENTE_D_EXECUTION:
                return tagStyles.tagOrange;
            case Statut.EXECUTER:
                return tagStyles.tagBlue;
            case Statut.CLOTURER:
                return tagStyles.tagYellow;
            default:
                return tagStyles.tagGrey;
        }
    };

    const getStatutText = statut => {
        switch (statut) {
            case Statut.PLANIFIER:
                return 'Planifiée';
            case Statut.EN_COURS:
                return 'En cours';
            case Statut.EN_ATTENTE_D_EXECUTION:
                return "En attente d'exécution";
            case Statut.EXECUTER:
                return 'Exécutée';
            case Statut.CLOTURER:
                return 'Clôturée';
            default:
                return '---';
        }
    };

    useInjectReducer({ key, reducer: reducer });
    useInjectSaga({ key, saga: saga });
    const { aideComplementaire, error, loading } = useSelector(omdbSelector);
    const dispatch = useDispatch();
    useEffect(() => {
        if (donor) {
            dispatch(loadAideComplementaire(donor.id));
        }
    }, [dispatch, donor]);
    const handleViewPlanning = id => {
        setExpandedRows(prev => ({
            ...prev,
            [id]: !prev[id], // Toggle the specific row
        }));
    };

    const isRoleAssistant = hasRoleAssistant();


    return (
        <div className={`pb-5 ${stylesList.backgroudStyle}`}>
            <div>
                <div className={styles.global}>
                    <div className={styles.header}>
                        <h4>Liste des Aide Complementaire</h4>
                    </div>
                    <div className={stylesList.content}>
                        <table className="table small">
                            <thead>
                                <tr>
                                    <th style={{ whiteSpace: 'nowrap' }}>Aide Complementaire</th>
                                    <th style={{ whiteSpace: 'nowrap' }}>Service</th>
                                    <th style={{ whiteSpace: 'nowrap' }}>la Date d'execution</th>
                                    <th style={{ whiteSpace: 'nowrap' }}>Montant (DH)</th>
                                    <th style={{ whiteSpace: 'nowrap' }}>Type Donation</th>
                                    <th style={{ whiteSpace: 'nowrap' }}>Statut</th>
                                    {!isRoleAssistant && (
                                        <th style={{ whiteSpace: 'nowrap' }}>Actions</th>
                                    )}
                                </tr>
                            </thead>
                            <tbody>
                                {aideComplementaire.length > 0 ? (
                                    aideComplementaire.map((item, index) => {
                                        const isExpanded = expandedRows[item.id] || false;
                                        const startIndex = (currentPage - 1) * pageSize;
                                        const endIndex = startIndex + pageSize;
                                        const paginatedItems = aideComplementaire.slice(
                                            startIndex,
                                            endIndex,
                                        );
                                        return (
                                            <React.Fragment key={item.id}>
                                                <tr
                                                    className={`${isExpanded ? styles.expandedRow : styles.row
                                                        }`}
                                                >
                                                    <td>{item.name}</td>
                                                    <td>
                                                        {!isRoleAssistant ? (
                                                            <Link to={`/services/fiche/${item.services.id}/info`}>
                                                                {item.services.name}
                                                            </Link>
                                                        ) : (
                                                            <span  >
                                                                {item.services.name}
                                                            </span>
                                                        )}
                                                    </td>
                                                    <td>
                                                        {item.execuionDate.split("T")[0]}
                                                    </td>
                                                    <td>
                                                        {item.amount}
                                                    </td>
                                                    <td>
                                                        {item.typeDonation  ? (
                                                            <span className={tagStyles.tagYellow}>Nature</span>
                                                        ) : (
                                                            <span className={tagStyles.tagGreen}>Financiare</span>
                                                        ) }
                                                    </td>
                                                    <td  >{getStatutText(item.statut)}</td>
                                                    {!isRoleAssistant && (
                                                        <td style={{ textAlign: 'center' }}>
                                                            <input
                                                                type="image"
                                                                onClick={() => viewHandler(item.id)}
                                                                className="p-2"
                                                                src={eye}
                                                                width="40px"
                                                                height="40px"
                                                                title="Consulter"
                                                            />
                                                        </td>
                                                    )}
                                                </tr>

                                            </React.Fragment>
                                        );
                                    })
                                ) : (
                                    <tr>
                                        <td colSpan="8" style={{ textAlign: 'center' }}>
                                            Aucune Aide Complementaire trouvée
                                        </td>
                                    </tr>
                                )}
                            </tbody>
                        </table>
                        {aideComplementaire && aideComplementaire.length > 0 && (
                            <div className="row justify-content-center my-4">
                                <CustomPagination
                                    totalCount={Math.ceil(
                                        aideComplementaire.length / pageSize,
                                    )}
                                    pageSize={pageSize}
                                    currentPage={currentPage}
                                    totalElements={aideComplementaire.length}
                                    onPageChange={setCurrentPage}
                                />
                            </div>
                        )}
                    </div>
                </div>
            </div>
        </div>
    );
}
