import {
    ADD_TAG,
    ADD_TAG_SUCCESS,
    ADD_TAG_ERROR,
    LOAD_TAGS,
    LOAD_TAGS_SUCCESS,
    LOAD_TAGS_ERROR,
    DELETE_TAG,
    LOAD_TAG_LIST,
    DELETE_TAG_SUCCESS,
    LOAD_TAG_LIST_SUCCESS,
    DELETE_TAG_ERROR,
    RESET_DELETE_TAG,
    RESET_ERROR,
    LOAD_TYPE_TAGS,
    LOAD_TYPE_TAGS_SUCCESS,
    LOAD_TYPE_TAGS_ERROR,
    LOAD_TAGS_BY_TYPE,
    LOAD_TAGS_BY_TYPE_SUCCESS,
    LOAD_TAGS_BY_TYPE_ERROR,
} from './constants';
import produce from 'immer';

const initialState = {
    tags: [],
    tagList: [],
    typeTags: null,
    loading: false,
    error: null,
    deleteSuccess: false,
    success: null,
};

const tagReducer = produce((draft, action) => {
    switch (action.type) {
        case ADD_TAG:
            draft.loading = true;
            draft.error = false;
            draft.success = false;
            break;
        case ADD_TAG_SUCCESS:
            draft.loading = false;
            draft.error = false;
            draft.success = true;
            draft.tag = action.tag;
            break;
        case ADD_TAG_ERROR:
            draft.loading = false;
            draft.error = action.error;
            break;
        case LOAD_TAGS:
            draft.loading = true;
            draft.error = false;
            draft.tags = [];
            draft.tagList = [];
            break;
        case LOAD_TAG_LIST:
            draft.loading = true;
            draft.error = false;
            draft.tagList = [];
            break;
        case LOAD_TAGS_SUCCESS:
            draft.loading = false;
            draft.error = false;
            draft.tags = action.tags;

            break;
        case LOAD_TAG_LIST_SUCCESS:
            draft.loading = false;
            draft.error = false;
            draft.tagList = action.tagList;
            break;

        case RESET_ERROR:
            draft.error = false;
            break;

        case LOAD_TAGS_ERROR:
            draft.loading = false;
            draft.error = action.error;
            break;

        case DELETE_TAG:
            draft.deleteSuccess = false;
            break;

        case DELETE_TAG_SUCCESS:
            draft.deleteSuccess = true;
            break;

        case DELETE_TAG_ERROR:
            draft.error = action.error;
            break;

        case RESET_DELETE_TAG:
            draft.deleteSuccess = false;
            break;

        case LOAD_TYPE_TAGS:
            draft.loading = true;
            draft.error = false;
            break;

        case LOAD_TYPE_TAGS_SUCCESS:
            draft.loading = false;
            draft.error = false;
            draft.typeTags = action.typeTags;
            break;

        case LOAD_TYPE_TAGS_ERROR:
            draft.loading = false;
            draft.error = action.error;
            break;

        case LOAD_TAGS_BY_TYPE:
            draft.loading = true;
            draft.error = false;
            draft.tagList = [];
            break;

        case LOAD_TAGS_BY_TYPE_SUCCESS:
            draft.loading = false;
            draft.error = false;
            draft.tagList = action.tagList;
            break;

        case LOAD_TAGS_BY_TYPE_ERROR:
            draft.loading = false;
            draft.error = action.error;
            break;

        default:
            return draft;
    }
}, initialState);

export default tagReducer;