import React, { useEffect, useState } from 'react';
import moment from 'moment';
import CorrespondenceForm from 'containers/Common/SubComponents/CorrespondenceForm';
import {
  makeSelecCorrespondence,
  makeSelecCorrespondenceDeleteSuccess,
  makeSelecCorrespondences,
  makeSelectCorrespondencesAddDonorSuccess,
  makeSelectError,
  makeSelectSuccess,
} from 'containers/Common/SubComponents/CorrespondenceForm/selectors';
import {
  deleteCorrespondence,
  loadCorrespondenceData,
  resetCorrespondence,
} from 'containers/Common/SubComponents/CorrespondenceForm/actions';
import { useParams } from 'react-router-dom';

import { createStructuredSelector } from 'reselect';
import { useDispatch, useSelector } from 'react-redux';
import { useInjectReducer, useInjectSaga } from 'redux-injectors';
import { deleteCorrespondenceSaga } from 'containers/Common/SubComponents/CorrespondenceForm/saga';
import reducer from 'containers/Common/SubComponents/CorrespondenceForm/reducer';

import {
  pushCorrespondenceDonor,
  updateCorrespondenceDonor,
} from 'containers/Donor/DonorProfile/actions';
import Modal2 from 'components/Common/Modal2';
import { Alert } from 'react-bootstrap';
import AccessControl, { isAuthorized } from 'utils/AccessControl';
import btnStyles from '../../../Css/button.css';
import list from '../../../Css/profileList.css';
import {
  DELETE_ICON,
  EDIT_ICON,
  VIEW_ICON,
} from '../../Common/ListIcons/ListIcons';
import DataTable from '../../Common/DataTable';
import CustomPagination from '../../Common/CustomPagination';

const key = 'correspondence';

const target = 'donor';

const formatDate = date => moment(date).format('DD/MM/YYYY');

const omdbSelector = createStructuredSelector({
  success: makeSelectSuccess,
  error: makeSelectError,
  correspondence: makeSelecCorrespondence,
  successDelete: makeSelecCorrespondenceDeleteSuccess,
  correspondences: makeSelecCorrespondences,
  correspondenceDonorAddSuccess: makeSelectCorrespondencesAddDonorSuccess,
});

export default function ListCorrespondences(props) {
  const dispatch = useDispatch();

  useInjectReducer({ key, reducer });
  useInjectSaga({ key, saga: deleteCorrespondenceSaga });

  const params = useParams();
  const [show, setShow] = useState(false);
  const [correspondenceToDelete, setCorrespondenceToDelete] = useState('');
  const [showDeleteModal, setShowDeleteModal] = useState(false);

  const handleClose = () => {
    setShow(false);
    setCorrespondenceToReadOnly({
      title: 'Consulter la correspondence',
      isRead: false,
      coreespondence: null,
    });
  };

  const handleShow = () => {
    setShow(true);
  };

  const handleCloseForDeleteModal = () => setShowDeleteModal(false);

  useEffect(
    () =>
      function cleanup() {
        dispatch(resetCorrespondence());
      },
    [],
  );

  const {
    success,
    error,
    correspondence,
    successDelete,
    correspondences,
    correspondenceDonorAddSuccess,
  } = useSelector(omdbSelector);
  const [correspondenceToEdit, setCorrespondenceToEdit] = useState('');
  const [message, setMessage] = useState('');
  const [showAlert, setShowAlert] = useState(false);
  const [correspondenceToReadOnly, setCorrespondenceToReadOnly] = useState({
    title: 'Consulter la correspondence',
    isRead: false,
    coreespondence: null,
  });

  // Define pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const pageSize = 5; // Define page size

  const donor = props.data;
  let donorCode;

  if (donor) {
    donorCode = donor.code;
  }

  let errorMessage = null;

  useEffect(() => {
    if (successDelete) {
      setShowAlert(true);
      setMessage('Correspondence supprimé avec succès');
    }
  }, [successDelete]);

  useEffect(() => {
    if (correspondenceDonorAddSuccess) {
      setShowAlert(true);
      if (correspondenceToEdit) {
        setMessage('Correspondence modifié avec succès');
      } else {
        setMessage('Correspondence ajouté avec succès');
      }
      setCorrespondenceToEdit('');
    }
  }, [correspondenceDonorAddSuccess]);

  useEffect(() => {
    if (showAlert) {
      setTimeout(() => {
        setShowAlert(false);
      }, 4000);
    }
  }, [showAlert]);

  useEffect(() => {
    if (error) {
      setTimeout(() => {
        dispatch(resetCorrespondence());
      }, 4000);
    }
  }, [error]);

  useEffect(() => {
    dispatch(loadCorrespondenceData(params.id, target));
  }, []);

  useEffect(() => {
    if (successDelete || correspondenceDonorAddSuccess) {
      dispatch(loadCorrespondenceData(params.id, target));
    }
  }, [successDelete, correspondenceDonorAddSuccess]);

  if (error) {
    errorMessage = 'Une erreur est survenue';
  }

  const emptyValue = <span>-</span>;

  let listCorrespondences = null;

  if (correspondences) {
    const correspondencesSorted = [...correspondences].sort((a, b) =>
      a.createdDate < b.createdDate ? 1 : -1,
    );

    // Implement pagination logic
    const startIndex = (currentPage - 1) * pageSize;
    const endIndex = Math.min(
      startIndex + pageSize,
      correspondencesSorted.length,
    );
    const paginatedCorrespondences = correspondencesSorted.slice(
      startIndex,
      endIndex,
    );

    const translateDirection = direction =>
      direction === 'OUTGOING'
        ? 'Sortant'
        : direction === 'INCOMING'
        ? 'Entrant'
        : '';

    listCorrespondences = paginatedCorrespondences.map(correspondence => ({
      documents: correspondence.documents
        ? correspondence.documents.fileUrl
        : null,
      id: correspondence.id,
      direction: translateDirection(correspondence.direction),
      content: correspondence.content,
      subject: correspondence.subject,
      date: correspondence.date ? formatDate(correspondence.date) : emptyValue,
      affectedTo: correspondence.affectedTo
        ? `${correspondence.affectedTo.firstName}${' '}${
            correspondence.affectedTo.lastName
          }`
        : null,
      canalCommunication: correspondence.canalCommunication
        ? correspondence.canalCommunication.name
        : null,
      correspondence,
    }));
  }

  const columns = [
    {
      field: 'direction',
      headerName: 'Direction',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
    {
      field: 'content',
      headerName: 'Contenu',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
    {
      field: 'subject',
      headerName: 'Type de correspondance',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
    {
      field: 'date',
      headerName: 'Date',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
    {
      field: 'affectedTo',
      headerName: 'Ajoutée par',
      headerAlign: 'center',
      align: 'center',
      flex: 1,
    },
    // {
    //   field: 'canalCommunication',
    //   headerName: 'Canal communication',
    //   headerAlign: 'center',
    //   align: 'center',
    //   flex: 1,
    // },
    // {
    //   field: 'documents',
    //   headerName: 'Nom de fichier',
    //   headerAlign: 'center',
    //   align: 'center',
    //   flex: 1,
    // },
  ];

  const connectedUser = useSelector(state => state.app.connectedUser);
  const isUpdateAuthorized = isAuthorized(connectedUser, 'DONOR', 'UPDATE');
  if (isUpdateAuthorized) {
    columns.push({
      field: 'actions',
      type: 'actions',
      headerName: 'Actions',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
      renderCell: params => (
        <div>
          <input
            type="image"
            className="p-0 mr-1"
            src={VIEW_ICON}
            width="20px"
            height="20px"
            onClick={() => {
              setCorrespondenceToEdit(params.row.correspondence);
              setCorrespondenceToReadOnly(prev => ({
                ...prev,
                isRead: true,
                coreespondence: params.row.correspondence,
              }));
              handleShow();
            }}
            title="consulter"
          />
          <input
            type="image"
            src={EDIT_ICON}
            className="p-0 mr-1"
            width="20px"
            height="20px"
            onClick={() => {
              const { correspondence } = params.row;
              setCorrespondenceToEdit(params.row.correspondence);
              handleShow();
            }}
            title="modifier"
          />
          <input
            type="image"
            src={DELETE_ICON}
            className="p-0"
            width="20px"
            height="20px"
            onClick={() => {
              setCorrespondenceToDelete(params.row);
              setShowDeleteModal(true);
            }}
            title="supprimer"
          />
        </div>
      ),
    });
  }

  return (
    <div>
      <Modal2
        title={
          correspondenceToReadOnly.isRead
            ? correspondenceToReadOnly.title
            : correspondenceToEdit
            ? 'Modifier la correspondence'
            : 'Ajouter une correspondence'
        }
        size="lg"
        customWidth="modal-90w"
        show={show}
        handleClose={() => {
          setShow(false);
          correspondenceToReadOnly.isRead &&
            setCorrespondenceToReadOnly({
              title: 'Consulter la correspondence',
              isRead: false,
              coreespondence: null,
            });
        }}
      >
        <CorrespondenceForm
          correspondence={
            correspondenceToEdit ||
            (correspondenceToReadOnly.coreespondence
              ? correspondenceToReadOnly.coreespondence
              : correspondenceToEdit)
          }
          handleClose={handleClose}
          button={correspondenceToEdit ? 'Modifier' : 'Ajouter'}
          target={target}
          id={params.id}
          isRead={correspondenceToReadOnly.isRead}
        />
      </Modal2>

      <Modal2
        centered
        className="mt-5"
        title="Confirmation de suppression"
        show={showDeleteModal}
        handleClose={handleCloseForDeleteModal}
      >
        <p className="mt-1 mb-5">
          Êtes-vous sûr de vouloir supprimer cette correspondence?
        </p>
        <div className="d-flex justify-content-end px-3 my-1">
          <button
            type="button"
            className={`mx-2 btn-style outlined`}
            onClick={handleCloseForDeleteModal}
          >
            Annuler
          </button>
          <button
            type="submit"
            className={`mx-2 btn-style primary`}
            onClick={() => {
              dispatch(deleteCorrespondence(correspondenceToDelete, target));
              setCorrespondenceToDelete('');
              handleCloseForDeleteModal();
            }}
          >
            Supprimer
          </button>
        </div>
      </Modal2>

      {showAlert ? (
        <Alert
          className="alert-style"
          variant="success"
          onClose={() => setShowAlert(false)}
          dismissible
        >
          <p>{message}</p>
        </Alert>
      ) : null}

      {error && (
        <Alert
          className="alert-style"
          variant="danger"
          onClose={() => setShowAlert(false)}
          dismissible
        >
          <p>{errorMessage}</p>
        </Alert>
      )}

      <div className={list.backgroudStyle}>
        <div className={list.global}>
          <div className={list.header}>
            <h4>Liste des Correspondences</h4>
            <AccessControl module="DONOR" functionality="UPDATE">
              <button
                className={btnStyles.addBtnProfile}
                onClick={() => {
                  handleShow();
                  setCorrespondenceToEdit('');
                }}
              >
                Ajouter
              </button>
            </AccessControl>
          </div>
          <DataTable
            rows={listCorrespondences}
            columns={columns}
            fileName={`Liste des correspondences du donateur ${donorCode} , ${new Date().toLocaleString()}`}
          />

          <div className="justify-content-center my-4">
            {correspondences && (
              <CustomPagination
                totalElements={correspondences.length}
                totalCount={Math.ceil(correspondences.length / pageSize)}
                pageSize={pageSize}
                currentPage={currentPage}
                onPageChange={setCurrentPage}
              />
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
