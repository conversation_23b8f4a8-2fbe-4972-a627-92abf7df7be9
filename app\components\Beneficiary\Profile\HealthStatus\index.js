import React, { useEffect, useState } from 'react';
import { Link, useLocation } from 'react-router-dom';

import emptyTable from 'containers/Common/Scripts/GenerateEmptyTable';
import styles from 'Css/personalInfo.css';
import btnStyles from 'Css/button.css';
import stylesList from 'Css/profileList.css';
import { HasAccess, useHasRole, useHasRoleToHide } from 'utils/hasAccess';
import {
  isAssistant,
  isCandidateWithoutRejected,
  isOnlyBeneficiary,
} from 'containers/Beneficiary/BeneficiaryProfile/statutUtils';
import { WHITE_UPLOAD_PICTURE } from 'containers/Common/RequiredElement/Icons';

export default function HealthStatus(props) {
  const beneficiary = props.data;
  let allergies = '';
  let diseases = '';

  const [hide, setHide] = useState(false);
  const location = useLocation();

  const hasRoleAssistant = useHasRole('ASSISTANT');
  const hasRoleAssistantToHide = useHasRoleToHide('ASSISTANT');
  const shouldHide = !!(
    beneficiary &&
    beneficiary.beneficiaryStatut &&
    hasRoleAssistantToHide &&
    !isAssistant(beneficiary.beneficiaryStatut.nameStatut) &&
    isCandidateWithoutRejected(beneficiary.beneficiaryStatut.nameStatut)
  );

  useEffect(() => {
    if (location && location.state && location.state.isOldBeneficiary) {
      setHide(location.state.isOldBeneficiary);
    } else {
      setHide(false);
    }
  }, [location]);

  if (beneficiary) {
    beneficiary.allergies.map(allergy => {
      allergies += `${allergy.name}, `;
    });
    beneficiary.diseases.map(disease => {
      diseases += `${disease.name}, `;
    });
    allergies = allergies.slice(0, -2);
    diseases = diseases.slice(0, -2);
  }

  let sanitaire = null;
  let diseaseTreatments = emptyTable(3);
  let showHandicapDetails = false;

  if (props.data) {
    sanitaire = (
      <div className={styles.data1}>
        <p>
          {beneficiary.allergies.length > 0
            ? beneficiary.allergies.map(allergy => allergy.name).join(', ')
            : '-'}
        </p>
        <p>
          {beneficiary.diseases.length > 0
            ? beneficiary.diseases.map(disease => disease.name).join(', ')
            : '-'}
        </p>
        <p>
          {beneficiary.glasses && beneficiary.glasses.useGlasses
            ? 'Oui'
            : 'Non'}
        </p>
        <p>{beneficiary.handicapped.length > 0 ? 'Oui' : 'Non'}</p>
        {beneficiary.handicapped.length > 0 &&
        beneficiary.handicapped[0].handicapType ? (
          <>
            <p>
              {beneficiary.handicapped[0].handicapType.name
                ? beneficiary.handicapped[0].handicapType.name
                : '-'}
            </p>
            <p>
              {beneficiary.handicapped[0].handicapCause &&
              beneficiary.handicapped[0].handicapCause !== ''
                ? beneficiary.handicapped[0].handicapCause
                : '---'}
            </p>
            <p>
              {beneficiary.handicapped[0].handicapCost
                ? `${beneficiary.handicapped[0].handicapCost} DH`
                : '-'}
            </p>
            <p>
              {beneficiary.handicapped[0].comment
                ? beneficiary.handicapped[0].comment
                : '-'}
            </p>
          </>
        ) : null}
      </div>
    );

    showHandicapDetails = !!(
      beneficiary.handicapped.length > 0 &&
      beneficiary.handicapped[0].handicapType
    );

    if (beneficiary.diseaseTreatments.length > 0) {
      diseaseTreatments = beneficiary.diseaseTreatments.map(
        diseaseTreatment => (
          <tr key={diseaseTreatment.id}>
            <th scope="row">{diseaseTreatment.type.name}</th>
            <td>{diseaseTreatment.cost} DH</td>
            <td>{diseaseTreatment.comment}</td>
          </tr>
        ),
      );
    }
  }

  const shouldShowButton = !!(!hide && shouldHide === false);

  return (
    <div>
      {beneficiary ? (
        <div className={stylesList.backgroudStyle}>
          <div className={styles.personalInfo}>
            <div className={styles.header}>
              <h4>Etat Sanitaire</h4>

              <HasAccess
                combinations={[
                  { feature: 'GERER_CANDIDATS', privilege: 'WRITE' },
                  {
                    feature: 'GERER_BENEFICIAIRES_KAFALAT',
                    privilege: 'WRITE',
                  },
                ]}
              >
                <Link
                  to={{
                    pathname: `/beneficiaries/edit/${beneficiary.id}`,
                    state: {
                      edit: 'health_status',
                      redirectTo: 'consultation',
                      candidate: isCandidateWithoutRejected(
                        beneficiary.beneficiaryStatut.id,
                      ),
                      beneficiaryEnAttente: !isOnlyBeneficiary(
                        beneficiary.beneficiaryStatut.id,
                      ),
                    },
                  }}
                >
                  {shouldShowButton && (
                    <button className="btn-style secondary">
                      <img
                        src={WHITE_UPLOAD_PICTURE}
                        width="16px"
                        height="16px"
                      />
                      Modifier
                    </button>
                  )}
                </Link>
              </HasAccess>
            </div>

            <div className={styles.content}>
              <div className={styles.section1}>
                <div className={styles.top}>
                  <div className={styles.label1}>
                    <p style={{ whiteSpace: 'nowrap' }}>Allergies : </p>
                    <p style={{ whiteSpace: 'nowrap' }}>Maladies : </p>
                    <p style={{ whiteSpace: 'nowrap' }}>
                      Porteur de lunettes :{' '}
                    </p>
                    <p style={{ whiteSpace: 'nowrap' }}>Handicapé : </p>
                    {showHandicapDetails && (
                      <>
                        <p style={{ whiteSpace: 'nowrap' }}>
                          Type d'handicap :{' '}
                        </p>
                        <p style={{ whiteSpace: 'nowrap' }}>
                          Cause d'handicap :{' '}
                        </p>
                        <p style={{ whiteSpace: 'nowrap' }}>
                          Coût des soins :{' '}
                        </p>
                        <p style={{ whiteSpace: 'nowrap' }}>
                          Description d'handicap :{' '}
                        </p>
                      </>
                    )}
                  </div>
                  {sanitaire}
                </div>
              </div>

              <div className={`${styles.section2} mt-4`}>
                <h5>Traitements des maladies chroniques</h5>
                <table className="table customTable">
                  <thead>
                    <tr>
                      <th scope="col">Maladie Chronique</th>
                      <th scope="col">Coût des soins</th>
                      <th scope="col">Description</th>
                    </tr>
                  </thead>
                  <tbody>{diseaseTreatments}</tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      ) : null}
    </div>
  );
}
