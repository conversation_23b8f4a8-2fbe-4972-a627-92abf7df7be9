import { createSelector } from "reselect";
import { initialState } from "./reducer"

// Select raw state
const selectResponsableEps = state => state.responsableEps || initialState;

// Memoized selector for cities
const makeSelectResponsableEps = createSelector(
    selectResponsableEps,
    responsableEps => responsableEps.responsableEps
);



const makeSelectDeleteSuccess = createSelector(
    selectResponsableEps,
    responsableEps => responsableEps.deleteSuccess,
);

const makeSelectLoading = createSelector(
    selectResponsableEps,
    responsableEps => responsableEps.loading
);

const makeSelectError = createSelector(
    selectResponsableEps,
    responsableEps => responsableEps.error
);

export {
    selectResponsableEps,
    makeSelectResponsableEps,
    makeSelectDeleteSuccess,
    makeSelectLoading,
    makeSelectError,
};

