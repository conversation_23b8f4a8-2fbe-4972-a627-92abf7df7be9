.accordion-item {
    border: 1px solid #ddd;
    background:black;
    border-radius: 6px;
    overflow: hidden;
    margin-bottom: 10px;
  }
  
  .accordion-header {
    width: 100%;
    padding: 12px;
    background: #f8f8f8;
    border: none;
    cursor: pointer;
    font-size: 16px;
    font-weight: bold;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: background 0.3s;
  }
  
  .accordion-header:hover {
    background: #eaeaea;
  }
  
  .icon {
    transition: transform 0.3s ease;
  }
  
  .rotate {
    transform: rotate(180deg);
  }
  
  .accordion-content {
    background: white;
    padding: 12px;
    border-top: 1px solid #ddd;
    display: flex;
    align-items: center;
    gap: 10px;
  }
  
  .old-value {
    color: red;
    font-weight: bold;
  }
  
  .new-value {
    color: green;
    font-weight: bold;
  }
  
  .separator {
    font-weight: bold;
    color: #888;
  }
  