<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Liste des Zones</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            padding: 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            color: white;
        }

        .header h1 {
            font-size: 28px;
            font-weight: 600;
            margin: 0;
        }

        .btn-add {
            background: rgba(255, 255, 255, 0.2);
            border: 2px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 12px 24px;
            border-radius: 12px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .btn-add:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .content {
            padding: 30px;
        }

        .zones-grid {
            display: grid;
            gap: 20px;
            margin-bottom: 30px;
        }

        .zone-card {
            background: white;
            border-radius: 16px;
            padding: 24px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            border: 1px solid rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
        }

        .zone-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
        }

        .zone-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }

        .zone-name {
            font-size: 18px;
            font-weight: 600;
            color: #2d3748;
        }

        .zone-status {
            background: #fee2e2;
            color: #dc2626;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
        }

        .zone-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 16px;
            margin-bottom: 16px;
        }

        .zone-detail {
            display: flex;
            flex-direction: column;
            gap: 4px;
        }

        .zone-detail-label {
            font-size: 12px;
            color: #6b7280;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .zone-detail-value {
            font-size: 14px;
            color: #374151;
            font-weight: 500;
        }

        .arabic-text {
            direction: rtl;
            text-align: right;
        }

        .subzone-section {
            background: linear-gradient(135deg, #fef7cd 0%, #fde68a 100%);
            border-radius: 12px;
            padding: 20px;
            margin-top: 20px;
        }

        .subzone-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }

        .subzone-title {
            font-size: 16px;
            font-weight: 600;
            color: #92400e;
        }

        .subzone-item {
            background: rgba(255, 255, 255, 0.7);
            border-radius: 8px;
            padding: 16px;
            display: grid;
            grid-template-columns: 1fr 1fr 1fr auto;
            gap: 16px;
            align-items: center;
        }

        .subzone-actions {
            display: flex;
            gap: 8px;
        }

        .btn-icon {
            width: 36px;
            height: 36px;
            border-radius: 8px;
            border: none;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s ease;
        }

        .btn-edit {
            background: #dbeafe;
            color: #2563eb;
        }

        .btn-edit:hover {
            background: #bfdbfe;
            transform: scale(1.05);
        }

        .btn-delete {
            background: #fee2e2;
            color: #dc2626;
        }

        .btn-delete:hover {
            background: #fecaca;
            transform: scale(1.05);
        }

        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 12px;
            margin-top: 30px;
        }

        .pagination-info {
            color: #6b7280;
            font-size: 14px;
        }

        .pagination-btn {
            width: 40px;
            height: 40px;
            border-radius: 10px;
            border: 1px solid #e5e7eb;
            background: white;
            color: #374151;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s ease;
        }

        .pagination-btn:hover {
            background: #f3f4f6;
            border-color: #d1d5db;
        }

        .pagination-btn.active {
            background: #4facfe;
            border-color: #4facfe;
            color: white;
        }

        .expand-btn {
            background: none;
            border: none;
            cursor: pointer;
            color: #6b7280;
            transition: transform 0.2s ease;
        }

        .expand-btn:hover {
            color: #374151;
        }

        .expand-btn.expanded {
            transform: rotate(180deg);
        }

        @media (max-width: 768px) {
            .header {
                flex-direction: column;
                gap: 16px;
                text-align: center;
            }

            .zone-details {
                grid-template-columns: 1fr;
            }

            .subzone-item {
                grid-template-columns: 1fr;
                gap: 12px;
            }

            .subzone-actions {
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Liste des Zones</h1>
            <button class="btn-add" onclick="addZone()">
                + Ajouter
            </button>
        </div>

        <div class="content">
            <div class="zones-grid">
                <!-- Zone 1 -->
                <div class="zone-card">
                    <div class="zone-header">
                        <div class="zone-name">Briar Dennis</div>
                        <div class="zone-status">Invalid date</div>
                        <button class="expand-btn" onclick="toggleExpand(this)">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M7 10l5 5 5-5z"/>
                            </svg>
                        </button>
                    </div>
                    
                    <div class="zone-details">
                        <div class="zone-detail">
                            <div class="zone-detail-label">Nom Arabe</div>
                            <div class="zone-detail-value arabic-text">أم الجمعة *</div>
                        </div>
                        <div class="zone-detail">
                            <div class="zone-detail-label">Code</div>
                            <div class="zone-detail-value">Quia sequi laboris e</div>
                        </div>
                        <div class="zone-detail">
                            <div class="zone-detail-label">Date d'affectation</div>
                            <div class="zone-detail-value">Invalid date</div>
                        </div>
                        <div class="zone-detail">
                            <div class="zone-detail-label">Date fin d'affectation</div>
                            <div class="zone-detail-value">Invalid date</div>
                        </div>
                    </div>

                    <div class="subzone-section" style="display: none;">
                        <div class="subzone-header">
                            <div class="subzone-title">Sous-Zones</div>
                        </div>
                        <div class="subzone-item">
                            <div>
                                <div class="zone-detail-label">Code Sous-Zone</div>
                                <div class="zone-detail-value">SZ-Code de la zone-001</div>
                            </div>
                            <div>
                                <div class="zone-detail-label">Nom</div>
                                <div class="zone-detail-value">hamza</div>
                            </div>
                            <div>
                                <div class="zone-detail-label">Nom Arabe</div>
                                <div class="zone-detail-value arabic-text">حمز</div>
                            </div>
                            <div class="subzone-actions">
                                <button class="btn-icon btn-edit" onclick="editSubzone()">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z"/>
                                    </svg>
                                </button>
                                <button class="btn-icon btn-delete" onclick="deleteSubzone()">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6v12zM19 4h-3.5l-1-1h-5l-1 1H5v2h14V4z"/>
                                    </svg>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Zone 2 -->
                <div class="zone-card">
                    <div class="zone-header">
                        <div class="zone-name">l'établissement</div>
                        <div class="zone-status">Invalid date</div>
                        <button class="expand-btn" onclick="toggleExpand(this)">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M7 10l5 5 5-5z"/>
                            </svg>
                        </button>
                    </div>
                    
                    <div class="zone-details">
                        <div class="zone-detail">
                            <div class="zone-detail-label">Nom Arabe</div>
                            <div class="zone-detail-value arabic-text">أم الجماعة</div>
                        </div>
                        <div class="zone-detail">
                            <div class="zone-detail-label">Code</div>
                            <div class="zone-detail-value">Code de la zone</div>
                        </div>
                        <div class="zone-detail">
                            <div class="zone-detail-label">Date d'affectation</div>
                            <div class="zone-detail-value">Invalid date</div>
                        </div>
                        <div class="zone-detail">
                            <div class="zone-detail-label">Date fin d'affectation</div>
                            <div class="zone-detail-value">Invalid date</div>
                        </div>
                    </div>

                    <div class="subzone-section" style="display: none;">
                        <div class="subzone-header">
                            <div class="subzone-title">Aucune sous-zone</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="pagination">
                <div class="pagination-info">Résultats trouvés: 2</div>
                <button class="pagination-btn">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z"/>
                    </svg>
                </button>
                <button class="pagination-btn active">1</button>
                <button class="pagination-btn">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z"/>
                    </svg>
                </button>
            </div>
        </div>
    </div>

    <script>
        function toggleExpand(btn) {
            const card = btn.closest('.zone-card');
            const subzoneSection = card.querySelector('.subzone-section');
            
            if (subzoneSection.style.display === 'none') {
                subzoneSection.style.display = 'block';
                btn.classList.add('expanded');
            } else {
                subzoneSection.style.display = 'none';
                btn.classList.remove('expanded');
            }
        }

        function addZone() {
            alert('Ajouter une nouvelle zone');
        }

        function editSubzone() {
            alert('Modifier la sous-zone');
        }

        function deleteSubzone() {
            if (confirm('Êtes-vous sûr de vouloir supprimer cette sous-zone?')) {
                alert('Sous-zone supprimée');
            }
        }

        // Add some interactive animations
        document.querySelectorAll('.zone-card').forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-4px)';
            });
            
            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0)';
            });
        });
    </script>
</body>
</html>