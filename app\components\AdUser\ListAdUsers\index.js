import React, { useState } from 'react';
import PropTypes from 'prop-types';
import Modal2 from 'components/Common/Modal2';
import btnStyles from 'Css/button.css';
import { useDispatch } from 'react-redux';
import {
  changeUserRoleRequest,
  deleteUserRequest,
} from 'containers/AdUser/actions';
import AccessControl from 'utils/AccessControl';
import DataTable from 'components/Common/DataTable';
import {
  CHANGE_PROFILE_ICON,
  DELETE_ICON,
} from '../../Common/ListIcons/ListIcons';

const ListAdUsers = ({ adUsers, roles, connectedUser, liste1 }) => {
  const dispatch = useDispatch();
  const connectedUserId = connectedUser && connectedUser.id;
  const [showModal, setShowModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [selectedUser, setSelectedUser] = useState(null);
  const [newRole, setNewRole] = useState('');

  const toggleModal = adUser => {
    setSelectedUser(adUser);
    setNewRole(adUser.row && adUser.row.role ? adUser.row.role.id : '');
    setShowModal(!showModal);
  };

  const DeleteModal = adUser => {
    setSelectedUser(adUser);
    setShowDeleteModal(!showDeleteModal);
  };

  const handleRoleChange = (adUser, newRole) => {
    dispatch(changeUserRoleRequest(adUser.id, newRole));
    setShowModal(false);
  };

  const handleDeleteUser = userId => {
    dispatch(deleteUserRequest(userId));
  };

  adUsers.sort((a, b) => (a.id === connectedUserId ? -1 : 1));

  return (
    <div>
      <DataTable
        rows={adUsers.map(adUser => ({
          ...adUser,
          fullName: `${adUser.firstName || ''} ${adUser.lastName || ''}`,
          roleName: adUser.role ? adUser.role.name : '',
          updateDateFormatted: new Date(adUser.creationDate).toLocaleDateString(
            'fr-FR',
          ),
        }))}
        columns={[
          {
            field: 'fullName',
            headerName: 'Nom complet',
            flex: 1,
            headerAlign: 'center',
            align: 'center',
          },
          {
            field: 'roleName',
            headerName: 'Role',
            flex: 1,
            headerAlign: 'center',
            align: 'center',
          },
          {
            field: 'mail',
            headerName: 'Email',
            flex: 1,
            headerAlign: 'center',
            align: 'center',
          },
          {
            field: 'updateDateFormatted',
            headerName: 'Date de création',
            flex: 1,
            headerAlign: 'center',
            align: 'center',
          },
          {
            field: 'actions',
            type: 'actions',
            headerName: 'Actions',
            flex: 1,
            headerAlign: 'center',
            align: 'center',
            renderCell: rowData => (
              <div>
                {rowData.id === connectedUserId ? (
                  <span
                    style={{
                      fontWeight: 'bold',
                      fontStyle: 'italic',
                      color: '#999',
                    }}
                  >
                    Utilisateur connecté
                  </span>
                ) : (
                  <>
                    <AccessControl module="USER" functionality="UPDATE">
                      <input
                        type="image"
                        src={CHANGE_PROFILE_ICON}
                        className="p-2"
                        width="50px"
                        height="50px"
                        onClick={() => toggleModal(rowData)}
                        title="Modifier le role"
                        style={{ marginRight: '10px' }} // Adding inline style
                      />
                    </AccessControl>
                    <AccessControl module="USER" functionality="UPDATE">
                      <input
                        type="image"
                        src={DELETE_ICON}
                        className="p-2"
                        width="40px"
                        height="50px"
                        onClick={() => DeleteModal(rowData)}
                        title="Supprimer l'utilisateur"
                      />
                    </AccessControl>
                  </>
                )}
              </div>
            ),
          },
        ]}
        fileName={`Liste des utilisateurs, ${new Date().toLocaleString()}`}
        totalElements={liste1.totalElements}
        numberOfElements={liste1.numberOfElements}
        pageable={liste1.pageable}
      />
      <Modal2
        title="Modification d'un role"
        show={showModal}
        handleClose={() => setShowModal(false)}
        className="custom-modal"
        size="md"
      >
        <div style={{ padding: '10px', marginBottom: '20px' }}>
          <p className="text-muted mb-2">
            {`Selectionnez le nouveau role de ${
              selectedUser
                ? `${selectedUser.row.firstName || ''} ${selectedUser.row
                    .lastName || ''}`
                : ''
            }`}{' '}
          </p>

          {roles &&
            roles.content &&
            roles.content.map(role => (
              <div key={role.id} className="form-check mb-2">
                <input
                  type="radio"
                  id={role.id}
                  value={role.id}
                  checked={newRole === role.id}
                  onChange={() => setNewRole(role.id)}
                  className="form-check-input"
                />
                <label htmlFor={role.id} className="form-check-label ml-2">
                  {role.name}
                </label>
              </div>
            ))}
          {/* Pagination controls */}
          {roles && roles.content && (
            <div className="d-flex justify-content-center mt-3 align-items-center">
              <button
                onClick={() => dispatch(loadRolesRequest(roles.number - 1))}
                disabled={roles.first}
                className={`btn btn-primary ${roles.first ? 'disabled' : ''} ${
                  btnStyles.paginationBtn
                }`}
              >
                <i className="fas fa-chevron-left"></i>
              </button>
              <span className="mx-2">{`Page ${roles.number + 1}`}</span>
              <button
                onClick={() => dispatch(loadRolesRequest(roles.number + 1))}
                disabled={roles.last}
                className={`btn btn-primary ${roles.last ? 'disabled' : ''} ${
                  btnStyles.paginationBtn
                }`}
              >
                <i className="fas fa-chevron-right"></i>
              </button>
            </div>
          )}
        </div>
        <div className="d-flex justify-content-end mt-3">
          <button
            type="button"
            className={`btn-style outlined mr-2`}
            onClick={() => setShowModal(false)}
          >
            Annuler
          </button>
          <button
            type="submit"
            className={`btn-style primary`}
            onClick={() => handleRoleChange(selectedUser, newRole)}
          >
            Confirmer
          </button>
        </div>
      </Modal2>

      {showDeleteModal && (
        <Modal2
          title={`Confirmation de suppression`}
          size="md"
          show={showDeleteModal}
          handleClose={() => setShowDeleteModal(false)}
          className="custom-modal"
        >
          <div style={{ padding: '20px' }}>
            <p>
              {' '}
              Voulez-vous vraiment supprimer l'utilisateur{' '}
              {selectedUser && selectedUser.row.fullName} ?
            </p>
            <div className="d-flex justify-content-end mt-4">
              <button
                type="button"
                className={`btn-style outlinded mr-2`}
                onClick={() => setShowDeleteModal(false)}
              >
                Annuler
              </button>
              <button
                type="button"
                className={`btn btn-danger`}
                onClick={() => handleDeleteUser(selectedUser.id)}
              >
                Confirmer
              </button>
            </div>
          </div>
        </Modal2>
      )}
    </div>
  );
};

ListAdUsers.propTypes = {
  adUsers: PropTypes.array.isRequired,
  activePage: PropTypes.number.isRequired,
  roles: PropTypes.object.isRequired,
};

export default ListAdUsers;
