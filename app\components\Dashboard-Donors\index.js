import React, { useEffect } from 'react';
import { Container, Typography, Paper, Box, Grid, CircularProgress } from '@mui/material';
import HomePage from '../../containers/HomePage';
import { createStructuredSelector } from 'reselect';
import { makeSelectDonorsDashboard, makeSelectDonorsDashboardLoading, makeSelectDonorsDashboardError, makeSelectDonorsDashboardSuccess } from '../Dashboard-General/selector';
import { useDispatch, useSelector } from 'react-redux';
import { useInjectReducer, useInjectSaga } from 'redux-injectors';
import generalDashboardReducer from '../Dashboard-General/reducer';
import generalDashboardSaga from '../Dashboard-General/saga';
import { fetchDonorsDashboard } from '../Dashboard-General/actions';
import AnimatedLineChart from '../charts/AnimatedLineChart';
import DonorStatusChart from '../charts/DonorStatusChart';

import ShowChartIcon from '@mui/icons-material/ShowChart';
import PersonIcon from '@mui/icons-material/Person';
import { PieCard, pieColors } from 'components/charts/PieCard';
import { AnimatedBarChart } from 'components/charts/AnimatedBarChart';

const selectDonorsDashboard = createStructuredSelector({
  data: makeSelectDonorsDashboard,
  loading: makeSelectDonorsDashboardLoading,
  error: makeSelectDonorsDashboardError,
  success: makeSelectDonorsDashboardSuccess,
});

export default function DashboardDonors() {
  const dispatch = useDispatch();
  useInjectReducer({ key: 'dashboardAll', reducer: generalDashboardReducer });
  useInjectSaga({ key: 'dashboardAll', saga: generalDashboardSaga });

  const { data, loading, error, success } = useSelector(selectDonorsDashboard);

  const donorsByRegion = data.donorsByRegion;
  const donorsByMonth = data.donorsByMonth;
  const donorsByType = data.donorsByType;
  const donorsByStatus = data.donorsByStatus || { actif: 0, inactif: 0 };

  const statusData = [
    {
      status: 'actif',
      title: 'Actif',
      value: donorsByStatus.actif,
    },
    {
      status: 'inactif',
      title: 'Inactif',
      value: donorsByStatus.inactif,
    },
  ];

  useEffect(() => {
    dispatch(fetchDonorsDashboard());
  }, [dispatch]);

  useEffect(() => {
    if (success) {
      console.log(data);
    }
  }, [success, data]);

  return (
    <>
      <HomePage />
      {loading && (
          <Box
            sx={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              zIndex: 1,
              borderRadius: '20px'
            }}
          >
            <CircularProgress sx={{ color: 'black' }} />
          </Box>
        )}
      {!loading && data && data.donorsByMonth && (
        <>
          <Container maxWidth="xl" sx={{ mt: 2}}>
            <Grid container spacing={2}>
              <Grid item xs={12} md={4}>
                <DonorStatusChart
                  data={statusData}
                  icon={<PersonIcon />}
                  title="Répartition des donateurs par statut"
                  activeColor={pieColors.info}
                  inactiveColor={pieColors.primary}
                  valueLabel="donateurs"
                  activeStatus="actif"
                />
              </Grid>
                <Grid item xs={12} md={8}>
                <AnimatedBarChart
                  title="Répartition géographique des donateurs (par ville)"
                  data={Object.values(donorsByRegion)}
                  labels={Object.keys(donorsByRegion)}
                  icon={<ShowChartIcon />}
                  colors={[pieColors.info,pieColors.error]}
                />
                </Grid>
              </Grid>
              <Grid container spacing={2} sx={{ mt: 2 }}>
                <Grid item xs={12} md={6}>
                <AnimatedLineChart
                  title="Évolution mensuelle des donateurs"
                  data={data.donorsByMonth}
                  icon={<ShowChartIcon />}
                  colors={[pieColors.info]}
                />
              </Grid>
                <Grid item xs={12} md={6}>
                <PieCard
                  title="Répartition des donateurs par type"
                  data={Object.values(donorsByType)}
                  labels={Object.keys(donorsByType).map(key => key.charAt(0).toUpperCase() + key.slice(1))}
                  icon={<ShowChartIcon />}
                  colors={[pieColors.error, pieColors.info, pieColors.primary]}
                />
                </Grid>
            </Grid>
          </Container>
        </>
      )}
    </>
  );
} 