import React, { useEffect, useState } from 'react';
import { Box, Typography, useTheme } from '@mui/material';
import { styled, keyframes } from '@mui/material/styles';

export const pieColors = {
  primary: '#4E79A7',
  secondary: '#F28E2B',
  success: '#59A14F',
  error: '#E15759',
  warning: '#EDC948',
  info: '#00C49F',
  purple: '#B07AA1',
  pink: '#FF9DA7',
  brown: '#9C755F',
  gray: '#BAB0AC',
  black: '#000000'
};

const growAnimation = keyframes`
  from {
    transform: rotate(-90deg) scale(0);
  }
  to {
    transform: rotate(-90deg) scale(1);
  }
`;

const CardWrapper = styled(Box)(({ theme }) => ({
  padding: theme.spacing(2),
  height: '100%',
  display: 'flex',
  flexDirection: 'column',
  background: 'linear-gradient(145deg, #ffffff, #f0f0f0)',
  borderRadius: '12px',
  boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
  transition: 'transform 0.3s ease, box-shadow 0.3s ease',
  '&:hover': {
    transform: 'translateY(-4px)',
    boxShadow: '0 8px 30px rgba(0, 0, 0, 0.12)',
  },
}));

const HeaderWrapper = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  gap: theme.spacing(1),
  marginBottom: theme.spacing(2),
}));

const ChartWrapper = styled(Box)({
  position: 'relative',
  width: '100%',
  height: '250px',
  display: 'flex',
  justifyContent: 'center',
  alignItems: 'center',
  marginBottom: '24px',
});

const LegendContainer = styled(Box)({
  display: 'flex',
  flexWrap: 'wrap',
  justifyContent: 'center',
  gap: '8px',
  marginTop: 'auto',
  padding: '16px 8px',
  maxHeight: '120px',
  overflowY: 'auto',
  '&::-webkit-scrollbar': {
    width: '6px',
  },
  '&::-webkit-scrollbar-track': {
    background: '#f1f1f1',
    borderRadius: '10px',
  },
  '&::-webkit-scrollbar-thumb': {
    background: '#888',
    borderRadius: '10px',
    '&:hover': {
      background: '#555',
    },
  },
});

const LegendItem = styled(Box)(({ theme, isSelected }) => ({
  display: 'flex',
  alignItems: 'center',
  gap: '8px',
  padding: '4px 8px',
  borderRadius: '6px',
  background: 'rgba(255, 255, 255, 0.9)',
  boxShadow: '0 2px 8px rgba(0, 0, 0, 0.05)',
  transition: 'all 0.2s ease',
  cursor: 'pointer',
  opacity: isSelected ? 1 : 0.3,
  minWidth: '120px',
  maxWidth: '180px',
  flexBasis: 'calc(33.333% - 8px)',
  '&:hover': {
    transform: 'scale(1.02)',
    opacity: 1,
  },
}));

const ColorBox = styled(Box)(({ color }) => ({
  width: '16px',
  height: '16px',
  backgroundColor: color,
  borderRadius: '4px',
  boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
}));

const PieSegment = styled(Box)(({ startAngle, endAngle, color, delay, isSelected }) => ({
  position: 'absolute',
  width: '180px',
  height: '180px',
  borderRadius: '50%',
  background: `conic-gradient(
    from ${startAngle}deg,
    ${color} 0deg,
    ${color} ${endAngle - startAngle}deg,
    transparent ${endAngle - startAngle}deg,
    transparent 360deg
  )`,
  transform: 'rotate(-90deg) scale(0)',
  animation: `${growAnimation} 0.5s ease forwards`,
  animationDelay: `${delay}s`,
  transition: 'all 0.3s ease',
  opacity: isSelected ? 1 : 0.3,
  '&:hover': {
    transform: 'rotate(-90deg) scale(1.05)',
  },
}));

const CenterCircle = styled(Box)(({ theme }) => ({
  position: 'absolute',
  width: '120px',
  height: '120px',
  borderRadius: '50%',
  background: 'linear-gradient(145deg, #ffffff, #f0f0f0)',
  display: 'flex',
  flexDirection: 'column',
  justifyContent: 'center',
  alignItems: 'center',
  boxShadow: 'inset 0 2px 4px rgba(0, 0, 0, 0.1)',
  opacity: 0,
  animation: 'fadeIn 0.5s ease forwards',
  animationDelay: '0.5s',
  '@keyframes fadeIn': {
    from: { opacity: 0 },
    to: { opacity: 1 },
  },
}));

const ValueDisplay = styled(Box)(({ theme, color }) => {
  const isDark = theme.palette.getContrastText(color) === '#FFFFFF';
  return {
    position: 'absolute',
    right: '20px',
    top: '50%',
    transform: 'translateY(-50%)',
    padding: theme.spacing(1.5),
    borderRadius: '8px',
    backgroundColor: color,
    color: isDark ? '#FFFFFF' : '#000000',
    minWidth: '80px',
    textAlign: 'center',
    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
    transition: 'all 0.3s ease',
    opacity: 0,
    animation: 'fadeIn 0.3s ease forwards',
    '@keyframes fadeIn': {
      from: { opacity: 0 },
      to: { opacity: 1 },
    },
  };
});

export function PieCard({ 
  title, 
  icon, 
  data, 
  labels, 
  colors = [
    pieColors.primary,
    pieColors.secondary,
    pieColors.error,
    pieColors.info,
    pieColors.success,
    pieColors.warning,
    pieColors.purple,
    pieColors.pink,
    pieColors.brown,
    pieColors.gray,
  ],
  showTotal = true
}) {
  const theme = useTheme();
  const [selectedIndex, setSelectedIndex] = useState(null);
  const total = data.reduce((sum, value) => sum + value, 0);
  
  // Calculate cumulative angles for each segment
  const segments = data.map((value, index) => {
    const percentage = (value / total) * 100;
    const startAngle = index === 0 ? 0 : 
      data.slice(0, index).reduce((sum, val) => sum + (val / total) * 360, 0);
    const endAngle = startAngle + (value / total) * 360;
    
    return {
      startAngle,
      endAngle,
      percentage,
      color: colors[index % colors.length],
      label: labels[index],
      value
    };
  });

  const handleLegendClick = (index) => {
    setSelectedIndex(selectedIndex === index ? null : index);
  };
  
  return (
    <CardWrapper>
      <HeaderWrapper>
        {icon}
        <Typography variant="h6" color="primary">
          {title}
        </Typography>
      </HeaderWrapper>

      <ChartWrapper>
        {segments.map((segment, index) => (
          <PieSegment
            key={index}
            startAngle={segment.startAngle}
            endAngle={segment.endAngle}
            color={segment.color}
            delay={index * 0.1}
            isSelected={selectedIndex === null || selectedIndex === index}
          />
        ))}
        {showTotal && (
          <CenterCircle>
            <Typography variant="h6" color="primary" sx={{ fontWeight: 600 }}>
              {total.toLocaleString()}
            </Typography>
            <Typography variant="caption" color="text.secondary">
              Total
            </Typography>
          </CenterCircle>
        )}
        {selectedIndex !== null && (
          <ValueDisplay color={segments[selectedIndex].color}>
            <Typography variant="subtitle1" color="white"
             sx={{ fontWeight: 600 }}>
              {`${segments[selectedIndex].value.toLocaleString()}/${total.toLocaleString()}`}
            </Typography>
          </ValueDisplay>
        )}
      </ChartWrapper>

      <LegendContainer>
        {segments.map((segment, index) => (
          <LegendItem 
            key={index}
            onClick={() => handleLegendClick(index)}
            isSelected={selectedIndex === null || selectedIndex === index}
          >
            <ColorBox color={segment.color} />
            <Box>
              <Typography variant="subtitle2"  sx={{ fontWeight: 600 }}>
                {segment.label}
              </Typography>
              <Typography variant="caption" color="text.secondary">
                {`${segment.percentage.toFixed(1)}%`}
              </Typography>
            </Box>
          </LegendItem>
        ))}
      </LegendContainer>
    </CardWrapper>
  );
} 