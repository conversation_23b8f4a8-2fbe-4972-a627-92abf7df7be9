import { date, mixed, object, ref, string } from 'yup';
import {
  invalidString,
  maxDate,
  maxString,
  minString,
  requiredString,
} from 'containers/Common/ValidationSchemas/ErrorMessages';
import {
  arabicRegex,
  arabicRegexForAddress,
  phoneRegex,
} from 'containers/Common/Scripts/Regex';

const minAge = 18;
const eighteenYearsAgo = new Date();
eighteenYearsAgo.setFullYear(eighteenYearsAgo.getFullYear() - minAge);

const deathValidation = {
  deathDate: date()
    .nullable(true)
    .test('deathDate_test', (value, { createError, path, resolve }) => {
      const birthDate = resolve(ref('birthDate'));
      const isDeceased = resolve(ref('deceased'));
      if (!value && isDeceased === true) {
        return createError({
          message: requiredString('Date du décès'),
          path,
        });
      }
      if (value && value > new Date()) {
        return createError({
          message: maxDate('Date du décès'),
          path,
        });
      }
      if (value && birthDate && value <= birthDate) {
        return createError({
          message:
            'La date du décès doit être ultérieure à la date de naissance',
          path,
        });
      }
      return true;
    }),
  deathReason: string().nullable(true),
};

export const validationSchema = object({
  familyRelationship: object().shape({
    id: string().required(requiredString('Lien de parenté')),
  }),
  tutorStartDate: mixed().when('tutor', {
    is: true,
    then: date().required(requiredString('Date début')),
  }),
  tutorEndDate: mixed().when('tutor', {
    is: true,
    then: date().min(
      ref('tutorStartDate'),
      'Date fin doit être supérieure à la date début',
    ),
  }),

  newTutorStartDate: mixed().when('hasNewTutor', {
    is: true,
    then: date().required(requiredString('Date début')),
  }),

  newTutorEndDate: mixed().when('hasNewTutor', {
    is: true,
    then: date().min(
      ref('newTutorStartDate'),
      'Date fin doit être supérieure à la date début',
    ),
  }),

  newTutorId: string().when('hasNewTutor', {
    is: true,
    then: string().required(requiredString('Nouveau tuteur')),
  }),

  person: object().shape({
    lastName: string()
      .required(requiredString('Nom'))
      .min(3, minString('Nom', 3))
      .matches(
        /^[a-zA-ZÀ-ÖØ-öø-ÿ\s-]+$/,
        invalidString('Nom', 'veuillez entrer un nom valide'),
      )
      .max(20, maxString('Nom', 20)),
    lastNameAr: string()
      .required(requiredString('Nom arabe'))
      .min(3, minString('Nom arabe', 3))
      .max(20, maxString('Nom arabe', 20))
      .matches(
        arabicRegex,
        invalidString('Nom arabe', 'veuillez entrer le nom en arabe'),
      ),
    firstName: string()
      .required(requiredString('Prénom'))
      .min(3, minString('Prénom', 3))
      .matches(
        /^[a-zA-ZÀ-ÖØ-öø-ÿ\s-]+$/,
        invalidString('Prénom', 'veuillez entrer un prénom valide'),
      )
      .max(20, maxString('Prénom', 20)),
    firstNameAr: string()
      .required(requiredString('Prénom arabe'))
      .min(3, minString('Prénom arabe', 3))
      .max(20, maxString('Prénom arabe', 20))
      .matches(
        arabicRegex,
        invalidString('Prénom arabe', 'veuillez entrer le prénom en arabe'),
      ),
    email: string().email(
      invalidString('Email', 'veuillez entrer un email valide'),
    ),
    birthDate: date().when('istutor', {
      is: true,
      then: date()
        .typeError(requiredString('Date de naissance requise'))
        .required('Date de naissance est requise pour le tuteur')
        .max(new Date(), maxDate('Date de naissance'))
        .test(
          'major_test',
          'Le tuteur doit être majeur (18 ans ou plus)',
          function(value) {
            if (value) {
              const today = new Date();
              const majorDate = new Date(value);
              majorDate.setFullYear(majorDate.getFullYear() + 18);
              return majorDate <= today;
            }
            return true;
          },
        ),
      otherwise: date()
        .typeError(requiredString('Date de naissance'))
        .max(new Date(), maxDate('Date de naissance')),
    }),
    // if his birthdate is more than 18 years old, so the identity code  and type of identity are required
    typeIdentity: object().when('istutor', {
      is: true,
      then: object().shape({
        id: string().required('Type d’identité requis pour le tuteur'),
      }),
      otherwise: object().nullable(), // Non requis si non tuteur
    }),
    identityCode: string().when('istutor', {
      is: true,
      then: string().required('Identifiant requis pour le tuteur'),
      otherwise: string().notRequired(), // Non requis si non tuteur
    }),

    phoneNumber: string().matches(
      phoneRegex,
      invalidString(
        'Numéro',
        'veuillez entrer un numéro de 10 chiffres seulement',
      ),
    ),
    addressAr: string().matches(
      arabicRegexForAddress,
      invalidString(
        'Adresse arabe',
        "veuillez entrer l'adresse en arabe ([0-9] et ،)",
      ),
    ),

    schoolLevelType: string().when('educated', {
      is: true,
      then: string().required(requiredString('Type de niveau scolaire')),
    }),
    // the same for schoolLevel
    schoolLevel: object().when('educated', {
      is: true,
      then: object().shape({
        id: string().required(requiredString('Niveau scolaire')),
      }),
    }),
    deceased: mixed(),
    ...deathValidation,
  }),
});
