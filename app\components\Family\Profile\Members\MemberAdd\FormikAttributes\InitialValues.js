import { PROFILE_PICTURE } from 'containers/Common/RequiredElement/Icons';

export const initialValues = {
  regions: '',
  cities: '',
  familyRelationship: '',
  tutor: '',
  generalComment: '',
  newTutorId: '',
  hasNewTutor: false,
  newTutorStartDate: '',
  newTutorEndDate: '',
  tutorStartDate: '',
  tutorEndDate: '',
  externalIncomes: [],
  person: {
    istutor: false,
    uploadedPicture: PROFILE_PICTURE,
    picture: [],
    firstName: '',
    lastName: '',
    firstNameAr: '',
    lastNameAr: '',
    sex: '',
    email: '',
    phoneNumber: '',
    address: '',
    addressAr: '',
    birthDate: '',
    deceased: false,
    educated: false,
    schoolLevelType: '',
    schoolName: '',
    deathDate: '',
    deathReason: '',
    identityCode: '',
    typeIdentity: {
      id: 1,
    },
    profession: {
      id: '',
    },
    deathReasonSelected: {
      id: '',
    },
    schoolLevel: {
      id: '',
    },
    country: {
      id: 149,
    },
    region: {
      id: '',
    },
    city: {
      id: '',
    },
  },
};
