import {
  LOAD_RELEVE_DONOR,
  LOAD_RELEVE_DONOR_ERROR,
  LOAD_RELEVE_DONOR_SUCCESS,
  LOAD_CANAL_DONATIONS,
  LOAD_CANAL_DONATIONS_SUCCESS,
  LOAD_CANAL_DONATIONS_ERROR,
} from './constants';

export function loadReleveDonor(id) {
  return {
    type: LOAD_RELEVE_DONOR,
    id,
  };
}

export function releveDonorLoaded(releves) {
  return {
    type: LOAD_RELEVE_DONOR_SUCCESS,
    releves,
  };
}

export function releveDonorLoadingError(error) {
  return {
    type: LOAD_RELEVE_DONOR_ERROR,
    error,
  };
}

export function loadCanalDonations(search) {
  return {
    type: LOAD_CANAL_DONATIONS,
    search,
  };
}

export function canalDonationsLoaded(canalDonations) {
  return {
    type: LOAD_CANAL_DONATIONS_SUCCESS,
    canalDonations,
  };
}

export function canalDonationsLoadingError(error) {
  return {
    type: LOAD_CANAL_DONATIONS_ERROR,
    error,
  };
}
