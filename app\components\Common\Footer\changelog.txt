[Version 1.1.43] - 12/05/2025
[Version 1.1.42] - 02/05/2025
[Version 1.1.41] - 25/04/2025
[Version 1.1.40] - 18/04/2024
[Version 1.1.39] - 09/04/2024
[Version 1.1.38] - 13/01/2024
[Version 1.1.37] - 13/01/2024
[Version 1.1.36] - 13/01/2024
[Version 1.1.35] - 13/01/2024
[Version 1.1.34] - 13/01/2024
[Version 1.1.33] - 13/01/2024
[Version 1.1.32] - 13/01/2024
[Version 1.1.31] - 13/01/2024
[Version 1.1.30] - 13/01/2024
[Version 1.1.29] - 30/12/2024
[Version 1.1.28] - 23/12/2024
[Version 1.1.27] - 16/12/2024
[Version 1.1.26] - 09/12/2024
[Version 1.1.25] - 02/12/2024
[Version 1.1.24] - 25/11/2024
[Version 1.1.23] - 19/11/2024
[Version 1.1.22] - 28/10/2024
- Corriger les Retours client.@<PERSON>
[Version 1.1.21] - 14/10/2024

### Fonctionnalités Ajoutées
- Finaliser la nouvelle version du module Donation. @Ahmed
- Implémenter la première version du sous-module Journal dans le module Service. @Ahmed
- Implémentation des nouvelles données de référentiel. @Ahmed
- Finaliser le module Service. @Hanane
- Poursuite du développement du module Campagne (Aide complémentaire).@Hanane
- Finaliser le module Bénéficiaire. @Soumaya

[Version 1.1.20] - 07/10/2024

### Fonctionnalités Ajoutées
- Finaliser le module de Famille.@Ahmed
- Implémenter la première version du module Service.@Hanane
- Finaliser le module Béneficiaire.@Soumaya

[Version 1.1.19] - 23/09/2024

### Fonctionnalités Ajoutées
- Gestion de la nouvelle version du module Donation.@Ahmed
- Gestion du module Aide complémentaire(Campagne).@Hanane
- Gestion du module Caisse.@Soumaya

[Version 1.1.18] - 09/09/2024

### Fonctionnalités Ajoutées
- Ajouter liste béneficiaires Archivé. @Soumaya
- Ajouter l'opération d'archivage et de rejet d'un béneficiaire.@Ahmed
- Ajouter l'envoi de notification par email lors de la demande de compléter un bénéficiaire.@Ahmed
- Implémentation du module Candidats.@Hanane
- Ajout de la fonctionnalité de création d'un nouveau candidat, intégrant un composant de traduction automatique.@Hanane
- Mise en place du circuit de validation des candidats, incluant les traitements nécessaires : validation, demande de complément et rejet.@Hanane
- Gestion des profils des bénéficiaires et des candidats archivés.@Hanane
### Fonctionnalités Modifiées
- Ajouter un module de gestion des zones.@Soumaya
- Ajouter un module de gestion des assistants.@Soumaya
- Modifier la liste des bénéficiaires pour ajouter les actifs et ceux en attente de prise en charge.@Ahmed
- Modifier l'opération d'impression de la fiche bénéficiaire selon le statut.@Ahmed
- Modifier l'opération d'export pour l'adapter aux statuts.@Ahmed

### Fonctionnalités Corrigées
-
[Version 1.1.16] - 15/08/2024

### Fonctionnalités Ajoutées
- Ajouter le nouveau logo dans l'application. @Ahmed
- Ajouter un module de gestion des zones.@Ahmed
- Ajouter un module de gestion des assistants.@Ahmed
- Créer un lien entre le bénéficiaire et le module de gestion des zones.@Ahmed
### Fonctionnalités Modifiées
- Améliorer l'affichage de l'utilisateur connecté.@Ahmed
### Fonctionnalités Corrigées
-
[Version 1.1.15] - 05/08/2024

### Fonctionnalités Ajoutées
-
### Fonctionnalités Modifiées
-
### Fonctionnalités Corrigées
-
[Version 1.1.14] - 02/08/2024

### Fonctionnalités Ajoutées
- Ajout des attributs code, nom en arabe et nom en anglais pour toutes les entités de référentiel.@Ahmed @Hanane

### Fonctionnalités Modifiées
- Modification des fonctionnalités de création et de modification pour générer automatiquement le code pour toutes les entités de référentiel(cote Backend).@Ahmed @Hanane
- Modification de la partie de référentiel pour afficher et contrôler (création et mise à jour) les nouveaux attributs(cote Frontend).@Ahmed @Hanane
- Modification de la fonctionnalité d'impression pour sélectionner au début la langue de la fiche, puis imprimer.@Ahmed @Hanane
### Fonctionnalités Corrigées
-

[Version 1.1.13] - 22/07/2024

### Fonctionnalités Ajoutées
- Ajout de la notification dans la fiche de module Donateur.@Ahmed

### Fonctionnalités Modifiées
- Modification des notifications du module Bénéficiaire pour inclure des informations sur les entités touchées lors d’un ajout ou d’une modification, ainsi qu'un lien pour diriger vers cette entité.@Ahmed
- Amélioration du processus utilisé dans l’opération d’export(Dans tous les modules principale).@Ahmed

### Fonctionnalités Corrigées
- Correction du processus de récupération de la date de dernière connexion.@Ahmed
- Correction des bugs et des erreurs trouvés dans le formulaire principal d’ajout ou de modification d’un bénéficiaire.@Ahmed



[Version 1.1.12] - 15/07/2024

### Fonctionnalités Ajoutées
- Fonctionnalité d'exportation globale ou basée sur les résultats de filtre dans les principaux modules (résultat sous forme de fichier Excel). @Ahmed
- Fonctionnalité d'ajout d'un indicateur affichant le nombre de lignes disponibles dans les tables principales.@Hanane

### Fonctionnalités Modifiées
- Modifier le processus d'ajout ou de modification dans le module « Profil » pour accepter la création de profils avec les mêmes droits.@Ahmed

### Fonctionnalités Corrigées
- Correction continue des anomalies dépendant des modules « Prise en charge » et « Donateur ». @Soumaya

[Version 1.1.11] - 01/07/2024

### Fonctionnalités Ajoutées
-

### Fonctionnalités Modifiées


### Fonctionnalités Corrigées
- Correction des anomalies détectées.



------

[Version 1.1.10] - 13/06/2024

### Fonctionnalités Ajoutées
-

### Fonctionnalités Modifiées


### Fonctionnalités Corrigées
- Correction des anomalies détectées.



------

[Version 1.1.9] - 3/06/2024

### Fonctionnalités Ajoutées
- Implémentation du Backend pour le module Suivie des assistants (AGR) notamment la partie du Zonning. @Ahmed et @Hanane

### Fonctionnalités Modifiées


### Fonctionnalités Corrigées
- Correction des anomalies détectées dans le module Donateur. @Ahmed et @Hanane
- Correction de l'affichage des dates d'audit en nouveau format. @Hanane


------

[Version 1.1.8] - 25/05/2024

### Fonctionnalités Ajoutées

### Fonctionnalités Modifiées
- Modifier la partie des "commentaires historiques" pour les nouveaux cas d'utilisation.@Soumaya
- Amélioration du processus de notification par e-mail pour les nouveaux cas d'utilisation afin d'accélérer l'envoi en le réalisant en arrière-plan.@Ahmed
- Amélioration du processus de notification par e-mail pour les rappels des dates limites en fonction de status.@Hanane
- Poursuite de développement dans la page d'accueil.@Soumaya


### Fonctionnalités Corrigées


------

[Version 1.1.7] - 12/05/2024

### Fonctionnalités Ajoutées
- Ajout de la fonctionnalité d'impression dans le module donateur. @Ahmed
- Mise en place d'un système de cache pour la majorité des fonctionnalités utilisateur, telles que la récupération de l'utilisateur connecté, sa modification et son ajout. @Ahmed
- Ajout de la fonctionnalité d'impression des informations des  bénéficiaires. @Hanane
- Intégrer un tableau pour visualiser l'historique des commentaires concernant les sous-modules "Action".@Soumaya

### Fonctionnalités Modifiées
- Ajout de la colonne des noms arabes pour les modules donateurs, bénéficiaires, donations et prise en charge. @Ahmed
- Amélioration de l'implémentation du cache.@Hanane
- Enrichirles ensembles de données existants.@Soumaya
- Poursuivre l'élaboration de la page d'accueil.@Soumaya


### Fonctionnalités Corrigées


------

[Version 1.1.6] - 13/05/2024

### Fonctionnalités Ajoutées
- Ajouter la fonctionnalité d'envoi d'email lors de la création ou la modification d'une action.@Ahmed
- Ajouter la fonctionnalité de déconnexion (Log out).@Ahmed
- Mise en place du module de suivi des actions.@Hanane
- Ajout d'un filtre pour le module de suivi des actions.@Hanane
- Mise en place de la fonctionnalité d'envoi d'emails de rappel pour les actions non effectuées, avec un délai d'un jour avant l'échéance.@Hanane
- Mise en place de la fonctionnalité permettant de vider le cache depuis l'interface utilisateur.@Hanane
- Ajouter la fonctionnalité d'impression dans le module donateur.@Ahmed

### Fonctionnalités Modifiées
- Modifier le formulaire d'ajout pour afficher le message d'erreur dans le pop-up de formulaire à la place des listes globales.@Ahmed
- Ajout de la colonne des noms des tuteurs arabes pour la liste des Familles.@Hanane
- Modification des statuts des actions au niveau du référentiel.@Hanane
- Ajouter du systeme de cache dans la majorité des fontionnalité des utilisateur comme le get de utilisateur connecter, sa modification et lors de son ajout.@Ahmed
- Ajout de la colonne des noms arabes pour les modules donateurs, beneficiaire, donation, et prise en charge.@Ahmed

### Fonctionnalités Corrigées


------

[Version 1.1.5] - 29/04/2024

### Fonctionnalités Ajoutées
- Ajout de la fonctionnalité d'ajout d'utilisateur dans l'application avec une vérification d'existence dans la base de données Azure.@Ahmed
- Création d'un filtre générique comprenant des champs dynamiques, avec une fonctionnalité de recherche automatique activée après un délai spécifié..@Ahmed
- Application du filtre générique aux modules DONATEUR, UTILISATEUR, DONATION et PROFILE.@Ahmed
- Mettre en place les filtres pour les modules familles, audits et bénéficiaires.@Hanane
- Création d'un Header et NavBar personnalisés.@Hanane
- Intégration du mécanisme de mise en cache dans les différents services.@Hanane
- Développement  d'une page dédiée à la piste d'audit, offrant une vision globale du suivi des audits et des modifications effectuées.@Hanane
- Développerla Page d'Accueil. @Soumaya
- Établirla connexion entre bénéficiaire et membre famille. @Soumaya
- Intégrer un filtre dans le module de prise en charge. @Soumaya
------
### Fonctionnalités Modifiées
- Amélioration de la page d'ajout d'un profil avec un nouveau design.@Ahmed
- Optimiserla qualité du jeu de données pourle module Famille. @Soumaya
- Améliorerla fonctionnalité de gestion des correspondances. @Soumaya


### Fonctionnalités Corrigées
- Transition vers l'utilisation de scripts SQL gérés par Flyway pour les opérations sur la base de données.@Ahmed
-  Afficher une fenêtre contextuelle qui arrête un utilisateur s'il essaie de se connecter à l'application sans être enregistré dans le système Almobadara meme s'il existe dans la base de données Azure.@Ahmed


[Version 1.1.4] - 15/04/2024

### Fonctionnalités Ajoutées
- Poursuivre le développement de la gestion des accès des utilisateurs en incluant la création automatique d'un MasterAdmin qui est affecté au premier utilisateur connecté, ainsi qu'un Profil par Défaut qui est assigné aux autres utilisateurs chargés dans la base de données. @Ahmed
- Organiser les types d'erreurs des acces aux utilisateurs selon leur code et type.@Ahmed
- Intégration de la fonctionnalité d'audit à travers tous les modules et sous-modules.@Hanane
- Implémentation du filtrage pour le module de la famille.@Hanane
- Implémentation du filtrage pour les modules DONATION et UTILISATEUR. @Ahmed
- Implémentation du module de correspondance. @Soumaya


### Fonctionnalités Modifiées

- Utiliser des énumérations lors de l'annotation au lieu de chaînes de caractères.@Ahmed
- Améliorerla fonctionnalité d'affichage de photo de profile dans le module bénéficiaire. @Soumaya
- Afficher10 entités de données au lieu de 5 dans les modules d'application. @Soumaya

### Fonctionnalités Corrigées
- Corriger du style du Footer. @Soumaya

------

[Version 1.1.3] - 01/04/2024

### Fonctionnalités Ajoutées
- Personnalisation du nom et logo de l'application.@Hanane
- gestion de Correspondence.@Soumaya
- Gestion des accès utilisateur dans les deux côtés front et back .@Ahmed
- Gestion des profils utilisateur.@Ahmed
### Fonctionnalités Modifiées
- Amelioration dans le composant de sélection des donateurs.@Ahmed
- Personnalisation des noms des fichiers exportés selon les codes et dates pour chaque module.@Hanane
- Révision de l'algorithme de calcul du temps d'exécution avec généralisation de la fonction pour tous les services.@Soumaya
- Intégration d'un jeu de données de 500 éléments pourles modules Donation et Prise en charge.@Soumaya

### Fonctionnalités Corrigées
- corriger l'ajoute d'un beneficiare.@Soumaya
------

[Version 1.1.2] - 18/03/2024

### Fonctionnalités Ajoutées
- Mis en place de la gestion des rôles des utilisateurs et de leurs accès à l'application. Pour le moment, il existe le rôle USER, qui permet uniquement la lecture, et le rôle ADMIN, qui peut modifier les données de l'application.@Ahmed
- Unification des tables dans toute l'application : Mise en place du composant DataGrid de Material-UI pour toutes les tables de l'application.@Hanane
- Personnalisation des textes et des libellés utilisés dans les composants DataGrid et Pagination pour qu'ils soient en français.@Hanane
- Intégration de la fonctionnalité de recherche permettant la recherche des éléments spécifiques par texte dans les composants liés aux pays, régions et villes.@Hanane
- Utilisation du composant Pagination fourni par Material-UI pour uniformiser la pagination à travers toutes les tables de l'application.@Hanane
- Ajouter aux logs les utilisateurs, la durée, l’état de passage de la fonctionnalité (OK/KO). @Soumaya

### Fonctionnalités Modifiées

- Amelioration dans le composant de sélection des donateurs => ne pas afficher de donateurs avant qu'une recherche soit lancée ou un choix effectué et l'ajout de la pagination.@Ahmed
- Finalisation du module Famille : Amélioration de la logique permettant au boutton "Precedent" de jouer également le rôle de modification du membre precedent.@Hanane
- Augmenter le jeu de données à 1160 éléments dans le module bénéficiaire et 300 éléments pour donateurs. @Soumaya
- Modifier les logs des services et Controller @Soumaya
- Réduction de la durée des consultations de page pour les bénéficiaires. @Soumaya

### Fonctionnalités Corrigées

- Corriger la partie de photo de profil pour les entités DONATEUR et BÉNÉFICIAIRE.@Ahmed
- Correction des libellés du formulaire "Actions" pour une meilleure précision et clarté.@Hanane

[Version 1.1.1] - 05/03/2024

### Fonctionnalités Ajoutées
- Ajout du fichier de journal des modifications pour améliorer la traçabilité et le suivi des tâches dans le projet. @Ahmed
- Ajout d'un pied de page contenant la version actuelle du projet et un lien vers le journal des modifications. @Ahmed
- Ajout des sous modules note et action dans le module donation @Ahmed
- Ajout des sous modules note et action dans le module takenInCharge @Soumaya
- Améliorer la modification dans le module takenInCharge @Soumaya
- Implémentation du sous-module "Actions" dans le module "Famille" @Hanane
- Intégration d'une API open-source permettant de récupérer des données sur les pays, les régions et les villes @Hanane
- Traitement des données récupérées de l'API pour les rendre utilisables dans l'application, sauvegarde dans la base de données et présentation dans l'interface utilisateur. @Hanane
- Ajouter un jeu de données dans l'application afin de faciliter les tests fonctionnels et d'évaluer sa capacité. @Soumaya

### Fonctionnalités Modifiées
- Séparation des fonctionnalités de création et d'enregistrement d'une famille ainsi que l'ajout et la modification des membres d'une famille existante @Hanane

### Fonctionnalités Corrigées
- Correction de la hiérarchie d'enregistrement des documents dans Minio pour tous les modules.@Ahmed
- Récupération des informations relatives aux pays, régions et villes dans le formulaire de modification des informations d'un membre @Hanane
- Affichage de la date au format DD/MM/YYYY dans tous les composants de l'application.@Ahmed

[Version 1.1.0] - 19/02/2024

### Fonctionnalités Ajoutées
------
### Fonctionnalités Modifiées

- S'occuper des fonctionnalités du module Prise en charge @Soumaya
- S'occuper des fonctionnalités du module Famille @Hanane
- S'occuper des fonctionnalités du module Donation @Ahmed

### Fonctionnalités Corrigées
------
