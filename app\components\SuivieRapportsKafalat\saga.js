import { call, put, takeLatest } from 'redux-saga/effects';
import request from 'utils/request';
import {
  GET_LIST_RAPPORT,
  GET_LIST_RAPPORT_SUCCESS,
  GET_LIST_RAPPORT_ERROR,
  VIEW_RAPPORT,
  NOTIFY_ASSISTANT,
  GET_LIST_DONOR,
  VIEW_RAPPORT_WITH_DONOR,
  ADD_AGENDA_RAPPORT_AUTOMATIC,
} from './constants';
import {
  getListRapportSuccess,
  getListRapportError,
  rapportViewed,
  rapportViewingError,
  notifyAssistantSuccess,
  notifyAssistantError,
  getListDonorSuccess,
  getListDonorError,
  rapportWithDonorViewed,
  rapportWithDonorViewingError,
  agendaRapportAutomaticAdded,
  agendaRapportAutomaticAddingError,
} from './actions';
import { openBlobInNewTab } from 'utils/utilFuncs/blobUtils';

export function* getAgendaRapportsSaga({ pageNumber, filters }) {
  try {
    let url = `/agenda-rapports/admin?page=${pageNumber}`;

    const params = new URLSearchParams();

    if (filters) {
      const {
        beneficiaryCode,
        beneficiaryName,
        numberRapport,
        reportStatus,
        plannedDateStart,
        plannedDateEnd,
        validationDateStart,
        validationDateEnd,
      } = filters;

      if (beneficiaryCode) {
        params.append('beneficiaryCode', beneficiaryCode);
      }
      if (beneficiaryName) {
        params.append('beneficiaryName', beneficiaryName);
      }
      if (numberRapport) {
        params.append('numberRapport', numberRapport);
      }
      if (reportStatus) {
        params.append('reportStatus', reportStatus);
      }
      if (plannedDateStart) {
        params.append('plannedDateStart', plannedDateStart);
      }
      if (plannedDateEnd) {
        params.append('plannedDateEnd', plannedDateEnd);
      }
      if (validationDateStart) {
        params.append('validationDateStart', validationDateStart);
      }
      if (validationDateEnd) {
        params.append('validationDateEnd', validationDateEnd);
      }
    }

    if (params.toString()) {
      url += `&${params.toString()}`;
    }
    const { data } = yield call(request.get, url);
    yield put(getListRapportSuccess(data));
  } catch (error) {
    yield put(getListRapportError(error));
  }
}

export function* viewRapportSaga({ rapport, language }) {
  try {
    const url = `rapport/view-report/${rapport}/${language}`;
    console.log({ rapportAPICALLED: rapport });
    const { data, headers } = yield call(request.get, url, {
      responseType: 'blob',
    });

    openBlobInNewTab(data, '', headers['content-disposition']);
    yield put(rapportViewed(data));
    return;
  } catch (error) {
    console.error('Error viewing the report:', error);
    yield put(rapportViewingError(error));
  }
}

export function* notifySaga({ rapportId }) {
  try {
    let url = `/rapport/notify-assistant/${rapportId}`;
    yield call(request.post, url);
    yield put(notifyAssistantSuccess({ success: true }));
  } catch (error) {
    yield put(notifyAssistantError({ error: true }));
  }
}

export function* getDonorForBeneficiarySaga({ idBeneficiary }) {
  try {
    let url = `/rapport/beneficiaries/${idBeneficiary}/donors`;

    const { data } = yield call(request.get, url);
    yield put(getListDonorSuccess(data));
  } catch (error) {
    yield put(getListDonorError(error));
  }
}

export function* viewRapportWithDonorSaga({ rapportId, donorId, language }) {
  try {
    const url = `rapport/view-report-with-donor/${rapportId}/${donorId}/${language}`;
    console.log({ rapportAPICALLED: rapportId, donorId, language });
    const { data, headers } = yield call(request.get, url, {
      responseType: 'blob',
    });

    openBlobInNewTab(data, '', headers['content-disposition']);
    yield put(rapportWithDonorViewed(data));
    return;
  } catch (error) {
    console.error('Error viewing the report:', error);
    yield put(rapportWithDonorViewingError(error));
  }
}

export function* planifierRapportsAutomatiquementSaga() {
  try {
    const url = `/agenda-rapports/planifier-automatiquement`;
    const response = yield call(request.post, url);

    yield put(agendaRapportAutomaticAdded(response.data));
  } catch (error) {
    yield put(agendaRapportAutomaticAddingError(error));
  }
}

export default function* agendaRapportSaga() {
  yield takeLatest(GET_LIST_RAPPORT, getAgendaRapportsSaga);
  yield takeLatest(VIEW_RAPPORT, viewRapportSaga);
  yield takeLatest(NOTIFY_ASSISTANT, notifySaga);
  yield takeLatest(GET_LIST_DONOR, getDonorForBeneficiarySaga);
  yield takeLatest(VIEW_RAPPORT_WITH_DONOR, viewRapportWithDonorSaga);
  yield takeLatest(
    ADD_AGENDA_RAPPORT_AUTOMATIC,
    planifierRapportsAutomatiquementSaga,
  );
}
