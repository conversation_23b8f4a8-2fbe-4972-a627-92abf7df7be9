import produce from 'immer';
import {
  ADD_<PERSON><PERSON>COMPLEMENTAIRE,
  ADD_<PERSON>DECOMPLEMENTAIRE_ERROR,
  ADD_<PERSON><PERSON>COMPLEMENTAIRE_RESET,
  ADD_AIDECOMPLEMENTAIRE_SUCCESS,
  DELETE_AIDECOMPLEMENTAIRE,
  <PERSON>LE<PERSON>_AIDECOMPLEMENTAIRE_ERROR,
  DELETE_AIDECOMPLEMENTAIRE_RESET,
  DELE<PERSON>_AIDECO<PERSON>LEMENTAIRE_SUCCESS,
  LOAD_AIDECOMPLEMENTAIRE_TO_ADD,
  LOAD_AI<PERSON>COMPLEMENTAIRE_TO_ADD_ERROR,
  LOAD_AIDECOMPLEMENTAIRE_TO_ADD_SUCCESS,
  LOAD_BENEFICIARIES_WITH_TYPEPRISEENCHARGE,
  LOAD_BENEFICIARIES_WITH_TYPEPRISEENCHARGE_ERROR,
  LOAD_BENEFICIARIES_WITH_TYPEPRISEENCHARGE_SUCCESS,
} from './constants';

export const initialState = {
  loading: false,
  error: false,
  success: false,
  successDelete: false,
  aideComplementaire: false,
  beneficiairesWithTypePriseEnCharge: [],
  loadingBeneficiairesWithTypePrise: false,
  errorBeneficiairesWithTypePrise: false,
  successBeneficiairesWithTypePrise: false,
};

/* eslint-disable default-case, no-param-reassign */
const aideComplementaireReducer = produce((draft, action) => {
  switch (action.type) {
    case ADD_AIDECOMPLEMENTAIRE:
      draft.loading = true;
      draft.error = false;
      draft.success = false;
      break;
    case ADD_AIDECOMPLEMENTAIRE_SUCCESS:
      draft.loading = false;
      draft.error = false;
      draft.success = true;
      break;
    case ADD_AIDECOMPLEMENTAIRE_ERROR:
      draft.loading = false;
      draft.error = action.error;
      break;
    case ADD_AIDECOMPLEMENTAIRE_RESET:
      draft.loading = false;
      draft.error = false;
      draft.success = false;
      draft.aideComplementaire = false;
      break;
    case DELETE_AIDECOMPLEMENTAIRE:
      draft.loading = true;
      draft.error = false;
      draft.success = false;
      draft.successDelete = false;
      break;
    case DELETE_AIDECOMPLEMENTAIRE_SUCCESS:
      draft.loading = false;
      draft.error = false;
      draft.success = false;
      draft.successDelete = true;
      draft.aideComplementaire = action.aideComplementaire;
      break;
    case DELETE_AIDECOMPLEMENTAIRE_RESET:
      draft.loading = false;
      draft.error = false;
      draft.success = false;
      draft.successDelete = false;
      draft.aideComplementaire = false;
      break;
    case DELETE_AIDECOMPLEMENTAIRE_ERROR:
      draft.loading = false;
      draft.error = action.error;
      break;
    case LOAD_AIDECOMPLEMENTAIRE_TO_ADD:
      draft.loading = true;
      draft.error = false;
      draft.aideComplementaire = false;
      break;
    case LOAD_AIDECOMPLEMENTAIRE_TO_ADD_SUCCESS:
      draft.loading = false;
      draft.error = false;
      draft.aideComplementaire = action.aideComplementaire;
      break;
    case LOAD_AIDECOMPLEMENTAIRE_TO_ADD_ERROR:
      draft.loading = false;
      draft.error = action.error;
      break;
    case LOAD_BENEFICIARIES_WITH_TYPEPRISEENCHARGE:
      draft.loadingBeneficiairesWithTypePrise = true;
      draft.errorBeneficiairesWithTypePrise = false;
      draft.beneficiairesWithTypePriseEnCharge =
        initialState.beneficiairesWithTypePriseEnCharge;
      break;
    case LOAD_BENEFICIARIES_WITH_TYPEPRISEENCHARGE_SUCCESS:
      draft.loadingBeneficiairesWithTypePrise = false;
      draft.errorBeneficiairesWithTypePrise = false;
      draft.successBeneficiairesWithTypePrise = true;
      draft.beneficiairesWithTypePriseEnCharge =
        action.beneficiairesWithTypePriseEnCharge;
      break;
    case LOAD_BENEFICIARIES_WITH_TYPEPRISEENCHARGE_ERROR:
      draft.loadingBeneficiairesWithTypePrise = false;
      draft.errorBeneficiairesWithTypePrise = action.error;
      break;
  }
}, initialState);

export default aideComplementaireReducer;
