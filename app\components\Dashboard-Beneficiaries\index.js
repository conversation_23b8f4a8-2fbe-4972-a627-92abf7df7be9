import React, { useEffect } from 'react';
import { Container, Typography, Paper, Box, Grid, CircularProgress } from '@mui/material';
import HomePage from '../../containers/HomePage';
import { createStructuredSelector } from 'reselect';
import { makeSelectBeneficiariesDashboard, makeSelectBeneficiariesDashboardLoading, makeSelectBeneficiariesDashboardError, makeSelectBeneficiariesDashboardSuccess } from '../Dashboard-General/selector';
import { fetchBeneficiariesDashboard } from '../Dashboard-General/actions';
import { useDispatch, useSelector } from 'react-redux';
import { useInjectReducer, useInjectSaga } from 'redux-injectors';
import generalDashboardReducer from '../Dashboard-General/reducer';
import generalDashboardSaga from '../Dashboard-General/saga';
import { PieCard, pieColors } from '../charts/PieCard';
import PieChartIcon from '@mui/icons-material/PieChart';
import PersonIcon from '@mui/icons-material/Person';
import { Animated<PERSON>ar<PERSON><PERSON> } from 'components/charts/AnimatedBarChart';
import <PERSON><PERSON><PERSON><PERSON><PERSON> from 'components/charts/AnimatedLineChart';

const selectBeneficiariesDashboard = createStructuredSelector({
  data: makeSelectBeneficiariesDashboard,
  loading: makeSelectBeneficiariesDashboardLoading,
  error: makeSelectBeneficiariesDashboardError,
  success: makeSelectBeneficiariesDashboardSuccess,
});

export default function DashboardBeneficiaries() {

  const dispatch = useDispatch();
  useInjectReducer({ key: 'dashboardAll', reducer: generalDashboardReducer });
  useInjectSaga({ key: 'dashboardAll', saga: generalDashboardSaga });

  const { data, loading } = useSelector(selectBeneficiariesDashboard);

  const beneficiariesByCategory = data.beneficiariesByCategory;
  const beneficiariesBySex = data.beneficiariesBySex;
  const beneficiariesByStructure = data.beneficiariesByStructure;
  const beneficiariesByType = data.beneficiariesByType;
  const beneficiariesByZone = data.beneficiariesByZone;
  const beneficiaryByMonth = data.beneficiaryByMonth;

  const getStatus = (text) => {
    const statusMap = {
      candidat_initial: 'Pré-candidat',
      candidat_valider_assistance: "Pré-candidat",
      candidat_valider_kafalat: 'Pré-candidat',
      candidat_rejete: 'Pré-candidat',
      candidat_a_completer_par_assistance: 'Pré-candidat',
      candidat_a_completer_par_kafalat: 'Pré-candidat',
      beneficiary_actif: 'Bénéficiaire',
      beneficiary_enattente: 'Bénéficiaire',
      candidat_a_updater: 'Bénéficiaire',
      beneficiaire_rejete: 'Bénéficiaire',
      beneficiary_ancien: 'Bénéficiaire',
    };
    return statusMap[text] || text;
  };

  useEffect(() => {
    dispatch(fetchBeneficiariesDashboard());
  }, [dispatch]);

  return (
    <>
      <HomePage />
      {loading && (
        <Box
          sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            zIndex: 1,
            borderRadius: '20px'
          }}
        >
          <CircularProgress sx={{ color: 'black' }} />
        </Box>
      )}
      {!loading && data && data.beneficiariesByCategory && data.beneficiariesBySex && data.beneficiariesByStructure && data.beneficiariesByType && data.beneficiariesByZone && data.beneficiaryByMonth && (
        <>
          <Container maxWidth="xl" sx={{ mt: 2, mb: 4 }}>
            <Grid container spacing={2}>
              <Grid item xs={12} md={4}>
                <PieCard
                  title="Bénéficiaires par sexe"
                  icon={<PersonIcon />}
                  data={Object.values(beneficiariesBySex || {})}
                  labels={Object.keys(beneficiariesBySex || {}).map(key =>
                    key === 'Homme' ? 'Masculin' :
                    key === 'Femme' ? 'Féminin' :
                    key
                  )}
                  colors={[pieColors.info, pieColors.error, pieColors.primary, pieColors.success, pieColors.warning]}
                />
              </Grid>

              <Grid item xs={12} md={8}>
              <PieCard
                title="Bénéficiaires par Catégorie"
                data={Object.values(beneficiariesByCategory)}
                labels={Object.keys(beneficiariesByCategory)}
                icon={<PieChartIcon color="primary" />}
                colors={[
                  pieColors.info,
                  pieColors.error,
                  pieColors.primary,
                  pieColors.success,
                  pieColors.warning
                ]}
              />
              </Grid>
            </Grid>
            <Grid container spacing={2} sx={{ mt: 2 }}>
            <Grid item xs={12} md={5}>
                <PieCard
                  title="Bénéficiaires par type"
                  icon={<PersonIcon />}
                  data={Object.values(beneficiariesByStructure || {})}
                  labels={Object.keys(beneficiariesByStructure || {}).map(key =>
                    key === 'independent' ? 'Indépendant' :
                    key === 'non_independent' ? 'Membre de famille' :
                    key.charAt(0).toUpperCase() + key.slice(1).replace(/_/g, ' ')
                  )}
                  colors={[pieColors.info, pieColors.error, pieColors.primary, pieColors.success, pieColors.warning]}
                />
              </Grid>
              <Grid item xs={12} md={7}>
                <AnimatedBarChart
                  title="Bénéficiaires par status"
                  data={Object.values(beneficiariesByType)}
                  labels={Object.keys(beneficiariesByType).map(getStatus)}
                  icon={<PieChartIcon color="primary" />}
                  colors={[pieColors.info, pieColors.error, pieColors.primary, pieColors.success, pieColors.warning]}
                />
              </Grid>
            </Grid>
            <Grid container spacing={2} sx={{ mt: 2 }}>
              <Grid item xs={12} md={6}>
                <AnimatedLineChart
                  title="Bénéficiaires par mois"
                  data={beneficiaryByMonth}
                  icon={<PieChartIcon color="primary" />}
                  colors={[pieColors.info, pieColors.error, pieColors.primary, pieColors.success, pieColors.warning]}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <PieCard
                  title="Bénéficiaires par zone"
                  data={Object.values(beneficiariesByZone)}
                  labels={Object.keys(beneficiariesByZone)}
                  icon={<PieChartIcon color="primary" />}
                  colors={[pieColors.info, pieColors.error, pieColors.primary, pieColors.success, pieColors.warning]}
                />
              </Grid>
            </Grid>
          </Container>
        </>
      )}


    </>
  );
}