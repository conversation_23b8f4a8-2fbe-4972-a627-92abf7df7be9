const fs = require('fs');
const { execSync } = require('child_process');

// Define the path to your changelog file
const CHANGELOG_FILE = 'changelog.txt';

// Initialize changelogContent with the content of changelog.txt
let changelogContent = '';
if (fs.existsSync(CHANGELOG_FILE)) {
  changelogContent = fs.readFileSync(CHANGELOG_FILE, 'utf-8');
}

// Get the most recent version from changelog.txt (if exists)
let lastVersion = '';
let versionNumber = 0;
if (changelogContent) {
  // Extract the latest version number from the first line
  const versionMatch = changelogContent.split('\n')[0]?.match(/\[Version (\S+)\]/);
  if (versionMatch) {
    lastVersion = versionMatch[1];
    // Extract the numerical part of the version (e.g., "1.1.41" -> 41)
    const versionParts = lastVersion.split('.');
    versionNumber = parseInt(versionParts[versionParts.length - 1], 10);
  }
}

// Get the commits since the last version
let commits = '';
if (lastVersion) {
  const lastDateMatch = changelogContent.split('\n')[0]?.match(/- (\d{2})\/(\d{2})\/(\d{4})/);
  const lastDate = lastDateMatch ? `${lastDateMatch[3]}-${lastDateMatch[2]}-${lastDateMatch[1]}` : '';
  commits = execSync(`git log --oneline --pretty=format:"%h %an %ad %s" --since="${lastDate}" --date=short`).toString();
} else {
  commits = execSync('git log --oneline --pretty=format:"%h %an %ad %s" --date=short').toString();
}

// Increment the version number (e.g., from 41 to 42)
versionNumber++;

// Generate the new version in 1.1.X format
const newVersion = `1.1.${versionNumber}`;

// Get the current date in dd/mm/yyyy format
const date = new Date().toLocaleDateString('en-GB');  // e.g., "25/04/2025"

// Prepare the changelog entry
let changes = '';
const commitLines = commits.split('\n');
console.log(commitLines)
if(!(commitLines.length===1 && commitLines[0]==='')){
  commitLines.forEach(commit => {
    const [hash, author, dateCommit, ...messageParts] = commit.split(' ');
    const message = messageParts.join(' ');
  
    // Skip commits that are merges (i.e., those with "Merge" in the message)
    if (!message.toLowerCase().includes('merge')) {
      changes += `- ${message} @${author} (${dateCommit})\n`;
    }
  });
  
  // Prepend the new version and changes to the beginning of changelog.txt
  const changelogEntry = `[Version ${newVersion}] - ${date}\n${changes}\n${changelogContent}`;
  fs.writeFileSync(CHANGELOG_FILE, changelogEntry);
  
  console.log(`Changelog updated with new version: ${newVersion}`);
  
}
