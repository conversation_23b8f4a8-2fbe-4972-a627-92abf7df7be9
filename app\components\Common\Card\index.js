import React from 'react';
import moment from 'moment';
import cardStyles from 'Css/card.css';
import {
  DELETE_CARD_ICON,
  EDIT_ICON,
} from 'containers/Common/RequiredElement/Icons';

const formatDate = date => moment(date).format('DD/MM/YYYY');

const Card = props => {
  // let currentDate = moment().format('DD/MM/YYYY');
  const currentDate = new Date();
  currentDate.setHours(0, 0, 0, 0);
  let cardNumber = '-';
  let accountNumber = '-';
  let deliveryDate = '-';
  let expiryDate = '-';
  let cardType = '-';
  let cardStyle = '';
  let hiddenText = cardStyles.hiddenTextRed;
  let hide = false;
  if (props && props.hide) {
    hide = props.hide;
  }

  if (props && props.data) {
    cardNumber = props.data.cardNumber ? props.data.cardNumber : '-';
    accountNumber = props.data.accountNumber ? props.data.accountNumber : '-';
    deliveryDate = props.data.deliveryDate
      ? formatDate(props.data.deliveryDate)
      : '-';
    expiryDate = props.data.expiryDate
      ? formatDate(props.data.expiryDate)
      : '-';
    cardType =
      props.data.cardType && props.data.cardType.name
        ? props.data.cardType.name
        : '-';
    const expiryDate2 = new Date(props.data.expiryDate);
    const diff = currentDate.getTime() - expiryDate2.getTime();
    if (diff >= 0) {
      cardStyle = cardStyles.expiredCard;
    } else if (props.data.status) {
      if (props.data.status == 'activated') {
        cardStyle = cardStyles.activatedCard;
        hiddenText = cardStyles.hiddenTextGreen;
      } else if (props.data.status == 'deactivated') {
        cardStyle = cardStyles.expiredCard;
      }
    }
  }

  return (
    <div className={cardStyle}>
      <div className="d-flex justify-content-between mb-3">
        <div className={hiddenText}>xxxxx xxxxx</div>
        <div className={cardStyles.cardTutor}>{props.tutorName}</div>
        <div className="d-flex align-items-center"></div>

        {hide === false && (
          <div className="d-flex align-items-center">
            {props.showDeleteIcon ? (
              <div className="mr-4">
                <input
                  type="image"
                  src={EDIT_ICON}
                  width="24px"
                  height="24px"
                  onClick={() => {
                    props.handleActualBankCard(props.data);
                    props.handleShowModal();
                    props.handleTutorId
                      ? props.handleTutorId(props.tutorId)
                      : null;
                  }}
                />
              </div>
            ) : null}
            {props.showDeleteIcon ? (
              <div>
                <input
                  type="image"
                  src={DELETE_CARD_ICON}
                  width="18px"
                  height="18px"
                  onClick={() => {
                    props.handleActualBankCard(props.data);
                    props.handleShowForDeleteModal();
                    props.handleTutorId
                      ? props.handleTutorId(props.tutorId)
                      : null;
                  }}
                />
              </div>
            ) : null}
          </div>
        )}
      </div>

      <div className="d-flex justify-content-between mr-3">
        <div className="d-flex justify-content-start">
          <div
            className={`d-flex flex-column bd-highlight mb-1 ${cardStyles.divLabels}`}
          >
            <p>N° de Carte :&nbsp;</p>
            <p>N° de Compte :&nbsp;</p>
          </div>
          <div
            className={`d-flex flex-column bd-highlight mb-1 ${cardStyles.divData}`}
          >
            <p>{cardNumber}</p>
            <p>{accountNumber}</p>
          </div>
        </div>

        <div className="d-flex justify-content-start">
          <div
            className={`d-flex flex-column bd-highlight mb-1 ${cardStyles.divLabels}`}
          >
            <p>Date livraison :&nbsp;</p>
            <p>Date expiration :&nbsp;</p>
          </div>
          <div
            className={`d-flex flex-column bd-highlight mb-1 ${cardStyles.divData}`}
          >
            <p>{deliveryDate}</p>
            <p>{expiryDate}</p>
          </div>
        </div>

        <div className="d-flex justify-content-start">
          <div
            className={`d-flex flex-column bd-highlight mb-1 ${cardStyles.divLabels}`}
          >
            <p>Type de carte :&nbsp;</p>
            <p>Total envoyé : </p>
          </div>
          <div
            className={`d-flex flex-column bd-highlight mb-1 ${cardStyles.divData}`}
          >
            <p>{cardType}</p>
            <p>00.00 DH</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Card;
