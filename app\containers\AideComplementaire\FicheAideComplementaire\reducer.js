import produce from 'immer';
import {
  CLOS<PERSON>_AIDE_COMPLEMENTAIRE_FAILURE,
  CLOSE_AIDE_COMPLEMENTAIRE_REQUEST,
  CLOSE_AIDE_COMPLEMENTAIRE_RESET,
  CLOSE_AIDE_COMPLEMENTAIRE_SUCCESS,
  EXECUTE_AIDE_COMPLEMENTAIRE_FAILURE,
  EXECUTE_AIDE_COMPLEMENTAIRE_REQUEST,
  EXECUTE_AIDE_COMPLEMENTAIRE_RESET,
  EXECUTE_AIDE_COMPLEMENTAIRE_SUCCESS,
  UNEXECUTE_AIDE_COMPLEMENTAIRE_FAILURE,
  UNEXECUTE_AIDE_COMPLEMENTAIRE_REQUEST,
  UNEXECUTE_AIDE_COMPLEMENTAIRE_RESET,
  UNEXECUTE_AIDE_COMPLEMENTAIRE_SUCCESS,
  FETCH_BUDGET_LINES_FAILURE,
  FETCH_BUDGET_LINES_REQUEST,
  FETCH_BUDGET_LINES_SUCCESS,
  GET_GROUPE_BENEFICIARIES_FAILURE,
  GET_GROUPE_BENEFICIARIES_REQUEST,
  GET_GROUPE_BENEFICIARIES_SUCCESS,
  GET_AIDE_BENEFICIARIES_FAILURE,
  GET_AIDE_BENEFICIARIES_REQUEST,
  GET_AIDE_BENEFICIARIES_SUCCESS,
  LOAD_AIDECOMPLEMENTAIRE,
  LOAD_AIDECOMPLEMENTAIRE_ERROR,
  LOAD_AIDECOMPLEMENTAIRE_SUCCESS,
  LOAD_BENEFICIARY_FOR_AIDECOMPLEMENTAIRE,
  LOAD_BENEFICIARY_FOR_AIDECOMPLEMENTAIRE_SUCCESS,
  LOAD_BENEFICIARY_FOR_AIDECOMPLEMENTAIREE_ERROR,
  LOAD_DONATION,
  LOAD_DONATION_ERROR,
  LOAD_DONATION_SUCCESS,
  LOAD_DONOR_FOR_AIDECOMPLEMENTAIRE,
  LOAD_DONOR_FOR_AIDECOMPLEMENTAIRE_SUCCESS,
  LOAD_DONOR_FOR_AIDECOMPLEMENTAIREE_ERROR,
  PROCESS_DONORS_BENEFICIARIES_ERROR,
  PROCESS_DONORS_BENEFICIARIES_REQUEST,
  PROCESS_DONORS_BENEFICIARIES_RESET,
  PROCESS_DONORS_BENEFICIARIES_SUCCESS,
  PROCESS_DONORS_ERROR,
  PROCESS_DONORS_REQUEST,
  PROCESS_DONORS_RESET,
  PROCESS_DONORS_SUCCESS,
  PUSH_DOCUMENT_DONATION,
  PUSH_NOTE_DONATION,
  PUSH_PRODUCT_DONATION,
  REMOVE_BENEFICIARY_FAILURE,
  REMOVE_BENEFICIARY_REQUEST,
  REMOVE_BENEFICIARY_RESET,
  REMOVE_BENEFICIARY_SUCCESS,
  REMOVE_DOCUMENT_DONATION,
  REMOVE_DONOR_FAILURE,
  REMOVE_DONOR_REQUEST,
  REMOVE_DONOR_RESET,
  REMOVE_DONOR_SUCCESS,
  REMOVE_NOTE_DONATION,
  REMOVE_PRODUCT_DONATION,
  RESERVE_ALL_BUDGET_LINES_FAILURE,
  RESERVE_ALL_BUDGET_LINES_REQUEST,
  RESERVE_ALL_BUDGET_LINES_RESET,
  RESERVE_ALL_BUDGET_LINES_SUCCESS,
  UPDATE_DOCUMENT_DONATION,
  UPDATE_MONTANT_BENEFICIARY_FAILURE,
  UPDATE_MONTANT_BENEFICIARY_REQUEST,
  UPDATE_MONTANT_BENEFICIARY_RESET,
  UPDATE_MONTANT_BENEFICIARY_SUCCESS,
  UPDATE_MONTANT_FOR_GROUP_FAILURE,
  UPDATE_MONTANT_FOR_GROUP_REQUEST,
  UPDATE_MONTANT_FOR_GROUP_RESET,
  UPDATE_MONTANT_FOR_GROUP_SUCCESS,
  UPDATE_MONTANT_RESERVED_BUDGETLINE_FAILURE,
  UPDATE_MONTANT_RESERVED_BUDGETLINE_REQUEST,
  UPDATE_MONTANT_RESERVED_BUDGETLINE_RESET,
  UPDATE_MONTANT_RESERVED_BUDGETLINE_SUCCESS,
  UPDATE_NOTE_DONATION,
  UPDATE_PRODUCT_DONATION,
  UPDATE_STATUT_RESERAVATION_BUDGETLINE_FAILURE,
  UPDATE_STATUT_RESERAVATION_BUDGETLINE_REQUEST,
  UPDATE_STATUT_RESERAVATION_BUDGETLINE_RESET,
  UPDATE_STATUT_RESERAVATION_BUDGETLINE_SUCCESS,
  UPDATE_STATUT_VALIDATION_BENEFICIARY_FAILURE,
  UPDATE_STATUT_VALIDATION_BENEFICIARY_REQUEST,
  UPDATE_STATUT_VALIDATION_BENEFICIARY_RESET,
  UPDATE_STATUT_VALIDATION_BENEFICIARY_SUCCESS,
  VALIDATE_ALL_BENEFICIARIES_REQUEST,
  VALIDATE_ALL_BENEFICIARIES_SUCCESS,
  VALIDATE_ALL_BENEFICIARIES_FAILURE,
  VALIDATE_ALL_BENEFICIARIES_RESET,
} from './constants';

export const initialState = {
  loading: false,
  loading2: false,
  error: false,
  donations: false,
  aideComplementaire: false,
  beneficiariesForAideComplementaire: [],
  beneficiaries: [],
  donorsForAideComplementaire: [],
  budgetLines: [],
  success: false,
  proceesBeneficiariesLoading: false,
  proceesBeneficiariesSuccess: false,
  proceesBeneficiariesError: false,

  proceesDonorsLoading: false,
  proceesDonorsSuccess: false,
  proceesDonorsError: false,

  removeBeneficiaryLoading: false,
  removeBeneficiarySuccess: false,
  removeBeneficiaryError: false,

  updateStatutValidationBeneficiaryLoading: false,
  updateStatutValidationBeneficiarySuccess: false,
  updateStatutValidationBeneficiaryError: false,

  updateMontantBeneficiaryLoading: false,
  updateMontantBeneficiarySuccess: false,
  updateMontantBeneficiaryError: false,

  updateMontantGroupLoading: false,
  updateMontantGroupSuccess: false,
  updateMontantGroupError: false,

  updateStatutReservationBudgetLineLoading: false,
  updateStatutReservationBudgetLineSuccess: false,
  updateStatutReservationBudgetLineError: false,

  updateMontantReservedBudgetLineLoading: false,
  updateMontantReservedBudgetLineSuccess: false,
  updateMontantReservedBudgetLineError: false,

  removeDonorLoading: false,
  removeDonorSuccess: false,
  removeDonorError: false,

  executeAideComplementaireLoading: false,
  executeAideComplementaireSuccess: false,
  executeAideComplementaireError: false,

  unexecuteAideComplementaireLoading: false,
  unexecuteAideComplementaireSuccess: false,
  unexecuteAideComplementaireError: false,

  reserveAllBudgetLineLoading: false,
  reserveAllBudgetLineSuccess: false,
  reserveAllBudgetLineError: false,

  closeAideComplementaireLoading: false,
  closeAideComplementaireSuccess: false,
  closeAideComplementaireError: false,

  validateAllBeneficiariesLoading: false,
  validateAllBeneficiariesSuccess: false,
  validateAllBeneficiariesError: false,
  validateAllBeneficiariesMessage: '',
  validateAllBeneficiariesHasFilters: false,

  lastActionMeta: false,

  groupe: false,
  successGroup: false,
};

/* eslint-disable default-case, no-param-reassign */
const aideComplementaireReducer = produce((draft, action) => {
  switch (action.type) {
    case LOAD_DONATION:
      draft.loading = true;
      draft.error = false;
      draft.donation1 = false;
      break;
    case LOAD_DONATION_SUCCESS:
      draft.loading = false;
      draft.error = false;
      draft.donation1 = action.donations;
      break;
    case LOAD_DONATION_ERROR:
      draft.loading = false;
      draft.error = action.error;
      break;

    case LOAD_AIDECOMPLEMENTAIRE:
      draft.loading = true;
      draft.error = false;
      draft.success = false;
      draft.aideComplementaire = false;
      break;
    case LOAD_AIDECOMPLEMENTAIRE_SUCCESS:
      draft.loading = false;
      draft.error = false;
      draft.success = true;
      draft.aideComplementaire = action.aideComplementaire;
      break;
    case LOAD_AIDECOMPLEMENTAIRE_ERROR:
      draft.loading = false;
      draft.success = false;
      draft.error = action.error;
      break;

    case LOAD_BENEFICIARY_FOR_AIDECOMPLEMENTAIRE:
      draft.loading2 = true;
      draft.error = false;
      draft.beneficiariesForAideComplementaire = false;
      break;
    case LOAD_BENEFICIARY_FOR_AIDECOMPLEMENTAIRE_SUCCESS:
      draft.loading2 = false;
      draft.error = false;
      draft.beneficiariesForAideComplementaire =
        action.beneficiariesForAideComplementaire;
      break;
    case LOAD_BENEFICIARY_FOR_AIDECOMPLEMENTAIREE_ERROR:
      draft.loading2 = false;
      draft.error = action.error;
      break;

    /////

    case LOAD_DONOR_FOR_AIDECOMPLEMENTAIRE:
      draft.loading = true;
      draft.error = false;
      draft.donorsForAideComplementaire = false;
      break;
    case LOAD_DONOR_FOR_AIDECOMPLEMENTAIRE_SUCCESS:
      draft.loading = false;
      draft.error = false;
      draft.donorsForAideComplementaire = action.donorsForAideComplementaire;
      break;
    case LOAD_DONOR_FOR_AIDECOMPLEMENTAIREE_ERROR:
      draft.loading = false;
      draft.error = action.error;
      break;

    ///////

    case FETCH_BUDGET_LINES_REQUEST:
      draft.loading = true;
      draft.error = false;
      draft.budgetLines = false;
      break;
    case FETCH_BUDGET_LINES_SUCCESS:
      draft.loading = false;
      draft.error = false;
      draft.budgetLines = action.budgetLines;
      break;
    case FETCH_BUDGET_LINES_FAILURE:
      draft.loading = false;
      draft.error = action.error;
      break;

    //////

    case PROCESS_DONORS_BENEFICIARIES_REQUEST:
      draft.proceesBeneficiariesLoading = true;
      draft.proceesBeneficiariesSuccess =
        initialState.proceesBeneficiariesSuccess;
      draft.proceesBeneficiariesError = initialState.proceesBeneficiariesError;
      break;

    case PROCESS_DONORS_BENEFICIARIES_SUCCESS:
      draft.proceesBeneficiariesLoading =
        initialState.proceesBeneficiariesLoading;
      draft.proceesBeneficiariesSuccess = true;
      draft.proceesBeneficiariesError = initialState.proceesBeneficiariesError;
      break;

    case PROCESS_DONORS_BENEFICIARIES_ERROR:
      draft.proceesBeneficiariesLoading =
        initialState.proceesBeneficiariesLoading;
      draft.proceesBeneficiariesSuccess =
        initialState.proceesBeneficiariesSuccess;
      draft.proceesBeneficiariesError = action.error;
      break;

    case PROCESS_DONORS_BENEFICIARIES_RESET:
      draft.proceesBeneficiariesLoading =
        initialState.proceesBeneficiariesLoading;
      draft.proceesBeneficiariesSuccess =
        initialState.proceesBeneficiariesSuccess;
      draft.proceesBeneficiariesError = initialState.proceesBeneficiariesError;
      break;

    ////

    case PROCESS_DONORS_REQUEST:
      draft.proceesDonorsLoading = true;
      draft.proceesDonorsSuccess = initialState.proceesDonorsSuccess;
      draft.proceesDonorsError = initialState.proceesDonorsError;
      break;

    case PROCESS_DONORS_SUCCESS:
      draft.proceesDonorsLoading = initialState.proceesDonorsLoading;
      draft.proceesDonorsSuccess = true;
      draft.proceesDonorsError = initialState.proceesDonorsError;
      break;

    case PROCESS_DONORS_ERROR:
      draft.proceesDonorsLoading = initialState.proceesDonorsLoading;
      draft.proceesDonorsSuccess = initialState.proceesDonorsSuccess;
      draft.proceesDonorsError = action.error;
      break;

    case PROCESS_DONORS_RESET:
      draft.proceesDonorsLoading = initialState.proceesDonorsLoading;
      draft.proceesDonorsSuccess = initialState.proceesDonorsSuccess;
      draft.proceesDonorsError = initialState.proceesDonorsError;
      break;

    ///////

    case REMOVE_BENEFICIARY_REQUEST:
      draft.removeBeneficiaryLoading = true;
      draft.removeBeneficiarySuccess = initialState.removeBeneficiarySuccess;
      draft.removeBeneficiaryError = initialState.removeBeneficiaryError;
      draft.lastActionMeta = action.meta;
      break;

    case REMOVE_BENEFICIARY_SUCCESS:
      draft.removeBeneficiaryLoading = initialState.removeBeneficiaryLoading;
      draft.removeBeneficiarySuccess = true;
      draft.removeBeneficiaryError = initialState.removeBeneficiaryError;
      break;

    case REMOVE_BENEFICIARY_FAILURE:
      draft.removeBeneficiaryLoading = initialState.removeBeneficiaryLoading;
      draft.removeBeneficiarySuccess = initialState.removeBeneficiarySuccess;
      draft.removeBeneficiaryError = action.error;
      break;

    case REMOVE_BENEFICIARY_RESET:
      draft.removeBeneficiaryLoading = initialState.removeBeneficiaryLoading;
      draft.removeBeneficiarySuccess = initialState.removeBeneficiarySuccess;
      draft.removeBeneficiaryError = initialState.removeBeneficiaryError;
      break;

    ///////
    case UPDATE_STATUT_VALIDATION_BENEFICIARY_REQUEST:
      draft.updateStatutValidationBeneficiaryLoading = true;
      draft.updateStatutValidationBeneficiarySuccess =
        initialState.updateStatutValidationBeneficiarySuccess;
      draft.updateStatutValidationBeneficiaryError =
        initialState.updateStatutValidationBeneficiaryError;
      draft.lastActionMeta = action.meta;
      break;

    case UPDATE_STATUT_VALIDATION_BENEFICIARY_SUCCESS:
      draft.updateStatutValidationBeneficiaryLoading =
        initialState.updateStatutValidationBeneficiaryLoading;
      draft.updateStatutValidationBeneficiarySuccess = true;
      draft.updateStatutValidationBeneficiaryError =
        initialState.updateStatutValidationBeneficiaryError;
      break;

    case UPDATE_STATUT_VALIDATION_BENEFICIARY_FAILURE:
      draft.updateStatutValidationBeneficiaryLoading =
        initialState.updateStatutValidationBeneficiaryLoading;
      draft.updateStatutValidationBeneficiarySuccess =
        initialState.updateStatutValidationBeneficiarySuccess;
      draft.updateStatutValidationBeneficiaryError = action.error;
      break;

    case UPDATE_STATUT_VALIDATION_BENEFICIARY_RESET:
      draft.updateStatutValidationBeneficiaryLoading =
        initialState.updateStatutValidationBeneficiaryLoading;
      draft.updateStatutValidationBeneficiarySuccess =
        initialState.updateStatutValidationBeneficiarySuccess;
      draft.updateStatutValidationBeneficiaryError =
        initialState.updateStatutValidationBeneficiaryError;
      break;

    //////

    case UPDATE_STATUT_RESERAVATION_BUDGETLINE_REQUEST:
      draft.updateStatutReservationBudgetLineLoading = true;
      draft.updateStatutReservationBudgetLineSuccess =
        initialState.updateStatutReservationBudgetLineSuccess;
      draft.updateStatutReservationBudgetLineError =
        initialState.updateStatutReservationBudgetLineError;
      break;

    case UPDATE_STATUT_RESERAVATION_BUDGETLINE_SUCCESS:
      draft.updateStatutReservationBudgetLineLoading =
        initialState.updateStatutReservationBudgetLineLoading;
      draft.updateStatutReservationBudgetLineSuccess = true;
      draft.updateStatutReservationBudgetLineError =
        initialState.updateStatutReservationBudgetLineError;
      break;

    case UPDATE_STATUT_RESERAVATION_BUDGETLINE_FAILURE:
      draft.updateStatutReservationBudgetLineLoading =
        initialState.updateStatutReservationBudgetLineLoading;
      draft.updateStatutReservationBudgetLineSuccess =
        initialState.updateStatutReservationBudgetLineSuccess;
      draft.updateStatutReservationBudgetLineError = action.error;
      break;

    case UPDATE_STATUT_RESERAVATION_BUDGETLINE_RESET:
      draft.updateStatutReservationBudgetLineLoading =
        initialState.updateStatutReservationBudgetLineLoading;
      draft.updateStatutReservationBudgetLineSuccess =
        initialState.updateStatutReservationBudgetLineSuccess;
      draft.updateStatutReservationBudgetLineError =
        initialState.updateStatutReservationBudgetLineError;
      break;

    //////////////

    case RESERVE_ALL_BUDGET_LINES_REQUEST:
      draft.reserveAllBudgetLineLoading = true;
      draft.reserveAllBudgetLineSuccess =
        initialState.reserveAllBudgetLineSuccess;
      draft.reserveAllBudgetLineError = initialState.reserveAllBudgetLineError;
      break;

    case RESERVE_ALL_BUDGET_LINES_SUCCESS:
      draft.reserveAllBudgetLineLoading =
        initialState.reserveAllBudgetLineLoading;
      draft.reserveAllBudgetLineSuccess = true;
      draft.reserveAllBudgetLineError = initialState.reserveAllBudgetLineError;
      break;

    case RESERVE_ALL_BUDGET_LINES_FAILURE:
      draft.reserveAllBudgetLineLoading =
        initialState.reserveAllBudgetLineLoading;
      draft.reserveAllBudgetLineSuccess =
        initialState.reserveAllBudgetLineSuccess;
      draft.reserveAllBudgetLineError = action.error;
      break;

    case RESERVE_ALL_BUDGET_LINES_RESET:
      draft.reserveAllBudgetLineLoading =
        initialState.reserveAllBudgetLineLoading;
      draft.reserveAllBudgetLineSuccess =
        initialState.reserveAllBudgetLineSuccess;
      draft.reserveAllBudgetLineError = initialState.reserveAllBudgetLineError;
      break;

    ////////

    case UPDATE_MONTANT_BENEFICIARY_REQUEST:
      draft.updateMontantBeneficiaryLoading = true;
      draft.updateMontantBeneficiarySuccess =
        initialState.updateMontantBeneficiarySuccess;
      draft.updateMontantBeneficiaryError =
        initialState.updateMontantBeneficiaryError;
      draft.lastActionMeta = action.meta;
      break;

    case UPDATE_MONTANT_BENEFICIARY_SUCCESS:
      draft.updateMontantBeneficiaryLoading =
        initialState.updateMontantBeneficiaryLoading;
      draft.updateMontantBeneficiarySuccess = true;
      draft.updateMontantBeneficiaryError =
        initialState.updateMontantBeneficiaryError;
      break;

    case UPDATE_MONTANT_BENEFICIARY_FAILURE:
      draft.updateMontantBeneficiaryLoading =
        initialState.updateMontantBeneficiaryLoading;
      draft.updateMontantBeneficiarySuccess =
        initialState.updateMontantBeneficiarySuccess;
      draft.updateMontantBeneficiaryError = action.error;
      break;

    case UPDATE_MONTANT_BENEFICIARY_RESET:
      draft.updateMontantBeneficiaryLoading =
        initialState.updateMontantBeneficiaryLoading;
      draft.updateMontantBeneficiarySuccess =
        initialState.updateMontantBeneficiarySuccess;
      draft.updateMontantBeneficiaryError =
        initialState.updateMontantBeneficiaryError;
      break;

    //////////
    case UPDATE_MONTANT_FOR_GROUP_REQUEST:
      draft.updateMontantGroupLoading = true;
      draft.updateMontantGroupSuccess = initialState.updateMontantGroupSuccess;
      draft.updateMontantGroupError = initialState.updateMontantGroupError;
      break;

    case UPDATE_MONTANT_FOR_GROUP_SUCCESS:
      draft.updateMontantGroupLoading = initialState.updateMontantGroupLoading;
      draft.updateMontantGroupSuccess = true;
      draft.updateMontantGroupError = initialState.updateMontantGroupError;
      break;

    case UPDATE_MONTANT_FOR_GROUP_FAILURE:
      draft.updateMontantGroupLoading = initialState.updateMontantGroupLoading;
      draft.updateMontantGroupSuccess = initialState.updateMontantGroupSuccess;
      draft.updateMontantGroupError = action.error;
      break;

    case UPDATE_MONTANT_FOR_GROUP_RESET:
      draft.updateMontantGroupLoading = initialState.updateMontantGroupLoading;
      draft.updateMontantGroupSuccess = initialState.updateMontantGroupSuccess;
      draft.updateMontantGroupError = initialState.updateMontantGroupError;
      break;

    /////////
    case UPDATE_MONTANT_RESERVED_BUDGETLINE_REQUEST:
      draft.updateMontantReservedBudgetLineLoading = true;
      draft.updateMontantReservedBudgetLineSuccess =
        initialState.updateMontantReservedBudgetLineSuccess;
      draft.updateMontantReservedBudgetLineError =
        initialState.updateMontantReservedBudgetLineError;
      break;

    case UPDATE_MONTANT_RESERVED_BUDGETLINE_SUCCESS:
      draft.updateMontantReservedBudgetLineLoading =
        initialState.updateMontantReservedBudgetLineLoading;
      draft.updateMontantReservedBudgetLineSuccess = true;
      draft.updateMontantReservedBudgetLineError =
        initialState.updateMontantReservedBudgetLineError;
      break;

    case UPDATE_MONTANT_RESERVED_BUDGETLINE_FAILURE:
      draft.updateMontantReservedBudgetLineLoading =
        initialState.updateMontantReservedBudgetLineLoading;
      draft.updateMontantReservedBudgetLineSuccess =
        initialState.updateMontantReservedBudgetLineSuccess;
      draft.updateMontantReservedBudgetLineError = action.error;
      break;

    case UPDATE_MONTANT_RESERVED_BUDGETLINE_RESET:
      draft.updateMontantReservedBudgetLineLoading =
        initialState.updateMontantReservedBudgetLineLoading;
      draft.updateMontantReservedBudgetLineSuccess =
        initialState.updateMontantReservedBudgetLineSuccess;
      draft.updateMontantReservedBudgetLineError =
        initialState.updateMontantReservedBudgetLineError;
      break;

    case REMOVE_DONOR_REQUEST:
      draft.removeDonorLoading = true;
      draft.removeDonorSuccess = initialState.removeDonorSuccess;
      draft.removeDonorError = initialState.removeDonorError;
      break;

    case REMOVE_DONOR_SUCCESS:
      draft.removeDonorLoading = initialState.removeDonorLoading;
      draft.removeDonorSuccess = true;
      draft.removeDonorError = initialState.removeDonorError;
      break;

    case REMOVE_DONOR_FAILURE:
      draft.removeDonorLoading = initialState.removeDonorLoading;
      draft.removeDonorSuccess = initialState.removeDonorSuccess;
      draft.removeDonorError = action.error;
      break;

    case REMOVE_DONOR_RESET:
      draft.removeDonorLoading = initialState.removeDonorLoading;
      draft.removeDonorSuccess = initialState.removeDonorSuccess;
      draft.removeDonorError = initialState.removeDonorError;
      break;

    ////////

    case EXECUTE_AIDE_COMPLEMENTAIRE_REQUEST:
      draft.executeAideComplementaireLoading = true;
      draft.executeAideComplementaireSuccess =
        initialState.executeAideComplementaireSuccess;
      draft.executeAideComplementaireError =
        initialState.executeAideComplementaireError;
      break;

    case EXECUTE_AIDE_COMPLEMENTAIRE_SUCCESS:
      draft.executeAideComplementaireLoading =
        initialState.executeAideComplementaireLoading;
      draft.executeAideComplementaireSuccess = true;
      draft.executeAideComplementaireError =
        initialState.executeAideComplementaireError;
      break;

    case EXECUTE_AIDE_COMPLEMENTAIRE_FAILURE:
      draft.executeAideComplementaireLoading =
        initialState.executeAideComplementaireLoading;
      draft.executeAideComplementaireSuccess =
        initialState.executeAideComplementaireSuccess;
      draft.executeAideComplementaireError = action.error;
      break;

    case EXECUTE_AIDE_COMPLEMENTAIRE_RESET:
      draft.executeAideComplementaireLoading =
        initialState.executeAideComplementaireLoading;
      draft.executeAideComplementaireSuccess =
        initialState.executeAideComplementaireSuccess;
      draft.executeAideComplementaireError =
        initialState.executeAideComplementaireError;
      break;


      case UNEXECUTE_AIDE_COMPLEMENTAIRE_REQUEST:
      draft.unexecuteAideComplementaireLoading = true;
      draft.unexecuteAideComplementaireSuccess =
        initialState.unexecuteAideComplementaireSuccess;
      draft.unexecuteAideComplementaireError =
        initialState.unexecuteAideComplementaireError;
      break;

    case UNEXECUTE_AIDE_COMPLEMENTAIRE_SUCCESS:
      draft.unexecuteAideComplementaireLoading =
        initialState.unexecuteAideComplementaireLoading;
      draft.unexecuteAideComplementaireSuccess = true;
      draft.unexecuteAideComplementaireError =
        initialState.unexecuteAideComplementaireError;
      break;

    case UNEXECUTE_AIDE_COMPLEMENTAIRE_FAILURE:
      draft.unexecuteAideComplementaireLoading =
        initialState.unexecuteAideComplementaireLoading;
      draft.unexecuteAideComplementaireSuccess =
        initialState.unexecuteAideComplementaireSuccess;
      draft.unexecuteAideComplementaireError = action.error;
      break;

    case UNEXECUTE_AIDE_COMPLEMENTAIRE_RESET:
      draft.unexecuteAideComplementaireLoading =
        initialState.unexecuteAideComplementaireLoading;
      draft.unexecuteAideComplementaireSuccess =
        initialState.unexecuteAideComplementaireSuccess;
      draft.unexecuteAideComplementaireError =
        initialState.unexecuteAideComplementaireError;
      break;
    //////////

    case CLOSE_AIDE_COMPLEMENTAIRE_REQUEST:
      draft.closeAideComplementaireLoading = true;
      draft.closeAideComplementaireSuccess =
        initialState.closeAideComplementaireSuccess;
      draft.closeAideComplementaireError =
        initialState.closeAideComplementaireError;
      break;

    case CLOSE_AIDE_COMPLEMENTAIRE_SUCCESS:
      draft.closeAideComplementaireLoading =
        initialState.closeAideComplementaireLoading;
      draft.closeAideComplementaireSuccess = true;
      draft.closeAideComplementaireError =
        initialState.closeAideComplementaireError;
      break;

    case CLOSE_AIDE_COMPLEMENTAIRE_FAILURE:
      draft.closeAideComplementaireLoading =
        initialState.closeAideComplementaireLoading;
      draft.closeAideComplementaireSuccess =
        initialState.closeAideComplementaireSuccess;
      draft.closeAideComplementaireError = action.error;
      break;

    case CLOSE_AIDE_COMPLEMENTAIRE_RESET:
      draft.closeAideComplementaireLoading =
        initialState.closeAideComplementaireLoading;
      draft.closeAideComplementaireSuccess =
        initialState.closeAideComplementaireSuccess;
      draft.closeAideComplementaireError =
        initialState.closeAideComplementaireError;
      break;

    ///////

    case VALIDATE_ALL_BENEFICIARIES_REQUEST:
      draft.validateAllBeneficiariesLoading = true;
      draft.validateAllBeneficiariesSuccess = initialState.validateAllBeneficiariesSuccess;
      draft.validateAllBeneficiariesError = initialState.validateAllBeneficiariesError;
      break;

    case VALIDATE_ALL_BENEFICIARIES_SUCCESS:
      draft.validateAllBeneficiariesLoading = initialState.validateAllBeneficiariesLoading;
      draft.validateAllBeneficiariesSuccess = true;
      draft.validateAllBeneficiariesError = initialState.validateAllBeneficiariesError;
      draft.validateAllBeneficiariesMessage = action.successMessage || '';
      draft.validateAllBeneficiariesHasFilters = action.hasFilters || false;
      break;

    case VALIDATE_ALL_BENEFICIARIES_FAILURE:
      draft.validateAllBeneficiariesLoading = initialState.validateAllBeneficiariesLoading;
      draft.validateAllBeneficiariesSuccess = initialState.validateAllBeneficiariesSuccess;
      draft.validateAllBeneficiariesError = action.error;
      break;

    case VALIDATE_ALL_BENEFICIARIES_RESET:
      draft.validateAllBeneficiariesLoading = initialState.validateAllBeneficiariesLoading;
      draft.validateAllBeneficiariesSuccess = initialState.validateAllBeneficiariesSuccess;
      draft.validateAllBeneficiariesError = initialState.validateAllBeneficiariesError;
      draft.validateAllBeneficiariesMessage = initialState.validateAllBeneficiariesMessage;
      draft.validateAllBeneficiariesHasFilters = initialState.validateAllBeneficiariesHasFilters;
      break;

    ///////
    case GET_GROUPE_BENEFICIARIES_REQUEST:
      draft.loading = true;
      draft.error = false;
      draft.successGroup = false;
      draft.groupe = false;
      break;
    case GET_GROUPE_BENEFICIARIES_SUCCESS:
      draft.loading = false;
      draft.error = false;
      draft.successGroup = true;
      draft.groupe = action.groupe;
      break;
    case GET_GROUPE_BENEFICIARIES_FAILURE:
      draft.loading = false;
      draft.error = action.error;
      draft.successGroup = false;
      break;

    //////
    case GET_AIDE_BENEFICIARIES_REQUEST:
      draft.loading = true;
      draft.error = false;
      draft.successBeneficiaries= false;
      draft.beneficiaries = false;
      break;
    case GET_AIDE_BENEFICIARIES_SUCCESS:
      draft.loading = false;
      draft.error = false;
      draft.successBeneficiaries = true;
      draft.beneficiaries = action.beneficiaries;
      break;
    case GET_AIDE_BENEFICIARIES_FAILURE:
      draft.loading = false;
      draft.error = action.error;
      draft.successbeneficiaries = false;
      break;

    //////

    case PUSH_DOCUMENT_DONATION:
      if (draft.donation1) {
        draft.donation1.documentDonations.push(action.document);
      }
      break;
    case UPDATE_DOCUMENT_DONATION:
      if (draft.donation1 && draft.donation1.documentDonations) {
        draft.donation1.documentDonations = draft.donation1.documentDonations.map(
          doc => (doc.id == action.document.id ? { ...action.document } : doc),
        );
      }
      break;
    case REMOVE_DOCUMENT_DONATION:
      if (draft.donation1 && draft.donation1.documentDonations) {
        draft.donation1.documentDonations = draft.donation1.documentDonations.filter(
          el => el.id != action.document.id,
        );
      }
      break;

    case PUSH_NOTE_DONATION:
      draft.donation1.notes.push(action.note);
      break;
    case UPDATE_NOTE_DONATION:
      draft.donation1.notes = draft.donation1.notes.map(note =>
        note.id === action.note.id ? { ...action.note } : note,
      );
      break;
    case REMOVE_NOTE_DONATION:
      draft.donation1.notes = draft.donation1.notes.filter(
        el => el.id !== action.note.id,
      );
      break;

    case PUSH_PRODUCT_DONATION:
      if (draft.donation1) {
        draft.donation1.donationProductNatures.push(action.product);
        if (draft.donation1.donor && draft.donation1.donor.donations) {
          draft.donation1.donor.donations = draft.donation1.donor.donations.map(
            donation => {
              if (donation.donationProductNatures) {
                donation.donationProductNatures = donation.donationProductNatures.map(
                  prod => {
                    if (prod.id == action.product.id) {
                      return action.product;
                    }
                    return prod;
                  },
                );
                donation.value +=
                  action.product.quantity * action.product.unitPrice;
              }
              return donation;
            },
          );
        }
      }
      break;

    case UPDATE_PRODUCT_DONATION:
      if (draft.donation1 && draft.donation1.donationProductNatures) {
        draft.donation1.donationProductNatures = draft.donation1.donationProductNatures.map(
          prod =>
            prod.id === action.product.id ? { ...action.product } : prod,
        );
        const oldDonation = draft.donation1;
        let oldValue = 0;
        if (draft.donation1.donor && draft.donation1.donor.donations) {
          draft.donation1.donor.donations = draft.donation1.donor.donations.map(
            donation => {
              if (donation.id === oldDonation.id) {
                if (donation.donationProductNatures) {
                  donation.donationProductNatures = donation.donationProductNatures.map(
                    prod => {
                      if (prod.id === action.product.id) {
                        oldValue = prod.quantity * prod.unitPrice;
                        return action.product;
                      }
                      return prod;
                    },
                  );
                  donation.value +=
                    action.product.quantity * action.product.unitPrice -
                    oldValue;
                }
              }
              return donation;
            },
          );
        }
      }
      break;

    case REMOVE_PRODUCT_DONATION:
      if (draft.donation1 && draft.donation1.donationProductNatures) {
        draft.donation1.donationProductNatures = draft.donation1.donationProductNatures.filter(
          el => el.id !== action.product.id,
        );
        if (draft.donation1.donor && draft.donation1.donor.donations) {
          draft.donation1.donor.donations = draft.donation1.donor.donations.map(
            donation => {
              if (donation.donationProductNatures) {
                donation.donationProductNatures = donation.donationProductNatures.map(
                  prod => {
                    if (prod.id === action.product.id) {
                      return action.product;
                    }
                    return prod;
                  },
                );
                donation.value -=
                  action.product.quantity * action.product.unitPrice;
              }
              return donation;
            },
          );
        }
      }
      break;
  }
}, initialState);

export default aideComplementaireReducer;
