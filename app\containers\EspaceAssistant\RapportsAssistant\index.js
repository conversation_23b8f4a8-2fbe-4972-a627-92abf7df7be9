import React, { useEffect, useState } from 'react';
import { useLocation } from 'react-router-dom';
import { useSelector, useDispatch } from 'react-redux';
import { useInjectReducer, useInjectSaga } from 'redux-injectors';
import { createStructuredSelector } from 'reselect';

import { Route, useParams } from 'react-router-dom';
import MesRapports from 'components/espaceAssistant/ficheAssistant/MesRapports';
import { Modal } from 'react-bootstrap';
import { CircularProgress } from '@material-ui/core';

import { loadConnectedAssistant } from '../FicheAssistant/actions';
import {
  makeSelectConnectedAssistant,
  makeSelectLoading,
} from '../FicheAssistant/selectors';
import rapportsAssistantReducer from '../FicheAssistant/reducer';
import saga from '../FicheAssistant/saga';
import { useHistory } from 'react-router-dom';
import { Alert } from 'react-bootstrap';

const key = 'assistantFiche';

const omdbSelector = createStructuredSelector({
  connectedAssistant: makeSelectConnectedAssistant,
  loading: makeSelectLoading,
});

export default function RapportsAssistant(props) {
  useInjectReducer({ key, reducer: rapportsAssistantReducer });
  useInjectSaga({ key, saga });
  const { connectedAssistant, loading } = useSelector(omdbSelector);

  console.log('RapportsAssistant - loading:', loading, 'connectedAssistant:', connectedAssistant);
  const history = useHistory();
  const location = useLocation();
  const [showAlert, setShowAlert] = useState(false);
  const [successMessage, setSuccessMessage] = useState('');
  const params = useParams();
  const dispatch = useDispatch();

  useEffect(() => {
    console.log('Loading connected assistant with ID:', params.id);
    dispatch(loadConnectedAssistant(params.id));
  }, []);

  const editHandler = id => {
    history.push({
      pathname: `/assistants/editAssistant/${id}`,
      state: { formEspaceAssistant: true },
    });
  };

  useEffect(() => {
    if (location.state && location.state.successMessage) {
      setShowAlert(true);
      setSuccessMessage(location.state.successMessage);
    }
  }, [location.state]);

  const handleCloseSuccessMessage = () => {
    setShowAlert(false);
    history.replace({ ...props.location, state: null });
  };

  useEffect(() => {
    if (showAlert) {
      const timer = setTimeout(() => {
        handleCloseSuccessMessage();
      }, 4000);
      return () => clearTimeout(timer);
    }
  }, [showAlert]);

  // Add timeout for loading to prevent infinite loading
  useEffect(() => {
    if (loading) {
      const timer = setTimeout(() => {
        console.warn('Loading timeout reached, there might be an issue with the API call');
      }, 10000); // 10 seconds timeout
      return () => clearTimeout(timer);
    }
  }, [loading]);

  return (
    <div>
      <Modal show={loading && !connectedAssistant} centered contentClassName="bg-transparent border-0">
        <div className="d-flex justify-content-center align-items-center">
          <CircularProgress style={{ width: '100px', height: '100px' }} />
        </div>
      </Modal>
        <div className="">
          {showAlert && (
            <Alert
              variant="success"
              onClose={() => handleCloseSuccessMessage()}
              dismissible
            >
              {successMessage}
            </Alert>
          )}
          <Route exact path="/espace-assistant/rapports/:id">
            <MesRapports />
          </Route>
        </div>
    </div>
  )
} 