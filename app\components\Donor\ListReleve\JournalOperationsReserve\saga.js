import { call, put, takeLatest } from 'redux-saga/effects';
import request from 'utils/request';
import {
  releveDonorLoaded,
  releveDonorLoadingError,
  canalDonationsLoaded,
  canalDonationsLoadingError,
} from './actions';
import { LOAD_RELEVE_DONOR, LOAD_CANAL_DONATIONS } from './constants';

export function* loadReleveDonor({ id }) {
  const url = `/donors/${id}/journal-operations-reserve`;
  try {
    const response = yield call(request.get, url);

    yield put(releveDonorLoaded(response.data));
  } catch (error) {
    yield put(releveDonorLoadingError(error));
  }
}

// export function* loadReleveDonor({ id, page, filtres }) {
//   const url = `/donors/${id}/releve-compte?page=${page}`;

//   try {
//     let response;
//     const headers = {
//       'Content-Type': 'application/json',
//       receptionDate: '2024-11-26', // Passer receptionDate dans les headers
//       ...filtres, // Si vous avez des filtres supplémentaires à ajouter
//     };

//     response = yield call(request.get, url, { headers });
//     yield put(releveDonorLoaded(response.data));
//   } catch (error) {
//     yield put(releveDonorLoadingError(error));
//   }
// }

export function* loadCanalDonations({ search }) {
  const url = `/ref/canalDonations`;
  try {
    const { data } = yield call(request.get, url);
    yield put(canalDonationsLoaded(data));
  } catch (error) {
    yield put(canalDonationsLoadingError(error));
  }
}

export default function* omdbSaga() {
  yield takeLatest(LOAD_RELEVE_DONOR, loadReleveDonor);
  yield takeLatest(LOAD_CANAL_DONATIONS, loadCanalDonations);
}
