import React, { useCallback, useEffect, useState } from 'react';
import { Link, useHistory } from 'react-router-dom';
import PropTypes from 'prop-types';
import { useDispatch, useSelector } from 'react-redux';
import AccessControl from 'utils/AccessControl';
import { DELETE_ICON } from 'components/Common/ListIcons/ListIcons';
import {
  deleteDonor,
  deleteDonorReset,
} from 'containers/Donor/DonorProfile/actions';
import Modal2 from 'components/Common/Modal2';
import { useInjectReducer, useInjectSaga } from 'redux-injectors';
import donorReducer from 'containers/Donor/DonorProfile/reducer';
import omdbSaga from 'containers/Donor/DonorProfile/saga';
import { createStructuredSelector } from 'reselect';
import {
  makeSelectDeleteError,
  makeSelectDeleteSuccess,
} from 'containers/Donor/DonorProfile/selectors';
import { removeDonorGlobal } from 'containers/Donor/Donors/actions';
import btnStyles from '../../../Css/button.css';
import DataTable from '../../Common/DataTable';
import styles from '../../../Css/tag.css';
import styled from 'styled-components';
import { Tag as StyledTag } from '../../Common/StyledTags';

const viewIcon = require('images/icons/eye.svg');
const editIcon = require('images/icons/edit.svg');

const key = 'donor';
const key2 = 'donorDelete';

const stateSelector = createStructuredSelector({
  successDelete: makeSelectDeleteSuccess,
  errorDelete: makeSelectDeleteError,
});

export const Donor = ({ donor, viewHandler }) => {
  const [actualEmail, setActualEmail] = useState(donor.email);
  const [actualPhoneNumber, setActualPhoneNumber] = useState(donor.phoneNumber);
  const [formLink, setFormLink] = useState('/donors/edit/physique/');
  const [name, setName] = useState('');
  const [ArabicName, setArabicName] = useState('');

  const dispatch = useDispatch();

  useEffect(() => {
    if (donor.type === 'Moral') {
      if (donor.shortCompany) {
        setName(`${donor.company} - ${donor.shortCompany}`);
      } else {
        setName(donor.company);
      }
      const mainContact = donor.donorContacts.filter(
        donarContact => donarContact.phoneNumber,
      );

      if (mainContact.length > 0) {
        setActualEmail(donor.donorContacts.map(e => e.email));
        setActualPhoneNumber(donor.donorContacts.map(e => e.phoneNumber));
      }

      setFormLink('/donors/edit/moral/');
    } else if (donor.type === 'Anonyme') {
      setName(donor.name);
      setFormLink('/donors/edit/Anonyme/');
    } else {
      setName(`${donor.firstName} ${donor.lastName}`);
      setArabicName(`${donor.firstNameAr} ${donor.lastNameAr}`);
    }
  }, [donor]);

  return (
    <tr key={donor.id}>
      <td style={{ textAlign: 'center' }}>{donor.code}</td>
      <td>{name}</td>
      <td>{ArabicName}</td>
      <td>{donor.type} </td>
      <td>
        {donor.city == null ? (
          <span style={{ textAlign: 'center' }}>-</span>
        ) : (
          donor.city.name
        )}
      </td>
      <td>{actualPhoneNumber}</td>
      <td>{actualEmail}</td>
      <td>{donor.availableBalance} DH</td>
      <td>
        {donor.status == null ? (
          <span>-</span>
        ) : (
          <span
            className={
              donor.status.name == 'actif' ? styles.tagGreen : styles.tagRed
            }
          >
            {donor.status.name}
          </span>
        )}
      </td>
      <td className="p-0">
        <input
          type="image"
          onClick={() => viewHandler(donor.id)}
          className="p-2"
          src={viewIcon}
          width="40px"
          height="40px"
        />
        <AccessControl module="DONOR" functionality="UPDATE">
          <>
            <Link
              to={{
                pathname: `${formLink}${donor.id}`,
              }}
            >
              {' '}
              <input
                type="image"
                src={editIcon}
                className="p-2"
                width="40px"
                height="40px"
              />
            </Link>
          </>
        </AccessControl>
      </td>
    </tr>
  );
};

Donor.propTypes = {
  donor: PropTypes.object.isRequired,
  viewHandler: PropTypes.func.isRequired,
};

export default function DonorForm({
  donors,
  donorsGlobal,
  onSuccessDelete = () => {},
  setShowErrorAlert = () => {},
  setShowSuccessAlert = () => {},
}) {
  const history = useHistory();
  const viewHandler = a => {
    history.push(`/donors/fiche/${a}/info`, { params: a });
  };

  const dispatch = useDispatch();

  useInjectReducer({ key: 'donorProfil', reducer: donorReducer });
  useInjectSaga({ key: 'donorProfil', saga: omdbSaga });
  const { successDelete, errorDelete } = useSelector(stateSelector);

  const [donorToDelete, setDonorToDelete] = useState('');
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [showAlert, setShowAlert] = useState(false);
  const handleCloseForDeleteModal = () => setShowDeleteModal(false);

  const displaySuccessMessage = () => {
    setShowSuccessAlert(true);
  };

  const failedToDelete = useCallback(() => {
    if (errorDelete) {
      setShowAlert(true);
      setShowErrorAlert(errorDelete);
    }
  }, [errorDelete]);

  useEffect(() => {
    errorDelete && failedToDelete();
  }, [errorDelete]);

  useEffect(() => {
    if (successDelete) {
      displaySuccessMessage();
      dispatch(removeDonorGlobal(donorToDelete));
      dispatch(deleteDonorReset());
      onSuccessDelete({}, true);
    }
  }, [successDelete]);

  const [formLink, setFormLink] = useState('/donors/edit/physique/');
  // const [name, setName] = useState('');
  // const [ArabicName, setArabicName] = useState('');
  // const [actualEmail, setActualEmail] = useState('');
  // const [actualPhoneNumber, setActualPhoneNumber] = useState('');
  const [rows, setRows] = useState([]);
  const populateRows = () => {
    setRows(() => {
      return donors.map(donor => {
        let name = '---',
          ArabicName = '---',
          actualEmail = '---',
          actualPhoneNumber = '---';

        if (donor.type === 'Moral') {
          ArabicName = '---'; // No Arabic name for 'Moral' donors

          // Set name to company and shortCompany or company only, fallback to '---'
          if (donor.company) {
            if (donor.shortCompany) {
              name = `${donor.company} - ${donor.shortCompany}`;
            } else {
              name = donor.company;
            }
          } else {
            name = '---';
          }

          // Check if donor has contacts with phone number and email, otherwise fallback to '---'
          const mainContact =
            donor.donorContacts &&
            donor.donorContacts.filter(
              donorContact => donorContact.mainContact,
            );
          if (mainContact && mainContact.length > 0) {
            actualEmail =
              mainContact
                .map(e => e.email)
                .filter(Boolean)
                .join(', ') || '---';
            actualPhoneNumber =
              mainContact
                .map(e => e.phoneNumber)
                .filter(Boolean)
                .join(', ') || '---';
          } else {
            actualEmail = '---';
            actualPhoneNumber = '---';
          }
          setFormLink('/donors/edit/moral/');
        } else if (donor.type === 'Anonyme') {
          name = donor.name ? donor.name : '---';
          setFormLink('/donors/edit/Anonyme/');
        } else {
          // Set firstName and lastName, fallback to '---'
          if (donor.firstName && donor.lastName) {
            name = `${donor.firstName} ${donor.lastName}`;
          } else {
            name = '---';
          }
          // Set Arabic firstNameAr and lastNameAr, fallback to '---'
          if (donor.firstNameAr && donor.lastNameAr) {
            ArabicName = `${donor.firstNameAr} ${donor.lastNameAr}`;
          } else {
            ArabicName = '---';
          }
          if (donor.email) {
            actualEmail = donor.email;
          }
          if (donor.phoneNumber) {
            actualPhoneNumber = donor.phoneNumber;
          }
        }

        return {
          id: donor.id,
          code: donor.code ? donor.code : '---',
          label: donor.label ? donor.label : '---',
          name,
          ArabicName,
          type: donor.type ? donor.type : '---',
          city: donor.city && donor.city.name ? donor.city.name : '---',
          phoneNumber: actualPhoneNumber,
          email: actualEmail,
          availableBalance: donor.availableBalance
            ? donor.availableBalance
            : '---',
          status: donor.status && donor.status.name ? donor.status.name : '---',
          tags: donor.tags || [],
        };
      });
    });
  };

  useEffect(() => {
    populateRows();
    console.log('donors', rows);
  }, [donors]); // Re-run when donors prop changes

  // Styled component for the tags container in the table
  const TagsContainer = styled.div`
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
    max-width: 200px;
  `;

  // Styled component for individual tags in the table
  const TableTag = styled(StyledTag)`
    padding: 2px 8px;
    font-size: 11px;
    white-space: nowrap;
    border-radius: 10px;
  `;

  const columns = [
    {
      field: 'code',
      headerName: 'Code donateur',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
    {
      field: 'name',
      headerName: 'Nom complet',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
    {
      field: 'label',
      headerName: 'Libellé',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
    {
      field: 'ArabicName',
      headerName: 'الاسم الكامل',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },

    {
      field: 'type',
      headerName: 'Type',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
    {
      field: 'city',
      headerName: 'Ville',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
    {
      field: 'phoneNumber',
      headerName: 'Téléphone',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
    {
      field: 'email',
      headerName: 'Email',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
    {
      field: 'availableBalance',
      headerName: 'Solde (DH)',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
    {
      field: 'status',
      headerName: 'Statut',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
      renderCell: params => (
        <span
          className={
            params.row.status === 'actif' ? styles.tagGreen : styles.tagRed
          }
        >
          {params.row.status}
        </span>
      ),
    },
    {
      field: 'tags',
      headerName: 'Tags',
      flex: 1.5,
      headerAlign: 'center',
      align: 'center',
      renderCell: params => {
        const tags = params.row.tags || [];
        return (
          <TagsContainer>
            {tags.length > 0 ? (
              tags.map(tag => (
                <TableTag key={tag.id} color={tag.color || 'cccccc'}>
                  {tag.name}
                </TableTag>
              ))
            ) : (
              <span style={{ color: '#999', fontSize: '12px' }}>Aucun tag</span>
            )}
          </TagsContainer>
        );
      },
    },
    {
      field: 'actions',
      type: 'actions',
      headerName: 'Actions',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
      renderCell: params => (
        <>
          <input
            type="image"
            onClick={() => viewHandler(params.row.id)}
            className="p-2"
            src={viewIcon}
            width="40px"
            height="40px"
            title="consulter"
          />
          <AccessControl module="DONOR" functionality="UPDATE">
            <Link
              to={`/donors/edit/${
                params.row.type === 'Moral'
                  ? 'moral'
                  : params.row.type === 'Anonyme'
                    ? 'Anonyme' // Add this case for anonymous donors
                    : 'physique'
              }/${params.row.id}`}
            >
              {' '}
              <input
                type="image"
                src={editIcon}
                className="p-2"
                width="40px"
                height="40px"
                title="modifier"
              />
            </Link>
          </AccessControl>
          <AccessControl module="DONOR" functionality="DELETE">
            <input
              type="image"
              src={DELETE_ICON}
              className="p-2 "
              width="40px"
              height="40px"
              title="Supprimer"
              onClick={() => {
                setDonorToDelete(params.row);
                setShowDeleteModal(true);
              }}
            />
          </AccessControl>
        </>
      ),
    },
  ];

  return (
    <div>
      <Modal2
        centered
        className="mt-5"
        title="Confirmation de suppression"
        show={showDeleteModal}
        handleClose={handleCloseForDeleteModal}
      >
        <p className="mt-1 mb-5">
          Êtes-vous sûr de vouloir supprimer ce donateur?
        </p>
        <div className="d-flex justify-content-end px-3 my-1">
          <button
            type="button"
            className="mx-2 btn-style outlined"
            onClick={handleCloseForDeleteModal}
          >
            Annuler
          </button>
          <button
            type="submit"
            className="mx-2 btn-style primary"
            onClick={() => {
              // dispatch(deleteDonor(donorToDelete));
              // setDonorToDelete('');
              dispatch(deleteDonor(donorToDelete));
              // setDonorToDelete('');
              handleCloseForDeleteModal();
            }}
          >
            Supprimer
          </button>
        </div>
      </Modal2>
      <DataTable
        rows={rows}
        columns={columns}
        fileName={`Liste des Donateurs , ${new Date().toLocaleString()}`}
        totalElements={donorsGlobal.totalElements}
        numberOfElements={donorsGlobal.numberOfElements}
        pageable={donorsGlobal.pageable}
      />
    </div>
  );
}
