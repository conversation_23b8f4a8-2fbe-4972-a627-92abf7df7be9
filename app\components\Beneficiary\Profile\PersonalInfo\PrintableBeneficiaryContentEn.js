import React, { useEffect, useState } from 'react';
import {
  Container,
  Grid,
  makeStyles,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
} from '@material-ui/core';
import defaultImage from '../../../../images/user.png';
import PrintHeader from '../../../Common/PrintHeader/PrintHeader';
import {
  isBeneficiary,
  isCandidate,
} from 'containers/Beneficiary/BeneficiaryProfile/statutUtils';

const useStyles = makeStyles(theme => ({
  container: {
    padding: '40px',
    fontFamily: "'Roboto', sans-serif",
    backgroundColor: '#f5f5f5',
    color: '#333',
  },
  paper: {
    padding: '40px',
    borderRadius: '15px',
    boxShadow: '0 8px 40px rgba(0, 0, 0, 0.12)',
    backgroundColor: '#ffffff',
    position: 'relative',
    overflow: 'hidden',
    marginBottom: '40px',
    '@media print': {
      '&:not(:first-child)': {
        pageBreakBefore: 'always',
      },
    },
  },
  sectionTitle: {
    marginBottom: '30px',
    color: '#3f51b5',
    textTransform: 'uppercase',
    letterSpacing: '1.5px',
    borderBottom: '2px solid #3f51b5',
    paddingBottom: '5px',
    marginTop: '20px',
  },
  gridItem: {
    marginBottom: '25px',
  },
  tableContainer: {
    marginBottom: '30px',
    pageBreakInside: 'avoid',
  },
  table: {
    minWidth: 650,
    '& th': {
      backgroundColor: '#3f51b5',
      color: '#ffffff',
    },
    '& td, & th': {
      border: '1px solid #e0e0e0',
      padding: '14px',
      fontSize: '16px',
    },
  },
  logo: {
    width: '60px',
    position: 'absolute',
    top: '20px',
    right: '20px',
  },
  beneficiaryPicture: {
    width: '150px',
    height: '150px',
    borderRadius: '50%',
    border: '5px solid #ffffff',
    boxShadow: '0 4px 10px rgba(0, 0, 0, 0.3)',
    marginBottom: '20px',
  },
  section: {
    marginBottom: '50px',
    pageBreakInside: 'avoid',
  },
  infoItem: {
    display: 'flex',
    alignItems: 'center',
    marginBottom: '15px',
  },
  infoLabel: {
    marginRight: '15px',
    fontWeight: 'bold',
    color: '#3f51b5',
    whiteSpace: 'nowrap',
  },
  contactInfo: {
    marginTop: '20px',
  },
  tableTitle: {
    pageBreakInside: 'avoid',
  },
}));

const PrintableBeneficiaryContentEn = React.forwardRef(
  ({ data, family }, ref) => {
    const classes = useStyles();

    if (!data) {
      return null;
    }

    const formatDate = date => new Date(date).toLocaleDateString('en-US');

    const personalInfoItems = [
      { label: 'First Name', value: data.person.firstName },
      { label: 'Last Name', value: data.person.lastName },
      { label: 'Email', value: data.person.email ? data.person.email : '-----' },
      { label: 'Phone Number', value: data.person.phoneNumber ? data.person.phoneNumber : data.phoneNumberFamily ? data.phoneNumberFamily : '---', },
      {
        label: 'Gender',
        value: data.person.sex == 'Homme' ? 'Male' : 'Female',
      },
      { label: 'Identity Code', value: data.person.identityCode ? data.person.identityCode : '-----' },
      { label: 'Address', value: data.person.address ? data.person.address : data.person.addressFamily ? data.person.addressFamily : '-----' },
      { label: 'City', value: 
        data &&
        data.person &&
        data.person.info &&
        data.person.info.region &&
        data.person.info.region.city &&
        data.person.info.region.city.name
        ? data.person.info.region.city.name
        : data &&
        data.cityFamily &&
        data.cityFamily.region &&
        data.cityFamily.region.city &&
        data.cityFamily.region.city.name
        ? data.cityFamily.region.city.name
        : '-----',
      },
      
      { label: 'Country', value:
        data &&
        data.person &&
        data.person.info &&
        data.person.info.region &&
        data.person.info.region.country &&
        data.person.info.region.country.name
        ? data.person.info.region.country.name
        : data &&
        data.cityFamily &&
        data.cityFamily.region &&
        data.cityFamily.region.country &&
        data.cityFamily.region.country.name
        ? data.cityFamily.region.country.name
        : '-----',
      },

      { label: 'Region', value: data &&
        data.person &&
        data.person.info &&
        data.person.info.region &&
        data.person.info.region.name
        ? data.person.info.region.name
        : data &&
        data.cityFamily &&
        data.cityFamily.region &&
        data.cityFamily.region.name
        ? data.cityFamily.region.name
        : '-----',
      },

      { label: 'School Level', value: data.person.schoolLevel.nameEn },
      { label: 'Birth Date', value: formatDate(data.person.birthDate) },
      { label: 'Creation date', value: formatDate(data.createdAt) },
      { label: 'Beneficiary Code', value: data.code },
      { label: 'Independent', value: data.independent ? 'Yes' : 'No' },
      { label: 'Zone', value: `${data.zone.code} - ${data.zone.name}` },
    ];

    const [nameFiche, setNameFiche] = useState('');

    useEffect(() => {
      if (data && data.statut) {
        if (isCandidate(data.statut)) {
          setNameFiche('Candidate Form');
        } else {
          setNameFiche('Beneficiary Form');
        }
      }
    }, [data.statut]);

    return (
      <Container
        ref={ref}
        className={classes.container}
        style={{ backgroundColor: '#ffffff' }}
      >
        <Paper className={classes.paper}>
          <PrintHeader headerText={nameFiche} />
          <div className={classes.section}>
            <Typography
              variant="h5"
              gutterBottom
              className={classes.sectionTitle}
            >
              Personal Information
            </Typography>
            <Grid container spacing={3}>
              <Grid item xs={12} sm={6}>
                {data.person.pictureBase64 ? (
                  <img
                    src={`data:image/png;base64,${atob(
                      data.person.pictureBase64,
                    )}`}
                    alt="Beneficiary"
                    className={classes.beneficiaryPicture}
                  />
                ) : (
                  <img
                    src={defaultImage}
                    alt="Default Beneficiary"
                    className={classes.beneficiaryPicture}
                  />
                )}
              </Grid>
              <Grid container spacing={3}>
                {personalInfoItems.map((item, index) => (
                  <Grid
                    item
                    xs={12}
                    sm={6}
                    key={index}
                    className={classes.gridItem}
                  >
                    <div className={classes.infoItem}>
                      <Typography
                        variant="subtitle1"
                        className={classes.infoLabel}
                      >
                        {item.label}:
                      </Typography>
                      <Typography variant="body1" className={classes.infoValue}>
                        {item.value}
                      </Typography>
                    </div>
                  </Grid>
                ))}
              </Grid>
            </Grid>
          </div>
        </Paper>
        <Paper className={classes.paper}>
          <PrintHeader headerText={nameFiche} />
          <div className={classes.section}>
            <Typography
              variant="h5"
              gutterBottom
              className={classes.sectionTitle}
            >
              Health Information
            </Typography>

            <div className={classes.infoItem}>
              <Typography variant="subtitle1" className={classes.infoLabel}>
                Handicaps (with their cost):
              </Typography>
              <Typography variant="body1" className={classes.infoValue}>
                {data.handicapped && data.handicapped.length > 0
                  ? data.handicapped
                      .map(
                        handicap =>
                          `${handicap.handicapType.nameEn} (${handicap.handicapCost} DH)`,
                      )
                      .join(', ')
                  : '------'}
              </Typography>
            </div>

            <div className={classes.infoItem}>
              <Typography variant="subtitle1" className={classes.infoLabel}>
                Allergies:
              </Typography>
              <Typography variant="body1" className={classes.infoValue}>
                {data.allergies.length > 0
                  ? data.allergies.map(allergy => allergy.nameEn).join(', ')
                  : '-----'}
              </Typography>
            </div>
            <div className={classes.infoItem}>
            <Typography variant="subtitle1" className={classes.infoLabel}>
              Diseases:
            </Typography>
            <Typography variant="body1" className={classes.infoValue}>
              {data.diseases.length > 0
                ? data.diseases.map(disease => disease.nameEn).join(', ')
                : '-----'}
            </Typography>
          </div>
        

            <div className={classes.infoItem}>
              <Typography variant="subtitle1" className={classes.infoLabel}>
                Disease Treatments (with their cost):
              </Typography>
              <Typography variant="body1" className={classes.infoValue}>
                {data.diseaseTreatments.length > 0
                  ? data.diseaseTreatments
                      .map(
                        treatment =>
                          `${treatment.type.nameEn} (${treatment.cost} DH)`,
                      )
                      .join(', ')
                  : '------'}
              </Typography>
            </div>
          </div>
          <div className={classes.section}>
            <Typography
              variant="h5"
              gutterBottom
              className={classes.sectionTitle}
            >
              Education History
            </Typography>
            <TableContainer component={Paper} className={classes.table}>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>School Year</TableCell>
                    <TableCell>School Level</TableCell>
                    <TableCell>School</TableCell>
                    <TableCell>Mark</TableCell>
                    <TableCell>Honor</TableCell>
                    <TableCell>Passed</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {data.educations.length > 0 ? (
                    data.educations.map((education, index) => (
                      <TableRow key={index}>
                        <TableCell>{education.schoolYear.nameEn}</TableCell>
                        <TableCell>{education.schoolLevel.nameEn}</TableCell>
                        <TableCell>{education.schoolName}</TableCell>
                        <TableCell>{education.mark}</TableCell>
                        <TableCell>{education.honor.nameEn}</TableCell>
                        <TableCell>
                          {education.succeed ? 'Oui' : 'Non'}
                        </TableCell>
                      </TableRow>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell colSpan={7} align="center">
                        No education history found
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </TableContainer>
          </div>
          <div className={classes.section}>
            <Typography
              variant="h5"
              gutterBottom
              className={classes.sectionTitle}
            >
              Scholarships
            </Typography>
            <TableContainer component={Paper} className={classes.table}>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Scholarship Name</TableCell>
                    <TableCell>Amount</TableCell>
                    <TableCell>Start Date</TableCell>
                    <TableCell>End Date</TableCell>
                    <TableCell>Periodicity</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {data.scholarshipBeneficiaries.length > 0 ? (
                    data.scholarshipBeneficiaries.map(scholarship => (
                      <TableRow key={scholarship.id}>
                        <TableCell>{scholarship.scholarship.nameEn}</TableCell>
                        <TableCell>{scholarship.amount}</TableCell>
                        <TableCell>
                          {formatDate(scholarship.startDate)}
                        </TableCell>
                        <TableCell>{formatDate(scholarship.endDate)}</TableCell>
                        <TableCell>{scholarship.periodicity}</TableCell>
                      </TableRow>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell colSpan={7} align="center">
                        No scholarships found
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </TableContainer>
          </div>
          {isBeneficiary(data.statut) && (
            <div className={classes.section}>
              <Typography
                variant="h5"
                gutterBottom
                className={classes.sectionTitle}
              >
                Taken In Charge
              </Typography>
              <TableContainer component={Paper} className={classes.table}>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Service</TableCell>
                      <TableCell>Statut</TableCell>
                      <TableCell>Start Date</TableCell>
                      <TableCell>End Date</TableCell>
                      <TableCell>Donor</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {data.takenInChargeBeneficiaries &&
                    data.takenInChargeBeneficiaries.length > 0 ? (
                      data.takenInChargeBeneficiaries.map(takeInCharge => (
                        <TableRow key={takeInCharge.id}>
                          <TableCell>
                            {takeInCharge.takenInCharge.service.nameEn}
                          </TableCell>
                          <TableCell>
                            {takeInCharge.takenInCharge.status.nameEn}
                          </TableCell>
                          <TableCell>
                            {formatDate(takeInCharge.takenInCharge.startDate)}
                          </TableCell>
                          <TableCell>
                            {formatDate(takeInCharge.takenInCharge.endDate)}
                          </TableCell>
                          <TableCell>
                            {takeInCharge.takenInCharge.takenInChargeDonors &&
                            takeInCharge.takenInCharge.takenInChargeDonors
                              .length > 0
                              ? takeInCharge.takenInCharge
                                  .takenInChargeDonors[0].donor
                                ? `${takeInCharge.takenInCharge.takenInChargeDonors[0].donor.firstName} ${takeInCharge.takenInCharge.takenInChargeDonors[0].donor.lastName}`
                                : 'Anonymous Donor'
                              : 'No donor'}
                          </TableCell>
                        </TableRow>
                      ))
                    ) : (
                      <TableRow>
                        <TableCell colSpan={5} align="center">
                          No taken in charge found
                        </TableCell>
                      </TableRow>
                    )}
                  </TableBody>
                </Table>
              </TableContainer>
            </div>
          )}
          {family && family.familyMembers && family.familyMembers.length > 0 && (
            <div className={classes.section}>
              <Typography
                variant="h5"
                gutterBottom
                className={classes.sectionTitle}
              >
                Family Information
              </Typography>
              <TableContainer component={Paper} className={classes.table}>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>First Name</TableCell>
                      <TableCell>Last Name</TableCell>
                      <TableCell>Relationship</TableCell>
                      <TableCell>Date of Birth</TableCell>
                      <TableCell>Tutor</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {family.familyMembers.map((member, index) => (
                      <TableRow key={index}>
                        <TableCell>
                          {member.person ? member.person.firstName : '-'}
                        </TableCell>
                        <TableCell>
                          {member.person ? member.person.lastName : '-'}
                        </TableCell>
                        <TableCell>
                          {member.familyRelationship
                            ? member.familyRelationship.nameEn
                            : '-'}
                        </TableCell>
                        <TableCell>
                          {formatDate(
                            member.person ? member.person.birthDate : null,
                          )}
                        </TableCell>
                        <TableCell>{member.tutor ? 'Yes' : 'No'}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </div>
          )}

          {data.remarqueEn && (
            <div className={classes.section}>
              <Typography
                variant="h5"
                gutterBottom
                className={classes.sectionTitle}
              >
                Remarks
              </Typography>
              <Typography variant="body1">{data.remarqueEn}</Typography>
            </div>
          )}
        </Paper>
      </Container>
    );
  },
);

export default PrintableBeneficiaryContentEn;
