import React, { useEffect, useState } from 'react';
import styles from 'Css/profileList.css';
import btnStyles from 'Css/button.css';
import Modal2 from 'components/Common/Modal2';
import {
  makeSelecProduct,
  makeSelecProductDeleteSuccess,
  makeSelectError,
  makeSelectSuccess,
} from 'containers/Common/SubComponents/ProductForm/selectors';
import { useDispatch, useSelector } from 'react-redux';
import { createStructuredSelector } from 'reselect';
import { useInjectReducer, useInjectSaga } from 'redux-injectors';
import { deleteProductSaga } from 'containers/Common/SubComponents/ProductForm/saga';
import {
  loadDonation,
  pushProductDonation,
  removeProductDonation,
  updateProductDonation,
} from 'containers/Donation/FicheDonation/actions';
import reducer from 'containers/Common/SubComponents/ProductForm/reducer';
import { useParams } from 'react-router-dom';
import {
  deleteProduct,
  resetProduct,
} from 'containers/Common/SubComponents/ProductForm/actions';
import { Alert } from 'react-bootstrap';
import ProductForm from 'containers/Common/SubComponents/ProductForm';
import AccessControl, { isAuthorized } from 'utils/AccessControl';
import {
  DELETE_ICON,
  EDIT_ICON,
  VIEW_ICON,
} from '../../../Common/ListIcons/ListIcons';
import DataTable from '../../../Common/DataTable';
import CustomPagination from '../../../Common/CustomPagination';

const key = 'product';

const emptyValue = <span>-</span>;

const target = 'donation';

const omdbSelector = createStructuredSelector({
  success: makeSelectSuccess,
  error: makeSelectError,
  product: makeSelecProduct,
  successDelete: makeSelecProductDeleteSuccess,
});

export default function Products(props) {
  const dispatch = useDispatch();

  useInjectReducer({ key, reducer });
  useInjectSaga({ key, saga: deleteProductSaga });
  const params = useParams();

  const { success, error, product, successDelete } = useSelector(omdbSelector);
  const [productToEdit, setProductToEdit] = useState('');
  const [message, setMessage] = useState('');
  let errorMessage = null;
  const [show, setShow] = useState(false);
  const [showAlert, setShowAlert] = useState(false);
  const [productToDelete, setProductToDelete] = useState('');
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [productToReadOnly, setProductToReadOnly] = useState({
    title: 'Consulter le produit',
    isRead: false,
    product: null,
  });

  // Define pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const pageSize = 5; // Define page size

  const donation = props.data;
  let donationCode;

  if (donation) {
    donationCode = donation.code;
  }

  const handleClose = () => {
    setShow(false);
    if (productToReadOnly.isRead) {
      setProductToReadOnly({
        title: 'Consulter le produit',
        isRead: false,
        product: null,
      });
    }
  };

  const handleShow = () => {
    setShow(true);
  };

  const handleCloseForDeleteModal = () => setShowDeleteModal(false);

  useEffect(
    () => () => {
      dispatch(resetProduct());
    },
    [dispatch],
  );

  if (error) {
    errorMessage = 'Une erreur est survenue';
  }

  useEffect(() => {
    if (successDelete) {
      setShowAlert(true);
      dispatch(loadDonation(params.id));
      setMessage('Produit supprimé avec succès');
    }
  }, [successDelete, dispatch, product, params.id]);

  useEffect(() => {
    if (success) {
      setShowAlert(true);
      if (productToEdit) {
        setMessage('Produit modifié avec succès');
      } else {
        setMessage('Produit ajouté avec succès');
      }
      setProductToEdit('');
      dispatch(loadDonation(params.id));
    }


  }, [success, dispatch, productToEdit, product, params.id]);


  // close alert after 4  seconds
  useEffect(() => {
    if (showAlert) {
      const timer = setTimeout(() => {
        setShowAlert(false);
      }, 4000);
      return () => clearTimeout(timer);
    }
  }, [showAlert]);

  useEffect(() => {
    if(error) {
      const timer = setTimeout(() => {
        setShowAlert(false);
      }, 4000);
      return () => clearTimeout(timer);
    }
  }, [error]);


  let listProducts = [];
  if (donation) {
    const donationProductsArray = donation.donationProductNatures;
    const ProductSorted = [...donationProductsArray].sort((a, b) =>
      a.id < b.id ? 1 : -1,
    );

    // Implement pagination logic
    const startIndex = (currentPage - 1) * pageSize;
    const endIndex = Math.min(startIndex + pageSize, ProductSorted.length);
    const paginatedProducts = ProductSorted.slice(startIndex, endIndex);

    listProducts = paginatedProducts.map(product => ({
      id: product.id,
      category:
        product.productNature && product.productNature.typeProductNature
          ? product.productNature.typeProductNature.name
          : emptyValue,
      product: product.productNature ? product.productNature.name : emptyValue,
      quantity: product.quantity ? product.quantity : emptyValue,
      unitPrice: product.unitPrice ? `${product.unitPrice} DH` : emptyValue,
      total:
        product.quantity && product.unitPrice
          ? `${product.unitPrice * product.quantity} DH`
          : emptyValue,
      unit: product.productUnit ? product.productUnit.name : emptyValue,
      produit: product,
    }));
  }

  const columns = [
    {
      field: 'category',
      headerName: 'Catégorie',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
    {
      field: 'product',
      headerName: 'Produit',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
    {
      field: 'quantity',
      headerName: 'Quantité',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
    {
      field: 'unitPrice',
      headerName: 'Prix unitaire',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
    {
      field: 'total',
      headerName: 'Total',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
    {
      field: 'unit',
      headerName: 'Unité',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
  ];

  const connectedUser = useSelector(state => state.app.connectedUser);
  const isUpdateAuthorized = isAuthorized(connectedUser, 'DONATION', 'UPDATE');
  if (isUpdateAuthorized) {
    columns.push({
      field: 'actions',
      type: 'actions',
      headerName: 'Actions',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
      renderCell: params => (
        <div>
          <input
            type="image"
            className="p-2"
            src={VIEW_ICON}
            width="40px"
            height="40px"
            title="consulter"
            onClick={() => {
              setProductToReadOnly(prev => ({
                ...prev,
                isRead: true,
                product: params.row.produit,
              }));
              handleShow();
            }}
          />
          <input
            type="image"
            src={EDIT_ICON}
            className="p-2"
            width="40px"
            height="40px"
            title="modifier"
            onClick={() => {
              handleShow();
              setProductToEdit(params.row.produit);
            }}
          />
          <input
            type="image"
            src={DELETE_ICON}
            className="p-2"
            width="40px"
            height="40px"
            title="supprimer"
            onClick={() => {
              setProductToDelete(params.row.produit);
              setShowDeleteModal(true);
            }}
          />
        </div>
      ),
    });
  }

  return (
    <div>
      {showAlert ? (
        <Alert
          className="alert-style"
          variant="success"
          onClose={() => setShowAlert(false)}
          dismissible
        >
          <p>{message}</p>
        </Alert>
      ) : null}

      {error && (
        <Alert
          className="alert-style"
          variant="danger"
          onClose={() => setShowAlert(false)}
          dismissible
        >
          <p>{errorMessage}</p>
        </Alert>
      )}

      <div className={styles.backgroudStyle}>
        <div className={styles.global}>
          <div className={styles.header}>
            <h4>Liste des produits de la donation</h4>
            <AccessControl module="DONATION" functionality="UPDATE">
              <button
                className={btnStyles.addBtnProfile}
                onClick={() => {
                  handleShow();
                  setProductToEdit('');
                }}
              >
                Ajouter
              </button>
            </AccessControl>
          </div>

          <div className={styles.content}>
            <DataTable
              rows={listProducts}
              columns={columns}
              fileName={`Liste des produits de la donation ${donationCode}, ${new Date().toLocaleString()}`}
            />
          </div>

          <div className="justify-content-center my-4">
            {donation && (
              <CustomPagination
                totalCount={Math.ceil(
                  donation.donationProductNatures.length / pageSize,
                )}
                pageSize={pageSize}
                currentPage={currentPage}
                onPageChange={setCurrentPage}
              />
            )}
          </div>
        </div>
      </div>

      <Modal2
        title={
          productToReadOnly.isRead
            ? productToReadOnly.title
            : productToEdit
              ? 'Modifier le produit'
              : 'Ajouter un produit'
        }
        size="lg"
        customWidth="modal-90w"
        show={show}
        handleClose={handleClose}
      >
        <ProductForm
          product={
            productToEdit ||
            (productToReadOnly.product
              ? productToReadOnly.product
              : productToEdit)
          }
          show={show}
          handleClose={handleClose}
          button={productToEdit ? 'Modifier' : 'Ajouter'}
          target={target}
          id={params.id}
          isRead={productToReadOnly.isRead}
        />
      </Modal2>

      <Modal2
        centered
        className="mt-5"
        title="Confirmation de suppression"
        show={showDeleteModal}
        handleClose={handleCloseForDeleteModal}
      >
        <p className="mt-1 mb-5 px-2">
          Êtes-vous sûr de vouloir supprimer ce produit?
        </p>
        <div className="d-flex justify-content-end px-2 my-1">
          <button
            type="button"
            className="btn-style outlined"
            onClick={handleCloseForDeleteModal}
          >
            Annuler
          </button>
          <button
            type="submit"
            className={`ml-3 btn-style primary`}
            onClick={() => {
              dispatch(deleteProduct(productToDelete, target));
              setProductToDelete('');
              handleCloseForDeleteModal();
            }}
          >
            Supprimer
          </button>
        </div>
      </Modal2>
    </div>
  );
}
