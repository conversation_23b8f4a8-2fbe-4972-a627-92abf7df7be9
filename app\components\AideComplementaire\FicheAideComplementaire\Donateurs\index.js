import React, { useEffect, useState } from 'react';
import moment from 'moment';
import styles from 'Css/profileList.css';
import stylesList from 'Css/profileList.css';
import { useDispatch } from 'react-redux';
import { useHistory, useLocation } from 'react-router-dom';
import CustomPagination from '../../../Common/CustomPagination';
import {
  Alert,
  Button,
  Form,
  InputGroup,
  ListGroup,
  Modal,
} from 'react-bootstrap';
import {
  processDonorsRequest,
  processDonorsReset,
  removeBeneficiaryReset,
  removeDonorReset,
  updateMontantBeneficiaryReset,
  updateStatutValidationBeneficiaryReset,
} from '../../../../containers/AideComplementaire/FicheAideComplementaire/actions';
import DonorTable from '../../../Common/DonorTable';

const formatDate = date =>
  date
    ? moment(date).isValid()
      ? moment(date).format('DD/MM/YYYY')
      : '-'
    : '-';

export default function Donateurs(props) {
  const history = useHistory();
  const successAlert = null;

  const dispatch = useDispatch();

  const [currentPage, setCurrentPage] = useState(1);
  const pageSize = 8;

  const [showSuccessMessage, setShowSuccessMessage] = useState(false);
  const [message, setMessage] = useState('');
  const location = useLocation();
  const [showModal, setShowModal] = useState(false);
  const [filterText, setFilterText] = useState('');
  const [selectedDonors, setSelectedDonors] = useState([]);

  const aideComplementaire = props.data;
  const donorsForAideComplementaire = props.donorsForAideComplementaire;

  const donorAideComplemenatireDTOList =
    aideComplementaire && aideComplementaire.donorAideComplemenatireDTOList
      ? aideComplementaire.donorAideComplemenatireDTOList
      : [];

  const sortedList = [...donorAideComplemenatireDTOList].sort((a, b) =>
    a.id < b.id ? 1 : -1,
  );

  const startIndex = (currentPage - 1) * pageSize;
  const endIndex = Math.min(startIndex + pageSize, sortedList.length);
  const paginatedList = sortedList.slice(startIndex, endIndex);

  useEffect(() => {
    if (location.state === 'proceesDonorsSuccess') {
      setShowSuccessMessage(true);
      setMessage('Donateurs ajoutés avec succès !');
      dispatch(processDonorsReset());
      history.replace({ ...location, state: null });
    }
  }, [location.state]);

  useEffect(() => {
    if (location.state === 'updateStatutValidationBeneficiarySuccess') {
      //setShowSuccessMessage(true);
      dispatch(updateStatutValidationBeneficiaryReset());
      history.replace({ ...location, state: null });
    }
  }, [location.state]);

  useEffect(() => {
    if (location.state === 'removeBeneficiarySuccess') {
      setShowSuccessMessage(true);
      setMessage('Bénéficiaire supprimé avec succès !');
      dispatch(removeBeneficiaryReset());
      history.replace({ ...location, state: null });
    }
  }, [location.state]);

  useEffect(() => {
    if (location.state === 'removeDonorSuccess') {
      setShowSuccessMessage(true);
      setMessage('Donateur supprimé avec succès !');
      dispatch(removeDonorReset());
      history.replace({ ...location, state: null });
    }
  }, [location.state]);

  useEffect(() => {
    if (location.state === 'updateMontantBeneficiarySuccess') {
      setShowSuccessMessage(true);
      setMessage('Montant du bénéficiaire est modifié avec succès !');
      dispatch(updateMontantBeneficiaryReset());
      history.replace({ ...location, state: null });
    }
  }, [location.state]);

  useEffect(() => {
    if (showSuccessMessage) {
      const timer = setTimeout(() => {
        handleCloseSuccessMessage();
      }, 4000);
      return () => clearTimeout(timer);
    }
  }, [showSuccessMessage]);

  const filteredDonors = donorsForAideComplementaire.filter(donor => {
    const fullName = `${donor.firstName.toLowerCase()} ${donor.lastName.toLowerCase()}`;
    const filter = filterText.toLowerCase();

    return (
      donor.firstName.toLowerCase().includes(filter) ||
      donor.lastName.toLowerCase().includes(filter) ||
      fullName.includes(filter)
    );
  });

  const handleCheckboxChange = donorId => {
    setSelectedDonors(prevSelected =>
      prevSelected.includes(donorId)
        ? prevSelected.filter(id => id !== donorId)
        : [...prevSelected, donorId],
    );
  };

  const handleSelectAll = () => {
    const allDonorIds = filteredDonors.map(b => b.id);
    if (allDonorIds.every(id => selectedDonors.includes(id))) {
      setSelectedDonors([]);
    } else {
      setSelectedDonors(allDonorIds);
    }
  };

  const handleCloseSuccessMessage = () => {
    setShowSuccessMessage(false);
    history.replace({ ...location, state: null });
  };

  const handleCloseModal = () => {
    setSelectedDonors([]);
    setShowModal(false);
  };

  return (
    <div>
      {showSuccessMessage && message && (
        <Alert
          className="alert-style"
          variant="success"
          onClose={handleCloseSuccessMessage}
          dismissible
        >
          <p>{message}</p>
        </Alert>
      )}
      {successAlert}
      <div className={`pb-5 pt-4 ${stylesList.backgroudStyle}`}>
        <div style={{ textAlign: 'right', marginBottom: '20px' }}>
          <Button
            variant="primary"
            className="align-right-btn"
            onClick={() => setShowModal(true)}
          >
            Ajouter des donateurs
          </Button>
        </div>
        <div>
          <div className={styles.global}>
            <div className={styles.header}>
              <h4>Liste des donateurs participants</h4>
            </div>
            <DonorTable
              donorAideComplemenatireDTOList={paginatedList}
              aideComplementaire={aideComplementaire}
            />

            <div className="justify-content-center my-4">
              {aideComplementaire &&
                aideComplementaire.donorAideComplemenatireDTOList.length >
                  0 && (
                  <CustomPagination
                    totalCount={Math.ceil(
                      aideComplementaire.donorAideComplemenatireDTOList.length /
                        pageSize,
                    )}
                    pageSize={pageSize}
                    currentPage={currentPage}
                    onPageChange={setCurrentPage}
                  />
                )}
            </div>
          </div>
        </div>
      </div>

      <Modal show={showModal} onHide={handleCloseModal} size="lg">
        <Modal.Header closeButton>
          <Modal.Title>Ajouter des donateurs</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <InputGroup className="mb-3">
            <Button variant="outline-secondary" onClick={handleSelectAll}>
              {filteredDonors.length === selectedDonors.length
                ? 'Désélectionner tout'
                : 'Sélectionner tout'}
            </Button>
            <Form.Control
              type="text"
              placeholder="Filtrer par nom ou prénom"
              value={filterText}
              onChange={e => setFilterText(e.target.value)}
            />
          </InputGroup>
          <div style={{ maxHeight: '500px', overflowY: 'auto' }}>
            <ListGroup className="mt-3">
              {filteredDonors.map(donor => (
                <ListGroup.Item key={donor.id}>
                  <Form.Check
                    type="checkbox"
                    label={`${donor.firstName} ${donor.lastName}`}
                    onChange={() => handleCheckboxChange(donor.id)}
                    checked={selectedDonors.includes(donor.id)}
                  />
                </ListGroup.Item>
              ))}
            </ListGroup>
          </div>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={handleCloseModal}>
            Annuler
          </Button>
          <Button
            variant="primary"
            onClick={() => {
              const aideComplementaireId2 = aideComplementaire.id;
              const aideComplementairePriority = aideComplementaire.priority;
              dispatch(
                processDonorsRequest(
                  aideComplementaireId2,
                  aideComplementairePriority,
                  selectedDonors,
                ),
              );
              handleCloseModal();
              //setShowModal(false);
              // You can trigger an action here with the selected beneficiaries
            }}
          >
            Valider
          </Button>
        </Modal.Footer>
      </Modal>
    </div>
  );
}
