import React, { useEffect, useState, useRef } from 'react';
import { Box, Typography, useTheme } from '@mui/material';
import { styled } from '@mui/material/styles';

const ChartContainer = styled(Box)(({ theme }) => ({
  padding: theme.spacing(2),
  background: 'linear-gradient(145deg, #ffffff, #f0f0f0)',
  borderRadius: '12px',
  boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
  transition: 'transform 0.3s ease, box-shadow 0.3s ease',
  '&:hover': {
    transform: 'translateY(-4px)',
    boxShadow: '0 8px 30px rgba(0, 0, 0, 0.12)',
  },
}));

const ChartHeader = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  gap: theme.spacing(1),
  marginBottom: theme.spacing(2),
  position: 'relative',
  [theme.breakpoints.down('sm')]: {
    paddingRight: '120px',
  },
}));

const YearRange = styled(Typography)(({ theme }) => ({
  position: 'absolute',
  right: theme.spacing(2),
  top: '50%',
  transform: 'translateY(-50%)',
  color: theme.palette.text.secondary,
  fontWeight: 500,
  fontSize: '1.2rem',
}));

const SVGContainer = styled('svg')({
  width: '100%',
  height: '100%',
  overflow: 'visible',
});

const AnimatedLineChart = ({ title, data = {}, labels = [], icon, colors = [] }) => {
  const theme = useTheme();
  const pathRef = useRef(null);
  const svgRef = useRef(null);
  const containerRef = useRef(null);
  const [pathLength, setPathLength] = useState(0);
  const [animationProgress, setAnimationProgress] = useState(0);
  const [hoverData, setHoverData] = useState({ show: false, value: 0, x: 0 });
  const [dimensions, setDimensions] = useState({ width: 800, height: 400 });

  // Get current date and previous year's same month
  const currentDate = new Date();
  const currentYear = currentDate.getFullYear();
  const previousYear = currentYear - 1;
  const currentMonth = currentDate.getMonth() + 1; // 1-12

  // Transform data for the last 12 months
  const transformedData = React.useMemo(() => {
    const months = Array(12).fill(0);
    let monthIndex = 11; // Start from the end of the array

    // Fill data from current month backwards
    for (let i = 0; i < 12; i++) {
      const date = new Date(currentYear, currentMonth - 1 - i);
      const year = date.getFullYear();
      const month = date.getMonth() + 1;
      const key = `${year}-${month.toString().padStart(2, '0')}`;
      months[monthIndex--] = data[key] || 0;
    }

    return months;
  }, [data, currentYear, currentMonth]);

  const monthLabels = React.useMemo(() => {
    const labels = [];
    for (let i = 0; i < 12; i++) {
      const date = new Date(currentYear, currentMonth - 1 - (11 - i));
      const monthName = date.toLocaleString('fr-FR', { month: 'short' });
      labels.push(monthName);
    }
    return labels;
  }, [currentYear, currentMonth]);

  useEffect(() => {
    const updateDimensions = () => {
      if (containerRef.current) {
        const { width } = containerRef.current.getBoundingClientRect();
        setDimensions({
          width: Math.min(width, 800),
          height: 400
        });
      }
    };

    updateDimensions();
    window.addEventListener('resize', updateDimensions);
    return () => window.removeEventListener('resize', updateDimensions);
  }, []);

  const margin = { top: 20, right: 20, bottom: 60, left: 60 };
  const width = dimensions.width;
  const height = dimensions.height;

  const chartWidth = width - margin.left - margin.right;
  const chartHeight = height - margin.top - margin.bottom;

  // Calculate scales
  const minValue = Math.min(...transformedData);
  const maxValue = Math.max(...transformedData);
  const padding = ((maxValue - minValue) || 100) * 0.1;

  const yRange = maxValue - minValue || 100;
  const step = Math.ceil(yRange / 4);
  const yMin = Math.floor(minValue / step) * step;
  const yMax = Math.ceil(maxValue / step) * step;

  const xScale = (index) => (chartWidth * index) / (Math.max(transformedData.length - 1, 1));
  const yScale = (value) => 
    chartHeight - ((value - yMin) / (yMax - yMin)) * chartHeight;

  // Generate path
  const generatePath = () => {
    if (!transformedData.length) return '';
    return transformedData.reduce((path, value, index) => {
      const x = xScale(index);
      const y = yScale(value);
      return path + (index === 0 ? `M ${x},${y}` : ` L ${x},${y}`);
    }, '');
  };

  // Generate area path
  const generateAreaPath = () => {
    if (!transformedData.length) return '';
    const path = generatePath();
    const lastX = xScale(transformedData.length - 1);
    const firstX = xScale(0);
    return `${path} L ${lastX},${chartHeight} L ${firstX},${chartHeight} Z`;
  };

  useEffect(() => {
    if (pathRef.current) {
      setPathLength(pathRef.current.getTotalLength());
    }
  }, [transformedData]);

  useEffect(() => {
    const timer = setInterval(() => {
      setAnimationProgress((prev) => {
        if (prev >= 1) {
          clearInterval(timer);
          return 1;
        }
        return prev + 0.02;
      });
    }, 16);

    return () => clearInterval(timer);
  }, []);

  // Generate y-axis ticks
  const yTicks = Array.from({ length: 5 }, (_, i) => {
    const value = yMin + (step * i);
    return {
      value,
      y: yScale(value),
      label: Math.round(value).toLocaleString(),
    };
  }).filter((tick, index, array) => 
    array.findIndex(t => t.label === tick.label) === index
  );

  const handleMouseMove = (event) => {
    if (!containerRef.current) return;

    const containerRect = containerRef.current.getBoundingClientRect();
    const mouseX = event.clientX - containerRect.left - margin.left;
    
    // Only process if mouse is within the chart area
    if (mouseX >= 0 && mouseX <= chartWidth) {
      const index = Math.round((mouseX / chartWidth) * (transformedData.length - 1));
      
      if (index >= 0 && index < transformedData.length) {
        const value = transformedData[index];
        setHoverData({
          show: true,
          value: value,
          x: xScale(index)
        });
      }
    } else {
      setHoverData({ show: false, value: 0, x: 0 });
    }
  };

  const handleMouseLeave = () => {
    setHoverData({ show: false, value: 0, x: 0 });
  };

  return (
    <ChartContainer sx={{ width: '100%', maxWidth: '800px' }} ref={containerRef}>
      <ChartHeader>
        {icon && React.cloneElement(icon, { color: "primary" })}
        <Typography variant="h6" color="primary">
          {title}
        </Typography>
        <YearRange>
          {previousYear}-{currentYear}
        </YearRange>
      </ChartHeader>
      <Box sx={{ height: 400, position: 'relative' }}>
        <SVGContainer
          ref={svgRef}
          viewBox={`0 0 ${width} ${height}`}
          preserveAspectRatio="xMidYMid meet"
          onMouseMove={handleMouseMove}
          onMouseLeave={handleMouseLeave}
        >
          <g transform={`translate(${margin.left}, ${margin.top})`}>
            {/* Grid lines */}
            {yTicks.map((tick, i) => (
              <g key={i}>
                <line
                  x1={0}
                  x2={chartWidth}
                  y1={tick.y}
                  y2={tick.y}
                  stroke={theme.palette.divider}
                  strokeDasharray="4,4"
                />
                <text
                  x={-8}
                  y={tick.y}
                  textAnchor="end"
                  alignmentBaseline="middle"
                  fill={theme.palette.text.secondary}
                  fontSize="16"
                  fontWeight="500"
                >
                  {tick.label}
                </text>
              </g>
            ))}

            {/* X-axis labels */}
            {monthLabels.map((label, i) => (
              <text
                key={i}
                x={xScale(i)-5}
                y={chartHeight + 30}
                textAnchor="middle"
                fill={theme.palette.text.secondary}
                fontSize="20"
                fontWeight="500"
                transform={`rotate(-45, ${xScale(i)}, ${chartHeight + 20})`}
              >
                {label}
              </text>
            ))}

            {/* Area fill */}
            <path
              d={generateAreaPath()}
              fill={`${colors[0] || theme.palette.primary.main}20`}
              opacity={animationProgress}
            />

            {/* Line */}
            <path
              ref={pathRef}
              d={generatePath()}
              fill="none"
              stroke={colors[0] || theme.palette.primary.main}
              strokeWidth={3}
              strokeDasharray={pathLength}
              strokeDashoffset={pathLength * (1 - animationProgress)}
            />

            {/* Data points */}
            {transformedData.map((value, index) => (
              <circle
                key={index}
                cx={xScale(index)}
                cy={yScale(value)}
                r={6}
                fill="#fff"
                stroke={colors[0] || theme.palette.primary.main}
                strokeWidth={3}
                opacity={animationProgress}
              />
            ))}

            {/* Crosshair and value indicator */}
            {hoverData.show && (
              <>
                {/* Vertical line */}
                <line
                  x1={hoverData.x}
                  y1={0}
                  x2={hoverData.x}
                  y2={chartHeight}
                  stroke={theme.palette.text.secondary}
                  strokeWidth={1}
                  strokeDasharray="4,4"
                />
                {/* Horizontal line */}
                <line
                  x1={0}
                  y1={yScale(hoverData.value)}
                  x2={chartWidth}
                  y2={yScale(hoverData.value)}
                  stroke={theme.palette.text.secondary}
                  strokeWidth={1}
                  strokeDasharray="4,4"
                />
                {/* Value indicator on Y-axis */}
                <text
                  x={-8}
                  y={yScale(hoverData.value)}
                  textAnchor="end"
                  alignmentBaseline="middle"
                  fill={theme.palette.text.primary}
                  fontSize="16"
                  fontWeight="500"
                >
                  {Math.round(hoverData.value).toLocaleString()}
                </text>
              </>
            )}
          </g>
        </SVGContainer>
      </Box>
    </ChartContainer>
  );
};

export default AnimatedLineChart; 