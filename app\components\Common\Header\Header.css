header{
    /* padding: 16px 0; */
    position: fixed;
    left: 0;
    top: 0;
    width: 100%;
    z-index: 10;
    transition: all 0.1s ease-in-out;
    background-color: white;
}

.containerHeader{
    /* max-width: 1200px; */
    margin: 0 3% 0 3%;
    /* padding: 0 15px; */
    display: flex;
    align-items: center;
    justify-content: space-around;
}

/* .container a img {
    width: 20%;
    height: auto;
} */

.mainNav {
    width: 100%;
    margin-left: 6%;
}

.mainNav ul {
    padding: 0;
    margin: 0;
    list-style: none;
    display: flex;
    width: 100%;
    justify-content: space-between;
    flex-wrap: wrap;
}

.mainNav ul li{
    /* display: inline-block; */
    /* margin-left: 2%;    */
}

.mainNav ul li a {
    font-weight: 700;
    text-decoration: none;
    color: #494949;
    font-size: 1.2em;
    transition: all 0.3s ease-in-out;
}

.mainNav ul li:hover a {
    color:#4F89D7;
}

.mainNav ul li a.navLinkActive {
    background-color: #4F89D7;
    border-radius: 12px;
    padding: 12px 15px;
    color: #fff;
}

.afterHeader {
    margin: 93px;
}

.header.fixed {
    opacity: 0;
    transform: translateY(-50%);
}

.header.active {
    transition: all 0.4s ease-in-out;
    transform: none;
    box-shadow: 0px 2px 15px rgba(0, 0, 0, 0.1);
    padding: 7px 0;
    background: #fff;
}
