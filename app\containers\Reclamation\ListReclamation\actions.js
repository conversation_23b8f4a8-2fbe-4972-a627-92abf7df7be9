import {
  FETCH_RECLAMATIONS_REQUEST,
  FETCH_RECLAMATIONS_SUCCESS,
  FETCH_RECLAMATIONS_FAILURE,
  UPDATE_RECLAMATION_REQUEST,
  UPDATE_RECLAMATION_SUCCESS,
  UPDATE_RECLAMATION_FAILURE,
  RESET_ERROR,
} from './constants';

// Fetch reclamations
export function fetchReclamationsRequest(params) {
  return {
    type: FETCH_RECLAMATIONS_REQUEST,
    params,
  };
}

export function fetchReclamationsSuccess(reclamations) {
  return {
    type: FETCH_RECLAMATIONS_SUCCESS,
    reclamations,
  };
}

export function fetchReclamationsFailure(error) {
  return {
    type: FETCH_RECLAMATIONS_FAILURE,
    error,
  };
}

// Update reclamation
export function updateReclamationRequest(id, data) {
  return {
    type: UPDATE_RECLAMATION_REQUEST,
    id,
    data,
  };
}

export function updateReclamationSuccess(reclamation) {
  return {
    type: UPDATE_RECLAMATION_SUCCESS,
    reclamation,
  };
}

export function updateReclamationFailure(error) {
  return {
    type: UPDATE_RECLAMATION_FAILURE,
    error,
  };
}

export function resetError() {
  return {
    type: RESET_ERROR,
  };
}
