import { RESET_DELETE_Responsable_EPS, LOAD_Responsable_EPS, LOAD_Responsable_EPS_SUCCESS, LOAD_Responsable_EPS_ERROR, DELETE_Responsable_EPS, DELETE_Responsable_EPS_SUCCESS, DELETE_Responsable_EPS_ERROR,FETCH_CITIES_REQUEST, FETCH_CITIES_SUCCESS, FETCH_CITIES_FAILURE } from "./constants";

export function loadResponsableEps(epsId) {
    return {
        type: LOAD_Responsable_EPS,
        epsId
    };
};

export function responsableEpsLoaded(responsableEps) {
    return {
        type: LOAD_Responsable_EPS_SUCCESS,
        responsableEps,
    };
};

export function responsableEpsLoadingError(error) {
    return {
        type: LOAD_Responsable_EPS_ERROR,
        error,
    };
};

export function deleteResponsableEps(epsId) {
    return {
        type: DELETE_Responsable_EPS,
        epsId,
    };
};

export function responsableEpsDeleted(epsId) {
    return {
        type: DELETE_Responsable_EPS_SUCCESS,
        epsId,
    };
};

export function responsableEpsDeleteError(error) {
    return {
        type: DELETE_Responsable_EPS_ERROR,
        error,
    };
};

export function resetDeleteResponsableEps() {
    return {
        type: RESET_DELETE_Responsable_EPS,
    };
};
