import React from 'react';
import { Box, Typography, Paper } from '@mui/material';
import { styled } from '@mui/material/styles';

const StyledPaper = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(2),
  display: 'flex',
  flexDirection: 'column',
  alignItems: 'center',
}));

const LegendContainer = styled(Box)({
  display: 'flex',
  flexWrap: 'wrap',
  justifyContent: 'center',
  gap: '16px',
  marginTop: '16px',
});

const LegendItem = styled(Box)({
  display: 'flex',
  alignItems: 'center',
  gap: '8px',
});

const ColorBox = styled(Box)(({ color }) => ({
  width: '16px',
  height: '16px',
  backgroundColor: color,
  borderRadius: '4px',
}));

const colors = [
  '#FF6384',
  '#36A2EB',
  '#FFCE56',
  '#4BC0C0',
  '#9966FF',
];

export function PieChart({ data, labels, title }) {
  const total = data.reduce((sum, value) => sum + value, 0);
  
  return (
    <StyledPaper>
      {title && (
        <Typography variant="h6" gutterBottom>
          {title}
        </Typography>
      )}
      <Box sx={{ 
        width: '100%', 
        height: '200px',
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        position: 'relative'
      }}>
        {data.map((value, index) => {
          const percentage = (value / total) * 100;
          const startAngle = index === 0 ? 0 : 
            data.slice(0, index).reduce((sum, val) => sum + (val / total) * 360, 0);
          const endAngle = startAngle + (value / total) * 360;
          
          return (
            <Box
              key={index}
              sx={{
                position: 'absolute',
                width: '150px',
                height: '150px',
                borderRadius: '50%',
                background: `conic-gradient(
                  ${colors[index % colors.length]} ${startAngle}deg,
                  ${colors[index % colors.length]} ${endAngle}deg,
                  transparent ${endAngle}deg,
                  transparent ${startAngle}deg
                )`,
                transform: 'rotate(-90deg)',
              }}
            />
          );
        })}
        <Box
          sx={{
            position: 'absolute',
            width: '100px',
            height: '100px',
            borderRadius: '50%',
            backgroundColor: 'white',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
          }}
        >
          <Typography variant="h6">
            {total}
          </Typography>
        </Box>
      </Box>
      <LegendContainer>
        {labels.map((label, index) => (
          <LegendItem key={index}>
            <ColorBox color={colors[index % colors.length]} />
            <Typography variant="body2">
              {label} ({((data[index] / total) * 100).toFixed(1)}%)
            </Typography>
          </LegendItem>
        ))}
      </LegendContainer>
    </StyledPaper>
  );
}