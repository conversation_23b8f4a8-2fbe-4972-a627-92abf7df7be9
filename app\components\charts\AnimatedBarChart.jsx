import React, { useState, useEffect } from 'react';
import { Box, Typography, Paper, useTheme } from '@mui/material';
import { styled } from '@mui/material/styles';

const StyledPaper = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(3),
  display: 'flex',
  flexDirection: 'column',
  alignItems: 'center',
  background: 'linear-gradient(145deg, #ffffff, #f0f0f0)',
  boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
  borderRadius: '16px',
  transition: 'transform 0.3s ease, box-shadow 0.3s ease',
  '&:hover': {
    transform: 'translateY(-4px)',
    boxShadow: '0 8px 30px rgba(0, 0, 0, 0.12)',
  },
}));

const ChartHeader = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  gap: theme.spacing(1),
  marginBottom: theme.spacing(2),
}));

const ChartContainer = styled(Box)({
  width: '100%',
  height: '350px',
  display: 'flex',
  flexDirection: 'column',
  justifyContent: 'flex-end',
  position: 'relative',
  background: 'rgba(255, 255, 255, 0.8)',
  borderRadius: '12px',
  boxShadow: 'inset 0 2px 4px rgba(0, 0, 0, 0.05)',
});

const CardWrapper = styled(Box)(({ theme }) => ({
  padding: '24px 16px 32px 16px',
  height: '100%',
  display: 'flex',
  flexDirection: 'column',
  background: 'linear-gradient(145deg, #ffffff, #f0f0f0)',
  borderRadius: '12px',
  boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
  transition: 'transform 0.3s ease, box-shadow 0.3s ease',
  '&:hover': {
    transform: 'translateY(-4px)',
    boxShadow: '0 8px 30px rgba(0, 0, 0, 0.12)',
  },
}));

const BarContainer = styled(Box)({
  display: 'flex',
  alignItems: 'flex-end',
  height: '100%',
  gap: '16px',
  padding: '0 16px 0 60px',
  position: 'relative',
  zIndex: 1,
  marginTop: '15px',
});

const BarWrapper = styled(Box)({
  flex: 1,
  display: 'flex',
  flexDirection: 'column',
  alignItems: 'center',
  position: 'relative',
  height: '100%',
  alignSelf: 'flex-end',
});

const Bar = styled(Box)(({ height, color, visible, delay }) => ({
  width: '100%',
  height: visible ? `${height}%` : '0%',
  background: `linear-gradient(to top, ${color}, ${color}80)`,
  borderRadius: '8px 8px 0 0',
  position: 'absolute',
  bottom: 0,
  cursor: 'pointer',
  transition: `height 1s cubic-bezier(0.2, 0.8, 0.2, 1) ${delay}s, transform 0.3s ease, box-shadow 0.3s ease`,
  transformOrigin: 'bottom',
  boxShadow: visible ? '0 -2px 10px rgba(0, 0, 0, 0.1)' : 'none',
  '&::after': {
    content: '""',
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: '4px',
    background: color,
    borderRadius: '0 0 4px 4px',
    boxShadow: '0 2px 6px rgba(0, 0, 0, 0.2)'
  },
  '&:hover': {
    transform: 'scaleY(1.05)',
    boxShadow: '0 -4px 12px rgba(0, 0, 0, 0.1)',
    '& + .value-tooltip': {
      opacity: 1,
      transform: 'translateX(-50%) translateY(-5px)',
    }
  },
}));

const Label = styled(Typography)({
  position: 'absolute',
  bottom: '-28px',
  fontSize: '12px',
  fontWeight: 500,
  whiteSpace: 'nowrap',
  overflow: 'hidden',
  textOverflow: 'ellipsis',
  maxWidth: '100%',
  color: '#666',
  textAlign: 'center',
});

const ValueTooltip = styled(Typography)({
  position: 'absolute',
  top: '0',
  left: '50%',
  transform: 'translateX(-50%) translateY(-25px)',
  fontSize: '14px',
  fontWeight: 700,
  opacity: 0,
  background: '#ffffff',
  padding: '8px 12px',
  borderRadius: '8px',
  boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
  zIndex: 10,
  color: '#333',
  border: '1px solid rgba(0, 0, 0, 0.1)',
  transition: 'all 0.2s ease',
  pointerEvents: 'none',
  whiteSpace: 'nowrap',
  maxWidth: '200px',
  overflow: 'hidden',
  textOverflow: 'ellipsis',
  textAlign: 'center',
  '&::after': {
    content: '""',
    position: 'absolute',
    bottom: '-6px',
    left: '50%',
    transform: 'translateX(-50%)',
    width: '0',
    height: '0',
    borderLeft: '6px solid transparent',
    borderRight: '6px solid transparent',
    borderTop: '6px solid #ffffff',
  }
});

const YAxis = styled(Box)({
  position: 'absolute',
  left: 0,
  top: 0,
  bottom: '32px',
  width: '60px',
  display: 'flex',
  flexDirection: 'column',
  justifyContent: 'space-between',
  padding: '16px 0',
  borderRight: '1px solid rgba(0, 0, 0, 0.05)',
});

const YAxisLabel = styled(Typography)({
  fontSize: '12px',
  color: '#666',
  padding: '0 10px',
  textAlign: 'right',
  fontWeight: 500,
});

const XAxis = styled(Box)({
  position: 'absolute',
  left: '60px',
  right: '16px',
  bottom: 0,
  height: '32px',
  borderTop: '1px solid rgba(0, 0, 0, 0.05)',
});

const GridLines = styled(Box)({
  position: 'absolute',
  left: '60px',
  right: '0',
  top: '15px',
  bottom: '32px',
  display: 'flex',
  flexDirection: 'column',
  justifyContent: 'space-between',
  pointerEvents: 'none',
});

const GridLine = styled(Box)({
  width: '100%',
  height: '1px',
  backgroundColor: 'rgba(0, 0, 0, 0.05)',
});

// Helper function to format numbers with suffixes (K, M)
const formatNumber = (num) => {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1).replace(/\.0$/, '') + 'M';
  }
  if (num >= 1000) {
    return (num / 1000).toFixed(1).replace(/\.0$/, '') + 'K';
  }
  return num.toString();
};

export function AnimatedBarChart({ data, labels, title, stacked = false, colors = [], icon }) {
  const theme = useTheme();
  const [isVisible, setIsVisible] = useState(false);
  
  // Process data to combine values for duplicate labels
  const processedData = labels.reduce((acc, label, index) => {
    const existingIndex = acc.labels.indexOf(label);
    if (existingIndex === -1) {
      acc.labels.push(label);
      acc.values.push(data[index]);
    } else {
      acc.values[existingIndex] += data[index];
    }
    return acc;
  }, { labels: [], values: [] });

  const maxValue = Math.max(...processedData.values);
  // Display Y-axis labels in descending order (highest at top)
  const yAxisLabels = [maxValue, maxValue * 0.75, maxValue * 0.5, maxValue * 0.25, 0];
  
  // Default colors if not provided
  const defaultColors = [
    theme.palette.primary.main,
    theme.palette.secondary.main,
    theme.palette.success.main,
    theme.palette.warning.main,
    theme.palette.error.main
  ];
  
  const barColors = colors.length > 0 ? colors : defaultColors;
  
  useEffect(() => {
    // Delay animation start to ensure the component is fully rendered
    const timer = setTimeout(() => {
      setIsVisible(true);
    }, 300);
    return () => clearTimeout(timer);
  }, []);
  
  return (
    <CardWrapper>
      {title && (
        <ChartHeader>
          {icon && React.cloneElement(icon, { color: "primary" })}
          <Typography variant="h6" color="primary">
            {title}
          </Typography>
        </ChartHeader>
      )}
      <ChartContainer>
        <YAxis>
          {yAxisLabels.map((value, index) => (
            <YAxisLabel key={index}>
              {formatNumber(value)}
            </YAxisLabel>
          ))}
        </YAxis>
        
        <XAxis />
        
        <GridLines>
          {yAxisLabels.map((_, index) => (
            <GridLine key={index} />
          ))}
        </GridLines>
        
        <BarContainer>
          {processedData.values.map((value, index) => {
            const height = (value / maxValue) * 100;
            const barColor = barColors[index % barColors.length];
            const delay = index * 0.15;
            
            return (
              <BarWrapper key={index}>
                <Bar
                  height={height}
                  color={barColor}
                  visible={isVisible}
                  delay={delay}
                />
                <ValueTooltip className="value-tooltip">
                  {formatNumber(value)}
                </ValueTooltip>
                <Label>
                  {processedData.labels[index]}
                </Label>
              </BarWrapper>
            );
          })}
        </BarContainer>
      </ChartContainer>
    </CardWrapper>
  );
} 