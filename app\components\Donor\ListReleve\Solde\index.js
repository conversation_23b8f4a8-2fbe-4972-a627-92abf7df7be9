import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import { useDispatch, useSelector } from 'react-redux';
import moment from 'moment';
import { useInjectReducer, useInjectSaga } from 'redux-injectors';
import { useParams } from 'react-router-dom';
import {
  makeSelectCanalDonations,
  makeSelectError,
  makeSelectLoading,
  makeSelectReleveDonor,
} from './selectors';
import { loadCanalDonations, loadReleveDonor } from './actions';
import reducer from './reducer';
import saga from './saga';
import ReactToPrint from 'react-to-print';
import CustomPagination from 'components/Common/CustomPagination';
import PrintableContent from '../PrintableContent/PrintableContent';

const formatDate = date => (date ? moment(date).format('DD/MM/YYYY') : '-');

const BodyComponent = ({ data }) => (
  <>
    {data && data.length > 0 ? (
      data.map(el => {
        if (el.typeDonationKafalat !== 'donation') {
          return (
            <tr key={el.id}>
              {/* <td>{el.code}</td> */}
              <td>{el.services.category}</td>
              <td>{el.services.typeCategory}</td>
              <td>{el.amountDisponible}</td>
              <td>{el.amountReserve}</td>
              <td>
                {el.montantSortie + el.amountDisponible + el.amountReserve}
              </td>
            </tr>
          );
        }
      })
    ) : (
      <tr>
        <td colSpan="8">Aucun relevé disponible</td>
      </tr>
    )}
  </>
);

const key = 'releveDonor';
const pageSize = 10;
export default function Solde(props) {
  const donor = props.data;
  const { error, loading, releves, canalDonations } = useSelector(state => ({
    error: makeSelectError(state),
    loading: makeSelectLoading(state),
    releves: makeSelectReleveDonor(state),
    canalDonations: makeSelectCanalDonations(state),
  }));

  useInjectReducer({ key, reducer });
  useInjectSaga({ key, saga });

  const dispatch = useDispatch();
  const params = useParams();
  const [activePage, setActivePage] = useState(1);
  const [startDate, setStartDate] = useState(moment().startOf('year'));
  const [endDate, setEndDate] = useState(moment().endOf('year'));
  const [typeFilter, setTypeFilter] = useState('');
  useEffect(() => {
    dispatch(loadReleveDonor(params.id));
    dispatch(loadCanalDonations());
  }, [dispatch, params.id]);

  const handlePageChange = pageNumber => {
    setActivePage(pageNumber);
  };
  const [filteredData, setFilteredData] = useState([]);
  const handleDataPaging = useCallback(() => {
    setFilteredData(() => {
      const startIndex = (activePage - 1) * pageSize;
      const endIndex = startIndex + pageSize;
      return releves ? releves.slice(startIndex, endIndex) : [];
    });
  }, [releves, pageSize, activePage]);
  useEffect(() => {
    handleDataPaging();
  }, [releves, pageSize, activePage]);

  const totalDonation = useMemo(() => {
    if (releves && releves.length > 0) {
      const item = releves.find(
        el => el.typeDonationKafalat === 'aideComplementaire',
      );
      return item ? item.total : 0;
    }
    return 0;
  }, [releves]);

  const totalDispo = useMemo(() => {
    if (filteredData && filteredData.length > 0) {
      return filteredData.reduce(
        (sum, el) => sum + (el.amountDisponible || 0),
        0,
      );
    }
    return 0;
  }, [filteredData]);

  const totalreserver = useMemo(() => {
    if (filteredData && filteredData.length > 0) {
      return filteredData.reduce((sum, el) => sum + (el.amountReserve || 0), 0);
    }
    return 0;
  }, [filteredData]);

  const applyFilter = () => {
    if (startDate && typeFilter && typeFilter !== 'Tous') {
      const startIndex = (activePage - 1) * pageSize;
      const endIndex = startIndex + pageSize;
      setFilteredData(() =>
        releves
          .filter(
            el =>
              el.receptionDate &&
              el.receptionDate.slice(0, 10) >=
                moment(startDate).format('YYYY-MM-DD') &&
              el.receptionDate &&
              el.receptionDate.slice(0, 10) <=
                moment(endDate).format('YYYY-MM-DD') &&
              el.type === typeFilter,
          )
          .slice(startIndex, endIndex),
      );
    } else {
      const startIndex = (activePage - 1) * pageSize;
      const endIndex = startIndex + pageSize;
      setFilteredData(() =>
        releves
          .filter(
            el =>
              el.receptionDate &&
              el.receptionDate.slice(0, 10) >=
                moment(startDate).format('YYYY-MM-DD') &&
              el.receptionDate &&
              el.receptionDate.slice(0, 10) <=
                moment(endDate).format('YYYY-MM-DD'),
          )
          .slice(startIndex, endIndex),
      );
    }
  };
  const componentRef = useRef();

  return (
    <>
      <div className="d-flex bg-white p-4">
        <h4>solde compte donateur par type d’affectation</h4>
      </div>
      <div
        style={{
          display: 'flex',
          flexDirection: 'row',
          justifyContent: 'space-around',
          backgroundColor: '#FFFFFF',
          gap: '10px'
        }}
      >
        <select
          value={typeFilter}
          onChange={e => setTypeFilter(e.target.value)}
          className="form-control input-border"
          style={{ width: '150px' }}
        >
          <option value="">Tous</option>
          <option value="Nature">Nature</option>
          <option value="Financière">Financière</option>
        </select>

        <div style={{display: 'flex', alignItems: 'center', gap: '10px'}}>
          <label>
            Date Debut:
          </label>
          <input
              type="date"
              value={startDate ? moment(startDate).format('YYYY-MM-DD') : ''}
              max={
                endDate
                    ? moment(endDate)
                        .subtract(1, 'days')
                        .format('YYYY-MM-DD')
                    : ''
              }
              onChange={e => setStartDate(e.target.value)}
              className="form-control input-border"
              style={{ width: '150px' }}
          />
        </div>


        <div style={{display: 'flex', alignItems: 'center', gap: '10px'}}>
          <label>
            Date Fin :
          </label>
          <input
            type="date"
            value={endDate ? moment(endDate).format('YYYY-MM-DD') : ''}
            min={
              startDate
                ? moment(startDate)
                    .add(1, 'days')
                    .format('YYYY-MM-DD')
                : ''
            }
            onChange={e => setEndDate(e.target.value)}
            className="form-control input-border"
            style={{ width: '150px' }}
          />
        </div>
      </div>
      <div
          style={{
            display: 'flex',
            flexDirection: 'row',
            justifyContent: 'flex-end',
            backgroundColor: '#FFFFFF',
            padding: 24,
            gap: 15,
          }}
      >
        <button
            type="button"
            className="btn-style primary"
            onClick={() => {
              if (startDate || endDate) {
                applyFilter();
              }
            }}
        >
          Appliquer
        </button>
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <ReactToPrint
              trigger={() => (
                  <button className="btn-style secondary">Imprimer</button>
              )}
              content={() => componentRef.current}
          />
          <div style={{ display: 'none' }}>
            <PrintableContent
                ref={componentRef}
                data={filteredData}
                totalDonation={totalDonation}
                donor={donor}
                startDate={moment(startDate).format('DD-MM-YYYY')}
                endDate={moment(endDate).format('DD-MM-YYYY')}
            />
          </div>
        </div>
        <button
            className="btn-style success"
            onClick={() => {
              handleDataPaging();
              setTypeFilter('Tous');
              setStartDate(moment().startOf('year'));
              setEndDate(moment().endOf('year'));
            }}
        >
          <span>Reset</span>
        </button>
      </div>
      <div className="d-flex flex-column bg-white p-4 br-bottoms-20">
        <table className="table table-bordered">
          <thead>
            <tr>
              <th colSpan={5} className="text-align-center">
                solde compte donateur
              </th>
            </tr>
            <tr>
              {/* <th>Code</th> */}
              <th>Catégorie</th>
              <th>Type de service</th>
              <th>Montant disponible</th>
              <th>Montant réservé ou planfié</th>
              <th>Solde</th>
            </tr>
          </thead>
          <tbody>
            <BodyComponent data={filteredData} />
          </tbody>
          <tfoot>
            <tr>
              <td>Total</td>
              <td></td>
              <td>{`${totalDispo} DH`}</td>
              <td>{`${totalreserver} DH`}</td>
              <td></td>
            </tr>
          </tfoot>
        </table>
        {releves.length > 0 && (
          <CustomPagination
            totalElements={releves.length}
            totalCount={
            releves &&
              releves.length > 0 &&
              Math.ceil(releves.length / pageSize)
            }
            pageSize={pageSize}
            currentPage={activePage}
            onPageChange={handlePageChange}
          />
        )}
      </div>
    </>
  );
}
