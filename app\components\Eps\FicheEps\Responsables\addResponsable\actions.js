import {
  ADD_EPS_RESPONSABLE,
  ADD_EPS_RESPONSABLE_ERROR,
  ADD_EPS_RESPONSABLE_RESET,
  ADD_EPS_RESPONSABLE_SUCCESS,
} from './constants';

export function addResponsable(search) {
  return {
    type: ADD_EPS_RESPONSABLE,
    search,
  };
}

export function responsableAdded(responsable) {
  return {
    type: ADD_EPS_RESPONSABLE_SUCCESS,
    responsable,
  };
}

export function responsableAddingError(error) {
  return {
    type: ADD_EPS_RESPONSABLE_ERROR,
    error,
  };
}

export function resetResponsable() {
  return {
    type: ADD_EPS_RESPONSABLE_RESET,
  };
}
