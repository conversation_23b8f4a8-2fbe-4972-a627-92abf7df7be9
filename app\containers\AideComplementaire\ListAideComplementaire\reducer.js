import produce from 'immer';
import {
  DELETE_AIDE_COMPLEMENTAIRE_FAILURE,
  DELETE_AIDE_COMPLEMENTAIRE_REQUEST,
  DELETE_AIDE_COMPLEMENTAIRE_RESET,
  DELETE_AIDE_COMPLEMENTAIRE_SUCCESS,
  EXPORT_AIDE_COMPLEMENTAIRE_CSV,
  EXPORT_AIDE_COMPLEMENTAIRE_CSV_ERROR,
  EXPORT_AIDE_COMPLEMENTAIRE_CSV_SUCCESS,
  LOAD_AIDE_COMPLEMENTAIRE,
  LOAD_AIDE_COMPLEMENTAIRE_ERROR,
  LOAD_AIDE_COMPLEMENTAIRE_SUCCESS,
  REMOVE_AIDE_COMPLEMENTAIRE,
} from './constants';

export const initialState = {
  loading: false,
  error: false,
  aideComplementaire: false,
  exportLoading: false,

  deleteAideComplementaireLoading: false,
  deleteAideComplementaireSuccess: false,
  deleteAideComplementaireError: false,
};

/* eslint-disable default-case, no-param-reassign */
const aideComplementaireReducer = produce((draft, action) => {
  switch (action.type) {
    case LOAD_AIDE_COMPLEMENTAIRE:
      draft.loading = true;
      draft.error = false;
      draft.aideComplementaire = false;
      break;
    case LOAD_AIDE_COMPLEMENTAIRE_SUCCESS:
      draft.loading = false;
      draft.error = false;
      draft.aideComplementaire = action.aideComplementaire;
      break;
    case LOAD_AIDE_COMPLEMENTAIRE_ERROR:
      draft.loading = false;
      draft.error = action.error;
      break;
    case REMOVE_AIDE_COMPLEMENTAIRE:
      if (draft.aideComplementaire && draft.aideComplementaire.content) {
        draft.aideComplementaire.content = draft.aideComplementaire.content.filter(
          el => el.id !== action.aideComplementaire.id,
        );
      }
      break;
    case EXPORT_AIDE_COMPLEMENTAIRE_CSV:
      draft.loading = true;
      draft.error = false;
      draft.exportLoading = true;
      break;
    case EXPORT_AIDE_COMPLEMENTAIRE_CSV_SUCCESS:
      draft.loading = false;
      draft.error = false;
      draft.exportLoading = false;
      break;
    case EXPORT_AIDE_COMPLEMENTAIRE_CSV_ERROR:
      draft.loading = false;
      draft.error = action.error;
      draft.exportLoading = false;
      break;
    case DELETE_AIDE_COMPLEMENTAIRE_REQUEST:
      draft.deleteAideComplementaireLoading = true;
      draft.deleteAideComplementaireSuccess =
        initialState.deleteAideComplementaireSuccess;
      draft.deleteAideComplementaireError =
        initialState.deleteAideComplementaireError;
      break;
    case DELETE_AIDE_COMPLEMENTAIRE_SUCCESS:
      draft.deleteAideComplementaireLoading =
        initialState.deleteAideComplementaireLoading;
      draft.deleteAideComplementaireSuccess = true;
      draft.deleteAideComplementaireError =
        initialState.deleteAideComplementaireError;
      break;
    case DELETE_AIDE_COMPLEMENTAIRE_FAILURE:
      draft.deleteAideComplementaireLoading =
        initialState.deleteAideComplementaireLoading;
      draft.deleteAideComplementaireSuccess =
        initialState.deleteAideComplementaireSuccess;
      draft.deleteAideComplementaireError = action.error;
      break;
    case DELETE_AIDE_COMPLEMENTAIRE_RESET:
      draft.deleteAideComplementaireLoading =
        initialState.deleteAideComplementaireLoading;
      draft.deleteAideComplementaireSuccess =
        initialState.deleteAideComplementaireSuccess;
      draft.deleteAideComplementaireError =
        initialState.deleteAideComplementaireError;
      break;
  }
}, initialState);

export default aideComplementaireReducer;
