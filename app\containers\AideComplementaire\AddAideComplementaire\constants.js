export const ADD_DONATION = 'almobadara/OMDBPage/ADD_DONATION';
export const ADD_DONATION_SUCCESS = 'almobadara/OMDBPage/ADD_DONATION_SUCCESS';
export const ADD_DONATION_ERROR = 'almobadara/OMDBPage/ADD_DONATION_ERROR';
export const ADD_DONATION_RESET = 'almobadara/OMDBPage/ADD_DONATION_RESET';

export const DELETE_DONATION = 'almobadara/OMDBPage/DELETE_DONATION';
export const DELETE_DONATION_SUCCESS =
  'almobadara/OMDBPage/DELETE_DONATION_SUCCESS';
// reset delete success
export const DELETE_DONATION_RESET =
  'almobadara/OMDBPage/DELETE_DONATION_RESET';
export const DELETE_DONATION_ERROR =
  'almobadara/OMDBPage/DELETE_DONATION_ERROR';
export const LOAD_DONATION = 'almobadara/addDonation/LOAD_DONATION';
export const LOAD_DONATION_SUCCESS =
  'almobadara/addDonation/LOAD_DONATION_SUCCESS';
export const LOAD_DONATION_ERROR = 'almobadara/addDonation/LOAD_DONATION_ERROR';

export const ADD_AIDECOMPLEMENTAIRE =
  'almobadara/OMDBPage/ADD_AIDECOMPLEMENTAIRE';
export const ADD_AIDECOMPLEMENTAIRE_SUCCESS =
  'almobadara/OMDBPage/ADD_AIDECOMPLEMENTAIRE_SUCCESS';
export const ADD_AIDECOMPLEMENTAIRE_ERROR =
  'almobadara/OMDBPage/ADD_AIDECOMPLEMENTAIRE_ERROR';
export const ADD_AIDECOMPLEMENTAIRE_RESET =
  'almobadara/OMDBPage/ADD_AIDECOMPLEMENTAIRE_RESET';

export const DELETE_AIDECOMPLEMENTAIRE =
  'almobadara/OMDBPage/DELETE_AIDECOMPLEMENTAIRE';
export const DELETE_AIDECOMPLEMENTAIRE_SUCCESS =
  'almobadara/OMDBPage/DELETE_AIDECOMPLEMENTAIRE_SUCCESS';
export const DELETE_AIDECOMPLEMENTAIRE_RESET =
  'almobadara/OMDBPage/DELETE_AIDECOMPLEMENTAIRE_RESET';
export const DELETE_AIDECOMPLEMENTAIRE_ERROR =
  'almobadara/OMDBPage/DELETE_AIDECOMPLEMENTAIRE_ERROR';

export const LOAD_AIDECOMPLEMENTAIRE_TO_ADD =
  'almobadara/OMDBPage/LOAD_AIDECOMPLEMENTAIRE_TO_ADD';
export const LOAD_AIDECOMPLEMENTAIRE_TO_ADD_SUCCESS =
  'almobadara/OMDBPage/LOAD_AIDECOMPLEMENTAIRE_TO_ADD_SUCCESS';
export const LOAD_AIDECOMPLEMENTAIRE_TO_ADD_ERROR =
  'almobadara/OMDBPage/LOAD_AIDECOMPLEMENTAIRE_TO_ADD_ERROR';

export const LOAD_BENEFICIARIES_WITH_TYPEPRISEENCHARGE =
  'almobadara/OMDBPage/LOAD_BENEFICIARIES_WITH_TYPEPRISEENCHARGE';
export const LOAD_BENEFICIARIES_WITH_TYPEPRISEENCHARGE_SUCCESS =
  'almobadara/OMDBPage/LOAD_BENEFICIARIES_WITH_TYPEPRISEENCHARGE_SUCCESS';
export const LOAD_BENEFICIARIES_WITH_TYPEPRISEENCHARGE_ERROR =
  'almobadara/OMDBPage/LOAD_BENEFICIARIES_WITH_TYPEPRISEENCHARGE_ERROR';
