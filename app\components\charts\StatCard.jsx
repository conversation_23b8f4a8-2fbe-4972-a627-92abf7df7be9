import React from 'react';
import { Box, Typography } from '@mui/material';
import { styled } from '@mui/material/styles';

const CardWrapper = styled(Box)(({ theme, color }) => ({
  padding: theme.spacing(2),
  height: '100%',
  display: 'flex',
  flexDirection: 'column',
  alignItems: 'center',
  justifyContent: 'center',
  background: 'linear-gradient(145deg, #ffffff, #f0f0f0)',
  borderRadius: '12px',
  boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
  transition: 'transform 0.3s ease, box-shadow 0.3s ease',
  '&:hover': {
    transform: 'translateY(-4px)',
    boxShadow: '0 8px 30px rgba(0, 0, 0, 0.12)',
  },
}));

const IconWrapper = styled(Box)(({ theme, color }) => ({
  width: '48px',
  height: '48px',
  borderRadius: '50%',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  marginBottom: theme.spacing(1.5),
  background: `${color}15`,
  color: color,
}));

export function StatCard({ icon, value, title, description, color = '#1976d2' }) {
  return (
    <CardWrapper color={color}>
      <IconWrapper color={color}>
        {icon}
      </IconWrapper>
      <Typography 
        variant="h4" 
        sx={{ 
          color: color, 
          fontWeight: 600, 
          mb: 0.5,
          textDecoration: 'none',
        }}
      >
        {value}
      </Typography>
      <Typography variant="h6" color="text.primary" fontWeight={600} align="center">
        {title}
      </Typography>
      {description && (
        <Typography variant="caption" color="text.secondary" align="center">
          {description}
        </Typography>
      )}
    </CardWrapper>
  );
}