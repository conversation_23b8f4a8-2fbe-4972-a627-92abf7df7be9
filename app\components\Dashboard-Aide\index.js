import React, { useEffect } from 'react';
import { Container, Typography, Paper, Box, Grid, CircularProgress } from '@mui/material';
import HomePage from '../../containers/HomePage';
import { useDispatch, useSelector } from 'react-redux';
import { useInjectReducer, useInjectSaga } from 'redux-injectors';
import generalDashboardReducer from '../Dashboard-General/reducer';
import generalDashboardSaga from '../Dashboard-General/saga';
import { createStructuredSelector } from 'reselect';
import { makeSelectAideDashboard, makeSelectAideDashboardLoading, makeSelectAideDashboardError, makeSelectAideDashboardSuccess } from '../Dashboard-General/selector';
import { fetchAideDashboard } from '../Dashboard-General/actions';
import { PieCard, pieColors } from '../charts/PieCard';
import PieChartIcon from '@mui/icons-material/PieChart';
import Animated<PERSON><PERSON><PERSON><PERSON> from 'components/charts/AnimatedLineChart';
import { AnimatedBar<PERSON><PERSON> } from 'components/charts/AnimatedBarChart';

const stateSelector = createStructuredSelector({
  data: makeSelectAideDashboard,
  loading: makeSelectAideDashboardLoading,
  error: makeSelectAideDashboardError,
  success: makeSelectAideDashboardSuccess
});

export default function DashboardAide() {
  useInjectReducer({ key: 'dashboardAll', reducer: generalDashboardReducer });
  useInjectSaga({ key: 'dashboardAll', saga: generalDashboardSaga });

  const { data, loading, error, success } = useSelector(stateSelector);

  const dispatch = useDispatch();

  useEffect(() => {
    dispatch(fetchAideDashboard());
  }, [dispatch]);

  return (
    <>
      <HomePage />
      {loading && (
          <Box
            sx={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              zIndex: 1,
              borderRadius: '20px'
            }}
          >
            <CircularProgress sx={{ color: 'black' }} />
          </Box>
        )}
      {success && data && data.aideByStatus && data.aideByDate && data.aideAmountByService && (
        <Container maxWidth="xl" sx={{ mt: 2, mb: 4 }}>
          <Grid container spacing={2}>
            <Grid item xs={12} md={6}>
            <PieCard
              title="Répartition des aides complémentaires par statut"
              data={Object.values(data.aideByStatus)}
              labels={Object.keys(data.aideByStatus).map(key => 
                key
                  .replace(/([A-Z])/g, ' $1')
                  .replace(/^./, str => str.toUpperCase())
              )}
              icon={<PieChartIcon color="primary" />}
              colors={[pieColors.info, pieColors.error, pieColors.primary, pieColors.success, pieColors.warning]}
            />
            </Grid>
            <Grid item xs={12} md={6}>
              <AnimatedLineChart
                title="Fréquence mensuelle des aides accordées"
                data={data.aideByDate}
                icon={<PieChartIcon color="primary" />}
                colors={[pieColors.info, pieColors.error, pieColors.primary, pieColors.success, pieColors.warning]}
              />
            </Grid>
          </Grid>
          <Grid container spacing={2} sx={{ mt: 2 }}>
            <Grid item xs={12} md={12}>
              <AnimatedBarChart
                title="Répartition des aides complémentaires par service"
                data={Object.values(data.aideAmountByService)}
                labels={Object.keys(data.aideAmountByService)}
                icon={<PieChartIcon color="primary" />}
                colors={[pieColors.info, pieColors.error, pieColors.primary, pieColors.success, pieColors.warning]}
              />
            </Grid>
          </Grid>
        </Container>
      )}
    </>
  );
} 