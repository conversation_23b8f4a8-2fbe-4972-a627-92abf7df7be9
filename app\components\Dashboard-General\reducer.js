// Fetch General Dashboard
import { <PERSON><PERSON><PERSON>_GENERAL_DASHBOARD_REQUEST, FETCH_DONORS_DASHBOARD_REQUEST, FETCH_BENEFICIARIES_DASHBOARD_REQUEST, <PERSON>ETCH_DONATIONS_DASHBOARD_REQUEST, <PERSON><PERSON><PERSON>_KAFALAT_DASHBOARD_REQUEST, <PERSON><PERSON><PERSON>_AIDE_DASHBOARD_REQUEST, FETCH_EPS_DASHBOARD_REQUEST, FETCH_GENERAL_DASHBOARD_SUCCESS, FETCH_GENERAL_DASHBOARD_FAILURE, FETCH_DONORS_DASHBOARD_SUCCESS, FETCH_DONORS_DASHBOARD_FAILURE, FETCH_BENEFICIARIES_DASHBOARD_SUCCESS, FETCH_BENEFICIARIES_DASHBOARD_FAILURE, FETCH_DONATIONS_DASHBOARD_SUCCESS, <PERSON><PERSON><PERSON>_DONATIONS_DASHBOARD_FAILURE, <PERSON><PERSON><PERSON>_KAFALAT_DASHBOARD_SUCCESS, <PERSON>ET<PERSON>_KAFALAT_DASHBOARD_FAILURE, FET<PERSON>_AIDE_DASHBOARD_SUCCESS, FETCH_AIDE_DASHBOARD_FAILURE, FETCH_EPS_DASHBOARD_SUCCESS, FETCH_EPS_DASHBOARD_FAILURE } from './constants';
import produce from 'immer';

const initialState = {
  loading: false,
  data: null,
  error: null,
  success: false,
};  

export const generalDashboardReducer = produce((draft, action) => {
  switch (action.type) {
    case FETCH_GENERAL_DASHBOARD_REQUEST:
      draft.loading = true;
      draft.error = null;
      draft.data = null;
      break;
    case FETCH_GENERAL_DASHBOARD_SUCCESS:
      draft.loading = false;
      draft.data = action.payload;
      break;
    case FETCH_GENERAL_DASHBOARD_FAILURE:
      draft.loading = false;
      draft.error = action.payload;
      break;
    case FETCH_DONORS_DASHBOARD_REQUEST:
      draft.loading = true;
      draft.error = null;
      draft.data = null;
      break;
    case FETCH_DONORS_DASHBOARD_SUCCESS:
      draft.loading = false;
      draft.data = action.payload;
      break;
    case FETCH_DONORS_DASHBOARD_FAILURE:
      draft.loading = false;
      draft.error = action.payload;
      break;
    case FETCH_BENEFICIARIES_DASHBOARD_REQUEST:
      draft.loading = true;
      draft.error = null;
      draft.data = null;
      break;
    case FETCH_BENEFICIARIES_DASHBOARD_SUCCESS:
      draft.loading = false;
      draft.success = true;
      draft.data = action.payload;
      break;
    case FETCH_BENEFICIARIES_DASHBOARD_FAILURE:
      draft.loading = false;
      draft.error = action.payload;
      break;
    case FETCH_DONATIONS_DASHBOARD_REQUEST:
      draft.loading = true;
      draft.error = null;
      draft.data = null;
      break;
    case FETCH_DONATIONS_DASHBOARD_SUCCESS:
      draft.loading = false;
      draft.success = true;
      draft.data = action.payload;
      break;
    case FETCH_DONATIONS_DASHBOARD_FAILURE:
      draft.loading = false;
      draft.error = action.payload;
      break;
    case FETCH_KAFALAT_DASHBOARD_REQUEST:
      draft.loading = true;
      draft.error = null;
      draft.data = null;
      break;
    case FETCH_KAFALAT_DASHBOARD_SUCCESS:
      draft.loading = false;
      draft.success = true;
      draft.data = action.payload;
      break;
    case FETCH_KAFALAT_DASHBOARD_FAILURE:
      draft.loading = false;
      draft.error = action.payload;
      break;
    case FETCH_AIDE_DASHBOARD_REQUEST:
      draft.loading = true;
      draft.error = null;
      draft.data = null;
      break;
    case FETCH_AIDE_DASHBOARD_SUCCESS:
      draft.loading = false;
      draft.success = true;
      draft.data = action.payload;
      break;
    case FETCH_AIDE_DASHBOARD_FAILURE:
      draft.loading = false;
      draft.error = action.payload;
      break;
    case FETCH_EPS_DASHBOARD_REQUEST:
      draft.loading = true;
      draft.error = null;
      draft.data = null;
      break;
    case FETCH_EPS_DASHBOARD_SUCCESS:
      draft.loading = false;
      draft.success = true;
      draft.data = action.payload;
      break;
    case FETCH_EPS_DASHBOARD_FAILURE:
      draft.loading = false;
      draft.error = action.payload;
      break;
    default:
      break;
  }
},initialState);

export default generalDashboardReducer;