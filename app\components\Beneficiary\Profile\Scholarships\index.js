import React, { useEffect, useState } from 'react';
import moment from 'moment';

import Modal2 from 'components/Common/Modal2';
import styles from 'Css/profileList.css';
import stylesList from 'Css/profileList.css';
import btnStyles from 'Css/button.css';
import { DELETE_ICON, EDIT_ICON } from 'components/Common/ListIcons/ListIcons';
import emptyTable from 'containers/Common/Scripts/GenerateEmptyTable';
import ScholarshipForm from 'containers/Common/Forms/ScholarshipForm';
import { scholarshipSaga } from 'containers/Common/Forms/ScholarshipForm/saga';
import {
  makeSelecScholarships,
  makeSelectScholarship,
  makeSelectScholarshipAddSuccess,
  makeSelectSuccess,
  makeSelectSuccessDelete,
} from 'containers/Common/Forms/ScholarshipForm/selectors';
import { useDispatch, useSelector } from 'react-redux';
import {
  pushScholarship,
  removeScholarship,
  updateScholarship,
} from 'containers/Beneficiary/BeneficiaryProfile/actions';
import { createStructuredSelector } from 'reselect';
import { useInjectReducer, useInjectSaga } from 'redux-injectors';
import {
  deleteScholarship,
  loadScholarshipData,
  resetScholarship,
} from 'containers/Common/Forms/ScholarshipForm/actions';
import reducer from 'containers/Common/Forms/ScholarshipForm/reducer';
import AccessControl from 'utils/AccessControl';
import { useLocation } from 'react-router-dom';

const key = 'beneficiaryScholarship';

const target = 'beneficiary';

const formatDate = date => moment(date).format('DD/MM/YYYY');

const omdbSelector = createStructuredSelector({
  success: makeSelectSuccess,
  scholarship: makeSelectScholarship,
  successDelete: makeSelectSuccessDelete,
  scholarships: makeSelecScholarships,
  scholarshipAddSuccess: makeSelectScholarshipAddSuccess,
});

export default function Scholarships(props) {
  const dispatch = useDispatch();
  const { isEducated } = props;
  const {
    success,
    scholarship,
    successDelete,
    scholarships,
    scholarshipAddSuccess,
  } = useSelector(omdbSelector);

  useInjectSaga({ key, saga: scholarshipSaga });
  useInjectReducer({ key, reducer });

  const beneficiary = props.data;
  const successAlert = null;
  let listScholarships = emptyTable(6);

  const [hide, setHide] = useState(false);
  const location = useLocation();

  useEffect(() => {
    if (location && location.state && location.state.isOldBeneficiary) {
      setHide(location.state.isOldBeneficiary);
    } else {
      setHide(false);
    }
  }, [location]);

  const [showModal, setShowModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [actualScholarship, setActualScholarship] = useState('');
  let message = null;
  const { hasScholarships } = props;
  const handleActualScholarship = scholarship => {
    setActualScholarship(scholarship);
    setShowModal(true);
  };
  const handleClose = () => setShowModal(false);
  const handleShow = () => setShowModal(true);
  const handleCloseForDeleteModal = () => setShowDeleteModal(false);
  const handleShowForDeleteModal = () => setShowDeleteModal(true);

  useEffect(() => {
    if (successDelete) {
      dispatch(removeScholarship(scholarship));
      message = 'La bourse a été bien supprimée !';

      props.educationAlertHandler(message);
      dispatch(resetScholarship());
    }
  }, [successDelete]);

  useEffect(() => {
    if (success) {
      if (actualScholarship == '') {
        dispatch(pushScholarship(scholarship));
        message = 'La bourse a été bien ajoutée !';
        setShowModal(false);
      } else {
        dispatch(updateScholarship(scholarship));
        message = 'La bourse a été bien modifiée !';
        setShowModal(false);
      }
      props.educationAlertHandler(message);
    }
    dispatch(resetScholarship());
  }, [success]);

  useEffect(() => {
    if (beneficiary && beneficiary.id) {
      dispatch(loadScholarshipData(beneficiary.id, target));
    }
  }, [beneficiary]);

  useEffect(() => {
    if (successDelete || scholarshipAddSuccess) {
      dispatch(loadScholarshipData(beneficiary.id, target));
    }
  }, [successDelete, scholarshipAddSuccess]);

  if (scholarships) {
    const sortedScholarships = [...scholarships].sort((a, b) =>
      a.createdAt < b.createdAt ? 1 : -1,
    );

    listScholarships = sortedScholarships.map(scholarship => (
      <tr key={scholarship.id}>
        <th scope="row">
          {scholarship.scholarship ? scholarship.scholarship.name : '-'}
        </th>
        <td>{scholarship.periodicity ? scholarship.periodicity : '-'}</td>
        <td>{scholarship.amount ? scholarship.amount : '-'}</td>
        <td>
          {scholarship.valueCurrency
            ? `${scholarship.valueCurrency} ${scholarship.currency.name}`
            : '-'}
        </td>
        <td>
          {scholarship.startDate ? formatDate(scholarship.startDate) : '-'}
        </td>
        <td>{scholarship.endDate ? formatDate(scholarship.endDate) : '---'}</td>
        {!hide && isEducated && (
          <td className="p-0">
            <AccessControl module="BENEFICIARY" functionality="UPDATE">
              <>
                <input
                  type="image"
                  src={EDIT_ICON}
                  className="p-2"
                  width="40px"
                  height="40px"
                  onClick={() => {
                    handleActualScholarship(scholarship);
                  }}
                />
                <input
                  type="image"
                  src={DELETE_ICON}
                  className="p-2"
                  width="40px"
                  height="40px"
                  onClick={() => {
                    handleShowForDeleteModal();
                    setActualScholarship(scholarship);
                  }}
                />
              </>
            </AccessControl>
          </td>
        )}
      </tr>
    ));
  }

  return (
    <div>
      {successAlert}
      <div className={stylesList.backgroudStyle}>
        <div className={styles.global}>
          <Modal2
            size="lg"
            customWidth="modal-90w"
            title={
              actualScholarship ? 'Modifier une bourse' : 'Ajouter une bourse'
            }
            show={showModal}
            handleClose={handleClose}
          >
            <ScholarshipForm
              scholarship={actualScholarship}
              show={showModal}
              handleClose={handleClose}
              beneficiaryId={beneficiary ? beneficiary.id : null}
            />
          </Modal2>

          <Modal2
            centered
            className="mt-5"
            title="Confirmation de suppression"
            show={showDeleteModal}
            handleClose={handleCloseForDeleteModal}
          >
            <p className="mt-1 mb-5 px-2">
              Êtes-vous sûr de vouloir supprimer cette bourse?
            </p>
            <div className="d-flex justify-content-end px-2 my-1">
              <button
                type="button"
                className="btn-style outlined"
                onClick={handleCloseForDeleteModal}
              >
                Annuler
              </button>
              <button
                type="submit"
                className="ml-3 btn-style primary"
                onClick={() => {
                  dispatch(deleteScholarship(actualScholarship));
                  setActualScholarship('');
                  handleCloseForDeleteModal();
                }}
              >
                Supprimer
              </button>
            </div>
          </Modal2>

          <div className={`${styles.header}`}>
            <h4>Liste des bourses</h4>
            {!hide && isEducated && (
              <AccessControl module="BENEFICIARY" functionality="UPDATE">
                <button
                  className={`mb-3 ${btnStyles.addBtnProfile}`}
                  onClick={() => {
                    handleActualScholarship('');
                    handleShow();
                  }}
                >
                  Ajouter
                </button>
              </AccessControl>
            )}
          </div>

          <div className={styles.content}>
            <table className="table small">
              <thead>
                <tr>
                  <th scope="col">Nom</th>
                  <th scope="col">Périodicité</th>
                  <th scope="col">Montant (DH)</th>
                  <th scope="col">Valeur en devise</th>
                  <th scope="col">Date début</th>
                  <th scope="col">Date fin</th>
                  {!hide && isEducated && (
                    <AccessControl module="BENEFICIARY" functionality="UPDATE">
                      <th scope="col">Actions</th>
                    </AccessControl>
                  )}
                </tr>
              </thead>
              {listScholarships && listScholarships.length > 0 ? (
                <tbody>{listScholarships}</tbody>
              ) : (
                <tbody>
                  <tr>
                    <td colSpan="7" style={{ textAlign: 'center' }}>
                      Aucune bourse trouvée
                    </td>
                  </tr>
                </tbody>
              )}
            </table>
          </div>
        </div>
      </div>
    </div>
  );
}
