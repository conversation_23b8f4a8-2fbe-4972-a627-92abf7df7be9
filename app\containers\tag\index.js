import React, { useEffect, useState } from 'react';
import listStyles from 'Css/list.css';
import { useDispatch, useSelector } from 'react-redux';
import { useInjectReducer, useInjectSaga } from 'redux-injectors';
import CustomPagination from 'components/Common/CustomPagination';

import Modal2 from 'components/Common/Modal2';
import btnStyles from 'Css/button.css';
import { createStructuredSelector } from 'reselect';
import AccessControl from 'utils/AccessControl';
import { Link, useHistory } from 'react-router-dom';
import DataTable from 'components/Common/DataTable';
import { Alert } from 'react-bootstrap';
import { makeSelectTags, makeSelectLoading, makeSelectDeleteSuccess, makeSelectError, makeSelectSuccess } from './selectors';
import tagSaga from './saga';
import tagReducer from './reducer';
import { loadTags, deleteTag, resetError } from './actions';
import TagForm from './TagForm';
import {
    DELETE_ICON,
    EDIT_ICON,
} from 'components/Common/ListIcons/ListIcons';

const key = 'tagList';

const stateSelector = createStructuredSelector({
    tags: makeSelectTags,
    loading: makeSelectLoading,
    error: makeSelectError,
    deleteSuccess: makeSelectDeleteSuccess,
    success: makeSelectSuccess,
});

export default function TagList(props) {
    useInjectReducer({ key, reducer: tagReducer });
    useInjectSaga({ key, saga: tagSaga });

    const {
        tags,
        loading,
        error,
        deleteSuccess,
        success,
    } = useSelector(stateSelector);

    const dispatch = useDispatch();
    const history = useHistory();
    const [showAddTag, setShowAddTag] = useState(false);
    const [showAlert, setShowAlert] = useState(false);
    const [message, setMessage] = useState('');
    const [showDeleteModal, setShowDeleteModal] = useState(false);
    const [tagToDelete, setTagToDelete] = useState('');
    const [tagToEdit, setTagToEdit] = useState(null);
    const [isEditMode, setIsEditMode] = useState(false);
    const [errorMessages, setErrorMessages] = useState('');

    const handleCloseForAddTag = () => {
        setShowAddTag(false);
        setTagToEdit(null);
    };
    const handleCloseForDeleteModal = () => setShowDeleteModal(false);

    useEffect(() => {
        if (error) {
            console.log('error', error);
            if (error.detail.includes('Type Tag with the same name already exists')) {
                setErrorMessages('Ce type de tag avec ce nom dejà existe');
            } else if (error.detail.includes('Tag with the same name already exists')) {
                setErrorMessages('Ce tag avec ce nom dejà existe');
            }

            setShowAddTag(false);
            const timer = setTimeout(() => {
                dispatch(resetError());
                setErrorMessages('');
            }, 4000);
            return () => clearTimeout(timer);
        }
    }, [error]);

    useEffect(() => {
        console.log('Loading tags...');
        dispatch(loadTags(0));
        console.log("isEditMode : ", isEditMode);
    }, [dispatch]);

    useEffect(() => {
        console.log('Tags data:', tags);
        console.log('Success state:', success);
        if (success) {
            setShowAlert(true);
            if (!isEditMode) {
                setMessage('Tag ajouté avec succées !');
            } else {
                setMessage('Tag modifié avec succées !');
            }
            setIsEditMode(false)
            dispatch(loadTags(0)); // Reload tags after success
            setTimeout(() => {
                setShowAlert(false);
            }, 3000);
        }
    }, [success, dispatch]);

    useEffect(() => {
        console.log('tg list', tags);
    }, [tags]);

    useEffect(() => {
        if (deleteSuccess) {
            setShowAlert(true);
            setMessage('Tag supprimé avec succès');
            dispatch(loadTags(0)); // Reload tags after delete
            setTimeout(() => {
                setShowAlert(false);
            }, 3000);
        }
    }, [deleteSuccess, dispatch]);

    const columns = [
        {
            field: 'name',
            headerName: 'Nom du tag',
            flex: 1,
            headerAlign: 'center',
            align: 'center',
            renderCell: params => (
                <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                    <span>{params.row.name}</span>
                     
                </div>
            ),
        },
        {
            field: 'typeTagName',
            headerName: 'Type de tag',
            flex: 1,
            headerAlign: 'center',
            align: 'center',
        },
        {
            field: 'color',
            headerName: 'Couleur du tag',
            flex: 1,
            headerAlign: 'center',
            align: 'center',
            renderCell: params => <ColorDisplay color={params.row.color} />,
        },
        {
            field: 'actions',
            headerName: 'Actions',
            flex: 1,
            headerAlign: 'center',
            align: 'center',
            renderCell: params => (
                <div
                    style={{
                        display: 'flex',
                        flexDirection: 'row',
                        justifyContent: 'space-around',
                        alignItems: 'center',

                    }}
                >
                    <input
                        type="image"
                        onClick={() => {
                            if (!params.row.readOnly) {
                                handleEdit(params.row.id);
                            }
                        }}
                        className="p-2"
                        src={EDIT_ICON}
                        width="40px"
                        height="40px"
                        title={params.row.readOnly ? "Modification non autorisée" : "Modifier"}
                        style={{
                            opacity: params.row.readOnly ? 0.5 : 1,
                            cursor: params.row.readOnly ? 'not-allowed' : 'pointer'
                        }}
                        disabled={params.row.readOnly}
                    />
                    <input
                        type="image"
                        src={DELETE_ICON}
                        className="p-2"
                        width="40px"
                        height="40px"
                        onClick={() => {
                            if (!params.row.readOnly) {
                                setTagToDelete(params.row.id);
                                setShowDeleteModal(true);
                            }
                        }}
                        title={params.row.readOnly ? "Suppression non autorisée" : "Supprimer"}
                        style={{
                            opacity: params.row.readOnly ? 0.5 : 1,
                            cursor: params.row.readOnly ? 'not-allowed' : 'pointer'
                        }}
                        disabled={params.row.readOnly}
                    />
                </div>
            ),
        },
    ];

    const handleEdit = (tagId) => {
        const tagToEdit = tags.content.find(tag => tag.id === tagId);
        if (tagToEdit && !tagToEdit.readOnly) {
            setIsEditMode(true)
            setTagToEdit(tagToEdit);
            setShowAddTag(true);
        }
    };

    const handleDelete = (tagId) => {
        const tagToDelete = tags.content.find(tag => tag.id === tagId);
        if (tagToDelete && !tagToDelete.readOnly) {
            dispatch(deleteTag(tagId));
            setShowDeleteModal(false);
        } else {
            setShowDeleteModal(false);
        }
    };

    const ColorDisplay = ({ color }) => (
        <div style={{
            display: 'flex',
            alignItems: 'center',
            gap: '10px',
            padding: '8px',
            backgroundColor: '#f8f9fa',
            borderRadius: '8px',
            borderColor: '#ffffff',
        }}>
            <div style={{
                backgroundColor: `#${color}`,
                width: '30px',
                height: '30px',
                borderRadius: '20%',
                border: 'solid',
                borderWidth: '1px',
                borderColor: '#05050550'
            }} />
            <span style={{
                fontFamily: 'monospace',
                color: '#495057'
            }}>
                #{color}
            </span>
        </div>
    );

    const handlePageChange = (page) => {
        dispatch(loadTags(page - 1));
    };

    return (
        <>
            {errorMessages && (
                <Alert
                    className='alert-style'
                    variant="danger"
                    onClose={() => setErrorMessages('')}
                    dismissible
                >
                    {errorMessages}
                </Alert>
            )}
            {showAlert ? (
                <Alert
                    className='alert-style'
                    variant="success"
                    onClose={() => setShowAlert(false)}
                    dismissible
                >
                    <p>{message}</p>
                </Alert>
            ) : null}

            <Modal2
                centered
                className="mt-5"
                title={tagToEdit ? "Modification du tag" : "Ajout d\'un nouveau tag"}
                show={showAddTag}
                handleClose={handleCloseForAddTag}
            >
                <TagForm
                    handleClose={handleCloseForAddTag}
                    tagToEdit={tagToEdit}
                />
            </Modal2>

            <Modal2
                centered
                className="mt-5"
                title="Confirmation de suppression"
                show={showDeleteModal}
                handleClose={handleCloseForDeleteModal}
            >
                <div className="p-3">
                    <p className="mb-4">
                        Êtes-vous sûr de vouloir supprimer ce tag ? Cette action est irréversible.
                    </p>
                    <div className="d-flex justify-content-end gap-2">
                        <button
                            type="button"
                            className={`${btnStyles.cancelBtn}`}
                            onClick={handleCloseForDeleteModal}
                        >
                            Annuler
                        </button>
                        <button
                            type="button"
                            className={`${btnStyles.deleteBtn}`}
                            onClick={() => {
                                handleDelete(tagToDelete);
                                handleCloseForDeleteModal();
                            }}
                        >
                            Supprimer
                        </button>
                    </div>
                </div>
            </Modal2>

            <div className={listStyles.backgroundStyle}>
                <div
                    className={listStyles.head}
                    style={{ display: 'flex', justifyContent: 'space-between' }}
                >
                    <h4 style={{ color: '#000' }}>Liste des Tags</h4>
                    <div
                        style={{
                            display: 'flex',
                            flexDirection: 'column',
                            alignItems: 'flex-end',
                        }}
                    >
                        <button
                            style={{ marginBottom: '10px' }}
                            className={btnStyles.addBtnProfile}
                            onClick={() => setShowAddTag(true)}
                            disabled={loading}
                        >
                            {loading ? 'Chargement...' : 'Ajouter un tag'}
                        </button>
                    </div>
                </div>
                <div className="sub-container">
                    <div className="table-container">
                        {loading ? (
                            <div style={{
                                display: 'flex',
                                justifyContent: 'center',
                                alignItems: 'center',
                                height: '200px'
                            }}>
                                <div className="spinner-border text-primary" role="status">
                                </div>
                            </div>
                        ) : (
                            <>
                                <DataTable
                                    rows={Array.isArray(tags.content) ? tags.content : []}
                                    columns={columns}
                                    fileName={`Liste des Tags, ${new Date().toLocaleString()}`}
                                />
                                {tags.totalPages > 0 && (
                                    <div className="justify-content-center mt-3">
                                        <CustomPagination
                                            totalElements={tags.totalElements}
                                            totalCount={tags.totalPages}
                                            pageSize={tags.pageSize}
                                            currentPage={tags.number + 1}
                                            onPageChange={handlePageChange}
                                        />
                                    </div>
                                )}
                            </>
                        )}
                    </div>
                </div>
            </div>
        </>
    );
}