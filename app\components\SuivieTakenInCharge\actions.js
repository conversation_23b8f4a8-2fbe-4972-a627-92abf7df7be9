import {
  GET_LIST_OPERATIONS_TAKEN_IN_CHARGE_ERROR,
  GET_LIST_OPERATIONS_TAKEN_IN_CHARGE,
  GET_LIST_OPERATIONS_TAKEN_IN_CHARGE_SUCCESS,
  CHANGE_OPERATION_STATUS,
  CHANGE_OPERATION_STATUS_SUCCESS,
  CHANGE_OPERATION_STATUS_ERROR,
} from './constants';

export function getListOperationsTakenInCharge(pageNumber, filters) {
  return {
    type: GET_LIST_OPERATIONS_TAKEN_IN_CHARGE,
    pageNumber,
    filters,
  };
}

export function getListOperationsTakenInChargeSuccess(data) {
  return {
    type: GET_LIST_OPERATIONS_TAKEN_IN_CHARGE_SUCCESS,
    data,
  };
}

export function getListOperationsTakenInChargeError(error) {
  return {
    type: GET_LIST_OPERATIONS_TAKEN_IN_CHARGE_ERROR,
    error,
  };
}

export function changeOperationStatus({
  ids = [],
  status = '',
  executionDate = '',
}) {
  return {
    type: CHANGE_OPERATION_STATUS,
    ids,
    status,
    executionDate,
  };
}

export function changeOperationStatusSuccess({ success = false }) {
  return {
    type: CHANGE_OPERATION_STATUS_SUCCESS,
    success,
  };
}
export function changeOperationStatusError({ error = false }) {
  return {
    type: CHANGE_OPERATION_STATUS_ERROR,
    error,
  };
}
