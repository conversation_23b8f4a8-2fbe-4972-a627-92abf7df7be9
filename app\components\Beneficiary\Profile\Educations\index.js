import React, { useEffect, useState } from 'react';
import moment from 'moment';
import styles from 'Css/profileList.css';
import stylesList from 'Css/profileList.css';
import btnStyles from 'Css/button.css';
import { DELETE_ICON, EDIT_ICON } from 'components/Common/ListIcons/ListIcons';
import emptyTable from 'containers/Common/Scripts/GenerateEmptyTable';
import tagStyles from 'Css/tag.css';
import Modal2 from 'components/Common/Modal2';
import EducationForm from 'containers/Common/Forms/EducationForm';
import {
  makeSelecEducations,
  makeSelectEducation,
  makeSelectEducationsAddBeneficiarySuccess,
  makeSelectSuccessDelete,
  makeSelectUpdateEducationSuccess,
} from 'containers/Common/Forms/EducationForm/selectors';

import {
  pushEducation,
  removeEducation,
  updateEducation,
  makeBeneficiaryNotEducated,
} from 'containers/Beneficiary/BeneficiaryProfile/actions';

import { useInjectReducer, useInjectSaga } from 'redux-injectors';
import { createStructuredSelector } from 'reselect';

import { educationSaga } from 'containers/Common/Forms/EducationForm/saga';

import {
  deleteEducation,
  loadEducationData,
  resetEducation,
  updateEducationCurrentReset,
  updateEducationCurrentSuccess,
} from 'containers/Common/Forms/EducationForm/actions';
import reducer from 'containers/Common/Forms/EducationForm/reducer';
import { useDispatch, useSelector } from 'react-redux';
import AccessControl from 'utils/AccessControl';
import { useLocation } from 'react-router-dom';
import { makeSelectSchoolLevels } from 'containers/Common/SubComponents/SchoolLevels/selectors';
import { makeSelectBeneficiaries } from 'containers/Beneficiary/BeneficiaryProfile/selectors';
import { has } from 'lodash';

const key = 'education';

const target = 'beneficiary';

const omdbSelector = createStructuredSelector({
  education: makeSelectEducation,
  successDelete: makeSelectSuccessDelete,
  educations: makeSelecEducations,
  educationBeneficiaryAddSuccess: makeSelectEducationsAddBeneficiarySuccess,
  schoolLevels: makeSelectSchoolLevels,
  beneficiary: makeSelectBeneficiaries,
  udpatEducationCurrentSuccess: makeSelectUpdateEducationSuccess,
});

export default function Educations(props) {
  const dispatch = useDispatch();
  const {isEducated} = props;
  const {hasEducations} = props;
  const {
    education,
    successDelete,
    educations,
    educationBeneficiaryAddSuccess,
    schoolLevels,
    beneficiary,
    udpatEducationCurrentSuccess,
  } = useSelector(omdbSelector);

  useInjectSaga({ key, saga: educationSaga });
  useInjectReducer({ key, reducer });

  const [educationType, setEducationType] = useState('');
  const [showModal, setShowModal] = useState(false);
  const [isEditModal, setIsEditModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [actualEducation, setActualEducation] = useState('');
  const [showNonScolariseModal, setShowNonScolariseModal] = useState(false);
  const handleConfirmNonScolarise = () => {
    if (beneficiary && beneficiary.id) {
      // Dispatch action to update education status to false
      dispatch(makeBeneficiaryNotEducated(beneficiary.id));
    }
    setShowNonScolariseModal(false);
  };

  let message = null;

  const [hide, setHide] = useState(false);
  const location = useLocation();

  useEffect(() => {
    if (location && location.state && location.state.isOldBeneficiary) {
      setHide(location.state.isOldBeneficiary);
    } else {
      setHide(false);
    }
  }, [location]);

  const handleActualEducation = scholarship => {
    setActualEducation(scholarship);
    setShowModal(true);
  };
  const handleClose = () => {
    setShowModal(false);
    setIsEditModal(false);
  };
  const handleShow = () => {
    setTimeout(() => {
      setShowModal(true);
    }, 450);
  };

  useEffect(() => {}, [showModal]);
  const handleCloseForDeleteModal = () => setShowDeleteModal(false);
  const handleShowForDeleteModal = () => setShowDeleteModal(true);

  // const beneficiary = props.data;
  let listEducations = emptyTable(8);

  useEffect(() => {
    if (successDelete) {
      dispatch(removeEducation(education));
      message = "Le niveau d'éducation a été bien supprimé !";

      props.educationAlertHandler(message);
      dispatch(resetEducation());
    }
  }, [successDelete]);

  useEffect(() => {
    if (educationBeneficiaryAddSuccess) {
      if (actualEducation == '') {
        dispatch(pushEducation(education));
        message = "Le niveau d'éducation a été bien ajouté !";
      } else {
        dispatch(updateEducation(education));
        message = "Le niveau d'éducation a été bien modifié !";
      }
      props.educationAlertHandler(message);
    }
    dispatch(resetEducation());
  }, [educationBeneficiaryAddSuccess]);

  useEffect(() => {
    if (udpatEducationCurrentSuccess) {
      if(isEditModal){
        message = "Le niveau d'éducation a été bien modifié !";
      } else {
        message = "Le niveau d'éducation a été bien ajouté !";
      }
      props.educationAlertHandler(message);
      dispatch(updateEducationCurrentReset());
    }
  }, [udpatEducationCurrentSuccess]);

  useEffect(() => {
    if (beneficiary && beneficiary.id) {
      dispatch(loadEducationData(beneficiary.id, target));
    }
  }, [beneficiary]);

  useEffect(() => {
    if (successDelete || educationBeneficiaryAddSuccess) {
      dispatch(loadEducationData(beneficiary.id, target));
    }
  }, [successDelete, educationBeneficiaryAddSuccess]);

  if (props.data) {
    const getMarkStyle = succeeded => {
      if (succeeded == true) {
        return styles.markGreen;
      }
      if (succeeded == false) {
        return styles.markRed;
      }
    };
    const getTag = succeeded => {
      if (succeeded == true) {
        return tagStyles.tagGreen;
      }
      if (succeeded == false) {
        return tagStyles.tagRed;
      }
    };

    if (educations) {
      const sortedEducations = [...educations].sort((a, b) =>
        a.createdAt < b.createdAt ? 1 : -1,
      );
      listEducations = sortedEducations.map(education => (
        <tr key={education.id}>
          <th scope="row">
            {education.educationType
              ? education.educationType.charAt(0).toUpperCase() +
                education.educationType.slice(1)
              : '-'}
          </th>
          <th scope="row">
            {education.schoolLevel ? education.schoolLevel.name : '-'}
          </th>
          <td>{education.schoolName ? education.schoolName : '-'}</td>
          <td>{education.info ? education.info.name : '-'}</td>
          <td>{education.schoolYear ? education.schoolYear.name : '-'}</td>
          <td>
            <div className={getTag(education.succeed)}>
              {education.succeed ? 'Réussi' : 'Non Réussi'}
            </div>
          </td>
          <td>
            <div className={getMarkStyle(education.succeed)}>
              {education.mark}
            </div>
          </td>
          <td>{education.honor ? education.honor.name : '-'}</td>
          <td>
            {education.major && education.major.name
              ? education.major.name
              : '---'}
          </td>
          {!hide && isEducated && (
            <td className="p-0">
              <>
                <input
                  type="image"
                  src={EDIT_ICON}
                  className="p-2"
                  width="40px"
                  height="40px"
                  onClick={() => {
                    setEducationType(education.educationType);
                    handleActualEducation(education);
                    setIsEditModal(true);
                  }}
                />
                <input
                  type="image"
                  src={DELETE_ICON}
                  className="p-2"
                  width="40px"
                  height="40px"
                  onClick={() => {
                    handleShowForDeleteModal();
                    setActualEducation(education);
                  }}
                />
              </>
            </td>
          )}
        </tr>
      ));
    }
  }
  const extractUniqueTypes = data => {
    const types = [...new Set(data.map(item => item.type))];
    return types.map(type => ({
      value: type,
      label: type.charAt(0).toUpperCase() + type.slice(1),
    }));
  };

  const [schoolLevelTypes, setSchoolLevelTypes] = useState([]);
  useEffect(() => {
    if (schoolLevels) {
      setSchoolLevelTypes(extractUniqueTypes(schoolLevels));
    }
  }, [schoolLevels]);

  useEffect(() => {
    if (schoolLevels) {
      setSchoolLevelTypes(extractUniqueTypes(schoolLevels));
    }
  }, [schoolLevels]);

  const schoolLevelHierarchy = [
    'préscolaire',
    'primaire',
    'secondaire',
    'universitaire',
  ];

  const currentSchoolLevelType = beneficiary
    ? beneficiary.person.schoolLevel.type
    : null;

  const isOptionAllowed = optionType => {
    if (!currentSchoolLevelType) return false;
    const currentIndex = schoolLevelHierarchy.indexOf(currentSchoolLevelType);
    const optionIndex = schoolLevelHierarchy.indexOf(optionType);
    return (
      currentIndex !== -1 && optionIndex !== -1 && optionIndex <= currentIndex
    );
  };

  return (
    <div className={styles.global}>
      {!isEducated && (
        <div
          style={{
            backgroundColor: '#e9f7ef', // A soft green background
            padding: '20px',
            borderRadius: '20px',
            marginBottom: '20px',
            border: '1px solid #c6e6d6', // A lighter green for the border
            textAlign: 'center',
          }}
        >
          <p
            style={{
              fontSize: '16px',
              color: '#333', // Dark grey for text
              marginBottom: '15px',
              fontWeight: '500',
            }}
          >
            📚 Ce bénéficiaire est actuellement <strong>non scolarisé</strong>.
            Vous pouvez mettre à jour son état de scolarisation si nécessaire.
          </p>
          <button
            className={`${btnStyles.addBtn}`}
            style={{
              backgroundColor: '#28a745', // A gentle green for the button
              color: 'white',
              fontWeight: '700',
              padding: '10px 20px',
              borderRadius: '4px',
              border: 'none',
              cursor: 'pointer',
            }}
            onClick={() => {
              handleActualEducation('');
              setEducationType('actuelle');
              handleShow(true);
              setIsEditModal(false);
            }}
          >
            Déclarer comme scolarisé
          </button>
        </div>
      )}

      <div className={stylesList.backgroudStyle}>
        <div>
          <Modal2
            centered------
            className="mt-5"
            title="Confirmation"
            show={showNonScolariseModal}
            handleClose={() => setShowNonScolariseModal(false)}
          >
            <p className="mt-1 mb-5 px-2">
              Êtes-vous sûr de vouloir marquer ce bénéficiaire comme non
              scolarisé ?
            </p>
            <div className="d-flex justify-content-end px-2 my-1 ">
              <button
                type="button"
                className={`${btnStyles.cancelBtn}`}
                onClick={() => setShowNonScolariseModal(false)}
              >
                Annuler
              </button>
              <button
                type="submit"
                className={`ml-3 ${btnStyles.addBtn}`}
                onClick={handleConfirmNonScolarise}
              >
                Confirmer
              </button>
            </div>
          </Modal2>

          <Modal2
            size="lg"
            customWidth="modal-90w"
            title={isEditModal ? "Modifier le niveau scolaire actuel" : 'Ajouter une éducation'}
            show={showModal}
            handleClose={handleClose}
          >
            <EducationForm
              education={actualEducation}
              educationType={educationType}
              show={showModal}
              isEditModal={isEditModal}
              handleClose={handleClose}
              beneficiaryId={beneficiary ? beneficiary.id : null}
              beneficiaryCurrentEducation={beneficiary}
              personEducationType={
                beneficiary ? beneficiary.person.schoolLevel.type : null
              }
            />
          </Modal2>

          <Modal2
            centered
            className="mt-5"
            title="Confirmation de suppression"
            show={showDeleteModal}
            handleClose={handleCloseForDeleteModal}
          >
            <p className="mt-1 mb-5 px-2">
              Êtes-vous sûr de vouloir supprimer ce niveau d'éducation?
            </p>
            <div className="d-flex justify-content-end px-2 my-1 ">
              <button
                type="button"
                className="btn-style outlined"
                onClick={handleCloseForDeleteModal}
              >
                Annuler
              </button>
              <button
                type="submit"
                className="ml-3 btn-style primary"
                onClick={() => {
                  dispatch(deleteEducation(actualEducation));
                  setActualEducation('');
                  handleCloseForDeleteModal();
                }}
              >
                Supprimer
              </button>
            </div>
          </Modal2>

          <div className={styles.global}>
            {beneficiary &&
              beneficiary.person &&
              beneficiary.person.schoolLevel &&
              beneficiary.person.schoolLevel.id && (
                <>
                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <div className={styles.header}>
                      <h4>Niveau scolaire actuel</h4>
                    </div>
                    {isEducated && (
                      <button
                      className={`${btnStyles.addBtn} mt-2`}
                      onClick={() => setShowNonScolariseModal(true)}
                      style={{
                        backgroundColor: '#6c757d', // A soft gray
                        color: 'white',
                        fontWeight: '700',
                          padding: '10px 20px',
                          borderRadius: '4px',
                        border: 'none',
                        cursor: 'pointer',
                        }}
                      >
                        Marquer comme Non Scolarisé
                      </button>
                    )}
                  </div>

                  <div className={styles.content}>
                    <table className="table">
                      <thead>
                        <tr>
                          <th scope="col"> Type </th>
                          <th scope="col"> Niveau </th>
                          <th scope="col">Etablissement</th>
                          <th scope="col"> Etablissement (Ar) </th>
                          {!hide && isEducated && (
                            <AccessControl
                              module="BENEFICIARY"
                              functionality="UPDATE"
                            >
                              <th scope="col">Actions</th>
                            </AccessControl>
                          )}
                        </tr>
                      </thead>
                      <tbody>
                        <tr key={education.id}>
                          <td scope="row">
                            {beneficiary && beneficiary.person.schoolLevel.type
                              ? beneficiary.person.schoolLevel.type
                                  .charAt(0)
                                  .toUpperCase() +
                                beneficiary.person.schoolLevel.type.slice(1)
                              : '-'}
                          </td>
                          <td>
                            {beneficiary && beneficiary.person.schoolLevel.name}
                          </td>
                          <td>
                            {' '}
                            {beneficiary && beneficiary.person.schoolName
                              ? beneficiary.person.schoolName
                              : '---'}{' '}
                          </td>
                          <td>
                            {' '}
                            {beneficiary && beneficiary.person.schoolNameAr
                              ? beneficiary.person.schoolNameAr
                              : '---'}{' '}
                          </td>
                          {!hide && isEducated && (
                            <td>
                              <input
                                type="image"
                                src={EDIT_ICON}
                                className="p-2"
                                width="40px"
                                height="40px"
                                onClick={() => {
                                  handleActualEducation('');
                                  setEducationType('actuelle');
                                  handleShow(true);
                                  setIsEditModal(true);
                                }}
                              />
                            </td>
                          )}
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </>
            )}
            {(isEducated || hasEducations) && (
              <>
                <div className={styles.header}>

                  <h4>Liste des éducations</h4>
                  <div className="dropdown ">
                    <AccessControl module="BENEFICIARY" functionality="UPDATE">
                      {!hide && isEducated && (
                        <button
                          className={`btn dropdown-toggle mb-3 ${btnStyles.addBtnProfile} `}
                          style={{
                            backgroundColor: '#FFB290',
                            color: 'white',
                            fontWeight: '700',
                          }} 
                          type="button"
                          id="dropdownMenuButton"
                          data-toggle="dropdown"
                          aria-haspopup="true"
                          aria-expanded="false"
                        >
                          Ajouter une éducation &nbsp;
                        </button>
                      )}
                    </AccessControl>
                    <div
                      className="dropdown-menu"
                      style={{
                        borderRadius: '20px',
                        transform: 'translate(30px, 43px)',
                      }}
                      aria-labelledby="dropdownMenuButton"
                    >{isOptionAllowed('préscolaire') && (
                        <a
                          className="dropdown-item"
                          onClick={() => {
                            handleActualEducation('');
                            setEducationType('préscolaire');
                            handleShow(true);
                            setIsEditModal(false);
                          }}
                        >
                          Niveau préscolaire
                        </a>
                      )}
                      {isOptionAllowed('primaire') && (
                        <a
                          className="dropdown-item"
                          onClick={() => {
                            handleActualEducation('');
                            setEducationType('primaire');
                            handleShow(true);
                            setIsEditModal(false);
                          }}
                        >
                          Niveau primaire
                        </a>
                      )}
                      {isOptionAllowed('secondaire') && (
                        <a
                          className="dropdown-item"
                          onClick={() => {
                            handleActualEducation('');
                            setEducationType('secondaire');
                            handleShow(true);
                            setIsEditModal(false);
                          }}
                        >
                          Niveau secondaire
                        </a>
                      )}
                      {isOptionAllowed('universitaire') && (
                        <a
                          className="dropdown-item"
                          onClick={() => {
                            handleActualEducation('');
                            setEducationType('universitaire');
                            handleShow(true);
                            setIsEditModal(false);
                          }}
                        >
                          Niveau universitaire
                        </a>
                      )}
                    </div>
                  </div>
                </div>

                <div className={styles.content}>
                  <table className="table small">
                    <thead>
                      <tr>
                        <th scope="col">Type</th>
                        <th scope="col">Niveau</th>
                        <th scope="col">Etablissement</th>
                        <th scope="col">Ville</th>
                        <th scope="col">Année universitaire</th>
                        <th scope="col">Statut</th>
                        <th scope="col">Note</th>
                        <th scope="col">Mention</th>
                        <th scope="col">Matière</th>
                        {!hide && isEducated && (
                          <AccessControl
                            module="BENEFICIARY"
                            functionality="UPDATE"
                          >
                            <th scope="col">Actions</th>
                          </AccessControl>
                        )}
                      </tr>
                    </thead>
                    {listEducations && listEducations.length > 0 ? (
                      <tbody>{listEducations}</tbody>
                    ) : (
                      <tbody>
                        <tr>
                          <td colSpan="8" style={{ textAlign: 'center' }}>
                            Aucune éducation trouvée
                          </td>
                        </tr>
                      </tbody>
                    )}
                  </table>
                </div>
              </>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
