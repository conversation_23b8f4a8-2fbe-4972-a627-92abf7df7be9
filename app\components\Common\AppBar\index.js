import React, { useEffect, useState } from 'react';
import {
  AppBar,
  Avatar,
  Button,
  CircularProgress,
  Collapse,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  Divider,
  Drawer,
  IconButton,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Menu,
  MenuItem,
  Toolbar,
  Tooltip,
  Typography,
  TextField,
  InputAdornment,
  Box,
  Alert,
} from '@mui/material';
import AssignmentLateIcon from '@mui/icons-material/AssignmentLate';
import VolunteerActivismIcon from '@mui/icons-material/VolunteerActivism';
import ArticleIcon from '@mui/icons-material/Article';
import SearchIcon from '@mui/icons-material/Search';
import PersonIcon from '@mui/icons-material/Person';
import ReportProblemIcon from '@mui/icons-material/ReportProblem';

import MenuIcon from '@mui/icons-material/Menu';
import { NavLink, useLocation, useHistory } from 'react-router-dom';
import sofwaLogo from 'images/sofwa_logo.png';
import Diversity3Icon from '@mui/icons-material/Diversity3';
import EscalatorWarningIcon from '@mui/icons-material/EscalatorWarning';
import AccountBalanceIcon from '@mui/icons-material/AccountBalance';
import AttributionIcon from '@mui/icons-material/Attribution';
import {
  AdmistrationSvg,
  AideComplementaireSvg,
  BenefeciarySvg,
  DonatorsSvg,
  FamilySvg,
  HomeSvg,
  TakenInChargesSvg,
  SettingSvg,
  LogoutSvg,
} from 'images/icons';
import ArrowLeft from 'images/icons/ArrowLeft';
import { ExpandLess, ExpandMore, Logout } from '@mui/icons-material';

import MapIcon from '@mui/icons-material/Map';
import AssistantIcon from '@mui/icons-material/Assistant';
import BuildIcon from '@mui/icons-material/Build';
import AssessmentIcon from '@mui/icons-material/Assessment';
import TrackChangesIcon from '@mui/icons-material/TrackChanges';
import AssignmentIcon from '@mui/icons-material/Assignment';
import SellIcon from '@mui/icons-material/Sell';
import PersonAddIcon from '@mui/icons-material/PersonAdd';
import GroupIcon from '@mui/icons-material/Group';
import ArchiveIcon from '@mui/icons-material/Archive';
import Inventory2Icon from '@mui/icons-material/Inventory2';

import { createStructuredSelector } from 'reselect';
import { useDispatch, useSelector } from 'react-redux';
import {
  makeSelectConnectedUser,
  makeSelectdisconnectUserSuccess,
  makeSelectIsImpersonating,
  makeSelectImpersonationLoading,
  makeSelectImpersonationError,
  makeSelectOriginalUser,
} from 'containers/App/selectors';
import { useMsal } from '@azure/msal-react';
import {
  CallDisconnectAction,
  startImpersonationRequest,
  stopImpersonationRequest,
  fetchConnectedUserRequest
} from 'containers/App/actions';
import { hasRoleAdmin, hasRoleAssistant, useHasAccess } from 'utils/hasAccess';
import ListItemWithIcon from './ListItemWithIcon';
import SideBarWrapper from './SideBarWrapper';
import request from 'utils/request';

const omdbSelector = createStructuredSelector({
  connectedUser: makeSelectConnectedUser,
  disconnectRegister: makeSelectdisconnectUserSuccess,
  isImpersonating: makeSelectIsImpersonating,
  impersonationLoading: makeSelectImpersonationLoading,
  impersonationError: makeSelectImpersonationError,
  originalUser: makeSelectOriginalUser,
});

const MyAppBar = () => {
  const {
    connectedUser,
    disconnectRegister,
    isImpersonating: reduxIsImpersonating,
    impersonationLoading: reduxImpersonationLoading,
    impersonationError: reduxImpersonationError,
    originalUser
  } = useSelector(omdbSelector);
  const [drawerOpen, setDrawerOpen] = useState(false);
  const [adminSubMenuOpen, setAdminSubMenuOpen] = useState(false);
  const [activePage, setActivePage] = useState('');
  const [logoutDialogOpen, setLogoutDialogOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [beneficiaryMenuOpen, setBeneficiaryMenuOpen] = useState(false);
  const [beneficiarySubMenuOpen, setBeneficiarySubMenuOpen] = useState(false);
  const [epsSubMenuOpen, setEpsSubMenuOpen] = useState(false);
  const [assistantMenuOpen, setAssistantMenuOpen] = useState(false);

  // Impersonation states
  const [impersonationDialogOpen, setImpersonationDialogOpen] = useState(false);
  const [users, setUsers] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedUser, setSelectedUser] = useState(null);
  // Using Redux state for impersonation status
  const [impersonationError, setImpersonationError] = useState(null);
  const [loadingUsers, setLoadingUsers] = useState(false);
  const [originalUserName, setOriginalUserName] = useState({
    firstName: localStorage.getItem('originalUserFirstName') || '',
    lastName: localStorage.getItem('originalUserLastName') || ''
  });

  const [
    beneficiaryAdHockSubMenuOpen,
    setBeneficiaryAdHockSubMenuOpen,
  ] = useState(false);
  const [assistantId, setAssistantId] = useState(null);

  useEffect(() => {
    if (connectedUser && connectedUser.assistantId) {
      setAssistantId(connectedUser.assistantId);
    }
  }, [connectedUser]);

  const toggleBeneficiarySubMenu = event => {
    event.stopPropagation();

    setBeneficiarySubMenuOpen(prev => !prev);
  };

  const toggleEpsSubMenu = event => {
    event.stopPropagation();

    setEpsSubMenuOpen(prev => !prev);
  };
  const toggleBeneficiaryAdHockSubMenu = event => {
    event.stopPropagation();

    setBeneficiaryAdHockSubMenuOpen(prev => !prev);
  };
  const dispatch = useDispatch();
  const history = useHistory();
  const registerLastDisconnectAction = () => {
    dispatch(CallDisconnectAction());
  };
  const [anchorEl, setAnchorEl] = useState(null);

  const handleMenuOpen = event => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };
  const location = useLocation();

  useEffect(() => {
    setActivePage(location.pathname);
  }, [location.pathname]);
  const handleDrawerOpen = () => {
    setDrawerOpen(true);
  };

  const handleDrawerClose = () => {
    setDrawerOpen(false);
  };

  const handleAdminMenuClick = event => {
    setAdminSubMenuOpen(true);
    setBeneficiaryMenuOpen(false);
    setEpsSubMenuOpen(false);
  };

  const handleEpsMenuClick = event => {
    setEpsSubMenuOpen(true);
    setBeneficiaryMenuOpen(false);
    setAdminSubMenuOpen(false);
  };

  const toggleAdminSubMenu = () => {
    setAdminSubMenuOpen(!adminSubMenuOpen);
  };

  const handleAdminItemClick = event => {
    event.stopPropagation();
    toggleAdminSubMenu();
  };

  const handleBeneficiaryMenuClick = event => {
    setBeneficiaryMenuOpen(true);
    setAdminSubMenuOpen(false);
    setEpsSubMenuOpen(false);
  };
  const handleBeneficiaryAdHocMenuClick = event => {
    setBeneficiaryMenuOpen(true);
    setAdminSubMenuOpen(false);
    setEpsSubMenuOpen(false);
  };
  const toggleBeneficiaryMenu = () => {
    setBeneficiaryMenuOpen(!beneficiaryMenuOpen);
  };

  const toggleAssistantMenu = event => {
    event.stopPropagation();
    setAssistantMenuOpen(prev => !prev);
  };

  const handleBeneficiaryMenuItemClick = event => {
    event.stopPropagation();
    toggleBeneficiaryMenu();
  };

  const hasAccessToCandidat = useHasAccess('GERER_CANDIDATS', 'WRITE');
  const hasAccessToBeneficiaryKafalat = useHasAccess(
    'GERER_BENEFICIAIRES_KAFALAT',
    'WRITE',
  );
  const hasAccessToBeneficiaryArchived = useHasAccess(
    'GERER_BENEFICIAIRES_ARCHIVES',
    'WRITE',
  );
  const hasAccessToBeneficiaryAdHoc = useHasAccess(
    'GERER_BENEFICIAIRES_AD_HOC',
    'WRITE',
  );

  const isAdmin = hasRoleAdmin();
  const isAssistant = hasRoleAssistant();

  const { instance } = useMsal();
  const handleLogout = () => {
    setLoading(true);
    closeLogoutDialog();
    instance.logoutRedirect().catch(err => {
      setLoading(false);
    });
  };
  const openLogoutDialog = () => {
    setLogoutDialogOpen(true);
  };

  const closeLogoutDialog = () => {
    setLogoutDialogOpen(false);
  };

  // Impersonation functions
  const openImpersonationDialog = () => {
    setImpersonationDialogOpen(true);
    fetchUsers();
  };

  const closeImpersonationDialog = () => {
    setImpersonationDialogOpen(false);
    setSearchTerm('');
    setSelectedUser(null);
    setImpersonationError(null);
  };

  const fetchUsers = async () => {
    try {
      setLoadingUsers(true);
      const response = await request.get('/batch/AdUsers?size=100');
      setUsers(response.data.content || []);
      setLoadingUsers(false);
    } catch (error) {
      console.error('Error fetching users:', error);
      setLoadingUsers(false);
    }
  };

  const handleSearchChange = (event) => {
    setSearchTerm(event.target.value);
  };

  const filteredUsers = users.filter(user => {
    const fullName = `${user.firstName || ''} ${user.lastName || ''}`.toLowerCase();
    return fullName.includes(searchTerm.toLowerCase()) ||
           (user.mail && user.mail.toLowerCase().includes(searchTerm.toLowerCase()));
  });

  const handleUserSelect = (user) => {
    setSelectedUser(user);
  };

  const startImpersonation = () => {
    if (!selectedUser) return;

    // Clear any previous error
    setImpersonationError(null);

    // Dispatch the Redux action to start impersonation
    dispatch(startImpersonationRequest(selectedUser.id));

    // Close the dialog
    closeImpersonationDialog();
  };

  const stopImpersonation = () => {
    // Dispatch the Redux action to stop impersonation
    dispatch(stopImpersonationRequest());
  };

  // Check for impersonation token on mount
  useEffect(() => {
    const impersonationToken = localStorage.getItem('impersonationToken');
    if (impersonationToken && !reduxIsImpersonating) {
      // If we have a token but Redux doesn't know we're impersonating,
      // fetch the connected user to update the Redux state
      dispatch(fetchConnectedUserRequest());
    }
  }, [dispatch, reduxIsImpersonating]);

  // Debug effect to log state changes
  useEffect(() => {
    console.log('Impersonation state:', {
      reduxIsImpersonating,
      originalUser: originalUser ? `${originalUser.firstName} ${originalUser.lastName}` : 'null',
      connectedUser: connectedUser ? `${connectedUser.firstName} ${connectedUser.lastName}` : 'null'
    });
  }, [reduxIsImpersonating, originalUser, connectedUser]);

  // Using Redux state directly for impersonation status

  // Use Redux state for impersonation loading
  useEffect(() => {
    setLoading(reduxImpersonationLoading);
  }, [reduxImpersonationLoading]);

  // Use Redux state for impersonation error
  useEffect(() => {
    if (reduxImpersonationError) {
      setImpersonationError(reduxImpersonationError);
    }
  }, [reduxImpersonationError]);

  // Update originalUserName when localStorage changes or impersonation state changes
  useEffect(() => {
    if (reduxIsImpersonating) {
      setOriginalUserName({
        firstName: localStorage.getItem('originalUserFirstName') || '',
        lastName: localStorage.getItem('originalUserLastName') || ''
      });
    } else {
      setOriginalUserName({ firstName: '', lastName: '' });
    }
  }, [reduxIsImpersonating]);

  const StyleTitle = {
    color: '#98a4ae',
    fontSize: '1.4rem',
    fontWeight: 'bold',
    marginLeft: '15px',
    // marginTop: '10px',
    display: 'inline-block',
    verticalAlign: 'middle',
  };

  return (
    <div>
      {loading && (
        <div
          style={{
            position: 'fixed',
            top: 0,
            left: 0,
            width: '100%',
            height: '100%',
            backgroundColor: 'rgba(255, 255, 255, 0.8)',
            zIndex: 2000,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
          }}
        >
          <CircularProgress />
        </div>
      )}
      <Dialog
        open={logoutDialogOpen}
        onClose={closeLogoutDialog}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
        sx={{
          '& .MuiDialog-paper': {
            padding: '10px 20px',
            borderRadius: '20px'
          }
        }}
      >
        <DialogTitle id="alert-dialog-title">
          Confirmer la déconnexion
        </DialogTitle>
        <DialogContent>
          <DialogContentText id="alert-dialog-description">
            Êtes-vous sûr de vouloir vous déconnecter ?
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button
            onClick={closeLogoutDialog}
            variant="outlined"
            color="primary"
          >
            Annuler
          </Button>
          <Button
            onClick={handleLogout}
            variant="contained"
            color="primary"
            autoFocus
          >
            Déconnexion
          </Button>
        </DialogActions>
      </Dialog>

      {/* Impersonation Dialog */}
      <Dialog
        open={impersonationDialogOpen}
        onClose={closeImpersonationDialog}
        aria-labelledby="impersonation-dialog-title"
        maxWidth="sm"
        fullWidth
        sx={{
          '& .MuiDialog-paper': {
            padding: '10px 20px',
            borderRadius: '20px'
          }
        }}
      >
        <DialogTitle id="impersonation-dialog-title">
          Impersonation d'utilisateur
        </DialogTitle>
        <DialogContent>
          <DialogContentText sx={{ mb: 2 }}>
            Sélectionnez un utilisateur à impersoner
          </DialogContentText>

          {impersonationError && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {impersonationError}
            </Alert>
          )}

          <TextField
            autoFocus
            margin="dense"
            label="Rechercher un utilisateur"
            type="text"
            fullWidth
            variant="outlined"
            value={searchTerm}
            onChange={handleSearchChange}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon />
                </InputAdornment>
              ),
            }}
            sx={{ mb: 2 }}
          />

          {loadingUsers ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', my: 2 }}>
              <CircularProgress />
            </Box>
          ) : (
            <Box sx={{ maxHeight: '300px', overflowY: 'auto', border: '1px solid #e0e0e0', borderRadius: '4px' }}>
              <List>
                {filteredUsers.length > 0 ? (
                  filteredUsers.map((user) => (
                    <ListItem
                      button
                      key={user.id}
                      onClick={() => handleUserSelect(user)}
                      selected={selectedUser && selectedUser.id === user.id}
                      sx={{
                        '&.Mui-selected': {
                          backgroundColor: 'rgba(79, 137, 215, 0.1)',
                        },
                        '&:hover': {
                          backgroundColor: 'rgba(79, 137, 215, 0.05)',
                        }
                      }}
                    >
                      <ListItemIcon>
                        <Avatar sx={{ bgcolor: '#4F89D7', width: 32, height: 32, fontSize: '0.9rem' }}>
                          {user.firstName ? user.firstName.charAt(0) : ''}
                          {user.lastName ? user.lastName.charAt(0) : ''}
                        </Avatar>
                      </ListItemIcon>
                      <ListItemText
                        primary={`${user.firstName || ''} ${user.lastName || ''}`}
                        secondary={user.mail || ''}
                      />
                    </ListItem>
                  ))
                ) : (
                  <ListItem>
                    <ListItemText primary="Aucun utilisateur trouvé" />
                  </ListItem>
                )}
              </List>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={closeImpersonationDialog} variant="outlined" color="primary">
            Annuler
          </Button>
          <Button
            onClick={startImpersonation}
            variant="contained"
            color="primary"
            disabled={!selectedUser}
          >
            Impersoner
          </Button>
        </DialogActions>
      </Dialog>
      <AppBar
        style={{
          position: 'sticky',
          left: 0,
          top: 0,
          padding: '0 95px',
          marginInline: 'auto',
          zIndex: 10,
          backgroundColor: 'transparent',
          boxShadow: 'unset',
          height: '70px',
        }}
      >
        <Toolbar
          style={{
            // marginRight: '3%',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            padding: '0 10px',
          }}
        >
          <div className="d-flex position-relative">
            <IconButton
              edge="start"
              color="inherit"
              aria-label="menu"
              onClick={handleDrawerOpen}
              style={{ color: '#98a4ae' }}
            >
              <MenuIcon />
            </IconButton>
            <Typography
              variant="h6"
              component="div"
              style={{
                position: 'absolute',
                left: '20px',
                top: '10px',
                width: 'max-content',
                lineHeight: '1',
              }}
            >
              {/*<NavLink to="/">*/}
              {/*    <img*/}
              {/*        src={sofwaLogo}*/}
              {/*        alt="Almobadara Logo"*/}
              {/*        style={{*/}
              {/*            height: '50px',*/}
              {/*            width: '70px',*/}
              {/*            marginTop: '5px',*/}
              {/*            verticalAlign: 'middle',*/}
              {/*        }}*/}
              {/*    />*/}

              {location.pathname === '/' && (
                <span
                  style={{
                    color: '#98a4ae',
                    fontSize: '1.4rem',
                    fontWeight: 'bold',
                    marginLeft: '15px',
                    display: 'inline-block',
                    verticalAlign: 'middle',
                  }}
                >
                  Sofwa Système
                </span>
              )}
              {/*</NavLink>*/}
              {location.pathname === '/donors' && (
                <span style={StyleTitle}>Liste des Donateurs</span>
              )}
              {location.pathname.startsWith('/donors/add') && (
                <span style={StyleTitle}>Formulaire Donateur</span>
              )}
              {location.pathname.startsWith('/donors/edit') && (
                <span style={StyleTitle}>Formulaire Donateur</span>
              )}
              {location.pathname === '/beneficiaries' && (
                <span style={StyleTitle}>Liste des Bénéficiaires</span>
              )}
              {location.pathname === '/candidatesKafalat' && (
                <span style={StyleTitle}>Liste des Candidats</span>
              )}
              {location.pathname === '/candidats' && (
                <span style={StyleTitle}>Liste des Pré-Candidats</span>
              )}
              {location.pathname === '/archived-beneficiaries' && (
                <span style={StyleTitle}>
                  Liste des Personnes Archivés et Rejetés
                </span>
              )}
              {location.pathname === '/families' && (
                <span style={StyleTitle}>Liste des Familles</span>
              )}
              {location.pathname === '/families/add' && (
                <span style={StyleTitle}>Formulaire Famille</span>
              )}
              {location.pathname === '/beneficiaries-ad-hoc' && (
                <span style={StyleTitle}>Liste des Bénéficiaires ad-hoc</span>
              )}
              {location.pathname === '/donations' && (
                <span style={StyleTitle}>Liste des Donations</span>
              )}
              {location.pathname === '/aide-complementaire' && (
                <span style={StyleTitle}>Liste des Aides Complémentaires</span>
              )}
              {location.pathname === '/donations/add' && (
                <span style={StyleTitle}>Formulaire Donation</span>
              )}
              {location.pathname.includes('/donations/edit') && (
                <span style={StyleTitle}>Fomulaire Donation</span>
              )}
              {location.pathname === '/takenInCharges' && (
                <span style={StyleTitle}>Liste des Kafalats</span>
              )}
              {location.pathname === '/aide-complementaire/add' && (
                <span style={StyleTitle}>Formulaire Aide Complémentaire</span>
              )}
              {location.pathname.includes('/aide-complementaire/edit') && (
                <span style={StyleTitle}>Formulaire Aide Complémentaire</span>
              )}
              {location.pathname === '/takenInCharges/add' && (
                <span style={StyleTitle}>Formulaire Kafalat</span>
              )}
              {location.pathname.includes('/takenInCharges/edit') && (
                <span style={StyleTitle}>Fomulaire Kafalat</span>
              )}
              {location.pathname === '/caisses' && (
                <span style={StyleTitle}>Liste des Caisses</span>
              )}
              {location.pathname === '/utilisateur' && (
                <span style={StyleTitle}>Liste des Utilisateurs</span>
              )}
              {location.pathname === '/ServiceCollectEps' && (
                <span style={StyleTitle}>Liste des services de collecte</span>
              )}
              {location.pathname === '/eps' && (
                <span style={StyleTitle}>Liste des EPS</span>
              )}
              {location.pathname === '/ServiceCollectEps/add' && (
                <span style={StyleTitle}>Formulaire service de collecte</span>
              )}
              {location.pathname.startsWith('/ServiceCollectEps/edit') && (
                <span style={StyleTitle}>Formulaire service de collecte</span>
              )}
              {location.pathname === '/audit' && (
                <span style={StyleTitle}>Liste des Audits</span>
              )}
              {location.pathname === '/profiles' && (
                <span style={StyleTitle}>Liste des Profils</span>
              )}
              {location.pathname === '/zones' && (
                <span style={StyleTitle}>Liste des Zones</span>
              )}
              {location.pathname === '/assistants' && (
                <span style={StyleTitle}>Liste des Assistants</span>
              )}
              {location.pathname === '/actions' && (
                <span style={StyleTitle}>Suivi Des Actions</span>
              )}
              {location.pathname === '/rapports' && (
                <span style={StyleTitle}>Rapports Kafalat</span>
              )}
              {location.pathname === '/prise' && (
                <span style={StyleTitle}>Suivi des Kafalat</span>
              )}
              {location.pathname === '/parametre' && (
                <span style={StyleTitle}>Paramètres</span>
              )}
              {location.pathname === '/services' && (
                <span style={StyleTitle}>Liste des Services</span>
              )}
              {location.pathname.startsWith('/beneficiaries/fiche') &&
                location.state &&
                location.state.isBeneficiary &&
                location.state.candidat != 'true' && (
                  <span style={StyleTitle}>Fiche Bénéficiaire</span>
                )}
              {location.pathname.startsWith('/beneficiaries/fiche') &&
                location.state &&
                location.state.isBeneficiaryEnAttente &&
                location.state.candidat != 'true' && (
                  <span style={StyleTitle}>Fiche Candidat</span>
                )}
              {location.pathname.startsWith('/beneficiaries/fiche') &&
                location.state &&
                location.state.isOldBeneficiary &&
                location.state.isOldBeneficiaryArchived &&
                location.state.candidat != 'true' && (
                  <span style={StyleTitle}>Fiche Bénéficiaire Archivé</span>
                )}
              {location.pathname.startsWith('/beneficiaries/fiche') &&
                location.state &&
                location.state.isOldBeneficiary &&
                location.state.isOldBeneficiaryRejected &&
                location.state.candidat != 'true' && (
                  <span style={StyleTitle}>Fiche Bénéficiaire Rejeté</span>
                )}
              {location.pathname.startsWith('/beneficiaries/fiche') &&
                location.state &&
                location.state.isOldBeneficiary &&
                location.state.isOldCandidateRejected &&
                location.state.candidat != 'true' && (
                  <span style={StyleTitle}>Fiche Pré-Candidat Rejeté</span>
                )}
              {location.pathname.startsWith('/beneficiaries/fiche') &&
                location.state &&
                (location.state.candidat === 'true' ||
                  location.state.isCandidate) && (
                  <span style={StyleTitle}>Fiche Pré-Candidat</span>
                )}
              {location.pathname.startsWith('/beneficiaries/add') && (
                <span style={StyleTitle}>Formulaire Pré-Candidat</span>
              )}
              {location.pathname.startsWith('/beneficiaries/edit') &&
                location.state &&
                !location.state.candidate && (
                  <span style={StyleTitle}>
                    Formulaire Candidat / Bénéficiaire
                  </span>
                )}
              {location.pathname.startsWith('/beneficiaries/edit') &&
                location.state &&
                location.state.candidate && (
                  <span style={StyleTitle}>Formulaire Candidat</span>
                )}
              {location.pathname.startsWith('/donors/fiche') && (
                <span style={StyleTitle}>Fiche Donateur</span>
              )}
              {location.pathname.startsWith('/aide-complementaire/fiche') && (
                <span style={StyleTitle}>Fiche Aide Complémentaire</span>
              )}
              {location.pathname.startsWith('/beneficiaries-ad-hoc/fiche') && (
                <span style={StyleTitle}>Fiche Bénéficiaire ad-hoc</span>
              )}
              {location.pathname.startsWith('/families/fiche') && (
                <span style={StyleTitle}>Fiche Famille</span>
              )}
              {location.pathname.startsWith('/donations/fiche') && (
                <span style={StyleTitle}>Fiche Donation</span>
              )}
              {location.pathname.startsWith('/takenInCharges/fiche') && (
                <span style={StyleTitle}>Fiche Kafalat</span>
              )}{' '}
              {location.pathname.startsWith('/caisses/fiche') && (
                <span style={StyleTitle}>Fiche Caisse</span>
              )}
              {location.pathname.startsWith('/zones/fiche') && (
                <span style={StyleTitle}>Fiche Zone</span>
              )}
              {location.pathname.startsWith('/services/fiche') && (
                <span style={StyleTitle}>Fiche Service</span>
              )}
              {location.pathname.startsWith('/SuiviepriseEncharge') && (
                <span style={StyleTitle}>Suivi des Kafalat</span>
              )}
              {location.pathname.startsWith('/SuivieRapportsKafalat') && (
                <span style={StyleTitle}>Rapports Kafalat</span>
              )}
              {location.pathname.startsWith('/espace-assistant/fiche') && (
                <span style={StyleTitle}>Espace Assistant</span>
              )}
            </Typography>
          </div>
          <NavLink to="/">
            <img
              src={sofwaLogo}
              alt="Almobadara Logo"
              style={{
                height: '50px',
                width: '70px',
                marginTop: '5px',
                verticalAlign: 'middle',
              }}
            />
          </NavLink>

          <div style={{ display: 'flex', alignItems: 'center' }}>
            {reduxIsImpersonating && (
              <Box
                sx={{
                  mr: 2,
                  backgroundColor: '#ffebee',
                  color: '#d32f2f',
                  padding: '4px 8px',
                  borderRadius: '4px',
                  fontSize: '0.85rem',
                  fontWeight: 'bold',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '4px',
                  border: '1px solid #d32f2f'
                }}
              >
                <PersonIcon fontSize="small" />
                {originalUserName.firstName} {originalUserName.lastName}
              </Box>
            )}
            <Tooltip title="Profile" arrow>
              <IconButton
                onClick={handleMenuOpen}
                sx={{
                  padding: 0,
                  '&:hover': {
                    backgroundColor: 'rgba(0, 0, 0, 0.1)',
                  },
                }}
              >
                <Avatar
                  sx={{
                    width: 50,
                    height: 50,
                    background: '#4F89D7',
                    border: '3px solid white',
                    boxShadow: '0 4px 8px rgba(0, 0, 0, 0.2)',
                    fontSize: '1.5rem',
                    color: 'white',
                  }}
                >
                  {connectedUser &&
                    (connectedUser.lastName
                      ? connectedUser.lastName.charAt(0).toUpperCase()
                      : '')}
                </Avatar>
              </IconButton>
            </Tooltip>

            <Menu
              anchorEl={anchorEl}
              open={Boolean(anchorEl)}
              onClose={handleMenuClose}
              disableScrollLock
              disableAutoFocus
              disableEnforceFocus
              sx={{
                mt: '10px',
                '& .MuiPaper-root': {
                  borderRadius: '16px',
                  boxShadow: '0 6px 12px rgba(0, 0, 0, 0.15)',
                  padding: '20px',
                  width: '320px',
                  backgroundColor: '#ffffff',
                  border: '1px solid #e0e0e0',
                },
              }}
            >
              <MenuItem
                disabled
                sx={{
                  mb: 2,
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                }}
              >
                {reduxIsImpersonating ? (
                  <>
                    <Typography
                      variant="h6"
                      sx={{ mb: 1, color: '#333', fontWeight: 'bold' }}
                    >
                      {connectedUser && `${connectedUser.firstName} ${connectedUser.lastName}`}
                    </Typography>
                    <Box
                      sx={{
                        mt: 1,
                        backgroundColor: '#ffebee',
                        color: '#d32f2f',
                        padding: '4px 8px',
                        borderRadius: '4px',
                        fontSize: '0.75rem',
                        fontWeight: 'bold',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        gap: '4px',
                        flexDirection: 'column'
                      }}
                    >
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
                        <PersonIcon fontSize="small" />
                        Mode Impersonation
                      </Box> 
                    </Box>
                  </>
                ) : (
                  <>
                    <Typography
                      variant="h6"
                      sx={{ mb: 1, color: '#333', fontWeight: 'bold' }}
                    >
                      {connectedUser && `${connectedUser.firstName} ${connectedUser.lastName}`}
                    </Typography>
                    <Typography variant="body2" sx={{ mb: 1, color: '#555' }}>
                      {connectedUser && connectedUser.mail}
                    </Typography>
                  </>
                )}
              </MenuItem>
              {isAssistant &&
                assistantId && [
                  <Divider key="divider" sx={{ my: 2 }} />,
                  <MenuItem
                    key="menu-item"
                    className='menuItem'
                    onClick={() => {
                      history.push(
                        `/espace-assistant/fiche/${assistantId}/sousZones`,
                      );
                      // we should xlose the menu after clicking
                      handleMenuClose();
                    }}
                    sx={{
                      '&:hover': {
                        backgroundColor: 'rgba(33, 150, 243, 0.1)', // Blue hover background
                      },
                      fontSize: '0.875rem',
                      fontWeight: 'medium',
                      display: 'flex',
                      alignItems: 'center',
                      gap: 1,
                    }}
                  >
                    <AssistantIcon
                      fontSize="small"
                      sx={{ color: '#2196f3' }} // Blue icon color
                    />
                    <Typography variant="body1">Espace Assistant</Typography>
                  </MenuItem>,
                ]}

              <Divider sx={{ my: 2 }} />

              {isAdmin && !reduxIsImpersonating && (
                <MenuItem
                  onClick={() => {
                    handleMenuClose();
                    openImpersonationDialog();
                  }}
                  sx={{
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                    color: '#4F89D7',
                    borderRadius: '12px',
                    gap: '10px',
                    mb: 1,
                    '& span': {
                      fontSize: '14px',
                      fontWeight: '600',
                    },
                    '&:hover': {
                      backgroundColor: 'rgba(79, 137, 215, 0.1)',
                    }
                  }}
                >
                  <PersonIcon />
                  <Typography variant="body1">Impersoner un utilisateur</Typography>
                </MenuItem>
              )}

              {reduxIsImpersonating && (
                <MenuItem
                  onClick={() => {
                    handleMenuClose();
                    stopImpersonation();
                  }}
                  sx={{
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                    color: '#d32f2f',
                    borderRadius: '12px',
                    gap: '10px',
                    mb: 1,
                    '& span': {
                      fontSize: '14px',
                      fontWeight: '600',
                    },
                    '&:hover': {
                      backgroundColor: 'rgba(211, 47, 47, 0.1)',
                    }
                  }}
                >
                  <PersonIcon />
                  <Typography variant="body1">Arrêter l'impersonation</Typography>
                </MenuItem>
              )}

              <MenuItem
                onClick={() => {
                  handleMenuClose();
                  openLogoutDialog();
                }}
                sx={{
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center',
                  color: '#FF0A00',
                  borderRadius: '12px',
                  gap: '10px',
                  '& span': {
                    fontSize: '14px',
                    fontWeight: '600',
                  },
                  '&:hover': {
                    backgroundColor: '#E56C5C',
                    color: '#FCE7E7',
                    '& svg path': {
                      fill: '#ffffff',
                    }
                  }
                }}
              >
                <LogoutSvg />
                <Typography variant="body1">Déconnexion</Typography>
              </MenuItem>
            </Menu>
          </div>
        </Toolbar>
      </AppBar>
      <SideBarWrapper
        anchor="left"
        open={drawerOpen}
        onClose={handleDrawerClose}
        sx={{
          width: 360,
          flexShrink: 0,
          '& .MuiDrawer-paper': {
            width: 300,
            height: '95vh',
            backgroundColor: 'white',
            borderRadius: '24px',
            color: 'black',
            top: '10px',
            left: '90px',
          },
          '& .MuiListItem-root': {
            padding: '8px 16px',
          },
          '& .MuiModal-backdrop': {
            backgroundColor: '#4F89D74C',
          },
        }}
      >
        <div className="px-4" onClick={handleDrawerClose}>
          <List>
            <div className="d-flex align-items-center justify-content-between mt-3 mb-3">
              <div className="d-flex align-items-center pl-3">
                <Avatar className='menuAvatar'>
                  {connectedUser &&
                    (connectedUser.firstName
                      ? connectedUser.firstName.charAt(0)
                      : `${connectedUser.lastName}`
                        ? connectedUser.lastName.charAt(0)
                        : '')}
                </Avatar>
                <span className='userStyle ml-4'>
                  {connectedUser && connectedUser.firstName}
                </span>
              </div>
              <div
                className='menuBackButton'
                onClick={handleDrawerClose}
              >
                <ArrowLeft />
              </div>
            </div>

            <ListItem
              className={`menuItem ${activePage === '/' ? 'active' : ''
                }`}
              button
              component={NavLink}
              to="/dashboard-general"
              exact
              onClick={() => {
                setAdminSubMenuOpen(false);
                setBeneficiaryMenuOpen(false);
              }}
            >
              <ListItemIcon>
                <HomeSvg />
              </ListItemIcon>
              <ListItemText primary="Accueil" />
            </ListItem>

            {!isAssistant && (
              <ListItem
                className={`menuItem ${activePage.startsWith('/donors') ? 'active' : ''
                  }`}
                button
                component={NavLink}
                to="/donors"
                onClick={() => {
                  setAdminSubMenuOpen(false);
                  setBeneficiaryMenuOpen(false);
                }}
              >
                <ListItemIcon>
                  <DonatorsSvg />
                </ListItemIcon>
                <ListItemText primary="Donateurs" />
              </ListItem>
            )}

            {(hasAccessToCandidat ||
              hasAccessToBeneficiaryKafalat ||
              hasAccessToBeneficiaryArchived ||
              hasAccessToBeneficiaryAdHoc) && (
                <ListItem button className='menuItem' onClick={handleBeneficiaryMenuItemClick}>
                  <ListItemIcon sx={{ color: 'black' }}>
                    <AttributionIcon />
                  </ListItemIcon>
                  <ListItemText primary="Bénéficiaires" />
                  {beneficiaryMenuOpen ? <ExpandLess /> : <ExpandMore />}
                </ListItem>
              )}
            <Collapse in={beneficiaryMenuOpen} timeout="auto" unmountOnExit>
              <List
                component="div"
                disablePadding
              >
                {(hasAccessToCandidat ||
                  hasAccessToBeneficiaryKafalat ||
                  hasAccessToBeneficiaryArchived) && (
                    <ListItem
                      button
                      className='menuItem'
                      onClick={toggleBeneficiarySubMenu}
                    >
                      {/* <ListItemIcon sx={{ color: 'black', marginLeft: '10px' }}>
                    <Diversity3Icon />
                  </ListItemIcon> */}
                      <ListItemIcon sx={{ color: 'black', marginLeft: '20px' }}>
                        <EscalatorWarningIcon />
                      </ListItemIcon>
                      <ListItemText sx primary="Bénéficiaire Kafalat" />
                      {beneficiarySubMenuOpen ? <ExpandLess /> : <ExpandMore />}
                    </ListItem>
                  )}
                <Collapse
                  in={beneficiarySubMenuOpen}
                  timeout="auto"
                  unmountOnExit
                >
                  <List
                    component="div"
                    disablePadding
                  >
                    {hasAccessToCandidat && (
                      <ListItemWithIcon
                        icon={<PersonAddIcon />}
                        text="Pré-candidats"
                        to="/candidats"
                        isActive={activePage.startsWith('/candidats')}
                        onClick={handleBeneficiaryMenuClick}
                        listItemClassName={` menuItem ${activePage.startsWith('/candidats') ? 'active' : ''
                          }`}
                      />
                    )}
                    {hasAccessToBeneficiaryKafalat && (
                      <ListItemWithIcon
                        icon={<GroupIcon />}
                        text="Candidats"
                        to="/candidatesKafalat"
                        isActive={activePage.startsWith('/candidatesKafalat')}
                        onClick={handleBeneficiaryMenuClick}
                        listItemClassName={` menuItem ${activePage.startsWith('/candidatesKafalat') ? 'active' : ''
                          }`}
                      />
                    )}
                    {hasAccessToBeneficiaryKafalat && (
                      <ListItemWithIcon
                        icon={<GroupIcon />}
                        text="Bénéficiaires"
                        to="/beneficiaries"
                        isActive={activePage.startsWith('/beneficiaries')}
                        onClick={handleBeneficiaryMenuClick}
                        listItemClassName={` menuItem ${activePage.startsWith('/beneficiaries') ? 'active' : ''
                          }`}
                      />
                    )}
                    {hasAccessToBeneficiaryArchived && (
                      <ListItemWithIcon
                        icon={<ArchiveIcon />}
                        text="Personnes archivés et rejetés"
                        to="/archived-beneficiaries"
                        isActive={activePage.startsWith(
                          '/archived-beneficiaries',
                        )}
                        onClick={handleBeneficiaryMenuClick}

                        listItemClassName={` menuItem ${activePage.startsWith('/archived-beneficiaries') ? 'active' : ''
                          }`}
                      />
                    )}
                  </List>
                </Collapse>
                {/* <ListItem
                  listItemButton
                  sx={{ marginLeft: '10px' }}
                  onClick={toggleBeneficiaryAdHockSubMenu}
                >
                  <ListItemText primary="Benificiaires ad-hoc" />
                  {beneficiaryAdHockSubMenuOpen ? (
                    <ExpandLess />
                  ) : (
                    <ExpandMore />
                  )}

                </ListItem> */}

                {!isAssistant  && (
                  <ListItem
                    button

                    className={` menuItem ${activePage.startsWith('/beneficiaries-ad-hoc')
                      ? 'active'
                      : ''
                      }`}
                    component={NavLink}
                    to="/beneficiaries-ad-hoc"
                    onClick={() => {
                      setAdminSubMenuOpen(false);
                      setBeneficiaryMenuOpen(false);
                    }}
                  >
                    <ListItemIcon>
                      <Diversity3Icon
                        sx={{ marginLeft: '20px', color: 'black' }} />
                    </ListItemIcon>
                    <ListItemText primary="Bénéficiaires ad-hoc" />
                  </ListItem>
                )}
              </List>
            </Collapse>

            <ListItem
              button
              className={` menuItem ${activePage.startsWith('/families') ? 'active' : ''
                }`}
              component={NavLink}
              to="/families"
              onClick={() => {
                setAdminSubMenuOpen(false);
                setBeneficiaryMenuOpen(false);
              }}
            >
              <ListItemIcon>
                <FamilySvg />
              </ListItemIcon>

              <ListItemText primary="Familles" />
            </ListItem>

            {!isAssistant && (
              <ListItem
                button
                className={` menuItem ${activePage.startsWith('/donations') ? 'active' : ''
                  }`}
                component={NavLink}
                to="/donations"
                onClick={() => {
                  setAdminSubMenuOpen(false);
                  setBeneficiaryMenuOpen(false);
                }}
              >
                <ListItemIcon>
                  <DonatorsSvg />
                </ListItemIcon>
                <ListItemText primary="Donations" />
              </ListItem>
            )}

            {!isAssistant && (
              <ListItem
                button
                className={` menuItem ${activePage.startsWith('/takenInCharges') ? 'active' : ''
                  }`}
                component={NavLink}
                to="/takenInCharges"
                onClick={() => {
                  setAdminSubMenuOpen(false);
                  setBeneficiaryMenuOpen(false);
                }}
              >
                <ListItemIcon>
                  <TakenInChargesSvg />
                </ListItemIcon>
                <ListItemText primary="Kafalats" />
              </ListItem>
            )}

            {isAssistant && assistantId && (
              <ListItem
                button
                className={` menuItem ${activePage.startsWith('/espace-assistant/fiche')
                    ? 'active'
                    : ''
                  }`}
                onClick={toggleAssistantMenu}
              >
                <ListItemIcon sx={{ color: 'black' }}>
                  <AssistantIcon />
                </ListItemIcon>
                <ListItemText primary="Espace Assistant" />
                {assistantMenuOpen ? <ExpandLess /> : <ExpandMore />}
              </ListItem>
            )}
            <Collapse in={assistantMenuOpen}
              timeout="auto"
              unmountOnExit>
              <List
                component="div"
                disablePadding
                sx={{ color: 'black' }}

              >
                <ListItemWithIcon
                  icon={<AssistantIcon />}

                  listItemClassName={` menuItem ${activePage.startsWith('/espace-assistant/fiche') ? 'active' : ''
                  }`}
                  text="Mon profil"
                  to={`/espace-assistant/fiche/${assistantId}`}
                  isActive={activePage.startsWith('/espace-assistant/fiche')}
                />
                <ListItemWithIcon
                  icon={<AssistantIcon />}
                  text="Rapports Kafalat"
                  listItemClassName={` menuItem ${activePage.startsWith('/espace-assistant/rapports') ? 'active' : ''
                    }`}
                  to={`/espace-assistant/rapports/${assistantId}`}
                  isActive={activePage.startsWith('/espace-assistant/rapports')}
                />
                <ListItemWithIcon
                  icon={<AssignmentLateIcon />}
                  text="Documents à renouveler"
                  listItemClassName={` menuItem ${activePage.startsWith('/espace-assistant/documents') ? 'active' : ''
                    }`}
                  to={`/espace-assistant/documents/torenew`}
                  isActive={activePage.startsWith('/espace-assistant/documents')}
                />
              </List>
            </Collapse>


            {
              !isAssistant && (<>
                <ListItem
              button
              className='menuItem'
              onClick={toggleEpsSubMenu}
            >
              {/* <ListItemIcon sx={{ color: 'black', marginLeft: '10px' }}>
                    <Diversity3Icon />
                  </ListItemIcon> */}
              <ListItemIcon sx={{ color: 'black' }}>
                <AccountBalanceIcon />
              </ListItemIcon>
              <ListItemText primary="Eps" />
              {epsSubMenuOpen ? <ExpandLess /> : <ExpandMore />}
            </ListItem>
            <Collapse
              in={epsSubMenuOpen}
              timeout="auto"
            >
              <List
                component="div"
                disablePadding
              >
                <ListItemWithIcon
                  icon={<AccountBalanceIcon />}
                  text="Liste des EPS"
                  to="/eps"
                  isActive={activePage.startsWith('/eps')}
                  onClick={handleEpsMenuClick}
                  listItemClassName={` menuItem ${activePage.startsWith('/eps') ? 'active' : ''
                    }`}
                />
                <ListItemWithIcon
                  icon={<Inventory2Icon  />}
                  text="Services de collecte"
                  to="/ServiceCollectEps"
                  isActive={activePage.startsWith('/ServiceCollectEps')}
                  onClick={handleEpsMenuClick}
                  listItemClassName={` menuItem ${activePage.startsWith('/ServiceCollectEps') ? 'active' : ''
                    }`}
                />
              </List>
            </Collapse></>)
            }

            {!isAssistant && (
              <ListItem
                button
                className={` menuItem ${activePage.startsWith('/aide-complementaire')
                    ? 'active'
                    : ''
                  }`}
                component={NavLink}
                to="/aide-complementaire"
                sx={{
                  backgroundColor: activePage.startsWith('/aide-complementaire')
                    ? '#f0f0f0'
                    : 'white',
                }}
                onClick={() => {
                  setAdminSubMenuOpen(false);
                  setBeneficiaryMenuOpen(false);
                }}
              >
                <ListItemIcon>
                  <AideComplementaireSvg />
                </ListItemIcon>
                <ListItemText primary="Aides Complementaire" />
              </ListItem>
            )}

            {!isAssistant && (
              <ListItem
                button
                className='menuItem'
                onClick={handleAdminItemClick}
              >
                <ListItemIcon>
                  <AdmistrationSvg />
                </ListItemIcon>
                <ListItemText
                  primary="Administration"
                />
                {adminSubMenuOpen ? <ExpandLess /> : <ExpandMore />}
              </ListItem>
            )}
            {!isAssistant && (
              <Collapse in={adminSubMenuOpen} timeout="auto" unmountOnExit>
                <List
                  component="div"
                  className='listStyle'
                  disablePadding
                >
                  <ListItemWithIcon
                    icon={<AssistantIcon />}
                    text="Assistants"
                    to="/assistants"
                    isActive={activePage.startsWith('/assistants')}
                    onClick={handleAdminMenuClick}
                    listItemClassName={` menuItem ${activePage.startsWith('/assistants') ? 'active' : ''
                      }`}
                  />
                  <ListItemWithIcon
                    icon={<MapIcon />}
                    text="Zones"
                    to="/zones"
                    isActive={activePage.startsWith('/zones')}
                    onClick={handleAdminMenuClick}
                    listItemClassName={` menuItem ${activePage.startsWith('/zones') ? 'active' : ''
                      }`}
                  />
                  <ListItemWithIcon
                    icon={<SellIcon />}
                    text="Tags"
                    to="/taglist"
                    isActive={activePage.startsWith('/taglist')}
                    onClick={handleAdminMenuClick}
                    listItemClassName={` menuItem ${activePage.startsWith('/taglist') ? 'active' : ''
                      }`}
                  />

                  <ListItemWithIcon
                    icon={<BuildIcon />}
                    text="Services"
                    to="/services"
                    isActive={activePage.startsWith('/services')}
                    onClick={handleAdminMenuClick}
                    listItemClassName={` menuItem ${activePage.startsWith('/services') ? 'active' : ''
                      }`}
                  />
                    <>
                      <ListItemWithIcon
                        icon={<PersonIcon />}
                        text="Utilisateurs"
                        to="/utilisateur"
                        isActive={activePage.startsWith('/utilisateur')}
                        onClick={handleAdminMenuClick}
                        listItemClassName={` menuItem ${activePage.startsWith('/utilisateur')
                          ? 'active'
                          : ''
                          }`}
                      />

                      {/*<ListItemWithIcon
                        icon={<AccountCircleIcon />}
                        text="Profils"
                        to="/profiles"
                        isActive={activePage.startsWith('/profiles')}
                        onClick={handleAdminMenuClick}
                      />
                      */}

                      <ListItemWithIcon
                        icon={<AssessmentIcon />}
                        text="Piste Audit"
                        to="/audit"
                        isActive={activePage.startsWith('/audit')}
                        onClick={handleAdminMenuClick}
                        listItemClassName={` menuItem ${activePage.startsWith('/audit') ? 'active' : ''
                          }`}
                      />

                      <ListItemWithIcon
                        icon={<TrackChangesIcon />}
                        text="Suivi Des Actions"
                        to="/actions"
                        isActive={activePage.startsWith('/actions')}
                        onClick={handleAdminMenuClick}
                        listItemClassName={` menuItem ${activePage.startsWith('/actions') ? 'active' : ''
                          }`}
                      />

                      <ListItemWithIcon
                        icon={<AssignmentLateIcon  />}
                        text="Documents à renouveler"
                        to="/SuivieDocumentsRenouvler"
                        isActive={activePage.startsWith('/SuivieDocumentsRenouvler')}
                        onClick={handleAdminMenuClick}
                        listItemClassName={` menuItem ${activePage.startsWith('/SuivieDocumentsRenouvler') ? 'active' : ''
                          }`}
                      />
                      <ListItemWithIcon
                        icon={<VolunteerActivismIcon  />}
                        text="Suivi des Kafalat"
                        to="/SuiviepriseEncharge"
                        isActive={activePage.startsWith('/SuiviepriseEncharge')}
                        onClick={handleAdminMenuClick}
                        listItemClassName={`menuItem ${activePage.startsWith('/SuiviepriseEncharge')
                            ? 'active'
                            : ''
                          }`}
                      />
                      <ListItemWithIcon
                        icon={<ArticleIcon  />}
                        text="Rapports Kafalat"
                        to="/SuivieRapportsKafalat"
                        isActive={activePage.startsWith('/SuivieRapportsKafalat')}
                        onClick={handleAdminMenuClick}
                        listItemClassName={`menuItem ${activePage.startsWith('/SuivieRapportsKafalat')
                            ? 'active'
                            : ''
                          }`}
                      />
                      {/* 
                      <ListItemWithIcon
                        //icon={<ReportProblemIcon />}
                        //text="Réclamations"
                        //to="/reclamations"
                        //isActive={activePage.startsWith('/reclamations')}
                        //onClick={handleAdminMenuClick}
                        listItemClassName={`menuItem ${activePage.startsWith('/reclamations')
                            ? 'active'
                            : ''
                          }`}
                      />
                      */}
                    </>
                </List>
              </Collapse>
            )}
          </List>
        </div>
        <hr className="mt-10-100" />
        <div className="px-4 mt-4">
          <span className='preferenceStyle'>Préférences</span>

            <ListItem
              button
              className={` menuItem ${activePage.startsWith('/parametre') ? 'active' : ''
                } mt-1`}
              component={NavLink}
              onClick={handleDrawerClose}
              to="/parametre"
            >
              <ListItemIcon>
                <SettingSvg />
              </ListItemIcon>
              <ListItemText primary="Paramètres" />
            </ListItem>

        </div>
        <div className="px-4 pb-3 mt-auto" onClick={handleDrawerClose}>
          <ListItem
            button
            className='logoutBtn'
            onClick={openLogoutDialog}
          >
            <ListItemIcon>
              <LogoutSvg />
            </ListItemIcon>
            <ListItemText primary="Déconnexion" />
          </ListItem>
        </div>
      </SideBarWrapper>
    </div>
  );
};

MyAppBar.defaultProps = {
  branding: 'Almobadara',
};

export default MyAppBar;
