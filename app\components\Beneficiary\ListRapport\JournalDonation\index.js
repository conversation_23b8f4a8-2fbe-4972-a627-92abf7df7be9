import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import { useDispatch, useSelector } from 'react-redux';
import moment from 'moment';
import { useInjectReducer, useInjectSaga } from 'redux-injectors';
import { Link, useParams } from 'react-router-dom';
import {
  makeSelectCanalDonations,
  makeSelectError,
  makeSelectLoading,
  makeSelectReleveDonor,
} from './selectors';
import { loadCanalDonations, loadReleveDonor } from './actions';
import reducer from './reducer';
import saga from './saga';
import ReactToPrint from 'react-to-print';
import CustomPagination from 'components/Common/CustomPagination';
import PrintableContent from './PrintableContent/PrintableContent';

const formatDate = date => (date ? moment(date).format('DD/MM/YYYY') : '-');

const BodyComponent = ({ data }) => (
  <>
    {data && data.length > 0 ? (
      data.map(el => {
        if (el.typeDonationKafalat === 'donation') {
          return (
            <tr key={el.id}>
              <td>
                {/* {el.code} */}
                <Link
                  to={{
                    pathname: `/donations/fiche/${el.id}/info`,
                  }}
                  style={{
                    color: 'blue',
                    cursor: 'pointer',
                  }}
                >
                  {`${el.code}`}
                </Link>
              </td>

              <td>{formatDate(el.receptionDate)}</td>
              <td>{el.montantEntree}</td>
              <td>{el.type}</td>
              <td>{el.canalDonation ? el.canalDonation.name : '-'}</td>
            </tr>
          );
        }
      })
    ) : (
      <tr>
        <td colSpan="8">Aucun relevé disponible</td>
      </tr>
    )}
  </>
);

const key = 'releveDonor';
const pageSize = 10;
export default function JournalDonation(props) {
  const donor = props.data;
  const { error, loading, releves, canalDonations } = useSelector(state => ({
    error: makeSelectError(state),
    loading: makeSelectLoading(state),
    releves: makeSelectReleveDonor(state),
    canalDonations: makeSelectCanalDonations(state),
  }));

  useInjectReducer({ key, reducer });
  useInjectSaga({ key, saga });
  const dispatch = useDispatch();
  const params = useParams();
  const [activePage, setActivePage] = useState(1);
  const [startDate, setStartDate] = useState(moment().startOf('year'));
  const [endDate, setEndDate] = useState(moment().endOf('year'));
  const [typeFilter, setTypeFilter] = useState('');
  useEffect(() => {
    dispatch(loadReleveDonor(params.id));
    dispatch(loadCanalDonations());
  }, [dispatch, params.id]);

  const handlePageChange = pageNumber => {
    setActivePage(pageNumber);
  };
  const [filteredData, setFilteredData] = useState([]);
  const handleDataPaging = useCallback(() => {
    setFilteredData(() => {
      const startIndex = (activePage - 1) * pageSize;
      const endIndex = startIndex + pageSize;
      return releves ? releves.slice(startIndex, endIndex) : [];
    });
  }, [releves, pageSize, activePage]);
  useEffect(() => {
    handleDataPaging();
  }, [releves, pageSize, activePage]);

  const totalDonation = useMemo(() => {
    if (filteredData && filteredData.length > 0) {
      return filteredData
        .filter(el => el.typeDonationKafalat === 'donation')
        .reduce((sum, el) => sum + (el.montantEntree || 0), 0);
    }
    return 0;
  }, [filteredData]);

  const [canalFilter, setCanalFilter] = useState('');

  const applyFilter = () => {
    const startDateFormatted = moment(startDate).format('YYYY-MM-DD');
    const endDateFormatted = moment(endDate).format('YYYY-MM-DD');

    setFilteredData(() => {
      if (!releves || releves.length === 0) return [];

      const filtered =
        releves &&
        releves.filter(el => {
          const dateValid =
            el.receptionDate &&
            el.receptionDate.slice(0, 10) >= startDateFormatted &&
            el.receptionDate.slice(0, 10) <= endDateFormatted;

          const typeValid =
            typeFilter === '' ||
            typeFilter === 'Tous' ||
            el.type === typeFilter;

          const canalValid =
            canalFilter === '' ||
            canalFilter === 'Tous les canaux' ||
            (el.canalDonation && el.canalDonation.code === canalFilter);

          return dateValid && typeValid && canalValid;
        });

      const startIndex = (activePage - 1) * pageSize;
      const endIndex = startIndex + pageSize;

      return filtered.slice(startIndex, endIndex);
    });
  };

  const componentRef = useRef();

  return (
    <>
      <div className="d-flex bg-white p-4">
        <h4>Journal des donations</h4>
      </div>
      <div
        style={{
          display: 'flex',
          flexDirection: 'row',
          backgroundColor: '#FFFFFF',
        }}
      >
        <div
          style={{
            width: '60%',
            display: 'flex',
            flexDirection: 'column',
            gap: 10,
          }}
        >
          <div
            style={{
              display: 'flex',
              flexDirection: 'row',
              justifyContent: 'space-around',
            }}
          >
            <div
              style={{
                width: '45%',
                display: 'flex',
                flexDirection: 'row',
                alignItems: 'center',
                gap: 10,
              }}
            >
              <label>Type:</label>
              <select
                value={typeFilter}
                onChange={e => setTypeFilter(e.target.value)}
                className="form-control"
                style={{ maxWidth: 170 }}
              >
                <option value="">Tous</option>
                <option value="Nature">Nature</option>
                <option value="Financière">Financière</option>
              </select>
            </div>
            <div
              style={{
                width: '45%',
                display: 'flex',
                flexDirection: 'row',
                alignItems: 'center',
                gap: 10,
              }}
            >
              <label>Canal donation:</label>
              <select
                value={canalFilter}
                onChange={e => setCanalFilter(e.target.value)}
                className="form-control"
                style={{ maxWidth: 170 }}
              >
                <option value="">Tous les canaux</option>
                {canalDonations &&
                  canalDonations.map(canal => (
                    <option key={canal.id} value={canal.code}>
                      {canal.name}
                    </option>
                  ))}
              </select>
            </div>
          </div>
          <div
            style={{
              display: 'flex',
              flexDirection: 'row',
              justifyContent: 'space-around',
            }}
          >
            <div
              style={{
                width: '45%',
                display: 'flex',
                flexDirection: 'row',
                alignItems: 'center',
                gap: 10,
              }}
            >
              <label>Date Debut:</label>
              <input
                type="date"
                value={startDate ? moment(startDate).format('YYYY-MM-DD') : ''}
                max={
                  endDate
                    ? moment(endDate)
                        .subtract(1, 'days')
                        .format('YYYY-MM-DD')
                    : ''
                }
                onChange={e => setStartDate(e.target.value)}
                className="form-control"
                style={{ maxWidth: 170 }}
              />
            </div>
            <div
              style={{
                width: '45%',
                display: 'flex',
                flexDirection: 'row',
                alignItems: 'center',
                gap: 10,
              }}
            >
              <label>Date Fin :</label>
              <input
                type="date"
                value={endDate ? moment(endDate).format('YYYY-MM-DD') : ''}
                min={
                  startDate
                    ? moment(startDate)
                        .add(1, 'days')
                        .format('YYYY-MM-DD')
                    : ''
                }
                style={{ maxWidth: 170 }}
                onChange={e => setEndDate(e.target.value)}
                className="form-control"
              />
            </div>
          </div>
        </div>
        <div
          style={{
            display: 'flex',
            flexDirection: 'row',
            width: '40%',
            alignItems: 'center',
            justifyContent: 'center',
          }}
        >
          <div
            style={{
              maxHeight: 40,
              minWidth: 100,

              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
            }}
            className="btn btn-primary"
            onClick={() => {
              if (startDate || endDate) {
                applyFilter();
              }
            }}
          >
            <span>Appliquer</span>
          </div>
          <div
            style={{
              maxHeight: 40,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              minWidth: 100,
            }}
          >
            <ReactToPrint
              trigger={() => (
                <button className="btn btn-secondary">Imprimer</button>
              )}
              content={() => componentRef.current}
            />
            <div style={{ display: 'none' }}>
              <PrintableContent
                ref={componentRef}
                data={filteredData}
                totalDonation={totalDonation}
                donor={donor}
                startDate={moment(startDate).format('DD-MM-YYYY')}
                endDate={moment(endDate).format('DD-MM-YYYY')}
              />
            </div>
          </div>

          <div
            style={{
              maxHeight: 40,
              width: 100,

              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
            }}
            className="btn btn-success"
            onClick={() => {
              handleDataPaging();
              setTypeFilter('Tous');
              setStartDate(moment().startOf('year'));
              setEndDate(moment().endOf('year'));
              setCanalFilter('Tous les canaux');
            }}
          >
            <span>Reset</span>
          </div>
        </div>
      </div>

      <div className="d-flex bg-white p-4">
        <table className="table table-bordered">
          <thead>
            <tr>
              <th colSpan={5} className="text-align-center">
                Donations effectuées
              </th>
            </tr>
            <tr>
              <th>Code</th>
              <th>Date</th>
              <th>Montant</th>
              <th>Type</th>
              <th>Canal</th>
            </tr>
          </thead>
          <tbody>
            <BodyComponent data={filteredData} />
          </tbody>
          <tfoot>
            <tr>
              <td>Total</td>
              <td></td>
              <td>{`${totalDonation} DH`}</td>
              <td></td>
              <td></td>
            </tr>
          </tfoot>
        </table>
      </div>

      {releves.length > 0 && (
        <div
          className="d-flex justify-content-center bg-white"
          style={{ paddingBottom: 15, marginTop: -10 }}
        >
          <CustomPagination
            totalCount={
              releves &&
              releves.length > 0 &&
              Math.ceil(releves.length / pageSize)
            }
            pageSize={pageSize}
            currentPage={activePage}
            onPageChange={handlePageChange}
          />
        </div>
      )}
    </>
  );
}
