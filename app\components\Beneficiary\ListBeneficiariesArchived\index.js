import React, { useEffect, useState } from 'react';
import { useHistory } from 'react-router-dom';
import moment from 'moment';
import tagStyles from 'Css/tag.css';
import { createStructuredSelector } from 'reselect';
import { useDispatch, useSelector } from 'react-redux';
import { Alert } from 'react-bootstrap';
import {
  makeSelectBeneficiary,
  makeSelectDeleteSuccess,
  makeSelectErrorFromAdd,
  makeSelectLoading,
  makeSelectSuccess,
} from 'containers/Beneficiary/BeneficiaryAdd/selectors';
import DataTable from '../../Common/DataTable';
import { VIEW_ICON } from '../../Common/ListIcons/ListIcons';
import { deleteBeneficiary } from 'containers/Beneficiary/BeneficiaryAdd/actions';
import Modal2 from 'components/Common/Modal2';
import btnStyles from '../../../Css/button.css';
import { useInjectReducer, useInjectSaga } from 'redux-injectors';
import { beneficiarySaga } from 'containers/Beneficiary/BeneficiaryAdd/saga';
import beneficiaryReducer from 'containers/Beneficiary/BeneficiaryAdd/reducer';
import { isOldBeneficiary } from '../../../containers/Beneficiary/BeneficiaryProfile/statutUtils';
import styled from 'styled-components';

const omdbSelector = createStructuredSelector({
  success: makeSelectSuccess,
  beneficiary: makeSelectBeneficiary,
  successDelete: makeSelectDeleteSuccess,
  loading: makeSelectLoading,
  errorDelete: makeSelectErrorFromAdd,
});

const viewIcon = require('images/icons/eye.svg');
const editIcon = require('images/icons/edit.svg');

const formatDate = date => moment(date).format('DD/MM/YYYY');

// Styled component for the tags container in the table
const TagsContainer = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  max-width: 200px;
`;

// Styled component for individual tags in the table
const TableTag = styled.div`
  padding: 2px 8px;
  font-size: 11px;
  white-space: nowrap;
  border-radius: 10px;
  background-color: ${props => `#${props.color || 'cccccc'}`};
  color: ${props => {
    const hex = props.color || 'cccccc';
    const r = parseInt(hex.substr(0, 2), 16);
    const g = parseInt(hex.substr(2, 2), 16);
    const b = parseInt(hex.substr(4, 2), 16);
    const brightness = (r * 299 + g * 587 + b * 114) / 1000;
    return brightness > 128 ? '#000000' : '#ffffff';
  }};
`;

export default function ListBeneficiariesArchived(props) {
  let listBeneficiaries = <p>Vide</p>;
  useInjectSaga({ key: 'beneficiaryAdd', saga: beneficiarySaga });
  useInjectReducer({ key: 'beneficiaryAdd', reducer: beneficiaryReducer });
  const dispatch = useDispatch();
  const { beneficiary, successDelete, loading, errorDelete } = useSelector(
    omdbSelector,
  );

  const history = useHistory();

  const [showAlert, setShowAlert] = useState(false);
  const [show, setShow] = useState(false);
  const [message, setMessage] = useState('');
  const [beneficiaryToEdit, setBeneficiaryToEdit] = useState('');
  const [beneficiaryToDelete, setBeneficiaryToDelete] = useState('');
  const [showDeleteModal, setShowDeleteModal] = useState(false);

  const handleCloseForDeleteModal = () => setShowDeleteModal(false);

  const handleShow = () => {
    setShow(true);
  };

  useEffect(() => {
    if (successDelete || errorDelete) {
      handleCloseForDeleteModal();
    }
  }, [successDelete, errorDelete]);

  // close the alert after 4 seconds
  useEffect(() => {
    if (showAlert) {
      setTimeout(() => {
        setShowAlert(false);
      }, 4000);
    }
  }, [showAlert]);

  const beneficiaries = props.candidats.content;
  const liste1 = props.candidats;

  const viewHandler = (id, destination, beneficiaryStatutId) => {
    if (destination === 'fiche') {
      history.push(`/beneficiaries/fiche/${id}/info`, {
        params: id,
        candidat: 'false',
        isOldBeneficiary: isOldBeneficiary(beneficiaryStatutId),
        isOldBeneficiaryArchived: beneficiaryStatutId === 9 ? true : false,
        isOldBeneficiaryRejected: beneficiaryStatutId === 7 ? true : false,
        isOldCandidateRejected: beneficiaryStatutId === 5 ? true : false,
      });
    }
  };

  const getTag = beneficiaryStatutId => {
    if (beneficiaryStatutId === 4 || beneficiaryStatutId === 8) {
      return tagStyles.tagYellow;
    }
    if (beneficiaryStatutId === 2 || beneficiaryStatutId === 3) {
      return tagStyles.tagGreen;
    }
    if (beneficiaryStatutId === 1) {
      return tagStyles.tagGrey;
    }
    return tagStyles.tagGrey;
  };

  const getStatutText = beneficiaryStatutId => {
    if (beneficiaryStatutId === 4 || beneficiaryStatutId === 8) {
      return 'A compléter';
    }
    if (beneficiaryStatutId === 2 || beneficiaryStatutId === 3) {
      return 'En cours de validation';
    }
    if (beneficiaryStatutId === 1) {
      return 'Initial';
    }
    return '---';
  };

  const count = 0;
  if (props.candidats) {
    listBeneficiaries = beneficiaries
      .filter(beneficiary => beneficiary && !beneficiary.archived) // Filter out undefined and archived elements
      .map(beneficiary => {
        return {
          id: beneficiary.id, // Ensure beneficiary is defined before accessing properties
          code: beneficiary.code,
          name: `${beneficiary.firstName} ${beneficiary.lastName}`,
          arabicName: `${beneficiary.firstNameAr} ${beneficiary.lastNameAr}`,
          birthDate: formatDate(beneficiary.birthDate),
          type: beneficiary.independent ? 'Indépendant' : 'Membre famille',
          phoneNumber:
            beneficiary.independent === false
              ? beneficiary.familyPhoneNumber
                ? beneficiary.familyPhoneNumber
                : '---'
              : beneficiary.phoneNumber
              ? beneficiary.phoneNumber
              : '---',
          city: beneficiary.city,
          zone: beneficiary.zoneName ? beneficiary.zoneName : '---',
          status:
            beneficiary.services && beneficiary.services.length > 0
              ? beneficiary.services[0].status
              : '---',
          service:
            beneficiary.services && beneficiary.services.length > 0
              ? beneficiary.services[0].service
              : '---',
          beneficiaryStatutId: beneficiary.beneficiaryStatutId,
          identityCode:
            beneficiary.independent === false
              ? beneficiary.familyIdentityCode
                ? beneficiary.familyIdentityCode
                : '---'
              : beneficiary.identityCode
              ? beneficiary.identityCode
              : '---',
          motif: beneficiary.rqReject ? beneficiary.rqReject : '---',
          tags: beneficiary.tags || [],
        };
      });
  }

  const columns = [
    {
      field: 'code',
      headerName: 'Code bénéficiaire',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
    {
      field: 'name',
      headerName: 'Nom Complet',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
    {
      field: 'arabicName',
      headerName: 'الاسم الكامل',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
    {
      field: 'birthDate',
      headerName: 'Date de naissance',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
    {
      field: 'type',
      headerName: 'Type',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
    {
      field: 'identityCode',
      headerName: "N° d'identité",
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
    {
      field: 'phoneNumber',
      headerName: 'Téléphone',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
    {
      field: 'zone',
      headerName: 'Zone',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
    {
      field: 'motif',
      headerName: 'Motif',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
    {
      field: 'tags',
      headerName: 'Tags',
      flex: 1.5,
      headerAlign: 'center',
      align: 'center',
      renderCell: params => {
        const tags = params.row.tags || [];
        return (
          <TagsContainer>
            {tags && tags.length > 0 ? (
              tags.map(tag => (
                <TableTag key={tag.id} color={tag.color || 'cccccc'}>
                  {tag.name}
                </TableTag>
              ))
            ) : (
              <span style={{ color: '#999', fontSize: '12px' }}>Aucun tag</span>
            )}
          </TagsContainer>
        );
      },
    },
    // {
    //   field: 'beneficiaryStatutId',
    //   headerName: 'Statut',
    //   flex: 1.5,
    //   headerAlign: 'center',
    //   align: 'center',
    //   renderCell: params => (
    //     <div className={getTag(params.row.beneficiaryStatutId)}>
    //       {getStatutText(params.row.beneficiaryStatutId)}
    //     </div>
    //   ),
    // },
  ];

  columns.push({
    field: 'actions',
    type: 'actions',
    headerName: 'Actions',
    flex: 1,
    headerAlign: 'center',
    align: 'center',
    renderCell: params => (
      <div>
        <input
          type="image"
          onClick={() =>
            viewHandler(params.row.id, 'fiche', params.row.beneficiaryStatutId)
          }
          className="p-2"
          src={VIEW_ICON}
          width="40px"
          height="40px"
          title="consulter"
        />
      </div>
    ),
  });

  return (
    <div>
      {showAlert ? (
        <Alert
          className="alert-style"
          variant="success"
          onClose={() => setShowAlert(false)}
          dismissible
        >
          <p>{message}</p>
        </Alert>
      ) : null}

      <Modal2
        centered
        className="mt-5"
        title="Confirmation de suppression"
        show={showDeleteModal}
        handleClose={handleCloseForDeleteModal}
      >
        <p className="mt-1 mb-5">
          Êtes-vous sûr de vouloir supprimer ce candidat?
        </p>
        <div className="d-flex justify-content-end px-3 my-1">
          <button
            type="button"
            className={`mx-2 btn-style outlined`}
            onClick={handleCloseForDeleteModal}
          >
            Annuler
          </button>
          <button
            type="submit"
            className={`mx-2 btn-style primary`}
            onClick={() => {
              dispatch(deleteBeneficiary({ beneficiary: beneficiaryToDelete }));
            }}
          >
            Supprimer
          </button>
        </div>
      </Modal2>
      <DataTable
        rows={listBeneficiaries}
        columns={columns}
        const
        fileName={`Liste des Beneficiaires , ${new Date().toLocaleString()}`}
        totalElements={liste1.totalElements}
        numberOfElements={liste1.numberOfElements}
        pageable={liste1.pageable}
      />
    </div>
  );
}
