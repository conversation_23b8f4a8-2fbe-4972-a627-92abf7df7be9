import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { createStructuredSelector } from 'reselect';
import { useInjectReducer, useInjectSaga } from 'redux-injectors';
import { Al<PERSON>, Card, Row, Col, Grid, Form, Button } from 'react-bootstrap';
import { StatCard } from 'components/charts/StatCard';
import { BudgetTable } from 'components/charts/BudgetTable';
import AssignmentIcon from '@mui/icons-material/Assignment';
import SendIcon from '@mui/icons-material/Send';
import MessageIcon from '@mui/icons-material/Message';
import LockIcon from '@mui/icons-material/Lock';
import EditIcon from '@mui/icons-material/Edit';
import SailingIcon from '@mui/icons-material/Sailing';
import DoneAllIcon from '@mui/icons-material/DoneAll';
import moment from 'moment';
import Modal2 from 'components/Common/Modal2';
import CustomPagination from 'components/Common/CustomPagination';
import btnStyles from 'Css/button.css';
import listStyles from 'Css/list.css';
import { VIEW_ICON } from 'components/Common/ListIcons/ListIcons';
import AccessControl from 'utils/AccessControl';
import { Chip, useTheme, useMediaQuery } from '@mui/material';
import { updateReclamationRequest } from 'containers/Reclamation/ListReclamation/actions';

import reclamationReducer from 'containers/Reclamation/ListReclamation/reducer';
import reclamationSaga from 'containers/Reclamation/ListReclamation/saga';
import {
  fetchReclamationsRequest,
  resetError,
} from 'containers/Reclamation/ListReclamation/actions';
import {
  makeSelectReclamations,
  makeSelectLoading,
  makeSelectError,
} from 'containers/Reclamation/ListReclamation/selectors';

import ReclamationView from '../ReclamationView';
import ReclamationStatCard from '../ReclamationStatCard';

const key = 'reclamationList';

const stateSelector = createStructuredSelector({
  reclamations: makeSelectReclamations(),
  loading: makeSelectLoading(),
  error: makeSelectError(),
});

export default function ListReclamation() {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const isTablet = useMediaQuery(theme.breakpoints.down('md'));

  useInjectReducer({ key, reducer: reclamationReducer });
  useInjectSaga({ key, saga: reclamationSaga });

  const dispatch = useDispatch();
  const { reclamations, loading, error } = useSelector(stateSelector);

  const [showViewModal, setShowViewModal] = useState(false);
  const [selectedReclamation, setSelectedReclamation] = useState(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(10);

  const [showResponseModal, setShowResponseModal] = useState(false);
  const [responseMessage, setResponseMessage] = useState('');
  const [selectedReclamationForResponse, setSelectedReclamationForResponse] = useState(null);
  const [isEditMode, setIsEditMode] = useState(false);

  const [showCloseConfirmModal, setShowCloseConfirmModal] = useState(false);
  const [reclamationToClose, setReclamationToClose] = useState(null);

  const [statusFilter, setStatusFilter] = useState(null);

  useEffect(() => {
      dispatch(fetchReclamationsRequest({ page: currentPage - 1, size: pageSize }));

  }, [dispatch, currentPage, pageSize]);



  const handlePageChange = page => {
    setCurrentPage(page);
  };

  const handleCloseViewModal = () => {
    setShowViewModal(false);
    setSelectedReclamation(null);
    setIsEditMode(false);
  };

  const handleCloseResponseModal = () => {
    setShowResponseModal(false);
    setResponseMessage('');
    setSelectedReclamationForResponse(null);
  };

  const handleSubmitResponse = () => {
    if (selectedReclamationForResponse && responseMessage.trim()) {
      const newStatus = selectedReclamationForResponse.status === 'envoyé' ? 'résolue' : 'fermé';
      dispatch(updateReclamationRequest(selectedReclamationForResponse.id, {
        status: newStatus,
        response: responseMessage,
        respondedBy: 'Admin',
        respondedAt: moment().format('YYYY-MM-DDTHH:mm:ss.SSSZ')
      }));
      handleCloseResponseModal();
    }
  };

  const handleEditClick = (reclamation) => {
    setSelectedReclamation(reclamation);
    setIsEditMode(true);
    setShowViewModal(true);
  };

  const handleCloseClick = (reclamation) => {
    setReclamationToClose(reclamation);
    setShowCloseConfirmModal(true);
  };

  const handleConfirmClose = () => {
    if (reclamationToClose) {
      dispatch(updateReclamationRequest(reclamationToClose.id, {
        status: 'fermé',
        response: reclamationToClose.response,
        respondedBy: reclamationToClose.respondedBy,
        respondedAt: reclamationToClose.respondedAt
      }));
      setShowCloseConfirmModal(false);
      setReclamationToClose(null);
    }
  };

  const handleCancelClose = () => {
    setShowCloseConfirmModal(false);
    setReclamationToClose(null);
  };

  // Transform reclamations data to match BudgetTable format
  const transformReclamationsToTableData = (reclamations) => {
    if (!reclamations) return [];
    
    let rows = [];
    if (Array.isArray(reclamations)) {
      rows = reclamations;
    } else if (reclamations.content && Array.isArray(reclamations.content)) {
      rows = reclamations.content;
    } else if (reclamations.data && reclamations.data.content && Array.isArray(reclamations.data.content)) {
      rows = reclamations.data.content;
    } else if (typeof reclamations === 'object' && !Array.isArray(reclamations) && !reclamations.content) {
      rows = [reclamations];
    }

    return rows;
  };

  const getStatusLabel = (status) => {
    switch (status) {
      case 'envoyé':
        return 'Nouveau';
      case 'résolue':
        return 'Traité';
      case 'fermé':
        return 'Fermé';
      default:
        return status;
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'envoyé':
        return '#2e7d32'; // Green for new
      case 'résolue':
        return '#1976d2'; // Blue for resolved
      case 'fermé':
        return '#757575'; // Gray for closed
      default:
        return '#757575';
    }
  };

  const handleUpdateReclamation = (id, newStatus) => {
    console.log('Updating reclamation status:', { id, newStatus });
    dispatch(updateReclamationRequest(id, { status: newStatus }));
  };

  const handleCloseReclamation = (id) => {
    const reclamation = rows.find(r => r.id === id);
    if (reclamation) {
      dispatch(updateReclamationRequest(id, {
        status: 'fermé',
        response: reclamation.response,
        respondedBy: reclamation.respondedBy,
        respondedAt: reclamation.respondedAt
      }));
    }
  };

  const columns = [
    {
      field: 'donorName',
      headerName: 'Donateur',
      flex: 0.8,
      minWidth: isMobile ? 80 : 100,
      headerClassName: 'custom-header',
      renderCell: (row) => {
        const donorId = row.donorId || (row.donor ? row.donor.id : null);
        let donorName = '-';
        if (row.donorName) {
          donorName = row.donorName;
        } else if (row.donor) {
          donorName = row.donor.label || row.donor.code || '-';
        }

        if (donorId) {
          return (
            <div style={{ 
              cursor: 'pointer', 
              color: '#007bff', 
              textDecoration: 'underline',
              fontSize: isMobile ? '0.75rem' : '0.875rem'
            }}
                 onClick={() => window.location.href = `/donors/fiche/${donorId}/info`}>
              {donorName}
            </div>
          );
        }
        return <div style={{ fontSize: isMobile ? '0.75rem' : '0.875rem' }}>{donorName}</div>;
      },
    },
    {
      field: 'title',
      headerName: 'Titre',
      flex: 0.8,
      minWidth: isMobile ? 80 : 100,
      headerClassName: 'custom-header',
      renderCell: (row) => (
        <div style={{ 
          whiteSpace: 'nowrap', 
          overflow: 'hidden', 
          textOverflow: 'ellipsis',
          fontSize: isMobile ? '0.75rem' : '0.875rem'
        }}>
          {row.title}
        </div>
      ),
    },
    {
      field: 'description',
      headerName: 'Description',
      flex: 4,
      minWidth: isMobile ? 200 : 400,
      headerClassName: 'custom-header',
      renderCell: (row) => (
        <div style={{ 
          whiteSpace: 'normal', 
          wordWrap: 'break-word',
          fontSize: isMobile ? '0.75rem' : '0.875rem'
        }}>
          {row.description && row.description.length > (isMobile ? 50 : 100)
            ? `${row.description.substring(0, isMobile ? 50 : 100)}...`
            : row.description}
        </div>
      ),
    },
    {
      field: 'createdAt',
      headerName: 'Date de création',
      flex: 0.6,
      minWidth: isMobile ? 60 : 80,
      headerClassName: 'custom-header',
      renderCell: (row) => (
        <div style={{ fontSize: isMobile ? '0.75rem' : '0.875rem' }}>
          {row.createdAt ? moment(row.createdAt).format('DD/MM/YYYY') : '-'}
        </div>
      ),
    },
    {
      field: 'status',
      headerName: 'Statut',
      flex: 0.6,
      minWidth: isMobile ? 60 : 80,
      headerClassName: 'custom-header',
      renderCell: (row) => (
        <Chip
          label={getStatusLabel(row.status)}
          sx={{
            backgroundColor: getStatusColor(row.status),
            color: 'white',
            fontWeight: 600,
            borderRadius: '16px',
            fontSize: isMobile ? '0.7rem' : '0.8rem',
            '& .MuiChip-label': {
              padding: '0 12px',
            },
          }}
        />
      ),
    },
    {
      field: 'respondedBy',
      headerName: 'Répondu par',
      flex: 0.6,
      minWidth: isMobile ? 60 : 80,
      headerClassName: 'custom-header',
      renderCell: (row) => (
        <div style={{ fontSize: isMobile ? '0.75rem' : '0.875rem' }}>
          {row.respondedBy ? row.respondedBy : '-'}
        </div>
      ),
    },
    {
      field: 'respondedAt',
      headerName: 'Répondu à',
      flex: 1,
      minWidth: isMobile ? 60 : 80,
      headerClassName: 'custom-header',
      renderCell: (row) => (
        <div style={{ fontSize: isMobile ? '0.75rem' : '0.875rem' }}>
          {row.respondedBy ? (row.respondedAt ? moment(row.respondedAt).format('DD/MM/YYYY HH:mm') : '-') : '-'}
        </div>
      ),
    },
    {
      field: 'actions',
      headerName: 'Actions',
      flex: 0.6,
      minWidth: isMobile ? 60 : 80,
      headerClassName: 'custom-header',
      renderCell: (row) => (
        <div style={{ 
          display: 'flex', 
          justifyContent: 'center', 
          gap: isMobile ? '4px' : '8px', 
          width: '100%' 
        }}> 
          <img 
            style={{ 
              cursor: 'pointer',
              width: isMobile ? '18px' : '25px',
              height: isMobile ? '18px' : '25px'
            }}
            onClick={() => {
              setSelectedReclamation(row);
              setIsEditMode(false);
              setShowViewModal(true);
            }}
            title="Voir les détails" 
            src={VIEW_ICON} 
            alt="Voir" 
          />
          {row.status === 'envoyé' && (
            <MessageIcon 
              style={{ 
                cursor: 'pointer', 
                color: '#1976d2',
                fontSize: isMobile ? '18px' : '25px'
              }}
              onClick={() => {
                setSelectedReclamationForResponse(row);
                setShowResponseModal(true);
              }}
              title="Répondre à la réclamation"
            />
          )}
          {row.status === 'résolue' && (
            <>
              <EditIcon 
                style={{ 
                  cursor: 'pointer', 
                  color: '#1976d2',
                  fontSize: isMobile ? '18px' : '25px'
                }}
                onClick={() => handleEditClick(row)}
                title="Modifier la réponse"
              />
              <LockIcon 
                style={{ 
                  cursor: 'pointer', 
                  color: '#1976d2',
                  fontSize: isMobile ? '18px' : '25px'
                }}
                onClick={() => handleCloseClick(row)}
                title="Fermer la réclamation"
              />
            </>
          )}
        </div>
      ),
    },
  ];

  // Extraire les données de réclamations en fonction de la structure de la réponse
  let rows = [];
  if (reclamations) {
    console.log('Processing reclamations data:', reclamations);
    if (Array.isArray(reclamations)) {
      rows = reclamations;
    } else if (reclamations.content && Array.isArray(reclamations.content)) {
      rows = reclamations.content;
    } else if (reclamations.data && reclamations.data.content && Array.isArray(reclamations.data.content)) {
      // Si la réponse a une structure avec data.content (comme dans l'erreur)
      rows = reclamations.data.content;
    } else if (typeof reclamations === 'object' && !Array.isArray(reclamations) && !reclamations.content) {
      // Si c'est un objet unique sans propriété content
      rows = [reclamations];
    }
  }
  console.log('Rows for table:', rows);

  // Fonction pour obtenir l'ID unique de chaque ligne
  const getRowId = (row) => {
    console.log('getRowId called with row:', row);
    return row.id;
  };

  // Calculate totals
  const totalReclamations = rows.length;
  const totalSentReclamations = rows.filter(row => row.status === 'envoyé').length;
  const totalProcessedReclamations = rows.filter(row => row.status === 'résolue').length;

  // Filter rows based on status
  const filteredRows = statusFilter 
    ? rows.filter(row => row.status === statusFilter)
    : rows;

  const handleCardClick = (status) => {
    if (status === statusFilter) {
      setStatusFilter(null); // Reset filter if clicking the same card
    } else {
      setStatusFilter(status);
    }
  };

  return (
    <div style={{ padding: isMobile ? '10px' : '20px' }}>
      {error && (
        <Alert variant="danger" onClose={() => dispatch(resetError())} dismissible>
          {error}
        </Alert>
      )}

      <div className={`row justify-content-evenly mb-3 ${isMobile ? 'g-2' : ''}`} 
        style={{ 
          display: 'flex', 
          flexWrap: 'wrap',
          gap: '20px',
          margin: '0 auto',
          maxWidth: '100%',
        }}
      > 
        <div className={`${isMobile ? 'col-12' : 'col-md-3'}`} style={{ flex: 1 }}>
          <div 
            onClick={() => handleCardClick(null)} 
            style={{ cursor: 'pointer', opacity: statusFilter === null ? 1 : 0.7 }}
          >
            <ReclamationStatCard
              icon={<AssignmentIcon />}
              value={totalReclamations}
              title="Total des Réclamations"
              iconBgColor="#4169E1"
            />
          </div>
        </div>
        <div className={`${isMobile ? 'col-12' : 'col-md-3'}`} style={{ flex: 1 }}>
          <div 
            onClick={() => handleCardClick('envoyé')} 
            style={{ cursor: 'pointer', opacity: statusFilter === 'envoyé' ? 1 : 0.7 }}
          >
            <ReclamationStatCard
              icon={<SailingIcon />}
              value={totalSentReclamations}
              title="Nouvelles réclamations"
              iconBgColor="#2E8B57"
            />
          </div>
        </div>
        <div className={`${isMobile ? 'col-12' : 'col-md-3'}`} style={{ flex: 1 }}>
          <div 
            onClick={() => handleCardClick('résolue')} 
            style={{ cursor: 'pointer', opacity: statusFilter === 'résolue' ? 1 : 0.7 }}
          >
            <ReclamationStatCard
              icon={<DoneAllIcon />}
              value={totalProcessedReclamations}
              title="Réclamations traité"
              iconBgColor="#FFA500"
            />
          </div>
        </div>
      </div>

      <div className={listStyles.backgroudStyle}>
        <div className={listStyles.global} style={{ 
          backgroundColor: 'white', 
          borderRadius: '8px', 
          padding: isMobile ? '10px' : '15px' 
        }}>
          <div className={listStyles.header} style={{ marginBottom: '30px' }}>
          </div>

          <div style={{ overflowX: 'auto' }}>
            <BudgetTable 
              columns={columns}
              data={transformReclamationsToTableData(filteredRows)}
              loading={loading}
              sx={{
                '& .custom-header': {
                  fontSize: isMobile ? '0.75rem' : '0.875rem',
                  fontWeight: 600,
                  color: '#666',
                  padding: '8px 16px',
                  backgroundColor: '#f5f5f5',
                }
              }}
            />
          </div>

          <div className="justify-content-center my-4">
            {reclamations && (
              <CustomPagination
                totalElements={
                  reclamations.data && reclamations.data.totalElements
                    ? reclamations.data.totalElements
                    : reclamations.totalElements || rows.length
                }
                totalCount={
                  reclamations.data && reclamations.data.totalPages
                    ? reclamations.data.totalPages
                    : reclamations.totalPages || Math.ceil(rows.length / pageSize)
                }
                pageSize={pageSize}
                currentPage={currentPage}
                onPageChange={handlePageChange}
              />
            )}
          </div>
        </div>
      </div>

      <Modal2
        title={selectedReclamation ? `Détails de la réclamation: ${selectedReclamation.title}` : "Détails de la réclamation"}
        size={isMobile ? "sm" : "lg"}
        show={showViewModal}
        handleClose={handleCloseViewModal}
        style={{ 
          maxWidth: isMobile ? '100%' : '800px', 
          margin: '0 auto',
          width: isMobile ? '95%' : 'auto'
        }}
      >
        <div style={{ 
          padding: isMobile ? '10px' : '20px', 
          backgroundColor: '#f9f9f9', 
          borderRadius: '5px' 
        }}>
          <ReclamationView 
            reclamation={selectedReclamation} 
            isEditMode={isEditMode}
            onUpdateResponse={(newResponse, respondedAt) => {
              if (selectedReclamation) {
                dispatch(updateReclamationRequest(selectedReclamation.id, {
                  response: newResponse,
                  respondedBy: 'Admin',
                  respondedAt: respondedAt
                }));
                handleCloseViewModal();
              }
            }}
            onCloseReclamation={() => {
              if (selectedReclamation) {
                handleCloseReclamation(selectedReclamation.id);
                handleCloseViewModal();
              }
            }}
          />
        </div>
      </Modal2>

      <Modal2
        title="Répondre à la réclamation"
        size={isMobile ? "sm" : "md"}
        show={showResponseModal}
        handleClose={handleCloseResponseModal}
        style={{ 
          maxWidth: isMobile ? '100%' : '600px', 
          margin: '0 auto',
          width: isMobile ? '95%' : 'auto'
        }}
      >
        <div style={{ padding: isMobile ? '10px' : '20px' }}>
          <Form>
            <Form.Group className="mb-3">
              <Form.Label style={{ fontSize: isMobile ? '0.875rem' : '1rem' }}>Message de réponse</Form.Label>
              <Form.Control
                as="textarea"
                rows={4}
                value={responseMessage}
                onChange={(e) => setResponseMessage(e.target.value)}
                placeholder="Écrivez votre réponse ici..."
                style={{ fontSize: isMobile ? '0.875rem' : '1rem' }}
              />
            </Form.Group>
            <div style={{ display: 'flex', justifyContent: 'flex-end', gap: '10px' }}>
              <Button 
                variant="secondary" 
                onClick={handleCloseResponseModal}
                style={{ fontSize: isMobile ? '0.875rem' : '1rem' }}
              >
                Annuler
              </Button>
              <Button 
                variant="primary" 
                onClick={handleSubmitResponse}
                disabled={!responseMessage.trim()}
                style={{ fontSize: isMobile ? '0.875rem' : '1rem' }}
              >
                Envoyer la réponse
              </Button>
            </div>
          </Form>
        </div>
      </Modal2>

      <Modal2
        title="Confirmer la fermeture"
        size={isMobile ? "sm" : "md"}
        show={showCloseConfirmModal}
        centered
        handleClose={handleCancelClose}
        style={{ 
          maxWidth: isMobile ? '100%' : '400px', 
          margin: '0 auto',
          width: isMobile ? '95%' : 'auto'
        }}
      >
        <div style={{ padding: isMobile ? '10px' : '20px' }}>
          <p style={{ 
            fontSize: isMobile ? '1rem' : '18px',
            textAlign: 'center'
          }}>
            Êtes-vous sûr de vouloir fermer cette réclamation ?
          </p>
          <div style={{ 
            display: 'flex', 
            justifyContent: 'center', 
            gap: '10px', 
            marginTop: '20px' 
          }}>
            <Button 
              variant="secondary" 
              onClick={handleCancelClose}
              style={{ fontSize: isMobile ? '0.875rem' : '1rem' }}
            >
              Annuler
            </Button>
            <Button 
              variant="primary" 
              onClick={handleConfirmClose}
              style={{ 
                backgroundColor: '#C4261E', 
                borderColor: '#CC261E',
                fontSize: isMobile ? '0.875rem' : '1rem'
              }}
            >
              Fermer
            </Button>
          </div>
        </div>
      </Modal2>
    </div>
  );
}
