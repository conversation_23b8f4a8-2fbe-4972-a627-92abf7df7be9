import React, { useState } from 'react';
import moment from 'moment';
import styles from 'Css/profileList.css';
import stylesList from 'Css/profileList.css';
import { useSelector } from 'react-redux';
import { isAuthorized } from 'utils/AccessControl';
import { Link } from 'react-router-dom';
import DataTable from '../../../Common/DataTable';
import CustomPagination from '../../../Common/CustomPagination';

const formatDate = date =>
  date
    ? moment(date).isValid()
      ? moment(date).format('DD/MM/YYYY')
      : '-'
    : '-';

export default function Entrees(props) {
  let listBudgetLines = [];
  const successAlert = null;

  const [currentPage, setCurrentPage] = useState(1);
  const pageSize = 5;

  const caisse = props.data;

  let caisseCode;

  if (caisse) {
    caisseCode = caisse.code;
  }

  if (caisse && caisse.budgetLines && caisse.budgetLines.length > 0) {
    const budgetLineSorted = [...caisse.budgetLines].sort((a, b) =>
      a.updatedAt < b.updatedAt ? 1 : -1,
    );

    const startIndex = (currentPage - 1) * pageSize;
    const endIndex = Math.min(startIndex + pageSize, budgetLineSorted.length);
    const paginatedBudgetLines = budgetLineSorted.slice(startIndex, endIndex);

    listBudgetLines = paginatedBudgetLines.map(budgetLine => ({
      ...budgetLine,
      amount: `${budgetLine.amount}`,
      codeDonation: `${budgetLine.codeDonation}`,
      receptionDateDonation: `${budgetLine.receptionDateDonation}`,
      fullNameDonor: `${budgetLine.fullNameDonor}`,
      idDonation: `${budgetLine.idDonation}`,
    }));
  }

  const columns = [
    {
      field: 'codeDonation',
      headerName: 'Code donation',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
      renderCell: params => (
        <Link to={`/donations/fiche/${params.row.idDonation}/info`}>
          {params.row.codeDonation}
        </Link>
      ),
    },
    {
      field: 'receptionDateDonation',
      headerName: 'Date de réception',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
      renderCell: params => formatDate(params.value),
    },
    {
      field: 'amount',
      headerName: 'Montant',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
    {
      field: 'fullNameDonor',
      headerName: 'Donateur',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
  ];

  return (
    <div>
      {successAlert}
      <div className={`pb-5 ${stylesList.backgroudStyle}`}>
        <div>
          <div className={styles.global}>
            <div className={styles.header}>
              <h4>liste des entrées</h4>
            </div>
            <DataTable
              rows={listBudgetLines}
              columns={columns}
              fileName={`Liste des entrées de la caisse ${caisseCode}, ${new Date().toLocaleString()}`}
            />
            <div className="justify-content-center my-4">
              {caisse && caisse.budgetLines.length > 0 && (
                <CustomPagination
                  totalCount={Math.ceil(caisse.budgetLines.length / pageSize)}
                  pageSize={pageSize}
                  currentPage={currentPage}
                  onPageChange={setCurrentPage}
                />
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
