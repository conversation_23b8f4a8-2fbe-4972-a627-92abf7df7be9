import React, { useEffect, useState } from 'react';
import stylesList from 'Css/profileList.css';
import navigationStyle from 'Css/sectionNavigation.css';
import { makeSelectSuccessExternalIncome } from 'containers/Family/FamilyProfile/selectors';
import { resetSuccessValues } from 'containers/Family/FamilyProfile/actions';
import { useDispatch, useSelector } from 'react-redux';
import { createStructuredSelector } from 'reselect';
import { Alert } from 'react-bootstrap';
import ExternalIncomes from './ExternalIncomes';
import InternalIncomes from './InternalIncomes';
import tabsStyle from 'Css/profileList.css';

const EXTERNAL = 'external';
const INTERNAL = 'internal';

const omdbSelector = createStructuredSelector({
  successExternalIncome: makeSelectSuccessExternalIncome,
});
export default function Incomes(props) {
  const { successExternalIncome } = useSelector(omdbSelector);
  const dispatch = useDispatch();

  const [section, setSection] = useState(INTERNAL);
  const [message, setMessage] = useState('');
  const [showAlert, setShowAlert] = useState(false);

  let successAlert = null;
  const family = props.data;
  let incomes = null;

  useEffect(() => {
    if (successExternalIncome) {
      if (successExternalIncome == 'add') {
        setMessage('La source de revenu a été bien ajoutée !');
      } else if (successExternalIncome == 'update') {
        setMessage('La source de revenu a été bien modifiée !');
      } else if (successExternalIncome == 'delete') {
        setMessage('La source de revenu a été bien supprimée !');
      }
      setShowAlert(true);
      dispatch(resetSuccessValues());
    }
  }, [successExternalIncome]);

  useEffect(() => {
    if (showAlert) {
      setTimeout(() => {
        setShowAlert(false);
      }, 4000);
    }
  }, [showAlert]);

  if (showAlert) {
    successAlert = (
      <Alert className="alert-style" variant="success" onClose={() => setShowAlert(false)} dismissible>
        {message}
      </Alert>
    );
  }

  if (family) {
    if (section == EXTERNAL) {
      incomes = <ExternalIncomes data={family} />;
    } else {
      incomes = <InternalIncomes data={family} />;
    }
  }

  return (
    <div>
      {successAlert}
      <div className={`pb-5 pt-4 ${stylesList.backgroudStyle}`}>
        <div className={tabsStyle.tabs}>
          <button
              className={`${tabsStyle.tab} ${section === INTERNAL ? tabsStyle.active : ''}`}
              onClick={() => {
                setSection(INTERNAL);
              }}
          >
            REVENUS ALMOBADARA PAR MOIS
          </button>
          <button
              className={`${tabsStyle.tab} ${section === EXTERNAL ? tabsStyle.active : ''}`}
              onClick={() => {
                setSection(EXTERNAL);
              }}
          >
            AUTRES SOURCES DE REVENUS
          </button>
        </div>
        <div className="mt-4">{incomes}</div>
      </div>
    </div>
  );
}
