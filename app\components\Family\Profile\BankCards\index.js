import React, { useEffect, useState } from 'react';
import { Alert } from 'react-bootstrap';

import { useDispatch, useSelector } from 'react-redux';
import { useInjectReducer, useInjectSaga } from 'redux-injectors';
import Card from 'components/Common/Card';
import { createStructuredSelector } from 'reselect';
import styles from 'Css/profileList.css';
import stylesList from 'Css/profileList.css';
import btnStyles from 'Css/button.css';
import Modal2 from 'components/Common/Modal2';
import BankCardForm from 'containers/Common/Forms/BankCardForm';

import {
  deleteBankCard,
  resetBankCard,
} from 'containers/Common/Forms/BankCardForm/actions';
import { bankCardSaga } from 'containers/Common/Forms/BankCardForm/saga';
import reducer from 'containers/Common/Forms/BankCardForm/reducer';
import {
  pushBankCard,
  removeBankCard,
  updateBankCard,
} from 'containers/Family/FamilyProfile/actions';
import {
  makeSelectBankCard,
  makeSelectSuccess,
  makeSelectSuccessDelete,
} from 'containers/Common/Forms/BankCardForm/selectors';
import getTutor from 'components/Family/Common/Functions/GetActiveTutor';
import getTutors from 'components/Family/Common/Functions/GetTutors';
import AccessControl from 'utils/AccessControl';
import { useHistory, useLocation } from 'react-router-dom';

const key = 'bankCard';

const omdbSelector = createStructuredSelector({
  success: makeSelectSuccess,
  bankCard: makeSelectBankCard,
  successDelete: makeSelectSuccessDelete,
});

export default function BankCards(props) {
  const { success, bankCard, successDelete } = useSelector(omdbSelector);
  useInjectSaga({ key, saga: bankCardSaga });
  useInjectReducer({ key, reducer });
  const [redirectionMessage, setRedirectionMessage] = useState('');

  const family = props.data;

  let cards = null;
  const tempCards = [];
  const adaptedCards = [];
  let tutorName = '';
  let successAlert = null;
  const history = useHistory();
  const location = useLocation();


  const dispatch = useDispatch();
  const [tutorId, setTutorId] = useState(false);
  const [personId, setPersonId] = useState(null);
  const handleTutorId = tutorId => {
    setTutorId(tutorId);
  };

  const [show, setShow] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [showAlert, setShowAlert] = useState(false);
  const [message, setMessage] = useState('');
  const [actualBankCard, setActualBankCard] = useState('');
  const [takeInchageId, setTakeInChargeId] = useState(null);
  const [openOperationId, setOpenOperationId] = useState(null);

  useEffect(() => {
    if (showAlert) {
      const timer = setTimeout(() => {
        setShowAlert(false);
      }, 3000);
      return () => clearTimeout(timer);
    }
  }, [showAlert]);

  const handleActualBankCard = bankCard => {
    setActualBankCard(bankCard);
  };
  const handleCloseModal = () => setShow(false);
  const handleShowModal = () => setShow(true);
  const handleCloseForDeleteModal = () => setShowDeleteModal(false);
  const handleShowForDeleteModal = () => setShowDeleteModal(true);

  useEffect(() => {
    if (successDelete) {
      dispatch(removeBankCard(bankCard, tutorId));
      setMessage('La carte bancaire a été bien supprimée !');
      dispatch(resetBankCard());
    }
  }, [successDelete]);

  useEffect(() => {
    if (success) {
      setShowAlert(true);
      if (actualBankCard === '') {
        dispatch(pushBankCard(bankCard, tutorId));
        setMessage('La carte bancaire a été bien ajoutée !');
        if (takeInchageId && openOperationId) {
          history.push({
            pathname: `/takenInCharges/fiche/${takeInchageId}/planification`,
            state: { openOperationId: openOperationId },
          });
        } else if (takeInchageId) {
          history.push(`/takenInCharges/fiche/${takeInchageId}/planification`);
        }

      } else {
        dispatch(updateBankCard(bankCard, tutorId));
        setMessage('La carte bancaire a été bien modifiée !');
      }
    }
  }, [success]);

  useEffect(() => {
    if (location.state && location.state.takenInChargeId) {
      handleActualBankCard('');
      handleShowModal();
      setTakeInChargeId(location.state.takenInChargeId);
      setOpenOperationId(location.state.operationId);
      setRedirectionMessage(
        "Dès que la carte bancaire sera ajoutée avec succès, vous serez redirigé vers l'opération pour continuer votre action.",
      );
    } else {
      setRedirectionMessage('');
    }
  }, []);

  if (showAlert) {
    successAlert = (
      <div>
        <Alert
          className="alert-style"
          variant="success"
          onClose={() => setShowAlert(false)}
          dismissible
        >
          {message}
        </Alert>
      </div>
    );
  }

  useEffect(() => {
    if (family) {
      // we should map this memeber familyMembers and check if a memeber have the attribute tutor = true
      for (let i = 0; i < family.familyMembers.length; i++) {
        if (family.familyMembers[i].tutor) {
          setTutorId(family.familyMembers[i].id);
          setPersonId(family.familyMembers[i].person.id);
        }
      }
    }
  }, [family]);

  if (family && family.familyMembers) {
    const tutors = getTutors(family);
    if (tutors && tutors.length > 0) {
      tutors.map(tutor => {
        if (tutor.person.bankCards && tutor.person.bankCards.length > 0) {
          tutorName = '';
          if (tutor.person.sex) {
            tutorName = tutor.person.sex == 'Femme' ? 'Mme ' : 'M. ';
          }
          tutorName += `${tutor.person.lastName} ${tutor.person.firstName}`;

          tutor.person.bankCards.map(card => {
            adaptedCards.push({
              ...card,
              tutorName,
              tutorId: tutor.id,
            });
          });
        }
      });

      const sortedCards = [...adaptedCards].sort((a, b) =>
        a.createdAt < b.createdAt ? 1 : -1,
      );

      cards = sortedCards.map(card => (
        <Card
          key={card.id}
          tutorName={card.tutorName}
          tutorId={card.tutorId}
          data={card}
          handleShowModal={handleShowModal}
          handleActualBankCard={handleActualBankCard}
          handleShowForDeleteModal={handleShowForDeleteModal}
          handleTutorId={handleTutorId}
          showDeleteIcon
        />
      ));
    }
  }

  return (
    <div>
      {successAlert}
      <div className={`pb-5 ${stylesList.backgroudStyle}`}>
        <div className={styles.global}>
          <Modal2
            size="lg"
            customWidth="modal-90w"
            title={
              actualBankCard
                ? 'Modifier une carte bancaire'
                : 'Ajouter une carte bancaire'
            }
            show={show}
            handleClose={handleCloseModal}
          >
            <BankCardForm
              bankCard={actualBankCard}
              show={show}
              handleClose={handleCloseModal}
              personId={personId}
              redirectionMessage={redirectionMessage}
            ></BankCardForm>
          </Modal2>
          <Modal2
            centered
            className="mt-5"
            title="Confirmation de suppression"
            show={showDeleteModal}
            handleClose={handleCloseForDeleteModal}
          >
            <p className="mt-1 mb-5 px-2">
              Êtes-vous sûr de vouloir supprimer cette carte bancaire?
            </p>
            <div className="d-flex justify-content-end px-2 my-1">
              <button
                type="button"
                className="btn-style outlined"
                onClick={handleCloseForDeleteModal}
              >
                Annuler
              </button>
              <AccessControl module="FAMILLE" functionality="UPDATE">
                <button
                  type="submit"
                  className={`ml-3 btn-style primary`}
                  onClick={() => {
                    dispatch(deleteBankCard(actualBankCard));
                    setActualBankCard('');
                    handleCloseForDeleteModal();
                  }}
                >
                  Supprimer
                </button>
              </AccessControl>
            </div>
          </Modal2>

          <div className={`${styles.header} align-items-center`}>
            <h4>Informations Bancaires</h4>

            {tutorId && (
              <AccessControl module="FAMILLE" functionality="UPDATE">
                <button
                  className="btn-style primary"
                  onClick={() => {
                    handleActualBankCard('');
                    handleShowModal();
                  }}
                  data-toggle="modal"
                  data-target="#exampleModal"
                >
                  <i className="fas fa-plus" />
                  &nbsp; Ajouter
                </button>
              </AccessControl>
            )}
          </div>

          {cards && cards.length == 0 ? (
            <p className="my-4">
              Il n'y a pas de carte bancaire enregistrée pour le tuteur de cette
              famille !
            </p>
          ) : cards && cards.length > 0 ? (
            cards
          ) : (
            <p className="my-4">Il n'y a pas de tuteur pour cette famille !</p>
          )}
        </div>
      </div>
    </div>
  );
}
