import { <PERSON>ET<PERSON>_GENERAL_DASHBOARD_REQUEST, <PERSON><PERSON>CH_GENERAL_DASHBOARD_SUCCESS, FETCH_GENERAL_DASHBOARD_FAILURE, FETCH_DONORS_DASHBOARD_REQUEST, <PERSON>ETCH_DONORS_DASHBOARD_SUCCESS, FETCH_DONORS_DASHBOARD_FAILURE, FETCH_BENEFICIARIES_DASHBOARD_REQUEST, FETCH_BENEFICIARIES_DASHBOARD_SUCCESS, FETCH_BENEFICIARIES_DASHBOARD_FAILURE, FETCH_DONATIONS_DASHBOARD_REQUEST, FETCH_DONATIONS_DASHBOARD_SUCCESS, FETCH_DONATIONS_DASHBOARD_FAILURE, FETCH_KAFALAT_DASHBOARD_REQUEST, FETCH_KAFALAT_DASHBOARD_SUCCESS, <PERSON>ET<PERSON>_KAFALAT_DASHBOARD_FAILURE, FETCH_AIDE_DASHBOARD_REQUEST, FETCH_AIDE_DASHBOARD_SUCCESS, <PERSON><PERSON><PERSON>_AIDE_DASHBOARD_FAILURE, FETCH_EPS_DASHBOARD_REQUEST, FETCH_EPS_DASHBOARD_SUCCESS, FETCH_EPS_DASHBOARD_FAILURE } from './constants';

// Fetch General Dashboard
export const fetchGeneralDashboard = () => {
  console.log('fetching data from action');
  return {
    type: FETCH_GENERAL_DASHBOARD_REQUEST,
  };
};
export const fetchGeneralDashboardSuccess = (data) => {
    console.log('dashboard data from action : ',data);
  return {
    type: FETCH_GENERAL_DASHBOARD_SUCCESS,
    payload: data,
  };
};
export const fetchGeneralDashboardFailure = (error) => {
  return {
    type: FETCH_GENERAL_DASHBOARD_FAILURE,
    payload: error,
  };
};

// Fetch Donors Dashboard
export const fetchDonorsDashboard = () => {
  console.log('fetching data from action');
  return {
    type: FETCH_DONORS_DASHBOARD_REQUEST,
  };
};
export const fetchDonorsDashboardSuccess = (data) => {
    console.log('dashboard data from action : ',data);
  return {
    type: FETCH_DONORS_DASHBOARD_SUCCESS,
    payload: data,
  };
};
export const fetchDonorsDashboardFailure = (error) => {
  return {
    type: FETCH_DONORS_DASHBOARD_FAILURE,
    payload: error,
  };
};

// Fetch Beneficiaries Dashboard
export const fetchBeneficiariesDashboard = () => {
  console.log('fetching data from action');
  return {
    type: FETCH_BENEFICIARIES_DASHBOARD_REQUEST,
  };
};
export const fetchBeneficiariesDashboardSuccess = (data) => {
    console.log('dashboard data from action : ',data);
  return {
    type: FETCH_BENEFICIARIES_DASHBOARD_SUCCESS,
    payload: data,
  };
};
export const fetchBeneficiariesDashboardFailure = (error) => {
  return {
    type: FETCH_BENEFICIARIES_DASHBOARD_FAILURE,
    payload: error,
  };
};

// Fetch Donations Dashboard
export const fetchDonationsDashboard = () => {
  console.log('fetching data from action');
  return {
    type: FETCH_DONATIONS_DASHBOARD_REQUEST,
  };
};
export const fetchDonationsDashboardSuccess = (data) => {
  console.log('dashboard data from action : ', data);
  return {
    type: FETCH_DONATIONS_DASHBOARD_SUCCESS,
    payload: data,
  };
};
export const fetchDonationsDashboardFailure = (error) => {
  return {
    type: FETCH_DONATIONS_DASHBOARD_FAILURE,
    payload: error,
  };
};

// Fetch Kafalat Dashboard
export const fetchKafalatDashboard = () => {
  console.log('fetching data from action');
  return {
    type: FETCH_KAFALAT_DASHBOARD_REQUEST,
  };
};
export const fetchKafalatDashboardSuccess = (data) => {
  console.log('dashboard data from action : ', data);
  return {
    type: FETCH_KAFALAT_DASHBOARD_SUCCESS,
    payload: data,
  };
};
export const fetchKafalatDashboardFailure = (error) => {
  return {
    type: FETCH_KAFALAT_DASHBOARD_FAILURE,
    payload: error,
  };
};

// Fetch Aide Dashboard
export const fetchAideDashboard = () => {
  console.log('fetching data from action');
  return {
    type: FETCH_AIDE_DASHBOARD_REQUEST,
  };
};
export const fetchAideDashboardSuccess = (data) => {
  console.log('dashboard data from action : ', data);
  return {
    type: FETCH_AIDE_DASHBOARD_SUCCESS,
    payload: data,
  };
};
export const fetchAideDashboardFailure = (error) => {
  return {
    type: FETCH_AIDE_DASHBOARD_FAILURE,
    payload: error,
  };
};

// Fetch EPS Dashboard
export const fetchEPSDashboard = () => {
  console.log('fetching data from action');
  return {
    type: FETCH_EPS_DASHBOARD_REQUEST,
  };
};
export const fetchEPSDashboardSuccess = (data) => { 
  console.log('dashboard data from action : ', data);
  return {
    type: FETCH_EPS_DASHBOARD_SUCCESS,
    payload: data,
  };
};
export const fetchEPSDashboardFailure = (error) => {
  return {
    type: FETCH_EPS_DASHBOARD_FAILURE,
    payload: error,
  };
};

