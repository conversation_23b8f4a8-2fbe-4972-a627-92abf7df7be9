import React from 'react';
import { NavLink, useParams } from 'react-router-dom';

import styles from '../../../../Css/profileHeader.css';

const HeaderEPS = () => {
  const params = useParams();
  return (
    <div className={styles.navBar}>
      <p>
        <NavLink
          exact
          to={'/eps/fiche/' + params.id + '/info'}
          activeClassName={styles.selected}
        >
          Informations générales
        </NavLink>
      </p>
      <p>
        <NavLink
          to={'/eps/fiche/' + params.id + '/responsables'}
          activeClassName={styles.selected}
        >
          Responsables
        </NavLink>
      </p>
      <p>
        <NavLink
          to={'/eps/fiche/' + params.id + '/beneficiaires'}
          activeClassName={styles.selected}
        >
          Bénéficiaires
        </NavLink>
      </p>

      
    </div>
  );
};

export default HeaderEPS;
