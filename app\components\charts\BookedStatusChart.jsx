import React, { useState, useEffect } from 'react';
import { Box, Typography, Paper, useTheme } from '@mui/material';
import { styled } from '@mui/material/styles';

const StyledPaper = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(3),
  display: 'flex',
  flexDirection: 'column',
  background: 'linear-gradient(145deg, #ffffff, #f0f0f0)',
  boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
  borderRadius: '16px',
  transition: 'transform 0.3s ease, box-shadow 0.3s ease',
  height: '100%',
  '&:hover': {
    transform: 'translateY(-4px)',
    boxShadow: '0 8px 30px rgba(0, 0, 0, 0.12)',
  },
}));

const ChartHeader = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  gap: theme.spacing(1),
  marginBottom: theme.spacing(2),
}));

const StatusContainer = styled(Box)({
  display: 'flex',
  flexDirection: 'column',
  gap: '16px',
  padding: '8px 0',
});

const StatusRow = styled(Box)({
  display: 'flex',
  alignItems: 'center',
  gap: '12px',
  position: 'relative',
});

const StatusLabel = styled(Typography)({
  minWidth: '80px',
  fontSize: '14px',
  fontWeight: 500,
  color: '#666',
  textTransform: 'uppercase',
});

const ProgressBarContainer = styled(Box)({
  flex: 1,
  height: '8px',
  backgroundColor: 'rgba(0, 0, 0, 0.05)',
  borderRadius: '4px',
  overflow: 'hidden',
  position: 'relative',
});

const ProgressBar = styled(Box)(({ color, width, visible, delay }) => ({
  height: '100%',
  width: visible ? `${width}%` : '0%',
  backgroundColor: color,
  borderRadius: '4px',
  transition: `width 1s cubic-bezier(0.4, 0, 0.2, 1) ${delay}s`,
  position: 'relative',
  '&::after': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    background: 'linear-gradient(90deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.3) 50%, rgba(255,255,255,0.1) 100%)',
    backgroundSize: '200% 100%',
    animation: 'shimmer 2s infinite',
  },
  '@keyframes shimmer': {
    '0%': {
      backgroundPosition: '200% 0',
    },
    '100%': {
      backgroundPosition: '-200% 0',
    },
  },
}));

const ValueLabel = styled(Typography)(({ color }) => ({
  fontSize: '14px',
  fontWeight: 600,
  color: color,
  marginLeft: '12px',
  minWidth: '60px',
}));

const TotalValue = styled(Typography)(({ theme }) => ({
  fontSize: '16px',
  fontWeight: 600,
  color: theme.palette.text.secondary,
  marginLeft: 'auto',
}));

const BookedStatusChart = ({ data = [], showTotal = false, title = "Booked", icon }) => {
  const theme = useTheme();
  const [isVisible, setIsVisible] = useState(false);

  // Calculate the maximum value for percentage calculation
  const maxValue = Math.max(...data.map(item => item.value));
  
  // Calculate total from data values
  const totalValue = data.reduce((sum, item) => sum + item.value, 0);

  useEffect(() => {
    const timer = setTimeout(() => {
      setIsVisible(true);
    }, 300);
    return () => clearTimeout(timer);
  }, []);

  const formatValue = (value) => {
    if (value >= 1000) {
      return `${(value / 1000).toFixed(1)}k`;
    }
    return value.toString();
  };

  return (
    <StyledPaper>
      <ChartHeader>
        {icon && React.cloneElement(icon, { color: "primary" })}
        <Typography variant="h6" color="primary">
          {title}
        </Typography>
        {showTotal && (
          <TotalValue>Total: {formatValue(totalValue)}</TotalValue>
        )}
      </ChartHeader>
      <StatusContainer>
        {data.map((item, index) => (
          <StatusRow key={item.status}>
            <StatusLabel>{item.title}</StatusLabel>
            <ProgressBarContainer>
              <ProgressBar
                color={item.color}
                width={(item.value / maxValue) * 100}
                visible={isVisible}
                delay={index * 0.2}
              />
            </ProgressBarContainer>
            <ValueLabel color={item.color}>
              {formatValue(item.value)}
            </ValueLabel>
          </StatusRow>
        ))}
      </StatusContainer>
    </StyledPaper>
  );
};

export default BookedStatusChart; 