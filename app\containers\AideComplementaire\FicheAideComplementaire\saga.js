import { call, put, takeLatest } from 'redux-saga/effects';
import request from 'utils/request';
import {
  aideComplementaireLoaded,
  aideComplementaireLoadingError,
  beneficiariesForaideComplementaireLoaded,
  beneficiariesForLoadingError,
  closeAideComplementaireFailure,
  closeAideComplementaireSuccess,
  donorsForAideComplementaireLoaded,
  donorsForAideComplementaireLoadingError,
  executeAideComplementaireFailure,
  executeAideComplementaireSuccess,
  unexecuteAideComplementaireFailure,
  unexecuteAideComplementaireSuccess,
  fetchBudgetLinesFailure,
  fetchBudgetLinesSuccess,
  getGroupeBeneficiariesFailure,
  getGroupeBeneficiariesSuccess,
  getAideBeneficiariesFailure,
  getAideBeneficiariesSuccess,
  processDonorsAndBeneficiariesError,
  processDonorsAndBeneficiariesSuccess,
  processDonorsError,
  processDonorsSuccess,
  removeBeneficiaryFailure,
  removeBeneficiarySuccess,
  removeDonorFailure,
  removeDonorSuccess,
  reserveAllBudgetLinesFailure,
  reserveAllBudgetLinesSuccess,
  updateMontantBeneficiaryFailure,
  updateMontantBeneficiarySuccess,
  updateMontantForGroupFailure,
  updateMontantForGroupSuccess,
  updateMontantReservedBudgetLineFailure,
  updateMontantReservedBudgetLineSuccess,
  updateStatutResarvationBudgetLineFailure,
  updateStatutResarvationBudgetLineSuccess,
  updateStatutValidationBeneficiaryFailure,
  updateStatutValidationBeneficiarySuccess,
  validateAllBeneficiariesSuccess,
  validateAllBeneficiariesFailure,
} from './actions';
import {
  CLOSE_AIDE_COMPLEMENTAIRE_REQUEST,
  EXECUTE_AIDE_COMPLEMENTAIRE_REQUEST,
  UNEXECUTE_AIDE_COMPLEMENTAIRE_REQUEST,
  FETCH_BUDGET_LINES_REQUEST,
  GET_GROUPE_BENEFICIARIES_REQUEST,
  GET_AIDE_BENEFICIARIES_REQUEST,
  LOAD_AIDECOMPLEMENTAIRE,
  LOAD_BENEFICIARY_FOR_AIDECOMPLEMENTAIRE,
  LOAD_DONOR_FOR_AIDECOMPLEMENTAIRE,
  PROCESS_DONORS_BENEFICIARIES_REQUEST,
  PROCESS_DONORS_REQUEST,
  REMOVE_BENEFICIARY_REQUEST,
  REMOVE_DONOR_REQUEST,
  RESERVE_ALL_BUDGET_LINES_REQUEST,
  UPDATE_MONTANT_BENEFICIARY_REQUEST,
  UPDATE_MONTANT_FOR_GROUP_REQUEST,
  UPDATE_MONTANT_RESERVED_BUDGETLINE_REQUEST,
  UPDATE_STATUT_RESERAVATION_BUDGETLINE_REQUEST,
  UPDATE_STATUT_VALIDATION_BENEFICIARY_REQUEST,
  VALIDATE_ALL_BENEFICIARIES_REQUEST,
} from './constants';
import { serialize } from '../../Common/FormDataConverter';

function buildUrlWithFilters(baseUrl, filters) {
  let url = baseUrl;

  if (filters) {
    const {
      text,
      type,
      beneficiaryStatut,
      withMemberParticipant,
      withDonor,
      withDonorParticipant,
      fromPreviousCampaign,
    } = filters;

    if (withMemberParticipant)
      url += `&withParticipatingMembers=${withMemberParticipant}`;
    if (withDonor) url += `&withDonor=${withDonor}`;
    if (withDonorParticipant)
      url += `&withParticipatingDonor=${withDonorParticipant}`;
    if (fromPreviousCampaign) url += `&withOldCampagne=${fromPreviousCampaign}`;
    if (text) url += `&text=${text}`;
    if (type) url += `&type=${type}`;
    if (beneficiaryStatut) url += `&beneficiaryStatut=${beneficiaryStatut}`;
  }

  return url;
}

export function* loadAideComplementaire({ search }) {
  const url = `/aide-complementaire/${search}`;

  try {
    const { data } = yield call(request.get, url);
    yield put(aideComplementaireLoaded(data));
  } catch (error) {
    yield put(aideComplementaireLoadingError(error));
  }
}

export function* loadBeneficiariesForAideComplementaire({
  page,
  aideComplementaireId,
  filters,
}) {
  let url = `/aide-complementaire/${aideComplementaireId}/active?page=${page}`;
  url = buildUrlWithFilters(url, filters);


  try {
    const { data } = yield call(request.get, url);
    yield put(beneficiariesForaideComplementaireLoaded(data));
  } catch (error) {
    yield put(beneficiariesForLoadingError(error));
  }
}



export function* loadPageableBeneficiariesForAideComplementaire({
  aideComplementaireId,
  page,
  filters,
}) {
  let url = `/aide-complementaire/${aideComplementaireId}/beneficiaries?page=${page}`;
  if (filters) {
    const {
      searchFullName,
      category,
      type,
      relatedToDonor,
      validationStatus
    } = filters;

    if (searchFullName)
      url += `&searchFullName=${searchFullName}`;
    if (category) url += `&category=${category}`;
    if (type)
      url += `&type=${type}`;
    if (relatedToDonor) url += `&relatedToDonor=${relatedToDonor}`;
    if (validationStatus) url += `&validationStatus=${validationStatus}`;
  }

  try {
    const { data } = yield call(request.get, url);
    yield put(getAideBeneficiariesSuccess(data));
  } catch (error) {
    yield put(getAideBeneficiariesFailure(error));
  }
}

export function* loadDonorsForAideComplementaire({ aideComplementaireId }) {
  const url = `/aide-complementaire/${aideComplementaireId}/activeDonors`;

  try {
    const { data } = yield call(request.get, url);
    yield put(donorsForAideComplementaireLoaded(data));
  } catch (error) {
    yield put(donorsForAideComplementaireLoadingError(error));
  }
}

export function* fetchBudgetLinesSaga({ serviceId, aideComplementaireId }) {
  const url = `/aide-complementaire/budgetLines/${serviceId}/${aideComplementaireId}/budgetLines`;

  try {
    const { data } = yield call(request.get, url);
    yield put(fetchBudgetLinesSuccess(data));
  } catch (error) {
    yield put(fetchBudgetLinesFailure(error));
  }
}

export function* fetchGroupeBeneficiariesSaga({
  aideComplementaireId,
  groupeId,
}) {
  const url = `/aide-complementaire/groupe-beneficiaries?aideComplementaireId=${aideComplementaireId}&groupeId=${groupeId}`;

  try {
    const { data } = yield call(request.get, url);
    yield put(getGroupeBeneficiariesSuccess(data));
  } catch (error) {
    yield put(getGroupeBeneficiariesFailure(error));
  }
}

export function* processDonorsAndBeneficiariesSaga({
  idAideComplementaire,
  beneficiaryIds,
}) {
  const url = `/aide-complementaire/processDonorsAndBeneficiaries?idAideComplementaire=${idAideComplementaire}`;



  try {
    const { data } = yield call(request.post, url, beneficiaryIds);
    yield put(processDonorsAndBeneficiariesSuccess());
  } catch (error) {
    yield put(processDonorsAndBeneficiariesError(error));
  }
}

export function* removeBeneficiaryFromAideComplementaireSaga({
  idAideComplementaire,
  idBeneficiary,
  idDonor,
  typeBeneficiary,
}) {
  let url;
  if (idDonor != null) {
    url = `/aide-complementaire/remove-beneficiary-from-aide?idAideComplementaire=${idAideComplementaire}&idBeneficiary=${idBeneficiary}&idDonor=${idDonor}&type=${typeBeneficiary}`;
  } else {
    url = `/aide-complementaire/remove-beneficiary-from-aide?idAideComplementaire=${idAideComplementaire}&idBeneficiary=${idBeneficiary}&type=${typeBeneficiary}`;
  }



  try {
    const { data } = yield call(request.delete, url);
    yield put(removeBeneficiarySuccess());
  } catch (error) {
    yield put(removeBeneficiaryFailure(error));
  }
}

export function* removeDonorFromAideComplementaireSaga({
  idAideComplementaire,
  idDonor,
}) {
  const url = `/aide-complementaire/remove-donor-from-aide?idAideComplementaire=${idAideComplementaire}&idDonor=${idDonor}`;



  try {
    const { data } = yield call(request.delete, url);
    yield put(removeDonorSuccess());
  } catch (error) {
    yield put(removeDonorFailure(error));
  }
}

export function* executeAideComplementaireSaga({
  aideComplementaireId,
  validatedAmount,
}) {
  const url = `/aide-complementaire/execute?aideComplementaireId=${aideComplementaireId}&validatedAmount=${validatedAmount}`;

  try {
    const { data } = yield call(request.post, url);
    yield put(executeAideComplementaireSuccess());
  } catch (error) {
    yield put(executeAideComplementaireFailure(error));
  }
}

export function* unexecuteAideComplementaireSaga({
  aideComplementaireId,
}) {
  const url = `/aide-complementaire/unexecute?aideComplementaireId=${aideComplementaireId}`;

  try {
    const { data } = yield call(request.post, url);
    yield put(unexecuteAideComplementaireSuccess());
  } catch (error) {
    yield put(unexecuteAideComplementaireFailure(error));
  }
}

export function* refreshDonorsSaga({
  idAideComplementaire,
  priority,
  idDonors,
}) {
  const url = `/aide-complementaire/refresh-donors?idAideComplementaire=${idAideComplementaire}&priority=${priority}`;



  try {
    const { data } = yield call(request.post, url, idDonors);
    yield put(processDonorsSuccess());
  } catch (error) {
    yield put(processDonorsError(error));
  }
}

export function* updateStatutValidationBeneficiarySaga({
  idAideComplementaire,
  idBeneficiary,
  idDonor,
  typeBeneficiary,
}) {
  let url;
  if (idDonor != null) {
    url = `/aide-complementaire/${idAideComplementaire}/beneficiary/${idBeneficiary}/update-statut?type=${typeBeneficiary}&idDonor=${idDonor}`;
  } else {
    url = `/aide-complementaire/${idAideComplementaire}/beneficiary/${idBeneficiary}/update-statut?type=${typeBeneficiary}`;
  }



  try {
    const { data } = yield call(request.put, url);
    yield put(updateStatutValidationBeneficiarySuccess());
  } catch (error) {
    yield put(updateStatutValidationBeneficiaryFailure(error));
  }
}

export function* updateStatutReservationBudgetLineSaga({
  donorId,
  aideComplementaireId,
  reservedAmount,
  operation,
  isNature
}) {
  let url;

  if (reservedAmount != null) {
    url = `/aide-complementaire/reserve-by-donor?donorId=${donorId}&aideComplementaireId=${aideComplementaireId}&reservedAmount=${reservedAmount}&operation=${operation}&isNature=${isNature}`;
  } else {
    url = `/aide-complementaire/reserve-by-donor?donorId=${donorId}&aideComplementaireId=${aideComplementaireId}&operation=${operation}&isNature=${isNature}`;
  }

  try {
    const { data } = yield call(request.post, url);
    yield put(updateStatutResarvationBudgetLineSuccess());
  } catch (error) {
    yield put(updateStatutResarvationBudgetLineFailure(error));
  }
}

/////////
export function* reserveAllBudgetLinesSaga({
  idAideComplementaire,
  donorAmounts,
  operation,
}) {
  const url = `/aide-complementaire/reserve-all-budget-lines/${idAideComplementaire}?operation=${operation}`;

  try {
    const { data } = yield call(request.post, url, donorAmounts);
    yield put(reserveAllBudgetLinesSuccess());
  } catch (error) {
    yield put(reserveAllBudgetLinesFailure(error));
  }
}

export function* updateMontantBeneficiarySaga({
  idAideComplementaire,
  idBeneficiary,
  idDonor,
  typeBeneficiary,
  newMontant,
  numberOfMembers
}) {
  let url;
  if (idDonor != null) {
    url = `/aide-complementaire/update-montant-beneficiary?idAideComplementaire=${idAideComplementaire}&idBeneficiary=${idBeneficiary}&idDonor=${idDonor}&type=${typeBeneficiary}&newMontant=${newMontant}`;
  } else {
    url = `/aide-complementaire/update-montant-beneficiary?idAideComplementaire=${idAideComplementaire}&idBeneficiary=${idBeneficiary}&type=${typeBeneficiary}&newMontant=${newMontant}&numberOfMembers=${numberOfMembers}`;
  }



  try {
    const { data } = yield call(request.put, url, {
      params: {
        idAideComplementaire,
        idBeneficiary,
        newMontant,
      },
    });
    yield put(updateMontantBeneficiarySuccess());
  } catch (error) {
    yield put(updateMontantBeneficiaryFailure(error));
  }
}

export function* updateMontantForGroupSaga({
  idAideComplementaire,
  idGroupe,
  typeGroup,
  beneficiaryAmounts,
}) {
  const url = `/aide-complementaire/update-montant-for-groupe?idAideComplementaire=${idAideComplementaire}&idGroupe=${idGroupe}&type=${typeGroup}`;



  try {
    const { data } = yield call(request.post, url, beneficiaryAmounts);
    yield put(updateMontantForGroupSuccess());
  } catch (error) {
    yield put(updateMontantForGroupFailure(error));
  }
}

export function* updateMontantReservedBudgetLineSaga({
  budgetLineId,
  newAmount,
}) {
  const url = `/aide-complementaire/update-reservedAmount-budgetLine?budgetLineId=${budgetLineId}&newAmount=${newAmount}`;



  try {
    const { data } = yield call(request.put, url, {
      params: {
        budgetLineId,
        newAmount,
      },
    });
    yield put(updateMontantReservedBudgetLineSuccess());
  } catch (error) {
    yield put(updateMontantReservedBudgetLineFailure(error));
  }
}

export function* closeAideComplementaireSaga({ documentAndEntityDto, target }) {
  const url = `/aide-complementaire/close`;

  let formData = new FormData();

  formData = serialize(documentAndEntityDto, {
    indices: true,
    nullsAsUndefineds: false,
  });
  const formData2 = new FormData();
  for (const par of formData.entries()) {
    if (par[1]) {
      if (par[0].includes('Date')) {
        formData2.append(par[0], new Date(par[1]));
      } else {
        formData2.append(par[0], par[1]);
      }
    }
  }


  try {
    const { data } = yield call(request.post, url, formData2);
    yield put(closeAideComplementaireSuccess(data));
  } catch (error) {
    yield put(closeAideComplementaireFailure(error));
  }
}

export function* validateAllBeneficiariesSaga({ aideComplementaireId, filters }) {
  // Base URL without any query parameters
  let url = `/aide-complementaire/${aideComplementaireId}/validateAll`;

  // Track if we have any active filters for the success message
  let hasActiveFilters = false;
  let filterDescription = '';

  // Only add query parameters if filters exist and have non-null values
  if (filters) {
    // Filter out null, undefined, and empty string values
    const validFilters = Object.entries(filters).filter(([_, value]) =>
      value !== null && value !== undefined && value !== ''
    );

    // If there are valid filters, add them as query parameters
    if (validFilters.length > 0) {
      hasActiveFilters = true;
      const queryParams = validFilters.map(([key, value]) => `${key}=${value}`);
      url = `${url}?${queryParams.join('&')}`;

      // Create a human-readable description of the filters
      filterDescription = validFilters.map(([key, value]) => {
        switch(key) {
          case 'category': return `catégorie: ${value}`;
          case 'type': return `type: ${value}`;
          case 'withDonor': return value ? 'avec donateur' : 'sans donateur';
          case 'text': return `recherche: ${value}`;
          default: return `${key}: ${value}`;
        }
      }).join(', ');
    }
  }

  try {
    yield call(request.put, url);

    // After successful validation, reload the aide complementaire data
    yield call(loadAideComplementaire, { search: aideComplementaireId });

    // Create success message based on whether filters were applied
    const successMessage = hasActiveFilters
      ? `Les bénéficiaires avec les filtres (${filterDescription}) sont validés !`
      : "Tous les bénéficiaires sont validés !";

    yield put(validateAllBeneficiariesSuccess(successMessage, hasActiveFilters));
  } catch (error) {
    yield put(validateAllBeneficiariesFailure(error));
  }
}

export default function* detailAideComplementaireSaga() {
  yield takeLatest(LOAD_AIDECOMPLEMENTAIRE, loadAideComplementaire);
  yield takeLatest(
    UPDATE_STATUT_VALIDATION_BENEFICIARY_REQUEST,
    updateStatutValidationBeneficiarySaga,
  );
  yield takeLatest(
    PROCESS_DONORS_BENEFICIARIES_REQUEST,
    processDonorsAndBeneficiariesSaga,
  );
  yield takeLatest(
    LOAD_BENEFICIARY_FOR_AIDECOMPLEMENTAIRE,
    loadBeneficiariesForAideComplementaire,
  );
  yield takeLatest(PROCESS_DONORS_REQUEST, refreshDonorsSaga);
  yield takeLatest(
    REMOVE_BENEFICIARY_REQUEST,
    removeBeneficiaryFromAideComplementaireSaga,
  );
  yield takeLatest(
    LOAD_DONOR_FOR_AIDECOMPLEMENTAIRE,
    loadDonorsForAideComplementaire,
  );
  yield takeLatest(
    UPDATE_MONTANT_BENEFICIARY_REQUEST,
    updateMontantBeneficiarySaga,
  );
  yield takeLatest(REMOVE_DONOR_REQUEST, removeDonorFromAideComplementaireSaga);
  yield takeLatest(FETCH_BUDGET_LINES_REQUEST, fetchBudgetLinesSaga);
  yield takeLatest(
    GET_GROUPE_BENEFICIARIES_REQUEST,
    fetchGroupeBeneficiariesSaga,
  );
  yield takeLatest(
    EXECUTE_AIDE_COMPLEMENTAIRE_REQUEST,
    executeAideComplementaireSaga,
  );

  yield takeLatest(
    UNEXECUTE_AIDE_COMPLEMENTAIRE_REQUEST,
    unexecuteAideComplementaireSaga,
  );

  yield takeLatest(
    UPDATE_STATUT_RESERAVATION_BUDGETLINE_REQUEST,
    updateStatutReservationBudgetLineSaga,
  );
  yield takeLatest(
    GET_AIDE_BENEFICIARIES_REQUEST,
    loadPageableBeneficiariesForAideComplementaire,
  );
  yield takeLatest(RESERVE_ALL_BUDGET_LINES_REQUEST, reserveAllBudgetLinesSaga);
  yield takeLatest(
    UPDATE_MONTANT_RESERVED_BUDGETLINE_REQUEST,
    updateMontantReservedBudgetLineSaga,
  );
  yield takeLatest(UPDATE_MONTANT_FOR_GROUP_REQUEST, updateMontantForGroupSaga);
  yield takeLatest(
    CLOSE_AIDE_COMPLEMENTAIRE_REQUEST,
    closeAideComplementaireSaga,
  );
  yield takeLatest(
    VALIDATE_ALL_BENEFICIARIES_REQUEST,
    validateAllBeneficiariesSaga,
  );
}
