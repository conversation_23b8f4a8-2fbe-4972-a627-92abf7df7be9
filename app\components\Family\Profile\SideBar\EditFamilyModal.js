import React, { useEffect, useState } from 'react';
import { Button } from 'react-bootstrap';
import { Form, Formik } from 'formik';
import * as Yup from 'yup';
import CountryCitiesWithRegions from '../../../../containers/Common/SubComponents/CountryCitiesWithRegions';
import AccommodationType from '../../../../containers/Common/SubComponents/AccommodationType';
import { CustomTextInput } from '../../../../containers/Common/CustomInputs/CustomTextInput';
import { CustomTextInputAr } from '../../../../containers/Common/CustomInputs/CustomTextInputAr';
import { CustomTextArea } from '../../../../containers/Common/CustomInputs/CustomTextArea';
import {
  arabicRegexForAddress,
  phoneRegex,
} from '../../../../containers/Common/Scripts/Regex';
import { invalidString } from '../../../../containers/Common/ValidationSchemas/ErrorMessages';
import AccommodationNature from '../../../../containers/Common/SubComponents/AccommodationNature';
import styles from '../../../../Css/form.css';
import Modal2 from '../../../Common/Modal2';
import SousZoneSelect from '../../../../containers/Common/SubComponents/SousZoneSelect';
import ZonesSelect from '../../../../containers/Common/SubComponents/Zones';

const EditFamilyModal = ({
  show,
  onClose,
  initialValues,
  onSubmit,
  handleLocationChange,
  zoneAssistantId,
}) => {
  const validationSchema = Yup.object().shape({
    phoneNumberFamily: Yup.string()
      .required('Téléphone est requis')
      .matches(
        phoneRegex,
        invalidString(
          'Numéro de téléphone',
          'veuillez entrer un numéro valide',
        ),
      ),
    addressFamily: Yup.string().required('Adresse est requise'),
    addressFamilyAr: Yup.string()
      .required('Adresse (Arabe) est requise')
      .matches(
        arabicRegexForAddress,
        invalidString(
          'Adresse arabe',
          "veuillez entrer l'adresse en arabe ([0-9] et ،)",
        ),
      ),
    accommodationType: Yup.string().required(
      "Nature d'hébergement est requise",
    ),
    accommodationNature: Yup.string().required("Type d'hébergement est requis"),
    zone: Yup.string().required('Zone est requise'),
  });

  const [showZone, setShowZone] = useState(false);
  const [selectedZoneId, setSelectedZoneId] = useState(null);

  return (
    <Modal2
      title="Modifier les informations de la famille"
      size="lg"
      customWidth="modal-90w"
      show={show}
      centered
      handleClose={onClose}
    >
      <Formik
        initialValues={initialValues}
        validationSchema={validationSchema}
        onSubmit={values => {
          onSubmit(values);
        }}
      >
        {({ isSubmitting, values, setFieldValue }) => {
          useEffect(() => {
            if (values.zone) {
              setSelectedZoneId(values.zone);
              setShowZone(true);
            } else {
              setFieldValue('sousZone', '');
              setShowZone(false);
            }
          }, [values.zone, setFieldValue]);

          return (
            <div className="container">
              <div className={styles.backgroundForm}>
                <Form>
                  <div>
                    {/* Ligne 1 */}
                    <CountryCitiesWithRegions
                      countries={[]}
                      regions={[]}
                      cities={[]}
                      initialData={{
                        country: initialValues.country,
                        region: initialValues.region,
                        city: initialValues.city,
                      }}
                      onChange={handleLocationChange}
                      isRequired={true}
                    >
                      <div className="form-group col-md-6">
                        <CustomTextInput
                          label="Téléphone de la famille"
                          placeholder="Téléphone"
                          name="phoneNumberFamily"
                          isRequired
                        />
                      </div>
                    </CountryCitiesWithRegions>

                    <div className="form-row">
                      <div className="form-group col-md-6">
                        <ZonesSelect
                          label="Zone"
                          placeholder="Sélectionner une zone"
                          name="zone"
                          isRequired
                          isMeta
                          unique
                          disabled={zoneAssistantId}
                        />
                      </div>
                      {showZone && selectedZoneId && (
                        <div className="form-group col-md-6">
                          <SousZoneSelect
                            label="Sous-zone"
                            placeholder="Sélectionner une sous-zone"
                            name="sousZone"
                            selectedZoneId={selectedZoneId}
                          />
                        </div>
                      )}
                    </div>

                    {/* Ligne 2 */}
                    <div className="form-row">
                      <div className="form-group col-md-6">
                        <CustomTextInput
                          label="Adresse de la famille"
                          placeholder="Adresse de la famille"
                          name="addressFamily"
                          isRequired
                        />
                      </div>
                      <div className="form-group col-md-6">
                        <CustomTextInputAr
                          label="عنوان العائلة"
                          placeholder="عنوان العائلة"
                          name="addressFamilyAr"
                          isRequired
                        />
                      </div>
                    </div>

                    {/* Ligne 3 */}
                    <div className="form-row">
                      <div className="form-group col-md-6">
                        <AccommodationType
                          label="Nature d'hébergement"
                          name="accommodationType"
                          isRequired
                          isMeta
                          unique
                        />
                      </div>
                      <div className="form-group col-md-6">
                        <AccommodationNature
                          label="Type d'hébergement"
                          name="accommodationNature"
                          isRequired
                          isMeta
                          unique
                        />
                      </div>
                    </div>

                    {/* Ligne 4 */}
                    <div className="form-group">
                      <CustomTextArea
                        label="Commentaire Général"
                        placeholder="Commentaire Général"
                        name="generalCommentFamily"
                      />
                    </div>
                  </div>

                  <div className="d-flex justify-content-end px-1 mt-4 gap-10">
                    <button className="btn-style outlined" onClick={onClose}>
                      Annuler
                    </button>
                    <button
                      className="btn-style primary"
                      type="submit"
                      disabled={isSubmitting}
                    >
                      {isSubmitting ? 'Sauvegarde...' : 'Sauvegarder'}
                    </button>
                  </div>
                </Form>
              </div>
            </div>
          );
        }}
      </Formik>
    </Modal2>
  );
};

export default EditFamilyModal;
