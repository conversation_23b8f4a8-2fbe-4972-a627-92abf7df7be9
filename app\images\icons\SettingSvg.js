import React from 'react';
const SettingSvg = () => (
  <svg
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M10.417 1.66699C10.9915 1.66699 11.5117 1.89953 11.8886 2.27562C11.9032 2.29019 11.9176 2.30498 11.9317 2.31998C12.2649 2.67264 12.4762 3.1416 12.4984 3.65955L12.4991 3.67704C12.4999 3.70136 12.5003 3.72579 12.5003 3.75033V3.76221C12.5003 3.79609 12.5209 3.82654 12.5522 3.83951C12.5835 3.8525 12.6196 3.84552 12.6436 3.82155L12.652 3.81314C12.6693 3.79579 12.6869 3.77882 12.7047 3.76221L12.7175 3.75033C13.0995 3.39978 13.5805 3.2176 14.0655 3.2038C14.0861 3.20321 14.1067 3.20293 14.1273 3.20295C14.6598 3.20351 15.192 3.40691 15.5983 3.81314L16.1875 4.4024C16.5937 4.80863 16.7971 5.34089 16.7977 5.87332C16.7977 5.89394 16.7974 5.91456 16.7969 5.93518C16.7831 6.42012 16.6009 6.90116 16.2503 7.28311L16.2384 7.29596C16.2218 7.31375 16.2049 7.33133 16.1875 7.34868L16.1791 7.35708C16.1551 7.38105 16.1482 7.41713 16.1611 7.44845C16.1741 7.47974 16.2046 7.50033 16.2384 7.50033H16.2503C16.2749 7.50033 16.2993 7.50075 16.3236 7.50159L16.3411 7.50227C16.8591 7.52447 17.328 7.7358 17.6807 8.06893C17.6957 8.0831 17.7105 8.09749 17.725 8.11209C18.1011 8.48897 18.3337 9.00916 18.3337 9.58366V10.417C18.3337 10.9915 18.1011 11.5117 17.725 11.8886C17.7105 11.9032 17.6957 11.9176 17.6807 11.9317C17.328 12.2649 16.8591 12.4762 16.3411 12.4984L16.3236 12.4991C16.2993 12.4999 16.2749 12.5003 16.2503 12.5003H16.2384C16.2046 12.5003 16.1741 12.5209 16.1611 12.5522C16.1482 12.5835 16.1551 12.6196 16.1791 12.6436L16.1875 12.652C16.2049 12.6693 16.2218 12.6869 16.2384 12.7047L16.2503 12.7175C16.6009 13.0995 16.7831 13.5805 16.7969 14.0655C16.7974 14.0861 16.7977 14.1067 16.7977 14.1273C16.7971 14.6598 16.5937 15.192 16.1875 15.5983L15.5983 16.1875C15.192 16.5937 14.6598 16.7971 14.1273 16.7977C14.1067 16.7977 14.0861 16.7974 14.0655 16.7969C13.5805 16.7831 13.0995 16.6009 12.7175 16.2503L12.7047 16.2384C12.6869 16.2218 12.6693 16.2049 12.652 16.1875L12.6436 16.1791C12.6196 16.1551 12.5835 16.1482 12.5522 16.1611C12.5209 16.1741 12.5003 16.2046 12.5003 16.2384V16.2503C12.5003 16.2749 12.4999 16.2993 12.4991 16.3236L12.4984 16.3411C12.4762 16.8591 12.2649 17.328 11.9317 17.6807C11.9176 17.6957 11.9032 17.7105 11.8886 17.725C11.5117 18.1011 10.9915 18.3337 10.417 18.3337H9.58366C9.00916 18.3337 8.48897 18.1011 8.11209 17.725C8.09749 17.7105 8.0831 17.6957 8.06893 17.6807C7.7358 17.328 7.52447 16.8591 7.50227 16.3411L7.50159 16.3236C7.50075 16.2993 7.50033 16.2749 7.50033 16.2503V16.2384C7.50033 16.2046 7.47974 16.1741 7.44845 16.1611C7.41713 16.1482 7.38105 16.1551 7.35708 16.1791L7.34867 16.1875C7.33133 16.2049 7.31375 16.2218 7.29595 16.2384L7.28311 16.2503C6.90116 16.6009 6.42012 16.7831 5.93518 16.7969C5.91456 16.7974 5.89394 16.7977 5.87332 16.7977C5.34089 16.7971 4.80863 16.5937 4.4024 16.1875L3.81314 15.5983C3.40691 15.192 3.20351 14.6598 3.20295 14.1273C3.20293 14.1067 3.20321 14.0861 3.2038 14.0655C3.2176 13.5805 3.39978 13.0995 3.75033 12.7175L3.76221 12.7047C3.77882 12.6869 3.7958 12.6693 3.81314 12.652L3.82155 12.6436C3.84552 12.6196 3.8525 12.5835 3.83951 12.5522C3.82654 12.5209 3.79609 12.5003 3.76221 12.5003H3.75033C3.7258 12.5003 3.70136 12.4999 3.67704 12.4991L3.65955 12.4984C3.1416 12.4762 2.67264 12.2649 2.31998 11.9317C2.30498 11.9176 2.29019 11.9032 2.27562 11.8886C1.89953 11.5117 1.66699 10.9915 1.66699 10.417V9.58366C1.66699 9.00916 1.89953 8.48897 2.27562 8.11209C2.29019 8.09749 2.30498 8.0831 2.31998 8.06893C2.67264 7.7358 3.1416 7.52447 3.65955 7.50227L3.67704 7.50159C3.70136 7.50075 3.72579 7.50033 3.75033 7.50033H3.76221C3.79609 7.50033 3.82654 7.47974 3.83951 7.44845C3.8525 7.41713 3.84552 7.38105 3.82155 7.35708L3.81314 7.34868C3.7958 7.33133 3.77882 7.31375 3.76221 7.29596L3.75033 7.28311C3.39978 6.90116 3.2176 6.42012 3.2038 5.93518C3.20321 5.91456 3.20293 5.89394 3.20295 5.87332C3.20351 5.34089 3.40691 4.80863 3.81314 4.4024L4.4024 3.81314C4.80863 3.40691 5.34089 3.20351 5.87332 3.20295C5.89394 3.20293 5.91456 3.20321 5.93518 3.2038C6.42012 3.2176 6.90116 3.39978 7.28311 3.75033L7.29596 3.76221C7.31375 3.77882 7.33133 3.79579 7.34867 3.81314L7.35708 3.82155C7.38105 3.84552 7.41713 3.8525 7.44845 3.83951C7.47974 3.82654 7.50033 3.79609 7.50033 3.76221V3.75033C7.50033 3.72579 7.50075 3.70136 7.50159 3.67704L7.50227 3.65955C7.52447 3.1416 7.7358 2.67264 8.06893 2.31998C8.0831 2.30498 8.09749 2.29019 8.11209 2.27562C8.48897 1.89953 9.00916 1.66699 9.58366 1.66699H10.417ZM9.16699 16.2503C9.16699 16.4804 9.35354 16.667 9.58366 16.667H10.417C10.6471 16.667 10.8337 16.4804 10.8337 16.2503V16.2384C10.8337 15.5221 11.268 14.8894 11.9138 14.6216C12.5609 14.3532 13.3159 14.4944 13.8221 15.0006L13.8305 15.009C13.9932 15.1717 14.257 15.1717 14.4197 15.009L15.009 14.4197C15.1717 14.257 15.1717 13.9932 15.009 13.8305L15.0006 13.8221C14.4944 13.3159 14.3532 12.5609 14.6216 11.9138C14.8894 11.268 15.5221 10.8337 16.2384 10.8337H16.2503C16.4804 10.8337 16.667 10.6471 16.667 10.417V9.58366C16.667 9.35354 16.4804 9.16699 16.2503 9.16699H16.2384C15.5221 9.16699 14.8894 8.73268 14.6216 8.08687C14.3532 7.43972 14.4944 6.68476 15.0006 6.17856L15.009 6.17016C15.1717 6.00745 15.1717 5.74363 15.009 5.58091L14.4197 4.99165C14.257 4.82893 13.9932 4.82893 13.8305 4.99165L13.8221 5.00006C13.3159 5.50625 12.5609 5.64742 11.9138 5.37906C11.268 5.11125 10.8337 4.4785 10.8337 3.76221V3.75033C10.8337 3.52021 10.6471 3.33366 10.417 3.33366H9.58366C9.35354 3.33366 9.16699 3.52021 9.16699 3.75033V3.76221C9.16699 4.4785 8.73268 5.11125 8.08686 5.37906C7.4397 5.64742 6.68476 5.50625 6.17857 5.00006L6.17016 4.99165C6.00744 4.82893 5.74363 4.82893 5.58091 4.99165L4.99165 5.58091C4.82893 5.74363 4.82893 6.00745 4.99165 6.17016L5.00005 6.17856C5.50625 6.68476 5.64742 7.43972 5.37906 8.08687C5.11125 8.73268 4.47851 9.16699 3.76221 9.16699H3.75033C3.52021 9.16699 3.33366 9.35354 3.33366 9.58366V10.417C3.33366 10.6471 3.52021 10.8337 3.75033 10.8337H3.76221C4.47851 10.8337 5.11125 11.268 5.37905 11.9138C5.64742 12.5609 5.50625 13.3159 5.00006 13.8221L4.99165 13.8305C4.82893 13.9932 4.82893 14.257 4.99165 14.4197L5.58091 15.009C5.74363 15.1717 6.00745 15.1717 6.17016 15.009L6.17857 15.0006C6.68476 14.4944 7.43971 14.3532 8.08686 14.6216C8.73266 14.8894 9.16699 15.5221 9.16699 16.2384V16.2503Z"
      fill="#262E3D"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M10.0003 11.667C10.9208 11.667 11.667 10.9208 11.667 10.0003C11.667 9.07985 10.9208 8.33366 10.0003 8.33366C9.07985 8.33366 8.33366 9.07985 8.33366 10.0003C8.33366 10.9208 9.07985 11.667 10.0003 11.667ZM10.0003 13.3337C11.8413 13.3337 13.3337 11.8413 13.3337 10.0003C13.3337 8.15938 11.8413 6.66699 10.0003 6.66699C8.15938 6.66699 6.66699 8.15938 6.66699 10.0003C6.66699 11.8413 8.15938 13.3337 10.0003 13.3337Z"
      fill="#262E3D"
    />
  </svg>
);
export default SettingSvg;
