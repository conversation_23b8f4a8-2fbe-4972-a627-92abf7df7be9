import { createSelector } from 'reselect';
import { initialState } from './reducer';

/**
 * Direct selector to the assistantFiche state domain
 */
const selectAssistantFicheDomain = state =>
  state.assistantFiche || initialState;

/**
 * Other specific selectors
 */

/**
 * Default selector used by AssistantFiche
 */

const makeSelectConnectedAssistant = () =>
  createSelector(
    selectAssistantFicheDomain,
    assistantFicheState => assistantFicheState.connectedAssistant,
  );

const makeSelectSousZones = () =>
  createSelector(
    selectAssistantFicheDomain,
    assistantFicheState => assistantFicheState.sousZones,
  );

const makeSelectLoading = () =>
  createSelector(
    selectAssistantFicheDomain,
    assistantFicheState => assistantFicheState.loading,
  );

const makeSelectZonesWithSousZones = () =>
  createSelector(
    selectAssistantFicheDomain,
    assistantFicheState => assistantFicheState.zonesWithSousZones,
  );

const makeSelectZonesLoading = () =>
  createSelector(
    selectAssistantFicheDomain,
    assistantFicheState => assistantFicheState.zonesLoading,
  );

const makeSelectZonesError = () =>
  createSelector(
    selectAssistantFicheDomain,
    assistantFicheState => assistantFicheState.zonesError,
  );

const makeSelectRapportByAssistant = createSelector(
  selectAssistantFicheDomain,
  assistantFicheState => assistantFicheState.rapportByAssistant,
);

export {
  selectAssistantFicheDomain,
  makeSelectConnectedAssistant,
  makeSelectSousZones,
  makeSelectLoading,
  makeSelectZonesWithSousZones,
  makeSelectZonesLoading,
  makeSelectZonesError,
  makeSelectRapportByAssistant,
};
