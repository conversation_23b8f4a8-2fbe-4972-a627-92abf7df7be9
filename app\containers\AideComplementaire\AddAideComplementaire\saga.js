import { call, put, takeLatest } from 'redux-saga/effects';
import request from 'utils/request';
import {
  aideComplementaireAdded,
  aideComplementaireAddingError,
  aideComplementaireLoaded,
  aideComplementaireLoadingError,
  beneficiariesWithTypePriseEnChargeLoaded,
  beneficiariesWithTypePriseEnChargeLoadingError,
} from './actions';
import {
  ADD_AIDECOMPLEMENTAIRE,
  LOAD_AIDECOMPLEMENTAIRE_TO_ADD,
} from './constants';

export function* addAideComplementaire({ search }) {
  console.log({ search });
  const url = `/aide-complementaire/create`;
  try {
    const { data } = yield call(request.post, url, search);
    yield put(aideComplementaireAdded(data));
  } catch (error) {
    yield put(aideComplementaireAddingError(error));
  }
}

export function* getBeneficiariesWithTypePriseEnCharge(idTypePriseEnCharge) {
  const url = `/aide-complementaire/beneficiaries_type-kafalat?typePriseEnChargeId=${idTypePriseEnCharge}`;
  try {
    const { data } = yield call(request.get, url);
    yield put(beneficiariesWithTypePriseEnChargeLoaded(data));
  } catch (error) {
    yield put(beneficiariesWithTypePriseEnChargeLoadingError(error));
  }
}

/*export function* deleteDonation({ donation }) {
  const url = `/donations/delete/${donation.id}`;
  try {
    yield call(request.delete, url);
    yield put(donationDeleted(donation));
    yield put(removeDonationGlobal(donation));
  } catch (error) {
    yield put(donationDeletingError(error));
  }
}

 */

export function* loadAideComplementaire({ search }) {
  const url = `/aide-complementaire/${search}`;

  console.log({ url });

  try {
    const { data } = yield call(request.get, url);
    yield put(aideComplementaireLoaded(data));
  } catch (error) {
    yield put(aideComplementaireLoadingError(error));
  }
}

export default function* aideComplementaireSaga() {
  yield takeLatest(ADD_AIDECOMPLEMENTAIRE, addAideComplementaire);
  yield takeLatest(LOAD_AIDECOMPLEMENTAIRE_TO_ADD, loadAideComplementaire);
}
