import {
  FETCH_ASSISTANTS_REQUEST,
  FETCH_ASSISTANTS_SUCCESS,
  FETCH_ASSISTANTS_FAILURE,
  DELETE_ASSISTANT_REQUEST,
  DELETE_ASSISTANT_SUCCESS,
  DELETE_ASSISTANT_FAILURE,
  RESET_DELETE_ASSISTANT,
  CHANGE_ZONE_REQUEST,
  CHANGE_ZONE_SUCCESS,
  CHANGE_ZONE_FAILURE,
  CHANGE_ZONE_RESET,
  GET_ALL_ASSISTANTS_REQUEST,
  GET_ALL_ASSISTANTS_SUCCESS,
  GET_ALL_ASSISTANTS_FAILURE,
} from './constants';

export function fetchAssistantsRequest(page) {
  return {
    type: FETCH_ASSISTANTS_REQUEST,
    page,
  };
}

export function fetchAssistantsSuccess(assistants) {
  return {
    type: FETCH_ASSISTANTS_SUCCESS,
    assistants,
  };
}

export function fetchAssistantsFailure(error) {
  return {
    type: <PERSON>ETCH_ASSISTANTS_FAILURE,
    error,
  };
}

export function deleteAssistantRequest(assistantId) {
  return {
    type: DELETE_ASSISTANT_REQUEST,
    assistantId,
  };
}

export function deleteAssistantSuccess(assistantId) {
  return {
    type: DELETE_ASSISTANT_SUCCESS,
    assistantId,
  };
}

export function deleteAssistantFailure(error) {
  return {
    type: DELETE_ASSISTANT_FAILURE,
    error,
  };
}

export function resetDeleteAssistant() {
  return {
    type: RESET_DELETE_ASSISTANT,
  };
}

export function changeZoneRequest({
  assistantId,
  endDate,
  newZoneId,
  newZoneDate,
}) {
  return {
    type: CHANGE_ZONE_REQUEST,
    assistantId,
    endDate,
    newZoneId,
    newZoneDate,
  };
}

export function changeZoneSuccess(assistantId) {
  return {
    type: CHANGE_ZONE_SUCCESS,
    assistantId,
  };
}

export function changeZoneFailure(error) {
  return {
    type: CHANGE_ZONE_FAILURE,
    error,
  };
}

export function changeZoneReset() {
  return {
    type: CHANGE_ZONE_RESET,
  };
}

export function getAllAssistantsRequest() {
  return {
    type: GET_ALL_ASSISTANTS_REQUEST,
  };
}

export function getAllAssistantsSuccess(assistants) {
  return {
    type: GET_ALL_ASSISTANTS_SUCCESS,
    assistants,
  };
}

export function getAllAssistantsFailure(error) {
  return {
    type: GET_ALL_ASSISTANTS_FAILURE,
    error,
  };
}
