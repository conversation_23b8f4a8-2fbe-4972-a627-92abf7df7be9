import styled from 'styled-components';

export const TagsContainer = styled.div`
  display: flex;
  flex-wrap: nowrap;
  gap: 10px;
  margin: 20px 0;
  padding: 15px;
  overflow-x: auto;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  background-color: #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  
  &::-webkit-scrollbar {
    height: 6px;
  }
  
  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }
  
  &::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 3px;
  }
  
  &::-webkit-scrollbar-thumb:hover {
    background: #555;
  }
`;

export const Tag = styled.div`
  background-color: ${props => `#${props.color}`};
  color: ${props => {
    const hex = props.color || 'ffffff';
    const r = parseInt(hex.substr(0, 2), 16);
    const g = parseInt(hex.substr(2, 2), 16);
    const b = parseInt(hex.substr(4, 2), 16);
    const brightness = (r * 299 + g * 587 + b * 114) / 1000;
    return brightness > 128 ? '#000000' : '#ffffff';
  }};
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
  flex-shrink: 0;
  border: 1px solid ${props => props.selected ? '#000000' : '#e0e0e0'};
  display: flex;
  align-items: center;
  gap: 8px;
  
  &:hover {
    transform: scale(1.05);
    border-color: #bdbdbd;
  }
`;

export const CloseIcon = styled.span`
  font-size: 16px;
  font-weight: bold;
  margin-left: 4px;
`; 