import React, { useEffect, useRef, useState } from 'react';
import moment from 'moment';
import { Link, useLocation } from 'react-router-dom';
import {
  Box,
  Button,
  MenuItem,
  Modal,
  Select,
  Typography,
} from '@mui/material';

import emptyTable from 'containers/Common/Scripts/GenerateEmptyTable';
import {
  PRINT_ICON,
  WHITE_UPLOAD_PICTURE,
} from 'containers/Common/RequiredElement/Icons';
import styles from 'Css/personalInfo.css';
import stylesList from 'Css/profileList.css';
import ReactToPrint from 'react-to-print';
import PrintableBeneficiaryContent from './PrintableBeneficiaryContent';
import PrintableBeneficiaryContentAr from './PrintableBeneficiaryContentAr';
import PrintableBeneficiaryContentEn from './PrintableBeneficiaryContentEn';
import { HasAccess, useHasRoleToHide } from '../../../../utils/hasAccess';
import {
  isAssistant,
  isCandidateWithoutRejected,
  isOnlyBeneficiary,
} from '../../../../containers/Beneficiary/BeneficiaryProfile/statutUtils';

const formatDate = date => moment(date).format('DD/MM/YYYY');

export default function PersonalInfo(props) {
  const [showPrintOptions, setShowPrintOptions] = useState(false); // State to manage print options visibility
  const [language, setLanguage] = useState('fr'); // State for language selection
  const [open, setOpen] = useState(false); // State for modal visibility
  const [hide, setHide] = useState(false);
  const componentRef = useRef();
  const { data: beneficiary, family } = props;
  const location = useLocation();

  const hasRoleAssistantToHide = useHasRoleToHide('ASSISTANT');
  const shouldHide = !!(
    beneficiary &&
    beneficiary.beneficiaryStatut &&
    hasRoleAssistantToHide &&
    !isAssistant(beneficiary.beneficiaryStatut.nameStatut) &&
    isCandidateWithoutRejected(beneficiary.beneficiaryStatut.nameStatut)
  );

  useEffect(() => {
    if (location && location.state && location.state.isOldBeneficiary) {
      setHide(location.state.isOldBeneficiary);
    } else {
      setHide(false);
    }
  }, [location]);

  const handleOpen = () => setOpen(true);
  const handleClose = () => setOpen(false);

  const data1 = beneficiary && (
    <div className={`${styles.data1} d-flex flex-column gap-5`}>
      <p style={{ whiteSpace: 'nowrap' }}>
        {beneficiary.person.lastName ? beneficiary.person.lastName : ' -'}{' '}
      </p>
      <p style={{ whiteSpace: 'nowrap' }}>
        {beneficiary.person.firstName ? beneficiary.person.firstName : ' -'}{' '}
      </p>
      <p style={{ whiteSpace: 'nowrap' }}>
        {beneficiary.person.birthDate
          ? formatDate(beneficiary.person.birthDate)
          : ' -'}
      </p>
      <p style={{ whiteSpace: 'nowrap' }}>
        {beneficiary.person.sex ? beneficiary.person.sex : ' -'}
      </p>

      {beneficiary.person.profession && beneficiary.person.profession.name && (
        <p style={{ whiteSpace: 'nowrap' }}>
          {beneficiary.person.profession.name
            ? beneficiary.person.profession.name
            : ' -'}
        </p>
      )}
      <p style={{ whiteSpace: 'nowrap' }}>
        {beneficiary.createdAt ? formatDate(beneficiary.createdAt) : ' -'}
      </p>
      <p style={{ whiteSpace: 'nowrap' }}>
        {beneficiary.person.categoryBeneficiary &&
        beneficiary.person.categoryBeneficiary.name
          ? beneficiary.person.categoryBeneficiary.name
          : ' -'}
      </p>
      <p style={{ whiteSpace: 'nowrap' }}>
        {beneficiary.person.typeKafalat && beneficiary.person.typeKafalat.name
          ? beneficiary.person.typeKafalat.name
          : ' -'}
      </p>

      <p style={{ whiteSpace: 'nowrap' }}>
        {beneficiary.zone
          ? beneficiary.zone.code.concat(' - ', beneficiary.zone.name)
          : ' -'}
      </p>
      <p style={{ whiteSpace: 'nowrap' }}>
        {beneficiary.sousZone && beneficiary.sousZone.name
          ? beneficiary.sousZone.name
          : ' '}
      </p>

      <p style={{ whiteSpace: 'nowrap' }}>
        {beneficiary.person.sourceBeneficiary &&
        beneficiary.person.sourceBeneficiary.name
          ? beneficiary.person.sourceBeneficiary.name
          : ' '}
      </p>

      <p style={{ whiteSpace: 'nowrap' }}>
        {beneficiary.person.sourceBeneficiaryComment
          ? beneficiary.person.sourceBeneficiaryComment
          : ' '}
      </p>
      {beneficiary.person.typePriseEnCharges &&
        beneficiary.person.typePriseEnCharges.length > 0 && (
          <p
            style={{
              flex: '1 1 50%',
              whiteSpace: 'normal',
              display: 'flex',
              flexWrap: 'wrap',
              gap: '5px', // Optional spacing between items
            }}
          >
            {beneficiary.person.typePriseEnCharges &&
            beneficiary.person.typePriseEnCharges.length > 0
              ? beneficiary.person.typePriseEnCharges.map(
                  (typePriseEnCharge, index) => (
                    <span key={index}>
                      {typePriseEnCharge.name}
                      {index !==
                      beneficiary.person.typePriseEnCharges.length - 1
                        ? ','
                        : ''}
                    </span>
                  ),
                )
              : ' -'}
          </p>
        )}
    </div>
  );

  const data2 = beneficiary && (
    <div className={`${styles.data2} d-flex flex-column gap-5`}>
      <p style={{ whiteSpace: 'nowrap' }}>
        {beneficiary.person.lastNameAr ? beneficiary.person.lastNameAr : ' -'}
      </p>
      <p style={{ whiteSpace: 'nowrap' }}>
        {beneficiary.person.firstNameAr ? beneficiary.person.firstNameAr : ' -'}
      </p>
    </div>
  );

  const shouldShowButton = !!(!hide && shouldHide === false);

  const handleBeforePrint = () => {
    // Format the current date and time in a more readable way
    const currentDateTime = moment().format('YYYY-MM-DD_HH-mm-ss'); // e.g., 2025-01-23_16-13-13
    const fullName = `${beneficiary.person.firstName} ${beneficiary.person.lastName}`; // Combine first and last name
    document.title = `${fullName} - ${beneficiary.code} - ${currentDateTime}`; // Organized format
  };

  const handleAfterPrint = () => {
    // Reset the title after printing
    document.title = 'Sofwa Système';
    handleClose(); // Close the modal after the print operation
  };

  return (
    <div>
      {beneficiary == false || null || undefined ? null : (
        <div className={stylesList.backgroudStyle}>
          <div className={styles.personalInfo}>
            <div className='d-flex justify-content-end'>
              <div className="d-flex align-items-center gap-10">
                {beneficiary && (
                  // for mutiple access
                  //   <HasAccess
                  //   combinations={[
                  //     { feature: 'GERER_CANiiDIDATS', privilege: 'WRITE' },
                  //     { feature: 'GERER_BENEFiiiICIAIRES_KAFALAT', privilege: 'WRITE' },
                  //   ]}
                  // >
                  // <HasSingleAccess feature="GERER_CANDIDATS" privilege="WRITE">
                  <>
                    <div>
                      <button
                        className="btn-style primary"
                        onClick={handleOpen}
                      >
                        <img src={PRINT_ICON} width="16px" height="16px" />
                        Imprimer
                      </button>
                      <Modal open={open} onClose={handleClose}>
                        <Box
                          sx={{ ...style, width: 400, borderRadius: '20px' }}
                        >
                          <Typography
                            variant="h6"
                            component="h2"
                            className="mb-2"
                          >
                            Sélectionner la langue
                          </Typography>
                          <Select
                            value={language}
                            onChange={e => setLanguage(e.target.value)}
                            fullWidth
                            styles={{
                              control: provided => ({
                                ...provided,
                                borderRadius: '35px',
                                borderColor: '#4F89D780',
                              }),
                            }}
                          >
                            <MenuItem value="fr">Français</MenuItem>
                            <MenuItem value="ar">Arabe</MenuItem>
                            <MenuItem value="en">Anglais</MenuItem>
                          </Select>
                          <ReactToPrint
                            trigger={() => (
                              <button
                                className="btn-style primary mt-3 ml-auto"
                                onClick={handleClose}
                              >
                                Visualiser
                              </button>
                            )}
                            content={() => componentRef.current}
                            onBeforeGetContent={handleBeforePrint}
                            onAfterPrint={handleAfterPrint}
                          />
                          {/* Dynamically render the content for the selected language */}
                          <div style={{ display: 'none' }}>
                            {language === 'fr' && (
                              <PrintableBeneficiaryContent
                                ref={componentRef}
                                data={beneficiary}
                                family={family}
                              />
                            )}
                            {language === 'ar' && (
                              <PrintableBeneficiaryContentAr
                                ref={componentRef}
                                data={beneficiary}
                                family={family}
                              />
                            )}
                            {language === 'en' && (
                              <PrintableBeneficiaryContentEn
                                ref={componentRef}
                                data={beneficiary}
                                family={family}
                              />
                            )}
                          </div>
                        </Box>
                      </Modal>
                    </div>

                    <div>
                      <HasAccess
                        combinations={[
                          { feature: 'GERER_CANDIDATS', privilege: 'WRITE' },
                          {
                            feature: 'GERER_BENEFICIAIRES_KAFALAT',
                            privilege: 'WRITE',
                          },
                        ]}
                      >
                        <Link
                          to={{
                            pathname: `/beneficiaries/edit/${beneficiary.id}`,
                            state: {
                              edit: 'personal_info',
                              redirectTo: 'consultation',
                              candidate: isCandidateWithoutRejected(
                                beneficiary.beneficiaryStatut.id,
                              ),
                              beneficiaryEnAttente: !isOnlyBeneficiary(
                                beneficiary.beneficiaryStatut.id,
                              ),
                            },
                          }}
                        >
                          {shouldShowButton && (
                            <button className="btn-style secondary">
                              <img
                                src={WHITE_UPLOAD_PICTURE}
                                width="16px"
                                height="16spx"
                              />
                              Modifier
                            </button>
                          )}
                        </Link>
                      </HasAccess>
                    </div>
                  </>
                )}
              </div>
            </div>

            <div className={styles.content}>
              <div className={`mt-4 ${styles.section1}`}>
              <h4>Informations Personnelles</h4>
                <div className={`${styles.top} gap-10`}>
                  <div className={`${styles.label1} d-flex flex-column gap-5`}>
                    <p style={{ whiteSpace: 'nowrap' }}>Nom : </p>
                    <p style={{ whiteSpace: 'nowrap' }}>Prénom : </p>
                    <p style={{ whiteSpace: 'nowrap' }}>Date Naissance : </p>
                    <p style={{ whiteSpace: 'nowrap' }}>Sexe : </p>
                    {beneficiary &&
                      beneficiary.person &&
                      beneficiary.person.profession &&
                      beneficiary.person.profession.name && (
                        <p style={{ whiteSpace: 'nowrap' }}>Profession : </p>
                      )}
                    <p style={{ whiteSpace: 'nowrap' }}>Inscrit le : </p>
                    <p style={{ whiteSpace: 'nowrap' }}>Catégorie : </p>
                    <p style={{ whiteSpace: 'nowrap' }}>Type de Kafalat : </p>

                    <p style={{ whiteSpace: 'nowrap' }}> Zone : </p>
                    {beneficiary &&
                      beneficiary.sousZone &&
                      beneficiary.sousZone.name && (
                        <p style={{ whiteSpace: 'nowrap' }}> Sous Zone : </p>
                      )}
                    {beneficiary &&
                      beneficiary.person &&
                      beneficiary.person.sourceBeneficiary &&
                      beneficiary.person.sourceBeneficiary.name && (
                        <p style={{ whiteSpace: 'nowrap' }}>
                          {' '}
                          Source de bénéficiaire :{' '}
                        </p>
                      )}
                    {beneficiary &&
                      beneficiary.person &&
                      beneficiary.person.sourceBeneficiaryComment && (
                        <p style={{ whiteSpace: 'nowrap' }}>
                          {' '}
                          Description de la source :{' '}
                        </p>
                      )}

                    {beneficiary &&
                      beneficiary.person &&
                      beneficiary.person.typePriseEnCharges &&
                      beneficiary.person.typePriseEnCharges.length > 0 && (
                        <p style={{ whiteSpace: 'nowrap' }}>
                          {' '}
                          Aide complémentaire :{' '}
                        </p>
                      )}
                  </div>
                  {data1}
                  {data2}
                  <div className={`${styles.label2} d-flex flex-column gap-5`}>
                    <p style={{ whiteSpace: 'nowrap' }}>: النسب</p>
                    <p style={{ whiteSpace: 'nowrap' }}> : الإسم</p>
                  </div>
                </div>
              </div>
              <div
                className={`${styles.section2} mt-4`}
                style={{
                  borderRadius: '20px',
                  padding: '16px',
                  backgroundColor: '#f9f9f9',
                }}
              >
                <h4
                  style={{
                    textAlign: 'left',
                    marginBottom: '16px',
                    color: '#43556880',
                  }}
                >
                  Remarques et Recommendations
                </h4>
                <div style={{ marginBottom: '10px' }}>
                  <p style={{ fontWeight: 'bold', color: '#555' }}>
                    Remarque (FR) :
                  </p>
                  <p style={{ marginLeft: '20px', color: '#444' }}>
                    {beneficiary && beneficiary.remarqueFr
                      ? beneficiary.remarqueFr
                      : ' Aucune remarque'}
                  </p>
                </div>
                <div
                  style={{
                    marginBottom: '10px',
                    direction: 'rtl',
                    textAlign: 'right',
                  }}
                >
                  <p style={{ fontWeight: 'bold', color: '#555' }}>ملاحظة :</p>
                  <p style={{ marginLeft: '20px', color: '#444' }}>
                    {beneficiary && beneficiary.remarqueAr
                      ? beneficiary.remarqueAr
                      : ' لا توجد ملاحظات'}
                  </p>
                </div>
                <div>
                  <p style={{ fontWeight: 'bold', color: '#555' }}>
                    Remarque (EN) :
                  </p>
                  <p style={{ marginLeft: '20px', color: '#444' }}>
                    {beneficiary && beneficiary.remarqueEn
                      ? beneficiary.remarqueEn
                      : ' No remarks'}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

const style = {
  position: 'absolute',
  top: '50%',
  left: '50%',
  transform: 'translate(-50%, -50%)',
  width: 400,
  bgcolor: 'background.paper',
  border: '2px solid #000',
  boxShadow: 24,
  p: 4,
};
