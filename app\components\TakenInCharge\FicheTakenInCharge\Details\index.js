import React, { useEffect, useState } from 'react';
import moment from 'moment';
import list from 'Css/profileList.css';
import profileStyle from 'Css/profileList.css';
import { Alert } from 'react-bootstrap';
import { Link, useHistory, useLocation } from 'react-router-dom';
import Modal2 from 'components/Common/Modal2';
import AccessControl from 'utils/AccessControl';
import {
  closeTakenInCharge,
  closeTakenInChargeReset,
  loadClotureMotifTypes,
} from 'containers/TakenInCharge/TakenInChargeProfile/actions';
import { useDispatch } from 'react-redux';
import styles from '../../../../Css/personalInfo.css';
import btnStyles from '../../../../Css/button.css';
const formatDate = date => moment(date).format('DD/MM/YYYY');
export default function Details(props) {
  const [showAlert, setShowAlert] = useState(false);
  const [showSuccessMessage, setShowSuccessMessage] = useState('');
  const [closeTakenInChargeError, setCloseTakenInChargeError] = useState('');
  const [closeTakenInChargeSuccess, setCloseTakenInChargeSuccess] = useState(
    '',
  );
  const [showCloseModal, setShowCloseModal] = useState(false);
  const [comment, setComment] = useState('');
  const [motifType, setMotifType] = useState('');
  const [motifTypeError, setMotifTypeError] = useState('');
  const [motifError, setMotifError] = useState('');
  const location = useLocation();
  const history = useHistory();
  const dispatch = useDispatch();
  const takenInCharge = props.data;
  const { operations } = props;
  const { clotureMotifTypesList } = props;
  const emptyValue = <span style={{ textAlign: 'center' }}>-</span>;
  let nbrOperations = 0;
  let nbrPlannedOperations = 0;
  let nbrReservedOperations = 0;
  let nbrExecutedOperations = 0;
  if (operations && operations.length > 0) {
    nbrOperations = operations.length;
    operations.map(operation => {
      if (operation.planningDate && !operation.executionDate && !operation.reserved) {
        nbrPlannedOperations += 1;
        console.log(nbrPlannedOperations);
      } else if (operation.executionDate && !operation.closureDate) {
        nbrExecutedOperations += 1;
      } else if (operation.reserved && !operation.executionDate) {
        nbrReservedOperations += 1;
      }
    });
  }

  const handleCloseModal = () => {
    setShowCloseModal(false);
    setComment('');
    setMotifType('');
    setMotifTypeError('');
    setMotifError('');
  };

  const handleConfirmClose = () => {
    let isValid = true;

    // Validate motifType
    if (!motifType) {
      setMotifTypeError('Le type de motif est obligatoire.');
      isValid = false;
    } else {
      setMotifTypeError(''); // Reset motifType error if valid
    }

    // Validate comment
    if (!comment) {
      setMotifError('Le motif de fermeture est obligatoire.');
      isValid = false;
    } else {
      setMotifError(''); // Reset motif error if valid
    }

    // If both validations pass, proceed with the action
    if (isValid) {
      // Dispatch action to close the taken in charge
      dispatch(closeTakenInCharge(takenInCharge.id, comment, motifType));

      // Close modal and reset form
      setShowCloseModal(false);
      setComment('');
      setMotifType('');
    }
  };

  useEffect(() => {
    if (motifType) setMotifTypeError('');
  }, [motifType]);

  useEffect(() => {
    if (comment) setMotifError('');
  }, [comment]);

  useEffect(() => {
    if (clotureMotifTypesList === null) {
      dispatch(loadClotureMotifTypes());
    }
  }, []);

  useEffect(() => {
    if (props.closeTakenInChargeSuccess) {
      setCloseTakenInChargeSuccess(props.closeTakenInChargeSuccess);
      dispatch(closeTakenInChargeReset());
    }
  }, [props.closeTakenInChargeSuccess]);

  useEffect(() => {
    if (props.closeTakenInChargeError) {
      setCloseTakenInChargeError(props.closeTakenInChargeError);
      dispatch(closeTakenInChargeReset());
    }
  }, [props.closeTakenInChargeError]);

  useEffect(() => {
    if (location.state === 'updateSuccess') {
      setShowSuccessMessage(true);
    }
  }, [location.state]);

  const handleCloseSuccessMessage = () => {
    setShowSuccessMessage(false);
    history.replace({ ...location, state: null });
  };

  const handleClosginTakenInChargeMessage = () => {
    setCloseTakenInChargeError('');
    setCloseTakenInChargeSuccess('');
    dispatch(closeTakenInChargeReset());
  };

  useEffect(() => {
    if (showAlert || closeTakenInChargeSuccess) {
      setTimeout(() => {
        setShowAlert(false);
        setCloseTakenInChargeSuccess('');
      }, 4000);
    }
  }, [showAlert]);


  return (
    <div>
    {closeTakenInChargeError && (
      <Alert
        className="alert-style w-100 mt-0"
        variant="danger"
        onClose={() => handleClosginTakenInChargeMessage()}
        dismissible
      >
        <p>{closeTakenInChargeError}</p>
      </Alert>
    )}

    {closeTakenInChargeSuccess && (
      <Alert
        className="alert-style w-100 mt-0"
        variant="success"
        onClose={() => handleClosginTakenInChargeMessage()}
        dismissible
      >
        <p>{closeTakenInChargeSuccess}</p>
      </Alert>
    )}

    {showSuccessMessage && (
      <Alert
        className="alert-style w-100 mt-0"
        variant="success"
        onClose={handleCloseSuccessMessage}
        dismissible
      >
        <p>Kafalat modifiée avec succès !</p>
      </Alert>
    )}
    <div className={profileStyle.backgroudStyle}>


      {takenInCharge && (
        <div className={list.global}>
          <div className={list.header}>
            <h4>Détails sur Kafalat</h4>
            {takenInCharge &&
              takenInCharge.status &&
              takenInCharge.status !== 'Fermé' && (
                <div style={{ display: 'flex', gap: '10px' }}>
                  <AccessControl module="TAKEINCHARGE" functionality="UPDATE">
                    {/* "Fermer Kafalat" Button */}
                    <button
                      onClick={() => setShowCloseModal(true)}
                      className="btn-style danger mb-2 shadow-sm"
                    >
                      Fermer Kafalat
                    </button>

                    {/* Conditional "Modifier" Button */}
                    {takenInCharge.status !== 'Actif' && (
                      <Link
                        to={{
                          pathname: `/takenInCharges/edit/${takenInCharge.id}`,
                          state: { redirectTo: 'consultation' },
                        }}
                      >
                        <button className={btnStyles.editBtnProfile}>
                          Modifier
                        </button>
                      </Link>
                    )}
                  </AccessControl>
                </div>
              )}
          </div>
          <div
            className={`d-flex justify-content-center mt-4 ${styles.donationContainer}`}
          >
            {/* Vertical Layout - Each piece of information stacked */}
            <div className={`d-flex py-3 px-5 ${styles.donationInfo}`}>
              {/* Left column for labels */}
              <div className={styles.labelColumn}>
                <p>
                  <strong>Date de début : </strong>
                </p>

                <p>
                  <strong>Commentaire : </strong>
                </p>
                <p>
                  <strong>Nombre d'opérations : </strong>
                </p>
                <p>
                  <strong>Nombre d'opérations planifiées : </strong>
                </p>
                <p>
                  <strong>Nombre d'opérations réservées : </strong>
                </p>
                <p>
                  <strong>Nombre d'opérations exécutées : </strong>
                </p>
                {/* New section for Cloture */}
                {takenInCharge.status === 'Fermé' && (
                  <div
                    style={{
                      marginTop: '30px',
                      paddingTop: '20px',
                      borderTop: '2px solid #ccc',
                    }}
                  >
                    {/* Title for the section */}
                    <h5
                      style={{
                        marginBottom: '15px',
                        color: '#333',
                        fontWeight: 'bold',
                      }}
                    >
                      Détails de fermeture
                    </h5>
                    <p>
                      <strong>Date de fin : </strong>{' '}
                      {takenInCharge.endDate
                        ? formatDate(takenInCharge.endDate)
                        : emptyValue}
                    </p>
                    <p>
                      <strong>Type de motif : </strong>{' '}
                      {takenInCharge.clotureMotifType
                        ? takenInCharge.clotureMotifType.name
                        : emptyValue}
                    </p>

                    <p>
                      <strong>Motif de fermeture : </strong>{' '}
                      {takenInCharge.clotureMotif || emptyValue}
                    </p>
                  </div>
                )}
              </div>

              {/* Right column for values */}
              <div style={{ marginLeft: '20px' }}>
                <p>
                  {takenInCharge.startDate
                    ? formatDate(takenInCharge.startDate)
                    : emptyValue}
                </p>
                <p>{takenInCharge.type || emptyValue}</p>
                <p>{nbrOperations}</p>
                <p>{nbrPlannedOperations}</p>
                <p>{nbrReservedOperations}</p>
                <p>{nbrExecutedOperations}</p>
              </div>
            </div>
          </div>
        </div>
      )}
      <Modal2
        centered
        className="mt-5"
        show={showCloseModal}
        title="Clôturer Kafalat"
        handleClose={handleCloseModal}
      >
        {/* Select Input for Type de Motif */}
        <div className="mb-3 px-3">
          <label style={{ marginRight: '10px', marginTop: '7px' }}>
            Type de Motif <span className="text-danger">*</span>
          </label>
          <select
            id="motifType"
            className="form-control"
            value={motifType}
            onChange={e => setMotifType(e.target.value)}
          >
            <option value="">Sélectionnez un motif</option>
            {clotureMotifTypesList}
          </select>
          {motifTypeError && (
            <div className="text-danger" style={{ fontSize: '0.9em' }}>
              {motifTypeError}
            </div>
          )}
        </div>

        {/* Comment Input */}
        <div className="mb-3 px-3">
          <label htmlFor="comment" className="form-label">
            Motif de fermeture <span className="text-danger">*</span>
          </label>
          <textarea
            id="comment"
            className="form-control"
            placeholder="Entrez le motif de fermeture..."
            value={comment}
            onChange={e => setComment(e.target.value)}
          />
          {motifError && (
            <div className="text-danger" style={{ fontSize: '0.9em' }}>
              {motifError}
            </div>
          )}
        </div>

        {/* Action Buttons */}
        <div className="d-flex justify-content-end px-3 my-1">
          <button
            type="button"
            className={`mx-2 btn-style outlined`}
            onClick={handleCloseModal}
          >
            Annuler
          </button>
          <button
            type="submit"
            className={`mx-2 btn-style primary`}
            onClick={() => {
              handleConfirmClose(comment, motifType);
            }}
          >
            Confirmer
          </button>
        </div>
      </Modal2>
    </div>
  </div>
  );
}
