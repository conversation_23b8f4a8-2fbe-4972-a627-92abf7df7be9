// actions.js
import {
  FETCH_USERS_REQUEST,
  FETCH_USERS_SUCCESS,
  FETCH_USERS_FAILURE,
  ADD_USER_REQUEST, 
  ADD_USER_SUCCESS, 
  ADD_USER_FAILURE,
  RESET_ADD_USER_SUCCESS,
  RESET_ADD_USER_ERROR,
  DELETE_USER_REQUEST,
  DELETE_USER_SUCCESS,
  DELETE_USER_FAILURE,
  RESET_DELETE_USER_SUCCESS, 
  CHANGE_USER_ROLE_REQUEST,
  CHANGE_USER_ROLE_SUCCESS,
  CHANGE_USER_ROLE_FAILURE,
  RESET_CHANGE_USER_ROLE_SUCCESS,
} from './constants';

export function fetchUsersRequest(page, filters) {
  return {
    type: FETCH_USERS_REQUEST,
    page, 
    filters 
  };
}

export const fetchUsersSuccess = users => ({
  type: FETCH_USERS_SUCCESS,
  payload: users,
});

export const fetchUsersFailure = error => ({
  type: FETCH_USERS_FAILURE,
  payload: error,
});

export function addUserRequest(email , roleId) {
  return {
    type: ADD_USER_REQUEST,
    payload: { email , roleId},
  };
}

export const addUserSuccess = () => ({
  type: ADD_USER_SUCCESS,
});

export const addUserFailure = error => ({
  type: ADD_USER_FAILURE,
  payload: error,
});

export const resetAddUserSuccess = () => ({
  type: RESET_ADD_USER_SUCCESS,
});

export const resetAddUserError = () => ({
  type: RESET_ADD_USER_ERROR,
});

export const deleteUserRequest = userId => ({
  type: DELETE_USER_REQUEST,
  payload: userId,
});

export const deleteUserSuccess = () => ({
  type: DELETE_USER_SUCCESS,
});

export const resetDeleteUserSuccess = () => ({
  type: RESET_DELETE_USER_SUCCESS, 
});

export const deleteUserFailure = error => ({
  type: DELETE_USER_FAILURE,
  payload: error,
});

export function changeUserRoleRequest(userId, newRole) {
  return {
    type: CHANGE_USER_ROLE_REQUEST,
    payload: { userId, newRole },
  };
}

export const changeUserRoleSuccess = () => ({
  type: CHANGE_USER_ROLE_SUCCESS,
});

export const changeUserRoleFailure = error => ({
  type: CHANGE_USER_ROLE_FAILURE,
  payload: error,
});

export const resetChangeUserRoleSuccess = () => ({
  type: RESET_CHANGE_USER_ROLE_SUCCESS,
});


