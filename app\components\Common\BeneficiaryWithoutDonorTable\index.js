import React, { useEffect, useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  IconButton,
  Switch,
  TextField,
  Tooltip,
  Typography,
} from '@mui/material';
import { Delete, Edit } from '@mui/icons-material';
import { makeStyles } from '@mui/styles';
import { useDispatch, useSelector } from 'react-redux';
import {
  getGroupeBeneficiariesRequest,
  removeBeneficiaryRequest,
  updateMontantBeneficiaryRequest,
  updateStatutValidationBeneficiaryRequest,
} from '../../../containers/AideComplementaire/FicheAideComplementaire/actions';
import { Link } from 'react-router-dom';
import { createStructuredSelector } from 'reselect';
import {
  makeSelectGroupeForAideComplementaire,
  makeSelectGroupSuccess,
} from '../../../containers/AideComplementaire/FicheAideComplementaire/selectors';
import { useInjectReducer, useInjectSaga } from 'redux-injectors';
import aideComplementaireReducer from '../../../containers/AideComplementaire/FicheAideComplementaire/reducer';
import detailAideComplementaireSaga from '../../../containers/AideComplementaire/FicheAideComplementaire/saga';
import GroupModal from '../GroupModal';
import DataTable from '../DataTable';

const key = 'aideComplementaireFiche';

const omdbSelector = createStructuredSelector({
  groupe: makeSelectGroupeForAideComplementaire,
  successGroup: makeSelectGroupSuccess,
});

const useStyles = makeStyles({
  tableHeader: {
    backgroundColor: '#f5f5f5',
    fontWeight: 'bold',
    textAlign: 'center', 
  },
  beneficiaryHeader: {
    backgroundColor: '#e0e0e0',
    fontWeight: 'bold',
    textAlign: 'center',
  },
  iconButton: {
    padding: '2px',
  },
  actionIcons: {
    display: 'flex',
    gap: '8px',
    justifyContent: 'center',
  },
  actionCell: {
    width: '100px',
    textAlign: 'center',
  },
  smallIcon: {
    fontSize: '1.5rem',
  },
  beneficiaryRow: {
    background: 'linear-gradient(to bottom, #e0e0e0, #ffffff)',
  },
  tableCell: {
    width: '150px',
    textAlign: 'center',
    verticalAlign: 'middle',
  },
  smallCell: {
    width: '50px',
    textAlign: 'center',
  },
  clickableChip: {
    cursor: 'pointer',
    textDecoration: 'none',
  },
  noBudgetLine: {
    textAlign: 'center',
    margin: '20px 0',
  },
});

export const getStatusName = statut => {
  if (
    statut === 'candidat_initial' ||
    statut === 'candidat_valider_assistance' ||
    statut === 'candidat_valider_kafalat' ||
    statut === 'candidat_a_completer_par_assistance' ||
    statut === 'candidat_a_completer_par_kafalat' ||
    statut === 'beneficiary_enattente'
  ) {
    return 'Candidat';
  }
  if (statut === 'beneficiary_actif') {
    return 'Bénéficiaire Kafalat';
  }
  if (
    statut === 'beneficiary_ad_hoc_individual' ||
    statut === 'beneficiary_ad_hoc_group'
  ) {
    return 'Bénéficiaire Ad Hoc';
  } else {
    return 'Statut Inconnu';
  }
};

function BeneficiaryWithoutDonorTable({
  beneficiaryAideComplemenatireDTOList,
  aideComplementaire,
}) {
  const classes = useStyles();
  const dispatch = useDispatch();

  useInjectReducer({ key, reducer: aideComplementaireReducer });
  useInjectSaga({ key, saga: detailAideComplementaireSaga });

  const { groupe, successGroup } = useSelector(omdbSelector);

  const [open, setOpen] = useState(false);
  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);
  const [selectedBeneficiaryId, setSelectedBeneficiaryId] = useState(null);
  const [selectedDonorId, setSelectedDonorId] = useState(null);
  const [selectedBeneficiaryType, setSelectedBeneficiaryType] = useState(null);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [editGroupDialogOpen, setEditGroupDialogOpen] = useState(false);
  const [newAmount, setNewAmount] = useState('');
  const [newGroup, setNewGroup] = useState('');
  const [groupData, setGroupData] = useState(false);
  const [editDialogGroupOpen, setEditDialogGroupOpen] = useState(false);

  const aideComplementaireId = aideComplementaire.id;

  console.log({ groupe });

  useEffect(() => {
    if (open && successGroup) {
      setGroupData(groupe);
      setEditDialogGroupOpen(true);
    }
  }, [successGroup]);

  console.log({ successGroup });
  console.log({ groupData });

  const handleSwitchChange = (event, beneficiary) => {
    dispatch(
      updateStatutValidationBeneficiaryRequest(
        aideComplementaireId,
        beneficiary.id,
        beneficiary.donorId,
        beneficiary.type,
        { source: 'BeneficiaryWithoutDonorTable' },
      ),
    );
  };

  const handleDeleteClick = (idBeneficiary, beneficiaryType, idDonor) => {
    setSelectedBeneficiaryId(idBeneficiary);
    setSelectedBeneficiaryType(beneficiaryType);
    setSelectedDonorId(idDonor);
    setConfirmDialogOpen(true);
  };

  const handleConfirmDelete = () => {
    dispatch(
      removeBeneficiaryRequest(
        aideComplementaireId,
        selectedBeneficiaryId,
        selectedDonorId,
        selectedBeneficiaryType,
        {
          source: 'BeneficiaryWithoutDonorTable',
        },
      ),
    );
    setSelectedDonorId(null);
    setConfirmDialogOpen(false);
  };

  const handleEditClick = beneficiary => {
    if (beneficiary.type === 'Groupe') {
      setNewAmount(beneficiary.montantAbeneficier);
      setNewGroup(beneficiary.numberOfMembers || 0);
      setSelectedBeneficiaryId(beneficiary.id);
      setSelectedDonorId(beneficiary.donorId);
      setSelectedBeneficiaryType('Group')
      setEditGroupDialogOpen(true);
    } else {
      setNewAmount(beneficiary.montantAbeneficier || '');
      setSelectedBeneficiaryId(beneficiary.id);
      setSelectedDonorId(beneficiary.donorId);
      setEditDialogOpen(true);
    }
  };

  const handleConfirmEdit = () => {
    if(selectedBeneficiaryType==="Group"){
      dispatch(
      updateMontantBeneficiaryRequest(
        aideComplementaireId,
        selectedBeneficiaryId,
        selectedDonorId,
        selectedBeneficiaryType,
        newAmount,
        newGroup,
        { source: 'BeneficiaryWithoutDonorTable' },
      ),
    );
    }
    else{
      dispatch(
        updateMontantBeneficiaryRequest(
          aideComplementaireId,
          selectedBeneficiaryId,
          selectedDonorId,
          selectedBeneficiaryType,
          newAmount,
          0,
          { source: 'BeneficiaryWithoutDonorTable' },
        ),
      );
    }
    
    setEditDialogOpen(false);
  };

   

  const handleCloseGroupeModal = () => {
    setEditDialogGroupOpen(false);
    setOpen(false);
  };

  // Creating columns for DataTable
  const columns = [
    {
      field: 'fullName',
      headerName: 'Nom Complet',
      flex: 1,
      cellClassName: 'centeredCell',
      renderCell: (params) => {
        const beneficiary = params.row;
        if (beneficiary.beneficiaryStatut.nameStatut === 'beneficiary_ad_hoc_group') {
          return (
            <Link to={`/beneficiaries/ad-hoc/fiche/${beneficiary.id}/info`}>
              {beneficiary.firstName}
            </Link>
          );
        } else if (beneficiary.beneficiaryStatut.nameStatut === 'beneficiary_ad_hoc_individual') {
          return (
            <Link
              to={{
                pathname: `/beneficiaries/ad-hoc/fiche/${beneficiary.id}/info`,
                state: { isPerson: true },
              }}
            >
              {`${beneficiary.firstName} ${beneficiary.lastName}`}
            </Link>
          );
        } else {
          return (
            <Link to={`/beneficiaries/fiche/${beneficiary.id}/info`}>
              {`${beneficiary.firstName} ${beneficiary.lastName}`}
            </Link>
          );
        }
      },
    },
    {
      field: 'category',
      headerName: 'Catégorie',
      flex: 1,
      cellClassName: 'centeredCell',
      valueGetter: (params) => getStatusName(params.row.statut),
    },
    {
      field: 'type',
      headerName: 'Type',
      flex: 1,
      cellClassName: 'centeredCell',
      renderCell: (params) => {
        const beneficiary = params.row;
        if (beneficiary.type === 'Indépendant') {
          return <Chip label="Indépendant" color="primary" />;
        } else if (beneficiary.type === 'Membre Famille') {
          return (
            <Link
              to={`/beneficiaries/fiche/${beneficiary.id}/family`}
              className={classes.clickableChip}
            >
              <Chip label="Membre famille" color="warning" clickable />
            </Link>
          );
        } else if (beneficiary.type === 'Groupe') {
          return (
            <Link
              to={`/beneficiaries/ad-hoc/fiche/${beneficiary.id}/info`}
              className={classes.clickableChip}
            >
              <Chip label="Groupe" color="secondary" clickable />
            </Link>
          );
        }
        return null;
      },
    },
    {
      field: 'donor',
      headerName: 'Donateur participant',
      flex: 1,
      cellClassName: 'centeredCell',
      renderCell: (params) => {
        const beneficiary = params.row;
        return beneficiary.donorId && beneficiary.nomCompletDuDonateur ? (
          <Link to={`/donors/fiche/${beneficiary.donorId}/info`}>
            {beneficiary.nomCompletDuDonateur}
          </Link>
        ) : (
          'aucun'
        );
      },
    },
    {
      field: 'montantPreciserParDonateur',
      headerName: 'Montant précisé par donateur',
      flex: 1,
      cellClassName: 'centeredCell',
      valueGetter: (params) => 
        params.row.montantPreciserParDonateur
          ? params.row.montantPreciserParDonateur
          : 'aucun',
    },
    {
      field: 'montantAbeneficier',
      headerName: 'Montant affecté',
      flex: 1,
      cellClassName: 'centeredCell',
      valueGetter: (params) => {
        const beneficiary = params.row;
        return beneficiary.type !== "Groupe" 
          ? (beneficiary.montantAbeneficier || 0)
          : ((beneficiary.montantAbeneficier || 0) * beneficiary.numberOfMembers);
      },
    },
    {
      field: 'statutValidation',
      headerName: 'Statut',
      flex: 1,
      cellClassName: 'centeredCell',
      renderCell: (params) => {
        return params.row.statutValidation ? (
          <Chip label="Validé" color="success" />
        ) : (
          <Chip label="Non validé" color="default" />
        );
      },
    },
  ];

  // Add actions column only if aide is not executed or closed
  if (aideComplementaire.statut !== 'executer' && aideComplementaire.statut !== 'cloturer') {
    columns.push({
      field: 'actions',
      headerName: 'Actions',
      flex: 1,
      cellClassName: 'centeredCell',
      renderCell: (params) => {
        const beneficiary = params.row;
        return (
          <div className={classes.actionIcons}>
            <Tooltip title={!beneficiary.statutValidation ? 'validé' : 'invalidé'}>
              <Switch
                checked={beneficiary.statutValidation}
                onChange={(event) => handleSwitchChange(event, beneficiary)}
                color="success"
              />
            </Tooltip>
            {!beneficiary.statutValidation && (
              <>
                <Tooltip title="Modifier le montant affecté" arrow>
                  <IconButton
                    className={classes.iconButton}
                    disableRipple
                    style={{ backgroundColor: 'transparent' }}
                    onClick={() => handleEditClick(beneficiary)}
                  >
                    <Edit className={classes.smallIcon} color="primary" />
                  </IconButton>
                </Tooltip>
                <Tooltip title="Retirer le bénéficiaire" arrow>
                  <IconButton
                    className={classes.iconButton}
                    disableRipple
                    style={{ backgroundColor: 'transparent' }}
                    onClick={() =>
                      handleDeleteClick(
                        beneficiary.id,
                        beneficiary.type,
                        beneficiary.donorId,
                      )
                    }
                  >
                    <Delete className={classes.smallIcon} color="error" />
                  </IconButton>
                </Tooltip>
              </>
            )}
          </div>
        );
      },
    });
  }

  // Prepare rows for DataTable
  const rows = beneficiaryAideComplemenatireDTOList.map((beneficiary, index) => ({
    id: beneficiary.id || index, // Ensure unique id
    ...beneficiary,
  }));

  return (
    <>
      <DataTable
        rows={rows}
        columns={columns}
        loading={false}
        fileName="beneficiaires_sans_donateurs"
        totalElements={beneficiaryAideComplemenatireDTOList.length}
        numberOfElements={beneficiaryAideComplemenatireDTOList.length}
        pageable={beneficiaryAideComplemenatireDTOList.length}
      />

      {/* Confirmation Modal */}
      <Dialog
        open={confirmDialogOpen}
        onClose={() => setConfirmDialogOpen(false)}
      >
        <DialogTitle>Confirmer la suppression</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Êtes-vous sûr de vouloir supprimer ce bénéficiaire ?
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setConfirmDialogOpen(false)} color="primary">
            Annuler
          </Button>
          <Button onClick={handleConfirmDelete} color="error" autoFocus>
            Supprimer
          </Button>
        </DialogActions>
      </Dialog>

      {/* Edit Modal */}
      <Dialog
        open={editDialogOpen}
        onClose={() => setEditDialogOpen(false)}
        sx={{
          '& .MuiDialog-paper': {
            padding: '20px',
            borderRadius: '12px',
            backgroundColor: '#f9f9f9',
          },
        }}
      >
        <DialogTitle
          sx={{
            fontSize: '1.5rem',
            fontWeight: 'bold',
            color: '#333',
            marginBottom: '10px',
          }}
        >
          Modifier le Montant affecté
        </DialogTitle>
        <DialogContent>
          <DialogContentText
            sx={{ fontSize: '1.1rem', color: '#555', marginBottom: '20px' }}
          >
            Veuillez entrer le nouveau montant affecté :
          </DialogContentText>
          <TextField
            type="number"
            value={newAmount}
            onChange={e => {
              const value = e.target.value;
              if (value === '' || /^[0-9]*\.?[0-9]*$/.test(value)) {
                setNewAmount(value);
              }
            }}
            placeholder="Montant"
            variant="outlined"
            inputProps={{ min: 0 }}
            sx={{
              '& .MuiInputBase-input': { fontSize: '1.2rem', padding: '12px' },
              '& .MuiOutlinedInput-root': {
                borderRadius: '8px',
                borderColor: '#ddd',
                boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
              },
              marginBottom: '20px',
              width: '100%',
            }}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setEditDialogOpen(false)} color="primary">
            Annuler
          </Button>
          <Button onClick={handleConfirmEdit} color="error" autoFocus>
            Confirmer
          </Button>

        </DialogActions>
      </Dialog>

      <Dialog
        open={editGroupDialogOpen}
        onClose={() => setEditGroupDialogOpen(false)}
        sx={{
          '& .MuiDialog-paper': {
            padding: '20px',
            borderRadius: '12px',
            backgroundColor: '#f9f9f9',
          },
        }}
      >
        <DialogTitle
          sx={{
            fontSize: '1.5rem',
            fontWeight: 'bold',
            color: '#333',
            marginBottom: '10px',
          }}
        >
          Modifier le Montant affecté
        </DialogTitle>
        <DialogContent>
          <DialogContentText
            sx={{ fontSize: '1.1rem', color: '#555', marginBottom: '20px' }}
          >
            Veuillez saisir le nouveau montant pour chaque personne:
          </DialogContentText>
          <TextField
            type="number"
            value={newAmount}
            onChange={e => {
              const value = e.target.value;
              if (value === '' || /^[0-9]*\.?[0-9]*$/.test(value)) {
                setNewAmount(value);
              }
            }}
            placeholder="Montant"
            variant="outlined"
            inputProps={{ min: 0 }}
            sx={{
              '& .MuiInputBase-input': { fontSize: '1.2rem', padding: '12px' },
              '& .MuiOutlinedInput-root': {
                borderRadius: '8px',
                borderColor: '#ddd',
                boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
              },
              marginBottom: '20px',
              width: '100%',
            }}
          />
          <DialogContentText
            sx={{ fontSize: '1.1rem', color: '#555', marginBottom: '20px' }}
          >
            Veuillez saisir le nombre de bénéficiaires dans ce groupe
          </DialogContentText>
          <TextField
            type="number"
            value={newGroup}
            onChange={e => {
              const value = e.target.value;
                setNewGroup(value);
                setNewAmount(newAmount)
            }}
            placeholder="nombre des personne"
            variant="outlined"
            inputProps={{ min: 0 }}
            sx={{
              '& .MuiInputBase-input': { fontSize: '1.2rem', padding: '12px' },
              '& .MuiOutlinedInput-root': {
                borderRadius: '8px',
                borderColor: '#ddd',
                boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
              },
              marginBottom: '20px',
              width: '100%',
            }}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setEditGroupDialogOpen(false)} color="primary">
            Annuler
          </Button>
          <Button onClick={handleConfirmEdit} color="error" autoFocus>
            Confirmer
          </Button>
        </DialogActions>
      </Dialog>

      {/*Edit Group Modal*/}
      <GroupModal
        groupData={groupData}
        isOpen={editDialogGroupOpen}
        onClose={handleCloseGroupeModal}
        aideComplementaireId={aideComplementaire.id}
      />
    </>
  );
}

export default BeneficiaryWithoutDonorTable;
