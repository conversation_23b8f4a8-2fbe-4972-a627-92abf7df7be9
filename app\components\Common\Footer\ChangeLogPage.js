import React, { useState, useEffect } from 'react';
import changelogTxt from './changelog.txt';

const getLineStyle = line => {
  if (line.includes('[')) {
    return styles.versionHeading;
  } else if (line.includes('###')) {
    return styles.sectionHeading;
  } else if (line.includes('-') && line.includes('@')) {
    const atIndex = line.indexOf('@');
    const beforeAtStyle = { ...styles.listItem };
    const afterAt = line
      .substring(atIndex + 1)
      .trim()
      .toLowerCase();
    let afterAtStyle;
    switch (afterAt) {
      case 'ahmed':
        afterAtStyle = { ...styles.assignee, color: '#4b0082' };
        break;
      case 'hanane':
        afterAtStyle = { ...styles.assignee, color: '#B22222' };
        break;
      case 'soumaya':
        afterAtStyle = { ...styles.assignee, color: '#009933' };
        break;
      case 'soufiane':
        afterAtStyle = { ...styles.assignee, color: 'orange' };
        break;
      default:
        afterAtStyle = styles.assignee;
        break;
    }
    return { beforeAtStyle, afterAtStyle, atIndex };
  } else if (line.includes('-')) {
    return styles.listItem;
  }
  return styles.listItem;
};

const renderLine = (line, style) => {
  const lineStyle = { ...style }; // Ensure the line does not break
  if (
    style &&
    style.beforeAtStyle &&
    style.afterAtStyle &&
    style.atIndex !== -1
  ) {
    return (
      <div style={lineStyle}>
        <span style={style.beforeAtStyle}>
          {line.substring(0, style.atIndex)}
        </span>
        <span style={style.afterAtStyle}>{line.substring(style.atIndex)}</span>
      </div>
    );
  }
  return <div style={lineStyle}>{line}</div>;
};

const ChangeLogPage = () => {
  const [changelog, setChangelog] = useState([]);
  const [selectedVersion, setSelectedVersion] = useState('');
  const [selectedUser, setSelectedUser] = useState('');
  const [filteredChangelog, setFilteredChangelog] = useState([]);

  useEffect(() => {
    const fetchMarkdown = async () => {
      try {
        const lines = changelogTxt.split('\n');
        setChangelog(lines);
        setFilteredChangelog(lines);
      } catch (error) {
        console.log('Error setting Markdown content:', error);
      }
    };
    fetchMarkdown();
  }, []);

  const handleVersionChange = e => {
    setSelectedUser('');
    const version = e.target.value;
    setSelectedVersion(version);

    if (version === '') {
      setFilteredChangelog(changelog);
    } else {
      const startIndex = changelog.findIndex(line =>
        line.startsWith(`[Version ${version}]`),
      );
      const endIndex = changelog.findIndex(
        (line, i) => i > startIndex && line.startsWith('[Version'),
      );

      if (startIndex !== -1) {
        const filtered = changelog.slice(
          startIndex,
          endIndex !== -1 ? endIndex : undefined,
        );
        setFilteredChangelog(filtered);
      } else {
        setFilteredChangelog([]);
      }
    }
  };

  const handleUserChange = e => {
    setSelectedVersion('');
    const user = e.target.value;
    setSelectedUser(user);
    if (user === '') {
      setFilteredChangelog(changelog);
    } else {
      const filtered = changelog.filter(line =>
        line.toLowerCase().includes(`@${user.toLowerCase()}`),
      );
      setFilteredChangelog(filtered);
    }
  };

  const renderOptions = () => {
    const versions = [];
    changelog.forEach(line => {
      if (line.startsWith('[Version')) {
        const version = line.match(/\d+\.\d+\.\d+/)[0];
        if (!versions.includes(version)) {
          versions.push(version);
        }
      }
    });
    return versions.map((version, index) => (
      <option key={index} value={version}>
        Version {version}
      </option>
    ));
  };

  const renderFilteredChangelog = () => {
    return filteredChangelog.map((line, index) => {
      const style = getLineStyle(line);
      return <div key={index}>{renderLine(line, style)}</div>;
    });
  };

  return (
    <div style={styles.container}>
      <h2 style={styles.heading}>Journal des Modifications!</h2>
      <p style={styles.description}>
        Cette page est conçue pour consulter les différentes modifications
        apportées au système.
      </p>
      <div style={styles.selectContainer}>
        <select
          style={styles.select}
          value={selectedVersion}
          onChange={handleVersionChange}
        >
          <option value="">Tous les options</option>
          {renderOptions()}
        </select>
        <select
          style={styles.select}
          value={selectedUser}
          onChange={handleUserChange}
        >
          <option value="">Tous les utilisateurs</option>
          <option value="ahmed">Ahmed</option>
          <option value="hanane">Hanane</option>
          <option value="soumaya">Soumaya</option>
          <option value="soufiane">Soufiane</option>
        </select>
      </div>
      <div style={styles.changelogContainer}>
        {filteredChangelog.length > 0 ? (
          renderFilteredChangelog()
        ) : (
          <p style={styles.noResults}>Pas de resultat.</p>
        )}
      </div>
    </div>
  );
};

const styles = {
  container: {
    padding: '20px',
    backgroundColor: '#f0f0f0',
    marginTop: '20px',
    fontFamily: 'Roboto, sans-serif',
    borderRadius: '10px',
    boxShadow: '0px 0px 10px 0px rgba(0,0,0,0.1)',
    maxWidth: '800px',
    margin: '0 auto',
    position: 'relative',
  },
  heading: {
    marginBottom: '20px',
    fontSize: '32px',
    fontWeight: 'bold',
    color: '#333',
    textAlign: 'center',
    textTransform: 'uppercase',
    letterSpacing: '2px',
  },
  description: {
    marginBottom: '20px',
    fontSize: '16px',
    color: '#666',
    textAlign: 'center',
  },
  versionHeading: {
    marginTop: '20px',
    fontSize: '20px',
    fontWeight: 'bold',
    color: '#FF5722',
  },
  sectionHeading: {
    marginTop: '15px',
    fontSize: '16px',
    fontWeight: 'bold',
    color: '#2196F3',
  },
  listItem: {
    marginBottom: '10px',
    marginLeft: '20px',
    fontSize: '14px',
    lineHeight: '1.6',
  },
  selectContainer: {
    marginBottom: '20px',
    textAlign: 'center',
  },
  select: {
    padding: '8px 10px',
    borderRadius: '5px',
    border: '1px solid #ccc',
    marginRight: '10px',
    fontSize: '16px',
  },
  changelogContainer: {
    marginTop: '20px',
  },
  noResults: {
    textAlign: 'center',
    color: '#666',
  },
  footer: {
    position: 'absolute',
    bottom: '10px',
    left: '50%',
    transform: 'translateX(-50%)',
    fontSize: '12px',
    color: '#999',
  },
};

export default ChangeLogPage;
