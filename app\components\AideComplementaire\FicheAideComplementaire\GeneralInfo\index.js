import React, { useEffect, useState } from 'react';
import moment from 'moment';
import { Link, useHistory, useLocation } from 'react-router-dom';
import AccessControl from 'utils/AccessControl';
import Alert from 'react-bootstrap/Alert';
import styles from '../../../../Css/personalInfo.css';
import btnStyles from '../../../../Css/button.css';
import {
  closeAideComplementaireReset,
  executeAideComplementaireReset,
  unexecuteAideComplementaireReset,
} from '../../../../containers/AideComplementaire/FicheAideComplementaire/actions';
import { useDispatch } from 'react-redux';
import { downloadDocument } from '../../../../containers/Common/SubComponents/DocumentForm/actions';

const formatDate = date => moment(date).format('DD/MM/YYYY');

export default function GeneralInfo(props) {
  const dispatch = useDispatch();
  const aideComplementaire = props.data;
  const location = useLocation();
  const history = useHistory();
  const [showSuccessMessage, setShowSuccessMessage] = useState(false);
  const [message, setMessage] = useState('');

  useEffect(() => {
    if (location.state === 'updateSuccess') {
      setShowSuccessMessage(true);
      setMessage('Aide complémentaire modifiée avec succès !');
    }
  }, [location.state]);

  useEffect(() => {
    if (location.state === 'executeAideComplementaireSuccess') {
      setShowSuccessMessage(true);
      setMessage('Aide complémentaire exécutée avec succès !');
      dispatch(executeAideComplementaireReset());
      history.replace({ ...location, state: null });
    }
  }, [location.state]);

  useEffect(() => {
    if (location.state === 'unexecuteAideComplementaireSuccess') {
      setShowSuccessMessage(true);
      setMessage('Aide complémentaire : exécution annulée avec succès !');
      dispatch(unexecuteAideComplementaireReset());
      history.replace({ ...location, state: null });
    }
  }, [location.state]);

  useEffect(() => {
    if (location.state === 'closeAideComplementaireSuccess') {
      setShowSuccessMessage(true);
      setMessage('Aide complémentaire clôturée avec succès !');
      dispatch(closeAideComplementaireReset());
      history.replace({ ...location, state: null });
    }
  }, [location.state]);

  useEffect(() => {
    if (showSuccessMessage) {
      const timer = setTimeout(() => {
        handleCloseSuccessMessage();
      }, 4000);
      return () => clearTimeout(timer);
    }
  }, [showSuccessMessage]);

  const handleCloseSuccessMessage = () => {
    setShowSuccessMessage(false);
    history.replace({ ...location, state: null });
  };

  let data1 = null;
  if (aideComplementaire) {
    data1 = (
      <div className={styles.data1}>
        <p style={{ fontWeight: '700' }}>{aideComplementaire.name}</p>
        <p style={{ fontWeight: '700' }}>{aideComplementaire.code}</p>
        <p style={{ fontWeight: '700' }}>{aideComplementaire.slogan}</p>
        <p style={{ fontWeight: '700' }}>
          <Link to={`/services/fiche/${aideComplementaire.services.id}/info`}>
            {aideComplementaire.services.name}
          </Link>
        </p>
        <p style={{ fontWeight: '700' }}>
          {aideComplementaire.montantPrevu} DH
        </p>
        <p>
          {aideComplementaire.dateDebut
            ? formatDate(aideComplementaire.dateDebut)
            : '---'}
        </p>
        <p>
          {aideComplementaire.dateFin
            ? formatDate(aideComplementaire.dateFin)
            : '---'}
        </p>
        <p>
          {aideComplementaire.datePlanification
            ? formatDate(aideComplementaire.datePlanification)
            : '---'}
        </p>
        <p>{aideComplementaire.costs} %</p>
        <p>{aideComplementaire.amountPerBeneficiary} DH</p>
        <p>{aideComplementaire.priority ? 'oui' : 'Non'}</p>
        <p>
          {aideComplementaire.commentaire
            ? aideComplementaire.commentaire
            : '---'}
        </p>

        {/* Si l'aide est clôturée, afficher les informations supplémentaires */}
        {aideComplementaire.statut === 'cloturer' && (
          <>
            <p>
              <hr style={{ margin: '20px 0', borderColor: '#ccc' }} />
            </p>
            <p>
              {aideComplementaire.dateCloture
                ? formatDate(aideComplementaire.dateCloture)
                : '---'}
            </p>
            <p>
              {aideComplementaire.documentDto &&
              aideComplementaire.documentDto.fileName &&
              aideComplementaire.documentDto.fileUrl ? (
                <>
                  <span
                    onClick={() =>
                      dispatch(
                        downloadDocument(
                          aideComplementaire.documentDto,
                          'campagne',
                        ),
                      )
                    }
                    style={{
                      textDecoration: 'underline',
                      color: '#4F89D7',
                      cursor: 'pointer',
                      fontWeight: 'bold',
                    }}
                    title="Télécharger"
                    className="me-2"
                  >
                    {aideComplementaire.documentDto.fileName}
                  </span>
                  <i
                    className="fas fa-download text-primary ms-2"
                    style={{ marginLeft: '8px', cursor: 'pointer' }}
                    title="Télécharger"
                    onClick={() =>
                      dispatch(
                        downloadDocument(
                          aideComplementaire.documentDto,
                          'campagne',
                        ),
                      )
                    }
                  ></i>
                </>
              ) : (
                <span className="text-muted">Aucun fichier</span>
              )}
            </p>
            <p>
              {aideComplementaire.documentDto &&
              aideComplementaire.documentDto.comment
                ? aideComplementaire.documentDto.comment
                : '---'}
            </p>
          </>
        )}
      </div>
    );
  }

  return (
    <div>
      {showSuccessMessage && (
        <Alert
          className="alert-style"
          variant="success"
          onClose={handleCloseSuccessMessage}
          dismissible
        >
          <p>{message}</p>
        </Alert>
      )}

      <div className={styles.personalInfo}>
        <div className={styles.header}>
          <h4>Informations sur l'aide complémentaire</h4>

          {aideComplementaire &&
          aideComplementaire.statut != 'executer' &&
          aideComplementaire.statut != 'cloturer' ? (
            <AccessControl module="DONATION" functionality="UPDATE">
              <Link
                to={{
                  pathname: `/aide-complementaire/edit/${aideComplementaire.id}`,
                  state: { redirectTo: 'consultation' },
                }}
              >
                <button className={btnStyles.editBtnProfile}>Modifier</button>
              </Link>
            </AccessControl>
          ) : null}
        </div>

        <div className="d-flex justify-content-center mt-4">
          <div className={`py-3 px-5 ${styles.donationInfo}`} style={{ width: '100%' }}>
            {aideComplementaire.name && <p style={{ fontSize: '1rem', marginBottom: '1rem' }}><strong>Nom:</strong> {aideComplementaire.name}</p>}
            {aideComplementaire.code && <p style={{ fontSize: '1rem', marginBottom: '1rem' }}><strong>Code:</strong> {aideComplementaire.code}</p>}
            {aideComplementaire.slogan && <p style={{ fontSize: '1rem', marginBottom: '1rem' }}><strong>Slogan:</strong> {aideComplementaire.slogan}</p>}
            {aideComplementaire.services && <p style={{ fontSize: '1rem', marginBottom: '1rem' }}><strong>Service:</strong> <Link to={`/services/fiche/${aideComplementaire.services==null?0:aideComplementaire.services.id}/info`}>{aideComplementaire.services==null?'---':aideComplementaire.services.name}</Link></p>}
            {aideComplementaire.montantPrevu && <p style={{ fontSize: '1rem', marginBottom: '1rem' }}><strong>Montant prévu:</strong> {aideComplementaire.montantPrevu} DH</p>}
            {aideComplementaire.dateDebut && <p style={{ fontSize: '1rem', marginBottom: '1rem' }}><strong>Date de début:</strong> {aideComplementaire.dateDebut ? formatDate(aideComplementaire.dateDebut) : '---'}</p>}
            {aideComplementaire.dateFin && <p style={{ fontSize: '1rem', marginBottom: '1rem' }}><strong>Date de fin:</strong> {aideComplementaire.dateFin ? formatDate(aideComplementaire.dateFin) : '---'}</p>}
            {aideComplementaire.datePlanification && <p style={{ fontSize: '1rem', marginBottom: '1rem' }}><strong>Date prévue d'exécution:</strong> {aideComplementaire.datePlanification ? formatDate(aideComplementaire.datePlanification) : '---'}</p>}
            {aideComplementaire.costs && <p style={{ fontSize: '1rem', marginBottom: '1rem' }}><strong>Frais de gestion:</strong> {aideComplementaire.costs} %</p>}
            {aideComplementaire.amountPerBeneficiary && <p style={{ fontSize: '1rem', marginBottom: '1rem' }}><strong>Montant prévue par bénéficiaire:</strong> {aideComplementaire.amountPerBeneficiary} DH</p>}
            {aideComplementaire.priority && <p style={{ fontSize: '1rem', marginBottom: '1rem' }}><strong>Priorité:</strong> {aideComplementaire.priority ? 'oui' : 'Non'}</p>}
            {aideComplementaire.commentaire && <p style={{ fontSize: '1rem', marginBottom: '1rem' }}><strong>Commentaire:</strong> {aideComplementaire.commentaire ? aideComplementaire.commentaire : '---'}</p>}
            {aideComplementaire.statut === 'cloturer' && (
              <>
                <hr style={{ margin: '20px 0', borderColor: '#ccc' }} />
                <p style={{ fontSize: '1.1rem', marginBottom: '1rem' }}><strong>Clôturée le:</strong> {aideComplementaire.dateCloture ? formatDate(aideComplementaire.dateCloture) : '---'}</p>
                <p style={{ fontSize: '1.1rem', marginBottom: '1rem' }}><strong>Fichier joint:</strong> {aideComplementaire.documentDto && aideComplementaire.documentDto.fileName && aideComplementaire.documentDto.fileUrl ? (
                  <>
                    <span
                      onClick={() => dispatch(downloadDocument(aideComplementaire.documentDto, 'campagne'))}
                      style={{
                        textDecoration: 'underline',
                        color: '#4F89D7',
                        cursor: 'pointer',
                        fontWeight: 'bold',
                      }}
                      title="Télécharger"
                      className="me-2"
                    >
                      {aideComplementaire.documentDto.fileName}
                    </span>
                    <i
                      className="fas fa-download text-primary ms-2"
                      style={{ marginLeft: '8px', cursor: 'pointer' }}
                      title="Télécharger"
                      onClick={() => dispatch(downloadDocument(aideComplementaire.documentDto, 'campagne'))}
                    ></i>
                  </>
                ) : (
                  <span className="text-muted">Aucun fichier</span>
                )}
                </p>
                <p style={{ fontSize: '1.1rem', marginBottom: '1rem' }}><strong>Détail:</strong> {aideComplementaire.documentDto && aideComplementaire.documentDto.comment ? aideComplementaire.documentDto.comment : '---'}</p>
              </>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
