import React, { useEffect, useRef, useState } from 'react';
import { Link, useLocation, useHistory } from 'react-router-dom';
import PropTypes from 'prop-types';
import moment from 'moment';
import stylesList from 'Css/profileList.css';
import AccessControl from 'utils/AccessControl';
import ReactToPrint from 'react-to-print';
import Alert from 'react-bootstrap/Alert';
import profile from '../../../Css/personalInfo.css';
import btnStyles from '../../../Css/button.css';
import PrintableContent from '../PrintableContent/PrintableContent';
import {
  PRINT_ICON,
  WHITE_UPLOAD_PICTURE,
} from '../../../containers/Common/RequiredElement/Icons';

const formatDate = date => moment(date).format('DD/MM/YYYY');

export default function PersonalInfo(props) {
  const donor = props.data;
  const location = useLocation();
  const history = useHistory();
  const [showSuccessMessage, setShowSuccessMessage] = useState(false);
  useEffect(() => {
    if (location.state === 'success') {
      setShowSuccessMessage(true);
    }
  }, [location.state]);

  useEffect(() => {
    if (showSuccessMessage) {
      const timer = setTimeout(() => {
        handleCloseSuccessMessage();
      }, 4000);
      return () => clearTimeout(timer);
    }
  }, [showSuccessMessage]);

  const handleCloseSuccessMessage = () => {
    setShowSuccessMessage(false);
    history.replace({ ...location, state: null });
  };

  const handleBeforePrint = () => {
    // Format the current date and time in a more readable way
    const currentDateTime = moment().format('YYYY-MM-DD_HH-mm-ss'); // e.g., 2025-01-23_16-13-13
    const fullName = `${donor.firstName} ${donor.lastName}`;
    document.title = `${fullName} - ${donor.code} - ${currentDateTime}`;
  };

  const handleAfterPrint = () => {
    document.title = "Sofwa Système";
  };

  let content = <p></p>;
  const emptyValue = <span> ----</span>;

  const componentRef = useRef();

  if (props.data) {
    content = (
      <div className={profile.content}>
        {/* SECTION 1 */}
        <div className={profile.section1}>
          {/* TOP SECTION */}
          <div className={profile.top}>
            <div className={`${profile.label1} ${profile.data1}`}>
              <p>
                <span style={{ fontWeight: 'bold' }}>Nom : </span>
                <span style={{ fontWeight: 'normal' }}>
                  {donor.lastName ? donor.lastName : emptyValue}
                </span>
              </p>
              <p>
                <span style={{ fontWeight: 'bold' }}>Prénom : </span>
                <span style={{ fontWeight: 'normal' }}>
                  {donor.firstName ? donor.firstName : emptyValue}
                </span>
              </p>
              <p>
                <span style={{ fontWeight: 'bold' }}>Sexe : </span>
                <span style={{ fontWeight: 'normal' }}>
                  {donor.sex ? donor.sex : emptyValue}
                </span>
              </p>

              <p>
                <span style={{ fontWeight: 'bold' }}>N° identité : </span>
                <span style={{ fontWeight: 'normal' }}>
                  {donor.identityCode ? donor.identityCode : emptyValue}
                </span>
              </p>
              <p>
                <span style={{ fontWeight: 'bold' }}>Profession : </span>
                <span style={{ fontWeight: 'normal' }}>
                  {donor.profession && donor.profession.name
                    ? donor.profession.name
                    : emptyValue}
                </span>
              </p>
             
              <p>
                <span style={{ fontWeight: 'bold' }}>Inscrit le : </span>
                <span style={{ fontWeight: 'normal' }}>
                  {donor.createdAt ? formatDate(donor.createdAt) : emptyValue}
                </span>
              </p>
            </div>
            <div className={`${profile.label2} ${profile.data2}`}>
              <p>
                <span style={{ fontWeight: 'bold' }}> النسب : </span>
                <span style={{ fontWeight: 'normal' }}>
                  {donor.lastNameAr ? donor.lastNameAr : emptyValue}
                </span>
              </p>
              <p>
                <span style={{ fontWeight: 'bold' }}>الإسم : </span>
                <span style={{ fontWeight: 'normal' }}>
                  {donor.firstNameAr ? donor.firstNameAr : emptyValue}
                </span>
              </p>
            </div>
          </div>
          {donor.comment && (
                <p style={{ marginBottom: '10px' }}>
                  <span style={{ fontWeight: 'bold' }}>Commentaire : </span>
                  <span style={{ fontWeight: 'normal' }}>
                    {donor.comment ? donor.comment : emptyValue}
                  </span>
                </p>
              )}
          {/* BOTTOM SECTION */}
          <div className={profile.bottom}>
            <div className={profile.label1}>
              <p>
                
                <span style={{ fontWeight: 'bold' }}>
                  Canal Communication :{' '}
                </span>
                <span style={{ fontWeight: 'normal' }}>
                  {donor.canalCommunications.length > 0
                    ? donor.canalCommunications.map(canal => (
                        <span key={canal.id}> {canal.name}, </span>
                      ))
                    : emptyValue}
                </span>
              </p>
              <p>
                <span style={{ fontWeight: 'bold' }}>
                  Langue Communication :{' '}
                </span>
                <span style={{ fontWeight: 'normal' }}>
                  {donor.languageCommunications.length > 0
                    ? donor.languageCommunications.map(language => (
                        <span key={language.id}> {language.name}, </span>
                      ))
                    : emptyValue}
                </span>
              </p> 
              
            </div>
          </div>
        </div>

        {/* SECTION 2 */}
        <div className={profile.section2}>
          <div className="row">
            <div className={`${profile.label1} ${profile.data1}`}>
              <p>
                <span style={{ fontWeight: 'bold' }}>Pays : </span>
                <span style={{ fontWeight: 'normal' }}>
                  {donor.info
                    ? donor.info.region.country.nameFrench
                    : emptyValue}
                </span>
              </p>
              <p>
                <span style={{ fontWeight: 'bold' }}>Région : </span>
                <span style={{ fontWeight: 'normal' }}>
                  {donor.info ? donor.info.region.name : emptyValue}
                </span>
              </p>
              <p>
                <span style={{ fontWeight: 'bold' }}>Ville : </span>
                <span style={{ fontWeight: 'normal' }}>
                  {donor.info ? donor.info.name : emptyValue}
                </span>
              </p>
              <p>
                <span style={{ fontWeight: 'bold' }}>Adresse : </span>
                <span style={{ fontWeight: 'normal' }}>
                  {donor.address ? donor.address : emptyValue}
                </span>
              </p>
              <p>
                <span style={{ fontWeight: 'bold' }}>العنوان : </span>
                <span style={{ fontWeight: 'normal' }}>
                  {donor.addressAr ? donor.addressAr : emptyValue}
                </span>
              </p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div>
      {showSuccessMessage && (
        <Alert
          className="alert-style"
          variant="success"
          onClose={handleCloseSuccessMessage}
          dismissible
        >
          <p>Donateur modifié avec succès</p>
        </Alert>
      )}
      <div className={stylesList.backgroudStyle}>
        <div className={profile.personalInfo}>
          <div className={profile.header}>
            <h4>Informations personnelles</h4>
            <div className="d-flex align-items-center gap-10">
              <ReactToPrint
                trigger={() => (
                  <button className="btn-style primary">
                    <img src={PRINT_ICON} width="16px" height="16px" />
                    Imprimer
                  </button>
                )}
                content={() => componentRef.current}
                onBeforePrint={handleBeforePrint}
                onAfterPrint={handleAfterPrint}
              />
              <div style={{ display: 'none' }}>
                <PrintableContent ref={componentRef} data={donor} />
              </div>
                <Link
                  to={{
                    pathname: `/donors/edit/physique/${donor.id}`,
                    state: { redirectTo: 'consultation' },
                  }}
                >
                  <button className="btn-style secondary">
                    <img
                      src={WHITE_UPLOAD_PICTURE}
                      width="16px"
                      height="16spx"
                    />
                    Modifier
                  </button>
                </Link>
            </div>
          </div>
          {content}
        </div>
      </div>
    </div>
  );
}

PersonalInfo.propTypes = {
  data: PropTypes.object.isRequired,
};
