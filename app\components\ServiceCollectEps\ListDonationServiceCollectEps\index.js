import React, { useEffect, useState } from 'react';
import listStyles from 'Css/list.css';
import { useDispatch, useSelector } from 'react-redux';
import { useInjectReducer, useInjectSaga } from 'redux-injectors';

import btnStyles from 'Css/button.css';
import { createStructuredSelector } from 'reselect';
import AccessControl from 'utils/AccessControl';
import { Link, useHistory } from 'react-router-dom';
import CustomPagination from 'components/Common/CustomPagination';
import reducer from './reducer';
import saga from './saga';
import GenericFilter from 'containers/Common/Filter/GenericFilter';
import { Alert } from 'react-bootstrap';
import { loadDonationServiceCollectEps, loadDonationServiceCollectEpsList } from "./actions";
import { makeSelectDonationServiceCollectEps, makeSelectError, makeSelectLoading } from "./selectors";
import ListServiceCollectEps from 'components/ServiceCollectEps/ListServiceCollectEps';
import DonationServiceCollectEps from './DonationCollectEps';

const key = 'donationServiceCollectEps';

const stateSelector = createStructuredSelector({
    donationServiceCollectEpsList: makeSelectDonationServiceCollectEps,
    loading: makeSelectLoading,
    error: makeSelectError,
});

export default function DonationServiceCollectEpsList(props) {
    const {
        donationServiceCollectEpsList,
        loading,
        deleteSuccess,
    } = useSelector(stateSelector);

    const [errorMessages, setErrorMessages] = useState('');
    const [showAlert, setShowAlert] = useState(false);
    const [successMessage, setSuccessMessage] = useState('');
    const [showModal, setShowModal] = useState(false);
    const [errorMessage, setErrorMessage] = useState('');
    const [showSuccessAlert, setShowSuccessAlert] = useState(false);


    const dispatch = useDispatch();
    const history = useHistory();

    useInjectReducer({ key, reducer: reducer });
    useInjectSaga({ key, saga: saga });


    
    useEffect(() => {
        if (props.data.id) {
            dispatch(loadDonationServiceCollectEps(props.data.id, 0));
            console.log(donationServiceCollectEpsList)
        }
    }, [dispatch, props.data]);
    const [filterValues, setFilterValues] = useState({
        searchByName: '',
        searchByNameAr: '',
        searchByMinBenificiary: '',
        searchByMaxBenificiary: '',
        searchByStatut: '',
        searchByEps: '',
        searchByService: '',
    });
 
    const additionalInputsConfig = [
        {
            field: 'searchByDonationType', // Matches buildUrlWithFilters
            type: 'select',
            placeholder: 'Type de donation',
            label: 'Type de donation',
            options: [
                { value: 'Financière', label: 'Financière' },
                { value: 'Nature', label: 'Nature' },
            ]
        },
        { field: 'searchByMinAmount', type: 'number', placeholder: 'Min Montant', label: 'Montant Min' },
        { field: 'searchByMaxAmount', type: 'number', placeholder: 'Max Montant', label: 'Montant Max' },
        {
            field: 'searchByDonationMinDate', 
            type: 'date',
            placeholder: 'Date de donation minimale',
            label: 'Date de donation minimale'
        },
        {
            field: 'searchByDonationMaxDate', 
            type: 'date',
            placeholder: 'Date de donation maximale',
            label: 'Date de donation maximale'
        },
    ];
    
    const principalInputsConfig = [
        {
            field: 'searchByDonateur', // Matches buildUrlWithFilters
            type: 'text',
            placeholder: 'Nom du Donateur',
            label: 'Nom du Donateur',
        },
    ];
    


    let messageAddedEps = null;



    const handleApplyFilter = filters => {
        setFilterValues(filters);
        dispatch(loadDonationServiceCollectEps(props.data.id, 0, filters));
    };

    const [addEditMessagSuccess, setAddEditMessagSuccess] = useState('');

    const [epsId, setEpsId] = useState(null)

    const handleCloseAlert = () => {
        setShowAlert(false);
        props.history.replace({ ...props.location, state: null });
    };

    if (showAlert) {
        messageAddedEps = (
            <Alert
                className="pb-0"
                variant="success"
                onClose={handleCloseAlert}
                dismissible
            >
                <p>
                    {addEditMessagSuccess}
                    {() => {
                        return (
                            <>
                                <Link to={`/ServiceCollectEps/fiche/${epsId}/info`} className="alert-link">
                                    Cliquez ici pour plus de détails
                                </Link>
                            </>
                        );

                    }}

                </p>
            </Alert>
        );
    }
    useEffect(() => {
        if (showAlert || showSuccessAlert) {
            const timer = setTimeout(() => {
                setShowAlert(false);
                setShowSuccessAlert(false)
                // Set props.location.state to empty or null
                props.history.replace({ ...props.location, state: null });
            }, 4000);
            return () => clearTimeout(timer);
        }
    }, [showAlert, showSuccessAlert]);

    const handleCloseSuccessMessage = () => {
        setShowAlert(false);
        setShowSuccessAlert(false);
        history.replace({ ...location, state: null });
    };

    useEffect(() => {
        if (showAlert || showSuccessAlert) {
            const timer = setTimeout(() => {
                handleCloseSuccessMessage();
            }, 4000);
            return () => clearTimeout(timer);
        }
    }, [showAlert, showSuccessAlert]);

    const handlePageChange = pageNumbers => {
        const nextPage = pageNumbers - 1;
        if (nextPage >= 0 && nextPage < donationServiceCollectEpsList.totalPages) {
            setActivePage(nextPage);
            setFilterValues(prevFilterValues => {
                dispatch(loadDonationServiceCollectEps(props.data.id, nextPage, prevFilterValues));
                return prevFilterValues;
            });
        }
    };
    const handleResetFilterComplete = () => {
        setFilterValues({
            searchBycode: '',
            searchByName: '',
            searchByNameAr: '',
            searchByMinBenificiary: '',
            searchByMaxBenificiary: '',
            searchByAssistantName: '',
            searchByStatus: '',
            searchByCity: '',
        });
    };

    useEffect(() => {
        if (donationServiceCollectEpsList) {
            setActivePage(donationServiceCollectEpsList.number);
        }
    }, [donationServiceCollectEpsList]);
    const [activePage, setActivePage] = useState(1);

    if (showAlert) {
        messageAddedEps = (
            <Alert
                className="pb-0"
                variant="success"
                onClose={handleCloseAlert}
                dismissible
            >
                {/* <p>Donateur ajouté avec succès ! </p> */}
                <p>
                    {addEditMessagSuccess}
                    <Link to={`/ServiceCollectEps/fiche/${epsId}/info`} className="alert-link">
                        Cliquez ici pour plus de détails
                    </Link>
                </p>
            </Alert>
        );
    }


    return (
        <>
            {showSuccessAlert && (
                <Alert
                    className="pb-0"
                    variant="success"
                    onClose={() => {
                        setShowSuccessAlert(false);
                    }}
                    dismissible
                >
                    <p>Le service de collecte a été supprimé avec succès.</p>
                </Alert>
            )}
            {messageAddedEps}
            {errorMessages && (
                <Alert
                    className="pb-0"
                    variant="danger"
                    onClose={() => {
                        setErrorMessages('Test');
                    }}
                    dismissible
                >
                    <p>{errorMessages}</p>
                </Alert>
            )}
            <div className={listStyles.backgroundStyle}>

                <GenericFilter
                    principalInputsConfig={principalInputsConfig}
                    additionalInputsConfig={additionalInputsConfig}
                    onApplyFilter={handleApplyFilter}
                    filterValues={filterValues}
                    setFilterValues={setFilterValues}
                    onResetFilterComplete={handleResetFilterComplete}
                />
                {loading ? (
                    <div className="d-flex justify-content-center pb-3">
                        <div
                            className="spinner-border"
                            style={{ width: '5rem', height: '5rem' }}
                            role="status"
                        >
                            <span className="sr-only">Chargement...</span>
                        </div>
                    </div>
                ) : donationServiceCollectEpsList && donationServiceCollectEpsList.content ? (
                    <>
                        <DonationServiceCollectEps
                            donationServiceCollectEpsList={donationServiceCollectEpsList.content}
                        />
                        {donationServiceCollectEpsList.totalPages > 0 && (
                            <div className="row justify-content-center my-4">
                                <CustomPagination
                                    totalCount={donationServiceCollectEpsList.totalPages}
                                    pageSize={donationServiceCollectEpsList.pageSize}
                                    currentPage={donationServiceCollectEpsList.number + 1}
                                    totalElements={donationServiceCollectEpsList.totalElements}
                                    onPageChange={handlePageChange}
                                />
                            </div>
                        )}
                    </>
                ) : (
                    <div className={`no-profiles-message ${listStyles.disable}`}>
                        Aucune Donation disponible
                    </div>
                )}
            </div>
        </>
    );
};


