import {LOAD_DOCS_RENEW,VIEW_DOCUMENT} from './constants';
import {documentsRenewLoaded,documentsRenewError} from './actions'
import request from 'utils/request';
import { downloadBlob, openBlobInNewTab } from 'utils/utilFuncs/blobUtils';
import { call, put, takeLatest } from "redux-saga/effects";

function buildUrlWithFilters(baseUrl, filters) {
  let url = baseUrl;
  if (filters) {
    const {
      searchByName,
      searchByModule,
      searchByType,
      searchByExpiryDate
    } = filters;

    if (searchByName) url += `&searchByName=${encodeURIComponent(searchByName)}`;
    if (searchByModule) url += `&searchByModule=${encodeURIComponent(searchByModule)}`;
    if (searchByType) url += `&searchByType=${encodeURIComponent(searchByType)}`;
    if (searchByExpiryDate) url += `&searchByExpiryDate=${encodeURIComponent(searchByExpiryDate)}`;
  }

  return url;
}

export function* loadDocumentsRenew({ page,zoneIds, filters}) {
    let requestURL = `/documents/renew?page=${page}`;

    // Add multiple zoneId parameters if zoneIds is provided
    if (zoneIds && Array.isArray(zoneIds) && zoneIds.length > 0) {
        // Extract just the id values from the zoneIds array
        const zoneIdValues = zoneIds.map(zone => zone.id || zone);
        zoneIdValues.forEach(zoneId => {
            requestURL += `&zoneId=${zoneId}`;
        });
    }

    requestURL = buildUrlWithFilters(requestURL, filters);
    try {
      const { data } = yield call(request.get, requestURL);
      yield put(documentsRenewLoaded(data));

    } catch (error) {
      yield put(documentsRenewError(error));
    }
  }

  export function* viewDocument({ documentId }) {  
    const url = `/documents/view/${documentId}`;
    
    try {
      const { data, headers } = yield call(request.get, url, {
        responseType: 'blob',
      });
  
      openBlobInNewTab(data, '', headers['content-disposition']);
    } catch (error) {
      console.error('Error viewing the document:', error);
    }
  }

  export default function* documentsRenewSaga() {
    yield takeLatest(LOAD_DOCS_RENEW, loadDocumentsRenew);
    yield takeLatest(VIEW_DOCUMENT, viewDocument);
  }