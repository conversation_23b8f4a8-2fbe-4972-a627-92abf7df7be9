// saga.js
import { call, put, takeLatest } from 'redux-saga/effects';
import request from 'utils/request'; // Assuming request utility is correctly configured
import {
  fetchUsersSuccess,
  fetchUsersFailure,
  addUserSuccess,
  addUserFailure,
  deleteUserSuccess,
  deleteUserFailure,
  changeUserRoleSuccess,
  changeUserRoleFailure,
} from './actions';
import {
  FETCH_USERS_REQUEST,
  ADD_USER_REQUEST,
  DELETE_USER_REQUEST,
  CHANGE_USER_ROLE_REQUEST,
} from './constants';

function* fetchUsersSaga({ page, filters }) {
  try {
    let url = `/batch/AdUsers?page=${page}`;
    if (filters) {
      const {
        size,
        searchByEmail,
        searchRole,
        searchByNom,
        searchByPrenom,
        minDate,
        maxDate,
      } = filters;

      if (searchByEmail) {
        url += `&searchByEmail=${searchByEmail}`;
      }
      if (size) {
        url += `&size=${size}`;
      }
      if (searchRole) {
        url += `&searchRole=${searchRole}`;
      }
      if (searchByNom) {
        url += `&searchByNom=${searchByNom}`;
      }
      if (searchByPrenom) {
        url += `&searchByPrenom=${searchByPrenom}`;
      }
      if (minDate) {
        url += `&minDate=${minDate}`;
      }
      if (maxDate) {
        url += `&maxDate=${maxDate}`;
      }
    }
    const users = yield call(request.get, url);
    yield put(fetchUsersSuccess(users));
  } catch (error) {
    yield put(fetchUsersFailure(error));
  }
}

function* changeUserRoleRequest(action) {
  try {
    const { userId, newRole } = action.payload;
    const url = `/batch/changeUserRole?userId=${userId}&newRoleId=${newRole}`;
    const response = yield call(request.post, url);
    yield put(changeUserRoleSuccess());
  } catch (error) {
    yield put(changeUserRoleFailure(error));
  }
}

function* addUserSaga(action) {
  try {
    const { email, roleId } = action.payload;
    const url = `/batch/users/loadByEmail?email=${email}&roleId=${roleId}`;
    const response = yield call(request.post, url);
    yield put(addUserSuccess());
  } catch (error) {
    if (error.response && error.response.status === 404) {
      yield put(addUserFailure('User not found'));
    } else if (error.response && error.response.status === 409) {
      yield put(addUserFailure('User already exists'));
    } else {
      yield put(addUserFailure(error.message));
    }
  }
}
function* deleteUserSaga(action) {
  try {
    const userId = action.payload;
    const url = `/batch/users/${userId}`;
    yield call(request.delete, url);
    yield put(deleteUserSuccess());
  } catch (error) {
    yield put(deleteUserFailure(error));
  }
}

export default function* adUserSaga() {
  yield takeLatest(FETCH_USERS_REQUEST, fetchUsersSaga);
  yield takeLatest(ADD_USER_REQUEST, addUserSaga);
  yield takeLatest(DELETE_USER_REQUEST, deleteUserSaga);
  yield takeLatest(CHANGE_USER_ROLE_REQUEST, changeUserRoleRequest);
}
