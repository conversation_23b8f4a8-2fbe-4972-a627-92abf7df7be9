import { call, put, takeLatest } from 'redux-saga/effects';
import request from 'utils/request';
import {
  aideComplementaireLoaded,
  aideComplementaireLoadingError,
  deleteAideComplementaireFailure,
  deleteAideComplementaireSuccess,
} from './actions';
import {
  DELETE_AIDE_COMPLEMENTAIRE_REQUEST,
  LOAD_AIDE_COMPLEMENTAIRE,
} from './constants';

// Helper function to build URL with filters
function buildUrlWithFilters(baseUrl, filters) {
  let url = baseUrl;

  if (filters) {
    const {
      searchByNom,
      searchByStatut,
      searchByMontant,
      searchByTypePriseEnChargeId,
      minDate,
      maxDate,
      searchByTagId,
    } = filters;

    if (searchByNom) url += `&searchByNom=${searchByNom}`;
    if (searchByStatut) url += `&searchByStatut=${searchByStatut}`;
    if (searchByMontant) url += `&searchByMontant=${searchByMontant}`;
    if (searchByTypePriseEnChargeId)
      url += `&searchByTypePriseEnChargeId=${searchByTypePriseEnChargeId}`;
    if (minDate) url += `&minDate=${minDate}`;
    if (maxDate) url += `&maxDate=${maxDate}`;
    if (searchByTagId) url += `&searchByTagId=${searchByTagId}`;
  }

  return url;
}

// Saga to load donations
export function* loadAideComplementaire({ page, filters }) {
  let url = `/aide-complementaire/findAll?page=${page}`;
  url = buildUrlWithFilters(url, filters);

  try {
    const { data } = yield call(request.get, url);

    yield put(aideComplementaireLoaded(data));
  } catch (error) {
    yield put(aideComplementaireLoadingError(error));
  }
}

// Saga to export donations to CSV
/*export function* exportDonations({ filters }) {
  let url = `/donations/csv?`;
  url = buildUrlWithFilters(url, filters);

  try {
    const { data } = yield call(request.get, url);
    downloadCSV(data.file64, data.fileName);
    yield put(donationsToCsvExported(data));
  } catch (error) {
    yield put(donationsToCsvExportingError(error));
  }
}

 */

// Function to download CSV
/*function downloadCSV(csvData, fileName) {
  const linkSource = `data:text/csv;base64,${csvData}`;
  const downloadLink = document.createElement('a');
  downloadLink.href = linkSource;
  downloadLink.download = fileName;
  downloadLink.click();
}

 */

export function* deleteAideComplementaireSaga({ id }) {
  const url = `/aide-complementaire/delete/${id}`;

  console.log({ url });

  try {
    const { data } = yield call(request.delete, url);
    yield put(deleteAideComplementaireSuccess());
  } catch (error) {
    yield put(deleteAideComplementaireFailure(error));
  }
}

// Root saga
export default function* aideComplementaireListSaga() {
  yield takeLatest(LOAD_AIDE_COMPLEMENTAIRE, loadAideComplementaire);
  yield takeLatest(
    DELETE_AIDE_COMPLEMENTAIRE_REQUEST,
    deleteAideComplementaireSaga,
  );
}
