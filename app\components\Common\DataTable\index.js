import {
  DataGrid,
  frFR,
  GridToolbarContainer,
  GridToolbarExport,

} from '@mui/x-data-grid';
import { Box } from '@mui/material';
import React from 'react';
import Wrapper from './Wrapper';

const frenchTranslations = {
  ...frFR,
  localeText: {
    ...frFR.localeText,
    rowsPerPage: 'Lignes par page',
    noRowsLabel: 'Aucune ligne',
    page: 'Page',
    of: 'de',
    loadingOverlay: 'Chargement en cours...',
    errorOverlay: "Une erreur s'est produite lors du chargement des données.",
    columnMenuLabel: 'Menu des colonnes',
    columnMenuShowColumns: 'Afficher les colonnes',
    columnMenuFilter: 'Filtrer',
    columnMenuHideColumn: 'Masquer la colonne',
    columnMenuUnsort: 'Annuler le tri',
    columnMenuSortAsc: 'Trier par ordre croissant',
    columnMenuSortDesc: 'Trier par ordre décroissant',
    columnMenuManageColumns: 'Gérer les colonnes',
    columnMenuColumns: 'Colonnes',
    columnMenuRemoveColumn: 'Supprimer la colonne',
    filterPanelAddFilter: 'Ajouter un filtre',
    filterPanelDeleteIconLabel: 'Supprimer',
    filterPanelOperatorAnd: 'Et',
    filterPanelOperatorOr: 'Ou',
    filterPanelColumns: 'Colonnes',
    filterPanelInputLabel: 'Valeur',
    filterPanelInputPlaceholder: 'Filtrer la valeur',
    filterPanelSubmitButtonText: 'Filtrer',
    filterPanelResetButtonText: 'Réinitialiser',
    filterPanelClearButtonText: 'Effacer',
    paginationFirstPage: 'Première page',
    paginationLastPage: 'Dernière page',
    paginationNextPage: 'Page suivante',
    paginationPreviousPage: 'Page précédente',
    paginationRowsPerPage: 'Lignes par page:',
    toolbarColumns: 'Colonnes',
    toolbarFilters: 'Filtres',
    toolbarFiltersTooltipHide: 'Masquer les filtres',
    toolbarFiltersTooltipShow: 'Afficher les filtres',
    toolbarFiltersTooltipActive: count => `${count} filtre(s) actif(s)`,
    toolbarFiltersTooltipInactive: 'Aucun filtre actif',
    toolbarSort: 'Trier',
    toolbarSearch: 'Rechercher',
    toolbarSearchPlaceholder: 'Rechercher',
    toolbarExport: 'Exporter',
    toolbarExportTitle: 'Exporter',
    toolbarExportCSV: 'Exporter en CSV',
    toolbarExportCSVTitle: 'Exporter en CSV',
    toolbarExportExcel: 'Exporter en Excel',
    toolbarExportExcelTitle: 'Exporter en Excel',
    toolbarExportPdf: 'Exporter en PDF',
    toolbarExportPdfTitle: 'Exporter en PDF',
    toolbarExportPrint: 'Imprimer',
    toolbarExportPrintTitle: 'Imprimer',
    columnsPanelTextFieldLabel: 'Rechercher une colonne',
    columnsPanelTextFieldPlaceholder: 'Titre de la colonne',
    columnsPanelDragIconLabel: 'Réorganiser la colonne',
    columnsPanelShowAllButton: 'Afficher tout',
    columnsPanelHideAllButton: 'Masquer tout',

    filterPanelOperator: 'Opérateur',

    filterOperatorContains: 'Contient',
    filterOperatorEquals: 'Égal à',
    filterOperatorStartsWith: 'Commence par',
    filterOperatorEndsWith: 'Se termine par',
    filterOperatorIs: 'is',
    filterOperatorNot: 'is not',
    filterOperatorAfter: 'is after',
    filterOperatorOnOrAfter: 'is on or after',
    filterOperatorBefore: 'is before',
    filterOperatorOnOrBefore: 'is on or before',
    filterOperatorIsEmpty: 'Est vide',
    filterOperatorIsNotEmpty: "N'est pas vide",
    filterOperatorIsAnyOf: 'Est un de',
  },
};

function DataTable({
  rows,
  columns,
  fileName,
  checkboxSelection = false,
  onRowSelectionModelChange = () => {},
  totalElements,
  numberOfElements,
  pageable,
  loading = false,
  getRowId,
}) {
  const CustomFooter = () => {
    if (!totalElements || !numberOfElements || !pageable) {
      return null;
    }

    const currentPage = pageable.pageNumber + 1;
    const startIndex = pageable.offset + 1;
    const endIndex = Math.min(
      pageable.offset + numberOfElements,
      totalElements,
    );

    const footerStyle = {
      display: 'none',
      textAlign: 'right',
      paddingRight: '10px', // Ajoutez d'autres styles selon vos besoins
      color: '#333', // Couleur du texte
      fontSize: '18px', // Taille de la police
      fontStyle: 'italic', // Style de police
      marginTop: '10px', // Marge supérieure ajoutée ici
      borderTop: '1px solid #ddd', // Bordure supérieure pour séparation
      paddingTop: '5px',
    };

    return (
      <div style={footerStyle}>
        {`${startIndex}–${endIndex} de ${totalElements}`}
      </div>
    );
  };

  const CustomToolbar = () => (
    <GridToolbarContainer>
      <GridToolbarExport
        csvOptions={{
          fileName,
          delimiter: ';',
          utf8WithBom: true,
        }}
      />
    </GridToolbarContainer>
  );

  return (
    <Wrapper>
      <Box sx={{    overflowY: 'auto' }}>
      <DataGrid
        loading={loading}
        localeText={frenchTranslations.localeText}
        rows={rows}
        columns={columns}
        // pageSize={5}
        autoHeight
        disableRowSelectionOnClick
        components={{
          Footer: CustomFooter,
        }}
        onRowSelectionModelChange={onRowSelectionModelChange}
        checkboxSelection={checkboxSelection}
        getRowId={getRowId}
      /></Box>
    </Wrapper>
  );
}

export default DataTable;
