import React, { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import { useDispatch, useSelector } from 'react-redux';
import { useInjectReducer, useInjectSaga } from 'redux-injectors';
import { createStructuredSelector } from 'reselect';
import { useHistory } from 'react-router-dom';
import Modal2 from 'components/Common/Modal2';
import btnStyles from 'Css/button.css';
import reducer from 'containers/Profile/AddProfile/reducer';
import saga from 'containers/Profile/AddProfile/saga';
import {
  makeSelectProfileData,
  makeSelectSuccess,
} from 'containers/Profile/AddProfile/selectors';
import { resetSuccessState } from 'containers/Profile/AddProfile/actions';
import { Alert } from 'react-bootstrap';
import { fetchProfilesRequest } from 'containers/Profile/Profiles/actions';
import { DELETE_ICON, EDIT_ICON } from '../../Common/ListIcons/ListIcons';
import DataTable from '../../Common/DataTable';

const key = 'addprofile';

const addProfileSelector = createStructuredSelector({
  success: makeSelectSuccess,
  profileData: makeSelectProfileData,
});

const ListProfiles = ({ profiles, handleDelete, liste1 }) => {
  const history = useHistory();
  const dispatch = useDispatch();
  const [profileToDelete, setProfileToDelete] = useState(null);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [showSuccessMessage, setShowSuccessMessage] = useState(false);
  const [successMessage, setSuccessMessage] = useState('');

  useInjectReducer({ key, reducer });
  useInjectSaga({ key, saga });

  const { success, profileData } = useSelector(addProfileSelector);

  useEffect(() => {
    if (success) {
      setSuccessMessage('Profiles changés avec succès !');
      setShowSuccessMessage(true);
      dispatch(resetSuccessState());
      dispatch(fetchProfilesRequest(0));
    }
  }, [success, profileData, dispatch]);

  const handleEdit = profileId => {
    history.push(`/Profiles/editProfile/${profileId}`);
  };

  const handleDeleteProfile = profileId => {
    setProfileToDelete(profileId);
    setShowDeleteModal(true);
  };

  const confirmDelete = () => {
    if (profileToDelete) {
      handleDelete(profileToDelete);
      setProfileToDelete(null);
      setShowDeleteModal(false);
    }
  };

  const columnNamesMap = {
    DONOR: 'Donateur',
    BENEFICIARY: 'Bénéficiaire',
    FAMILLE: 'Famille',
    DONATION: 'Donation',
    TAKEINCHARGE: 'Kafalat',
    USER: 'Administration',
    ACTIONS: 'Actions',
  };

  const transformFunctionalities = functionalities => {
    const translatedFunctionalities = {
      CREATE: 'Créer',
      UPDATE: 'Modifier',
      DELETE: 'Supprimer',
      VIEW: 'Afficher',
    };

    if (functionalities.length === 0) {
      return '❌';
    }
    if (
      functionalities.includes('CREATE') &&
      functionalities.includes('UPDATE') &&
      functionalities.includes('DELETE') &&
      functionalities.includes('VIEW')
    ) {
      return '✔️ TOUS';
    }
    return functionalities
      .map(func => translatedFunctionalities[func])
      .join(', ');
  };

  const rows = profiles.map((profile, index) => {
    const rowObj = { id: `profile-${index}`, Profile: profile.nameProfile };
    Object.entries(columnNamesMap).forEach(([moduleEnum, moduleName]) => {
      const functionalities = profile.moduleFunctionalities[moduleEnum] || [];
      rowObj[moduleName] = transformFunctionalities(functionalities);
    });
    return rowObj;
  });

  const columns = [
    { field: 'Profile', headerName: 'Profile', flex: 1 },
    ...Object.values(columnNamesMap).map(moduleName => ({
      field: moduleName,
      headerName: moduleName,
      flex: 1,
      renderCell: rowData => {
        if (moduleName === columnNamesMap.ACTIONS) {
          const profileIndex = rowData.id.split('-')[1];
          const profileId = profiles[profileIndex].id;
          const profileName = profiles[profileIndex].nameProfile;

          return (
            <div style={{ textAlign: 'center' }}>
              {profileName !== 'Master Admin' && (
                <>
                  <input
                    type="image"
                    onClick={() => handleEdit(profileId)}
                    className="p-2"
                    src={EDIT_ICON}
                    width="40px"
                    height="40px"
                    title="modifier"
                  />
                  <input
                    type="image"
                    src={DELETE_ICON}
                    className="p-2"
                    width="40px"
                    height="40px"
                    title="supprimer"
                    onClick={() => handleDeleteProfile(profileId)}
                  />
                </>
              )}
            </div>
          );
        }
        return rowData[moduleName];
      },
    })),
  ];

  return (
    <div className="table-container">
      <Alert
        show={showSuccessMessage}
        className="alert-style"
        variant="success"
        onClose={() => setShowSuccessMessage(false)}
        dismissible
      >
        {successMessage}
      </Alert>
      <DataTable
        rows={rows}
        columns={columns}
        className="custom-table"
        totalElements={liste1.totalElements}
        numberOfElements={liste1.numberOfElements}
        pageable={liste1.pageable}
      />
      <Modal2
        centered
        className="mt-5"
        title="Confirmation de suppression"
        show={showDeleteModal}
        handleClose={() => setShowDeleteModal(false)}
      >
        <p className="mt-1 mb-5 px-2">
          Êtes-vous sûr de vouloir supprimer ce profil ?
        </p>
        <div className="d-flex justify-content-end px-2 my-1 ">
          <button
            type="button"
            className="btn-style outlined"
            onClick={() => setShowDeleteModal(false)}
          >
            Annuler
          </button>
          <button
            type="submit"
            className={`ml-3 btn-style primary`}
            onClick={confirmDelete}
          >
            Supprimer
          </button>
        </div>
      </Modal2>
    </div>
  );
};

ListProfiles.propTypes = {
  profiles: PropTypes.array.isRequired,
  handleDelete: PropTypes.func.isRequired,
};

export default ListProfiles;
