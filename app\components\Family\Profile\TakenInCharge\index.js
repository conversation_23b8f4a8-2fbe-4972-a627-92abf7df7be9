import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import eye from 'images/icons/eye.svg';
import styles from '../../../../Css/profileList.css';
import stylesList from '../../../../Css/profileList.css';
import tagStyles from '../../../../Css/tag.css';
import plus from '../../../../images/icons/plus.svg';
import minus from '../../../../images/icons/minus.svg';
import Details from '../../../Beneficiary/Profile/OperationsDetail';
import { hasRoleAssistant } from '../../../../utils/hasAccess';

export default function TakenInCharge(props) {
  const family = props.data;
  let listBeneficiariesTakenIncharges = [];
  let Beneficiaries = [];
  let totalPlanned = 0;
  let totalExecuted = 0;
  let totalclosed = 0;
  let serviceName = '-';
  const isRoleAssistant = hasRoleAssistant();
  const [showDetails, setShowDetails] = useState({});

  const toggleShowDetails = id => {
    setShowDetails(prevState => ({
      ...prevState,
      [id]: !prevState[id],
    }));
  };

  if (family) {
    family.familyMembers.map(member => {
      if (
        member.person.beneficiary &&
        member.person.beneficiary.archived !== true
      ) {
        Beneficiaries = Beneficiaries.concat(member.person.beneficiary);
      }
    });
  }

  if (Beneficiaries) {
    Beneficiaries.map(beneficiary => {
      const BeneficiaryTakenIncharges = beneficiary.takenInChargeBeneficiaries;
      if (BeneficiaryTakenIncharges) {
        const sortedBeneficiaryTakenIncharges = [
          ...BeneficiaryTakenIncharges,
        ].sort((a, b) => (a.id < b.id ? 1 : -1));

        const listBeneficiaryTakenIncharges = sortedBeneficiaryTakenIncharges.map(
          takenInCharge => {
            totalclosed = 0;
            totalPlanned = 0;
            totalExecuted = 0;

            if (
              takenInCharge.takenInCharge &&
              takenInCharge.takenInCharge.services
            ) {
              serviceName = takenInCharge.takenInCharge.services.name;
            }

            takenInCharge.takenInCharge.takenInChargeDonors &&
              takenInCharge.takenInCharge.takenInChargeDonors[0].takenInChargeOperations.map(
                operation => {
                  if (operation.planningDate && !operation.executionDate) {
                    totalPlanned += operation.amount;
                  } else if (
                    operation.executionDate &&
                    !operation.closureDate
                  ) {
                    totalExecuted += operation.amount;
                  } else if (operation.closureDate) {
                    totalclosed += operation.amount;
                  }
                },
              );
            const getTag = status => {
              if (status === 'attente') {
                return tagStyles.tagRed;
              }
              if (status === 'Actif') {
                return tagStyles.tagGreen;
              }
              if (status === 'suspendue') {
                return tagStyles.tagYellow;
              }
              if (status === 'Inactif') {
                return tagStyles.tagRed;
              }
              return tagStyles.tagGrey;
            };

            let donorName = <span>-</span>;
            if (
              takenInCharge.takenInCharge.takenInChargeDonors[0] &&
              takenInCharge.takenInCharge.takenInChargeDonors[0].donor.type ===
                'Physique'
            ) {
              donorName = isRoleAssistant ? (
                `${takenInCharge.takenInCharge.takenInChargeDonors[0].donor.firstName} ${takenInCharge.takenInCharge.takenInChargeDonors[0].donor.lastName}`
              ) : (
                <Link
                  to={`/donors/fiche/${takenInCharge.takenInCharge.takenInChargeDonors[0].donor.id}/info`}
                >
                  {`${takenInCharge.takenInCharge.takenInChargeDonors[0].donor.firstName} ${takenInCharge.takenInCharge.takenInChargeDonors[0].donor.lastName}`}
                </Link>
              );
            } else if (
              takenInCharge.takenInCharge.takenInChargeDonors[0] &&
              takenInCharge.takenInCharge.takenInChargeDonors[0].donor.type ===
                'Moral'
            ) {
              donorName = isRoleAssistant ? (
                takenInCharge.takenInCharge.takenInChargeDonors[0].donor.company
              ) : (
                <Link
                  to={`/donors/fiche/${takenInCharge.takenInCharge.takenInChargeDonors[0].donor.id}/info`}
                >
                  {
                    takenInCharge.takenInCharge.takenInChargeDonors[0].donor
                      .company
                  }
                </Link>
              );
            } else if (
              takenInCharge.takenInCharge.takenInChargeDonors[0] &&
              takenInCharge.takenInCharge.takenInChargeDonors[0].donor.type ===
                'Anonyme'
            ) {
              donorName = isRoleAssistant ? (
                takenInCharge.takenInCharge.takenInChargeDonors[0].donor.name
              ) : (
                <Link
                  to={`/donors/fiche/${takenInCharge.takenInCharge.takenInChargeDonors[0].donor.id}/info`}
                >
                  {
                    takenInCharge.takenInCharge.takenInChargeDonors[0].donor
                      .name
                  }
                </Link>
              );
            }

            const value = takenInCharge.takenInCharge.takenInChargeDonors[0].donor.donations.reduce(
              (a, curr) => a + curr.value,
              0,
            );
            return (
              <>
                <tr key={takenInCharge.id}>
                  <td>
                    <button
                      type="button"
                      onClick={() => toggleShowDetails(takenInCharge.id)}
                      className="bg-transparent"
                    >
                      <img
                        src={showDetails[takenInCharge.id] ? minus : plus}
                        width="20px"
                        height="20px"
                        alt={showDetails[takenInCharge.id] ? 'minus' : 'plus'}
                      />
                    </button>
                  </td>
                  <td className="col-md-1">{takenInCharge.id}</td>
                  <td className="col-md-1">
                    {takenInCharge.takenInCharge.services
                      ? takenInCharge.takenInCharge.services.name
                      : '--'}
                  </td>
                  <td className="col-md-2">
                    {beneficiary.person.firstName} {beneficiary.person.lastName}
                  </td>
                  <td className="col-md-1">{donorName}</td>
                  <td className="col-md-1">
                    <div
                      className={`${
                        takenInCharge.takenInCharge.status
                          ? getTag(takenInCharge.takenInCharge.status)
                          : ''
                      }`}
                    >
                      {takenInCharge.takenInCharge.status
                        ? takenInCharge.takenInCharge.status
                        : '--'}
                    </div>
                  </td>
                  {/*<td className="col-md-1">{value}</td>*/}
                  <td className="col-md-1">{totalPlanned}</td>
                  <td className="col-md-1">{totalExecuted}</td>
                  <td className="col-md-1">{totalclosed}</td>
                  {!isRoleAssistant && (
                    <td className="col-md-1">
                      <Link
                        to={`/takenInCharges/fiche/${takenInCharge.takenInCharge.id}/info`}
                      >
                        <img
                          src={eye}
                          width="20px"
                          height="20px"
                          className="ml-3"
                          data-dismiss="modal"
                          alt="rightArrow"
                        />
                      </Link>
                    </td>
                  )}
                </tr>
                {showDetails[takenInCharge.id] && (
                  <td colSpan={11} className="border-top-0">
                    <Details data={takenInCharge} target="family" />
                  </td>
                )}
              </>
            );
          },
        );
        listBeneficiariesTakenIncharges = listBeneficiariesTakenIncharges.concat(
          listBeneficiaryTakenIncharges,
        );
      }
    });
  }

  return (
    <div>
      <div className={`pb-5 ${stylesList.backgroudStyle}`}>
        <div>
          <div className={styles.global}>
            <div className={styles.header}>
              <h4>Liste des Kafalats</h4>
            </div>

            <div className={styles.content}>
              <table className="table small">
                <thead>
                  <tr>
                    <th scope="col"></th>
                    <th scope="col">Numéro</th>
                    <th scope="col">Service</th>
                    <th scope="col">Bénéficiaire</th>
                    <th scope="col">Donateur</th>
                    <th scope="col">Statut</th>
                    {/*<th scope="col">Total donation</th>*/}
                    <th scope="col">Total Planifié</th>
                    <th scope="col">Total Executé</th>
                    <th scope="col">Total Clôturé</th>
                    {!isRoleAssistant && (
                      <th style={{ whiteSpace: 'nowrap' }}>Actions</th>
                    )}
                  </tr>
                </thead>
                <tbody>
                  {listBeneficiariesTakenIncharges.length === 0 ? (
                    <tr>
                      <td colSpan="11" className="text-center">
                        Aucune prise en charge
                      </td>
                    </tr>
                  ) : (
                    listBeneficiariesTakenIncharges
                  )}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
