import React, { useEffect, useState } from 'react';
import moment from 'moment';
import { Link, useHistory } from 'react-router-dom';
import listStyle from 'Css/profileList.css';
import btnStyles from 'Css/button.css';
import AddDonation from 'containers/Donation/AddDonation';
import Modal2 from 'components/Common/Modal2';
import styled from 'styled-components';
import {
  deleteDonation,
  resetDonation,
  resetDelete,
} from 'containers/Donation/AddDonation/actions';

import {
  makeSelectDeleteSuccess,
  makeSelectDonation,
  makeSelectError,
  makeSelectSuccess,
} from 'containers/Donation/AddDonation/selectors';

import { createStructuredSelector } from 'reselect';
import { useDispatch, useSelector } from 'react-redux';
import { Alert } from 'react-bootstrap';
import { useInjectReducer, useInjectSaga } from 'redux-injectors';

import donationListSaga from 'containers/Donation/Donations/saga';
import donationsReducer from 'containers/Donation/Donations/reducer';
import donationSaga from 'containers/Donation/AddDonation/saga';
import AccessControl from 'utils/AccessControl';
import DataTable from '../../Common/DataTable';
import {
  DELETE_ICON,
  EDIT_ICON,
  VIEW_ICON,
} from '../../Common/ListIcons/ListIcons';

const omdbSelector = createStructuredSelector({
  success: makeSelectSuccess,
  donation: makeSelectDonation,
  successDelete: makeSelectDeleteSuccess,
  error: makeSelectError,
});

const keyDonationList = 'donationList';
const keyDonationDelete = 'donationAdd';

// Styled component for the tags container in the table
const TagsContainer = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  max-width: 200px;
`;

// Styled component for individual tags in the table
const TableTag = styled.div`
  padding: 2px 8px;
  font-size: 11px;
  white-space: nowrap;
  border-radius: 10px;
  background-color: ${props => `#${props.color || 'cccccc'}`};
  color: ${props => {
    const hex = props.color || 'cccccc';
    const r = parseInt(hex.substr(0, 2), 16);
    const g = parseInt(hex.substr(2, 2), 16);
    const b = parseInt(hex.substr(4, 2), 16);
    const brightness = (r * 299 + g * 587 + b * 114) / 1000;
    return brightness > 128 ? '#000000' : '#ffffff';
  }};
`;
export default function DonationForm(props) {
  let listDonations;

  const liste1 = props.listesGlobal;

  const formatDate = date => moment(date).format('DD/MM/YYYY');

  useInjectReducer({
    key: keyDonationList,
    reducer: donationsReducer,
  });
  useInjectSaga({ key: keyDonationList, saga: donationListSaga });

  useInjectSaga({ key: keyDonationDelete, saga: donationSaga });

  const dispatch = useDispatch();
  const { error } = useSelector(omdbSelector);
  const history = useHistory();
  const [show, setShow] = useState(false);
  const [showAlert, setShowAlert] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [message, setMessage] = useState('');
  const [donationToEdit, setDonationToEdit] = useState('');
  const [donationToDelete, setDonationToDelete] = useState('');
  const [errorMessage, setErrorMessage] = useState(null);

  useEffect(
    () =>
      function cleanup() {
        dispatch(resetDonation());
      },
    [],
  );


  const viewHandler = id => {
    history.push(`/donations/fiche/${id}/info`, { params: id });
  };

  const handleClose = () => {
    setShow(false);
  };

  const handleShow = () => {
    setShow(true);
  };

  const handleCloseForDeleteModal = () => setShowDeleteModal(false);

  let donor = null;

  if (props.donations) {
    let { donations } = props;

    donor = {
      id: donations.id,
      firstName: donations.firstName,
      lastName: donations.lastName,
      company: donations.company,
      type: donations.type,
      logoUrl: donations.logoUrl,
      logo64: donations.logo64,
      activitySector: donations.activitySector,
    };

    if (props.profile) {
      donations = props.donations.donations;
    }

    const donorName = <span>-</span>;

    const donationsSorted = [...donations].sort((a, b) =>
      a.createdAt < b.createdAt ? 1 : -1,
    );
    // a voir
    listDonations = donationsSorted.map(donation => ({
      id: donation.id,
      code: donation.code,

      receptionDate: donation.receptionDate
        ? formatDate(donation.receptionDate)
        : '-',
      donorName: donorName || <span>-</span>,
      identifiedDonor: donation.donor ? donation.donor.type : '-',

      canalDonation:
        donation.canalDonation && donation.canalDonation.name
          ? donation.canalDonation.name
          : '-',
      type: donation.type,
      donations: props,
      archived: donation.archived,
      donor: donation.donor,
      value: donation.value,
      tags: donation.tags || [],
    }));
  }
  const columns = [
    {
      field: 'code',
      headerName: 'Code donation',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
    {
      field: 'receptionDate',
      headerName: 'Date de réception',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
    {
      field: 'donorName',
      headerName: 'Donateur',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
      renderCell: params => {
        const donation = params.row;
        let donorName = '';
        if (donation.archived !== true) {
          if (!donation.donations.profile) {
            if (donation.donor && donation.donor.type === 'Physique') {
              donorName = (
                <Link to={`/donors/fiche/${donation.donor.id}/info`}>
                  {`${donation.donor.firstName} ${donation.donor.lastName}`}
                </Link>
              );
            } else if (donation.donor && donation.donor.type === 'Moral') {
              donorName = (
                <Link to={`/donors/fiche/${donation.donor.id}/info`}>
                  {donation.donor.company}
                </Link>
              );
            } else if (donation.donor && donation.donor.type === 'Anonyme') {
              donorName = (
                <Link to={`/donors/fiche/${donation.donor.id}/info`}>
                  {donation.donor.name}
                </Link>
              );
            }
          }
        }
        return donorName || '-';
      },
    },
    {
      field: 'ArabicName',
      headerName: 'المتبرع ',
      flex: 1,
      headerAlign: 'center',
      align: 'center',

      renderCell: params => {
        const donation = params.row;
        let donorName = '';

        if (donation.archived !== true) {
          if (!donation.donations.profile) {
            if (donation.identifiedDonor) {
              if (donation.donor && donation.donor.type === 'Physique') {
                donorName = (
                  <Link to={`/donors/fiche/${donation.donor.id}/info`}>
                    {`${donation.donor.firstNameAr} ${donation.donor.lastNameAr}`}
                  </Link>
                );
              } else if (donation.donor && donation.donor.type === 'Moral') {
                donorName = (
                  <Link to={`/donors/fiche/${donation.donor.id}/info`}>
                    -----
                  </Link>
                );
              }
            } else {
              donorName = donation.unidentifiedDonorName;
            }
          }
        }
        return donorName || '-';
      },
    },
    {
      field: 'identifiedDonor',
      headerName: 'Type Donateur',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
    {
      field: 'value',
      headerName: 'Montant Total (DH)',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
    {
      field: 'canalDonation',
      headerName: 'Canal',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
    {
      field: 'type',
      headerName: 'Type',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
    {
      field: 'tags',
      headerName: 'Tags',
      flex: 1.5,
      headerAlign: 'center',
      align: 'center',
      renderCell: params => {
        const tags = params.row.tags || [];
        return (
          <TagsContainer>
            {tags && tags.length > 0 ? (
              tags.map(tag => (
                <TableTag key={tag.id} color={tag.color || 'cccccc'}>
                  {tag.name}
                </TableTag>
              ))
            ) : (
              <span style={{ color: '#999', fontSize: '12px' }}>Aucun tag</span>
            )}
          </TagsContainer>
        );
      },
    },
    {
      field: 'actions',
      headerName: 'Actions',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
      renderCell: params => (
        <div>
          <input
            type="image"
            onClick={() => viewHandler(params.row.id)}
            className="p-0 mr-1"
            src={VIEW_ICON}
            width="20px"
            height="20px"
            title="consulter"
          />

          {!props.profile ? (
            <AccessControl module="DONATION" functionality="UPDATE">
              <Link to={{ pathname: `/donations/edit/${params.row.id}` }}>
                <input
                  type="image"
                  src={EDIT_ICON}
                  className="p-0 mr-1"
                  width="20px"
                  height="20px"
                  title="Modifier"
                  onClick={() => {
                    const row = params.row.donation;
                    setDonationToEdit(params.row);
                    handleShow();
                  }}
                />
              </Link>
            </AccessControl>
          ) : null}

          {!props.profile ? (
            <AccessControl module="DONATION" functionality="DELETE">
              <input
                type="image"
                src={DELETE_ICON}
                className="p-0"
                width="20px"
                height="20px"
                title="Supprimer"
                onClick={() => {
                  setDonationToDelete(params.row);
                  setShowDeleteModal(true);
                }}
              />
            </AccessControl>
          ) : null}
        </div>
      ),
    },
  ];

  return (
    <div>
      {showAlert ? (
        <Alert
          className="alert-style"
          variant="success"
          onClose={() => setShowAlert(false)}
          dismissible
        >
          <p>{message}</p>
        </Alert>
      ) : null}

      {errorMessage ? (
        <Alert
          className="alert-style"
          variant="danger"
          onClose={() => setErrorMessage(null)}
          dismissible
        >
          <p>{errorMessage}</p>
        </Alert>
      ) : null}

      <div className={` ${!props.profile ? 'p-0' : listStyle.backgroudStyle}`}>
        <Modal2
          title={
            donationToEdit ? 'Modifier la donation' : 'Ajouter une donation'
          }
          size="lg"
          customWidth="modal-90w"
          show={show}
          handleClose={() => {
            setShow(false);
          }}
        >
          <AddDonation
            handleClose={handleClose}
            edit={!!donationToEdit}
            donor={donor || null}
            profile
            donation={donationToEdit}
          />
        </Modal2>

        <Modal2
          centered
          className="mt-5"
          title="Confirmation de suppression"
          show={showDeleteModal}
          handleClose={handleCloseForDeleteModal}
        >
          <p className="mt-1 mb-5">
            Êtes-vous sûr de vouloir supprimer cette donation?
          </p>
          <div className="d-flex justify-content-end px-3 my-1">
            <button
              type="button"
              className={`mx-2 btn-style outlined`}
              onClick={handleCloseForDeleteModal}
            >
              Annuler
            </button>
            <button
              type="submit"
              className={`mx-2 btn-style primary`}
              onClick={() => {
                dispatch(deleteDonation(donationToDelete));
                setDonationToDelete('');
                handleCloseForDeleteModal();
              }}
            >
              Supprimer
            </button>
          </div>
        </Modal2>

        {props.profile ? (
          <div className={listStyle.global}>
            <div className={listStyle.header}>
              <h4>Liste des Donations</h4>
            </div>
          </div>
        ) : null}

        <div>
          <DataTable
            rows={listDonations}
            columns={columns}
            fileName={`Liste des Donations , ${new Date().toLocaleString()}`}
            totalElements={liste1 ? liste1.totalElements : 0}
            numberOfElements={liste1 ? liste1.numberOfElements : 0}
            pageable={liste1 ? liste1.pageable : 0}
          />
        </div>
      </div>
    </div>
  );
}
