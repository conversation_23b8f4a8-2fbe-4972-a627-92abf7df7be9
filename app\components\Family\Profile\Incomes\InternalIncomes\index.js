import React, { useState } from 'react';
import moment from 'moment';
import Details from './Details';

export default function InternalIncomes(props) {
  const family = props.data;
  let totalOperations = [];
  const beneficiaries = [];
  const operations = [];

  const mois = [
    'Jan<PERSON>',
    '<PERSON><PERSON>vrier',
    '<PERSON>',
    'A<PERSON><PERSON>',
    '<PERSON>',
    'Juin',
    '<PERSON><PERSON><PERSON>',
    'Aout',
    'Septembre',
    'Octobre',
    'Novembre',
    'Décembre',
  ];

  // Helper functions to extract month and year from a date
  const getMonth = date => moment(date).month();
  const getYear = date => moment(date).year();

  // Collecting beneficiaries from family data
  if (family) {
    family.familyMembers.forEach(member => {
      const beneficiary = member.person.beneficiary;
      if (beneficiary && !beneficiary.archived) {
        beneficiaries.push([beneficiary, member.familyRelationship]);
      }
    });
  }

  // Collecting operations for each beneficiary
  beneficiaries.forEach(beneficiary => {
    const takenIncharges = beneficiary[0].takenInChargeBeneficiaries || [];
    takenIncharges.forEach(takenInCharge => {
      const donors = takenInCharge.takenInCharge.takenInChargeDonors;
      if (donors) {
        donors[0].takenInChargeOperations.forEach(operation => {
          if (operation.executionDate) {
            operations.push([
              operation,
              getMonth(operation.executionDate),
              getYear(operation.executionDate),
              beneficiary,
              takenInCharge.takenInCharge,
            ]);
          }
        });
      }
    });
  });

  // Sorting operations by year and month
  const sortedOperations = operations.sort(
    (a, b) => b[2] - a[2] || a[1] - b[1],
  );
  const operationsByYearAndMonth = sortedOperations.reduce((acc, curr) => {
    acc[curr[2]] = acc[curr[2]] || [];
    acc[curr[2]].push(curr);
    return acc;
  }, {});

  const filteredOperations = Object.values(operationsByYearAndMonth).map(op =>
    op.reduce((acc, curr) => {
      acc[curr[1]] = acc[curr[1]] || [];
      acc[curr[1]].push(curr);
      return acc;
    }, {}),
  );

  // Generating total operations list for rendering
  filteredOperations.forEach(operationsByYear => {
    const list = Object.values(operationsByYear).map(operationsByMonth => {
      let amount = 0;
      const count = new Set();
      let month = null;
      let year = null;

      operationsByMonth.forEach(operation => {
        amount += operation[0].amount;
        count.add(operation[3]);
        if (month === null && year === null) {
          month = operation[1];
          year = operation[2];
        }
      });

      const [show, setShow] = useState(false);
      const handleViewPlanning = () => setShow(!show);

      return (
        <React.Fragment key={`${year}-${month}`}>
          <tr>
            <td>
              <span
                title={!show ? 'Voir détails' : 'Cacher détails'}
                onClick={handleViewPlanning}
                className="toggle-button"
                role="button" // Makes it accessible as a button
                tabIndex={0} // Makes it focusable
                onKeyPress={e => {
                  if (e.key === 'Enter') {
                    handleViewPlanning();
                  }
                }} // Allows enter key to trigger the click
                style={{
                  cursor: 'pointer',
                  backgroundColor: 'lightblue',
                  padding: '5px',
                  borderRadius: '5px',
                }}
              >
                <i className={`fas fa-arrow-${show ? 'up' : 'down'}`} />
              </span>
            </td>

            <td
              style={{
                width: '100px',
                borderBottom: '1px solid #dee2e6',
                padding: '8px',
              }}
            >
              {mois[month]}
            </td>
            <td
              style={{
                width: '100px',
                borderBottom: '1px solid #dee2e6',
                padding: '8px',
              }}
            >
              {year}
            </td>
            <td
              style={{
                width: '150px',
                borderBottom: '1px solid #dee2e6',
                padding: '8px',
              }}
            >
              {amount}
            </td>
            <td
              style={{
                width: '200px',
                textAlign: 'center',
                borderBottom: '1px solid #dee2e6',
                padding: '8px',
              }}
            >
              <span>{count.size}</span>
            </td>
          </tr>
          {show && (
            <tr>
              <td colSpan={5} style={{ borderTop: 'none' }}>
                <Details data={operationsByMonth} />
              </td>
            </tr>
          )}
        </React.Fragment>
      );
    });

    totalOperations = totalOperations.concat(list);
  });

  return (
    <div style={{ paddingBottom: '40px' }}>
      <div style={{ maxWidth: '1200px', margin: '0 auto', padding: '20px' }}>
        <table
          style={{
            width: '100%',
            borderCollapse: 'collapse',
            boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
          }}
        >
          <thead>
            <tr
              style={{
                backgroundColor: '#f8f9fa',
                borderBottom: '1px solid #dee2e6',
                textAlign: 'left',
                fontWeight: '100',
              }}
            >
              <th
                scope="col"
                style={{ width: '50px', borderBottom: '1px solid #dee2e6' }}
              />
              <th
                scope="col"
                style={{
                  width: '100px',
                  borderBottom: '1px solid #dee2e6',
                  padding: '8px',
                }}
              >
                Mois
              </th>
              <th
                scope="col"
                style={{
                  width: '100px',
                  borderBottom: '1px solid #dee2e6',
                  padding: '8px',
                }}
              >
                Année
              </th>
              <th
                scope="col"
                style={{
                  width: '150px',
                  borderBottom: '1px solid #dee2e6',
                  padding: '8px',
                }}
              >
                Total Revenu
              </th>
              <th
                scope="col"
                style={{
                  width: '200px',
                  borderBottom: '1px solid #dee2e6',
                  padding: '8px',
                }}
              >
                Nombre de personnes impliquées
              </th>
            </tr>
          </thead>
          <tbody>
            {totalOperations.length === 0 ? (
              <tr>
                <td
                  colSpan={5}
                  style={{ textAlign: 'center', padding: '20px' }}
                >
                  Aucune source de revenus
                </td>
              </tr>
            ) : (
              totalOperations
            )}
          </tbody>
        </table>
      </div>
    </div>
  );
}
