import { call, put, takeLatest } from 'redux-saga/effects';
import request from '../../../../../utils/request';
import { memberAdded, memberAddingError } from './actions';
import { ADD_FAMILY_MEMBER } from './constants';
import { serialize } from '../../../../../containers/Common/FormDataConverter';

export function* addMember({ search }) {
  const url = '/family-member';

  try {
    let formData = new FormData();

    formData = serialize(search, { indices: true, nullsAsUndefineds: false });
    const formData2 = new FormData();
    for (const par of formData.entries()) {
      if (par[1]) {
        if (par[0].includes('Date')) {
          formData2.append(par[0], new Date(par[1]));
        } else {
          formData2.append(par[0], par[1]);
        }
      }
    }
    const { data } = yield call(request.post, url, formData2);
    yield put(memberAdded(data));
  } catch (error) {
    yield put(memberAddingError(error));
  }
}

export function* addMemberSaga() {
  yield takeLatest(ADD_FAMILY_MEMBER, addMember);
}
