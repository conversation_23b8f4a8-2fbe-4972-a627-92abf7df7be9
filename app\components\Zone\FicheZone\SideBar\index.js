import React from 'react';
import { Link } from 'react-router-dom';
import sideBarStyles from '../../../../Css/sideBar.css';
import { useHistory } from 'react-router-dom';
import tagStyles from '../../../../Css/tag.css';
const rightArrow = require('../../../../images/icons/right-arrow.svg');
const SideBar = props => {
  const zone = props.data;
  const eps = props.data.eps;
  const history = useHistory();
  console.log('info of zone : ',props);

  const getTag = () => {
    // Ensure that zone.status exists and is a boolean
    if (zone.status === true) {
      return tagStyles.tagGreen;  // Return green tag if status is true
    }
    return tagStyles.tagRed;  // Return red tag if status is false
  };
  console.log('zone : ',zone);

  let top = null;
  let text = null;
  if (zone) {
top = (
  <div className={sideBarStyles.topCard}>
    <div className={sideBarStyles.top}>
      <h5> Zone </h5>
      <p> {zone.code}  - {zone.name} </p>
      {zone.status !== undefined && (
        <div className={getTag()}>
          {/* Render 'Actif' if status is true, 'Inactive' if false */}
          {zone.status ? 'Actif' : 'Inactif'}
        </div>
      )}
    </div>
  </div>
);

  text = (
      <div>
        <p>
          <span className={sideBarStyles.value}>{zone.name}</span>
        </p>
        <p>
          <span className={sideBarStyles.value}>{zone.nameAr}</span>
        </p>
        <p>
          <span className={sideBarStyles.value}>{zone.code}</span>
        </p>
        {eps!=null &&(<p style={{ color: "blue", textDecoration: "underline", cursor: "pointer", fontSize: "0.9rem" }}> 
          <Link to={`/eps/fiche/${zone.epsId}/info`}>
          {(zone.epsId != null?eps.name:'')}
          </Link>
        </p>)}
        
        <p>
          <span className={sideBarStyles.value}>{zone.cityDetails?zone.cityDetails.map((city) => city.name).join(', '):'-'}</span>
        </p>
        <p>
          <span className={sideBarStyles.value}>{zone.details?zone.details:'-'}</span>
        </p>
        

      </div>
  );
}

  return (
    <>
      <div className={sideBarStyles.sideBar}>
        {top}
        {/*<hr className={sideBarStyles.hr} />*/}
        <div className={`d-flex justify-content-center gap-30 mt-5`}>
          <div >
            <p> Nom  : </p>
            <p> Nom arabe : </p>
            <p> Code : </p>
            <p><span>{zone.epsId!=null?'Eps':''}</span></p>
            <p> Villes associées : </p>
            <p> Commentaire : </p>
           
          
          </div>
          {text}
        </div>

          <>
            <hr className={sideBarStyles.hr} />
            <div className={sideBarStyles.text}>
            <div className={`text-center ${sideBarStyles.label}`}>
            <h5 className="mb-3">Assistant actuelle </h5>
            {zone.assistantId  ? (
            <p className={sideBarStyles.linkDonor}>
                <Link to={`/assistants/consultAssistant/${zone.assistantId}/1`}>
                  <p className={sideBarStyles.linkDonor}>
                    {zone.assistantName}
                    <img
                  src={rightArrow}
                  width="20px"
                  height="20px"
                  className="ml-3"
                  alt="Right Arrow"
                />
                  </p>
                </Link>

                </p>
            ) : (
              <p> -------</p>
            )}


            </div>
            </div>
          </>

      </div>
    </>
  );
};

export default SideBar;
