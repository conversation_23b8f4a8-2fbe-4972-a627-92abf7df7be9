import produce from 'immer';
import {
  LOAD_CAISSE_CHART_DETAILS_ERROR,
  LOAD_CAISSE_CHART_DETAILS_SUCCESS,
  LOAD_CAISSE_CHART_DETAILS,
} from './constants';

export const initialState = {
  loading: false,
  error: false,
  caisseChartDetails: false,
};

/* eslint-disable default-case, no-param-reassign */
const caisseChartReducer = produce((draft, action) => {
  switch (action.type) {
    case LOAD_CAISSE_CHART_DETAILS:
      draft.loading = true;
      draft.error = false;
      draft.caisseChartDetails = false;
      break;
    case LOAD_CAISSE_CHART_DETAILS_SUCCESS:
      draft.loading = false;
      draft.error = false;
      draft.caisseChartDetails = action.caisseChartDetails;
      break;
    case LOAD_CAISSE_CHART_DETAILS_ERROR:
      draft.loading = false;
      draft.error = action.error;
      break;
  }
}, initialState);

export default caisseChartReducer;
