import { call, put, takeLatest } from 'redux-saga/effects';
import request from '../../../../../utils/request';
import { responsableAdded, responsableAddingError } from './actions';
import { ADD_EPS_RESPONSABLE } from './constants';
import { serialize } from '../../../../../containers/Common/FormDataConverter';

export function* addResponsable({ search }) {
  const url = '/responsable-eps';

  try {
    let formData = new FormData();

    formData = serialize(search, { indices: true, nullsAsUndefineds: false });
    const formData2 = new FormData();
    for (const par of formData.entries()) {
      if (par[1]) { 
          formData2.append(par[0], par[1]); 
      }
    }
    const { data } = yield call(request.post, url, formData2);
    yield put(responsableAdded(data));
  } catch (error) {
    yield put(responsableAddingError(error));
  }
}

export default function* omdbSaga() {
  yield takeLatest(ADD_EPS_RESPONSABLE, addResponsable);
}
