import React, { useEffect, useState } from 'react';
import { Form, Formik } from 'formik';
import { useDispatch, useSelector } from 'react-redux';
import { Button, Box, Grid, Typography } from '@mui/material';
import { addTagRequest, loadTypeTags } from './actions';
import { makeSelectSuccess, makeSelectTypeTags } from './selectors';
import { CustomSelect } from 'containers/Common/CustomInputs/CustomSelect';
import { CustomTextInput } from 'containers/Common/CustomInputs/CustomTextInput';
import * as Yup from 'yup';
import {
  invalidString,
  maxDate,
  maxString,
  minString,
  requiredString,
} from 'containers/Common/ValidationSchemas/ErrorMessages';
import styled from 'styled-components';

// Styled component for tag preview
const TagPreview = styled.div`
  background-color: ${props => props.color};
  color: ${props => {
    // Calculate if text should be black or white based on background color brightness
    const hex = props.color.replace('#', '');
    const r = parseInt(hex.substring(0, 2), 16);
    const g = parseInt(hex.substring(2, 2), 16);
    const b = parseInt(hex.substring(4, 2), 16);
    const brightness = (r * 299 + g * 587 + b * 114) / 1000;
    return brightness > 128 ? '#000000' : '#ffffff';
  }};
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  display: inline-block;
  margin-top: 10px;
  margin-bottom: 15px;
  border: 1px solid #e0e0e0;
`;

const PreviewContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 20px;
  padding: 15px;
  border: 1px dashed #ccc;
  border-radius: 8px;
  background-color: #f9f9f9;
`;

const TagForm = ({ handleClose, tagToEdit }) => {
  const dispatch = useDispatch();
  const success = useSelector(makeSelectSuccess);
  const typeTags = useSelector(makeSelectTypeTags);
  const [isNewTypeTag, setIsNewTypeTag] = useState(false);

  useEffect(() => {
    dispatch(loadTypeTags());
  }, [dispatch]);

  const initialValues = tagToEdit ? {
    id: tagToEdit.id,
    name: tagToEdit.name,
    typeTagId: tagToEdit.typeTagId,
    typeTagName: tagToEdit.typeTagName,
    color: `#${tagToEdit.color}`,
    isNewTypeTag: false
  } : {
    name: '',
    typeTagId: '',
    typeTagName: '',
    color: '#000000',
    isNewTypeTag: false
  };

  const handleSubmit = (values, { setSubmitting, resetForm }) => {
    // Convert color from hex to string without #
    const formattedColor = values.color.replace('#', '');

    let tagData = {
      ...values,
      color: formattedColor
    };

    // If using an existing type tag
    if (!values.isNewTypeTag) {
      // Find the selected type tag to get its name
      const selectedTypeTag = typeTags && typeTags.content &&
        typeTags.content.find(typeTag => typeTag.id === Number(values.typeTagId));
      tagData.typeTagName = selectedTypeTag ? selectedTypeTag.name : '';
    } else {
      // If creating a new type tag, set typeTagId to null
      tagData.typeTagId = null;
      // typeTagName is already set from the input field
    }

    dispatch(addTagRequest(tagData));
    setSubmitting(false);
    resetForm();
    handleClose();
  };

  return (
    <Formik
      initialValues={initialValues}
      onSubmit={handleSubmit}
      validationSchema={Yup.object().shape({
        name: Yup.string().required(requiredString('Nom du tag')),
        typeTagId: Yup.string().when('isNewTypeTag', {
          is: false,
          then: Yup.string().required(requiredString('Type de tag')),
          otherwise: Yup.string()
        }),
        typeTagName: Yup.string().when('isNewTypeTag', {
          is: true,
          then: Yup.string().required(requiredString('Nom du type de tag')),
          otherwise: Yup.string()
        })
      })}
      enableReinitialize
    >
      {({ values, handleChange, handleBlur, isSubmitting, setFieldValue }) => {
        // Custom handler for type tag selection
        const handleTypeTagChange = (e) => {
          const typeTagId = e.target.value;
          setFieldValue('typeTagId', typeTagId);

          // Find the selected type tag to get its name
          const selectedTypeTag = typeTags && typeTags.content &&
            typeTags.content.find(typeTag => typeTag.id === Number(typeTagId));
          if (selectedTypeTag) {
            setFieldValue('typeTagName', selectedTypeTag.name);
          } else {
            setFieldValue('typeTagName', '');
          }
        };

        // Toggle between existing and new type tag
        const toggleTypeTagMode = (isNew) => {
          setFieldValue('isNewTypeTag', isNew);
          if (isNew) {
            setFieldValue('typeTagId', '');
          } else {
            setFieldValue('typeTagName', '');
          }
          setIsNewTypeTag(isNew);
        };

        return (
        <Form>
          <Box sx={{ p: 2 }}>
            <Box sx={{ mb: 2 }}>
              <div style={{ marginBottom: '15px' }}>
                <div style={{ display: 'flex', gap: '15px', marginBottom: '10px' }}>
                  <label style={{ display: 'flex', alignItems: 'center', cursor: 'pointer' }}>
                    <input
                      type="radio"
                      name="typeTagMode"
                      checked={!values.isNewTypeTag}
                      onChange={() => toggleTypeTagMode(false)}
                      style={{ marginRight: '5px' }}
                    />
                    Sélectionner un type existant
                  </label>
                  <label style={{ display: 'flex', alignItems: 'center', cursor: 'pointer' }}>
                    <input
                      type="radio"
                      name="typeTagMode"
                      checked={values.isNewTypeTag}
                      onChange={() => toggleTypeTagMode(true)}
                      style={{ marginRight: '5px' }}
                    />
                    Créer un nouveau type
                  </label>
                </div>
              </div>

              {!values.isNewTypeTag ? (
                <CustomSelect
                  isMeta={true}
                  unique={true}
                  label="Type de tag"
                  name="typeTagId"
                  value={values.typeTagId}
                  onChange={handleTypeTagChange}
                  onBlur={handleBlur}
                  isRequired
                >
                  <option value="">-- Type de Tag --</option>
                  {typeTags  && typeTags.filter(typeTag => !typeTag.readOnly).map((typeTag) => (
                    <option key={typeTag.id} value={typeTag.id}>
                      {typeTag.name}
                    </option>
                  ))}
                </CustomSelect>
              ) : (
                <CustomTextInput
                  name="typeTagName"
                  label="Nom du nouveau type de tag"
                  placeholder="Entrez le nom du nouveau type"
                  value={values.typeTagName}
                  onChange={handleChange}
                  onBlur={handleBlur}
                  isRequired
                />
              )}
            </Box>

            <Grid container spacing={2}>
              <Grid item xs={8}>
                <CustomTextInput
                  name="name"
                  label="Nom du tag"
                  placeholder="Nom du tag"
                  value={values.name}
                  onChange={handleChange}
                  onBlur={handleBlur}
                  isRequired
                />
              </Grid>
              <Grid item xs={4}>
                <CustomTextInput
                  name="color"
                  label="Couleur"
                  type="color"
                  value={values.color}
                  onChange={handleChange}
                  onBlur={handleBlur}
                  isRequired
                />
              </Grid>
            </Grid>

          {/* Tag Preview Section */}
          <PreviewContainer> 
            <TagPreview color={values.color}>
              {values.name || 'Nom du tag'}
            </TagPreview>
          </PreviewContainer>

            <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 2, mt: 2 }}>
              <button
              type='button'
              className="btn-style secondary"
                onClick={handleClose}
              >
                Annuler
              </button>
              <button
                type="submit"
                className="btn-style primary"
                disabled={isSubmitting}
              >
                {tagToEdit ? 'Modifier' : 'Ajouter'}
              </button>
            </Box>
          </Box>
        </Form>
        );
      }}
    </Formik>
  );
};

export default TagForm;