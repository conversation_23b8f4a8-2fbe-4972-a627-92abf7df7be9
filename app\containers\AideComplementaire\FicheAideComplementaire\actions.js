import {
  CLOSE_AIDE_COMPLEMENTAIRE_FAILURE,
  C<PERSON><PERSON>E_AIDE_COMPLEMENTAIRE_REQUEST,
  CLOSE_AIDE_COMPLEMENTAIRE_RESET,
  CLOSE_AIDE_COMPLEMENTAIRE_SUCCESS,
  EXECUTE_AIDE_COMPLEMENTAIRE_FAILURE,
  EXECUTE_AIDE_COMPLEMENTAIRE_REQUEST,
  EXECUTE_AIDE_COMPLEMENTAIRE_RESET,
  EXECUTE_AIDE_COMPLEMENTAIRE_SUCCESS,
  UNEXECUTE_AIDE_COMPLEMENTAIRE_FAILURE,
  UNEXECUTE_AIDE_COMPLEMENTAIRE_REQUEST,
  UNEXECUTE_AIDE_COMPLEMENTAIRE_RESET,
  UNEXECUTE_AIDE_COMPLEMENTAIRE_SUCCESS,
  FETCH_BUDGET_LINES_FAILURE,
  FETCH_BUDGET_LINES_REQUEST,
  FETCH_BUDGET_LINES_SUCCESS,
  GET_GROUPE_BENEFICIARIES_FAILURE,
  GET_GROUPE_BENEFICIARIES_REQUEST,
  GET_GROUPE_BENEFICIARIES_SUCCESS,
  GET_AIDE_BENEFICIARIES_FAILURE,
  GET_AIDE_BENEFICIARIES_REQUEST,
  GET_AIDE_BENEFICIARIES_SUCCESS,
  LOAD_AIDECOMPLEMENTAIRE,
  LOAD_AIDECOMPLEMENTAIRE_ERROR,
  LOAD_AIDECOMPLEMENTAIRE_SUCCESS,
  LOAD_BENEFICIARY_FOR_AIDECOMPLEMENTAIRE,
  LOAD_BENEFICIARY_FOR_AIDECOMPLEMENTAIRE_SUCCESS,
  LOAD_BENEFICIARY_FOR_AIDECOMPLEMENTAIREE_ERROR,
  LOAD_DONOR_FOR_AIDECOMPLEMENTAIRE,
  LOAD_DONOR_FOR_AIDECOMPLEMENTAIRE_SUCCESS,
  LOAD_DONOR_FOR_AIDECOMPLEMENTAIREE_ERROR,
  PROCESS_DONORS_BENEFICIARIES_ERROR,
  PROCESS_DONORS_BENEFICIARIES_REQUEST,
  PROCESS_DONORS_BENEFICIARIES_RESET,
  PROCESS_DONORS_BENEFICIARIES_SUCCESS,
  PROCESS_DONORS_ERROR,
  PROCESS_DONORS_REQUEST,
  PROCESS_DONORS_RESET,
  PROCESS_DONORS_SUCCESS,
  PUSH_ACTION_DONATION,
  PUSH_DOCUMENT_DONATION,
  PUSH_NOTE_DONATON,
  PUSH_PRODUCT_DONATION,
  REMOVE_ACTION_DONATION,
  REMOVE_BENEFICIARY_FAILURE,
  REMOVE_BENEFICIARY_REQUEST,
  REMOVE_BENEFICIARY_RESET,
  REMOVE_BENEFICIARY_SUCCESS,
  REMOVE_DOCUMENT_DONATION,
  REMOVE_DONOR_FAILURE,
  REMOVE_DONOR_REQUEST,
  REMOVE_DONOR_RESET,
  REMOVE_DONOR_SUCCESS,
  REMOVE_NOTE_DONATION,
  REMOVE_PRODUCT_DONATION,
  RESERVE_ALL_BUDGET_LINES_FAILURE,
  RESERVE_ALL_BUDGET_LINES_REQUEST,
  RESERVE_ALL_BUDGET_LINES_RESET,
  RESERVE_ALL_BUDGET_LINES_SUCCESS,
  UPDATE_ACTION_DONATION,
  UPDATE_DOCUMENT_DONATION,
  UPDATE_MONTANT_BENEFICIARY_FAILURE,
  UPDATE_MONTANT_BENEFICIARY_REQUEST,
  UPDATE_MONTANT_BENEFICIARY_RESET,
  UPDATE_MONTANT_BENEFICIARY_SUCCESS,
  UPDATE_MONTANT_FOR_GROUP_FAILURE,
  UPDATE_MONTANT_FOR_GROUP_REQUEST,
  UPDATE_MONTANT_FOR_GROUP_RESET,
  UPDATE_MONTANT_FOR_GROUP_SUCCESS,
  UPDATE_MONTANT_RESERVED_BUDGETLINE_FAILURE,
  UPDATE_MONTANT_RESERVED_BUDGETLINE_REQUEST,
  UPDATE_MONTANT_RESERVED_BUDGETLINE_RESET,
  UPDATE_MONTANT_RESERVED_BUDGETLINE_SUCCESS,
  UPDATE_NOTE_DONATION,
  UPDATE_PRODUCT_DONATION,
  UPDATE_STATUT_RESERAVATION_BUDGETLINE_FAILURE,
  UPDATE_STATUT_RESERAVATION_BUDGETLINE_REQUEST,
  UPDATE_STATUT_RESERAVATION_BUDGETLINE_RESET,
  UPDATE_STATUT_RESERAVATION_BUDGETLINE_SUCCESS,
  UPDATE_STATUT_VALIDATION_BENEFICIARY_FAILURE,
  UPDATE_STATUT_VALIDATION_BENEFICIARY_REQUEST,
  UPDATE_STATUT_VALIDATION_BENEFICIARY_RESET,
  UPDATE_STATUT_VALIDATION_BENEFICIARY_SUCCESS,
  VALIDATE_ALL_BENEFICIARIES_REQUEST,
  VALIDATE_ALL_BENEFICIARIES_SUCCESS,
  VALIDATE_ALL_BENEFICIARIES_FAILURE,
  VALIDATE_ALL_BENEFICIARIES_RESET,
} from './constants';

export function loadAideComplementaire(search) {
  return {
    type: LOAD_AIDECOMPLEMENTAIRE,
    search,
  };
}

export function aideComplementaireLoaded(aideComplementaire) {
  return {
    type: LOAD_AIDECOMPLEMENTAIRE_SUCCESS,
    aideComplementaire,
  };
}

export function aideComplementaireLoadingError(error) {
  return {
    type: LOAD_AIDECOMPLEMENTAIRE_ERROR,
    error,
  };
}

export function loadBeneficiariesForAideComplementaire(
  page,
  aideComplementaireId,
  filters,
) {
  return {
    type: LOAD_BENEFICIARY_FOR_AIDECOMPLEMENTAIRE,
    page,
    aideComplementaireId,
    filters,
  };
}

export function beneficiariesForaideComplementaireLoaded(
  beneficiariesForAideComplementaire,
) {
  return {
    type: LOAD_BENEFICIARY_FOR_AIDECOMPLEMENTAIRE_SUCCESS,
    beneficiariesForAideComplementaire,
  };
}

export function beneficiariesForLoadingError(error) {
  return {
    type: LOAD_BENEFICIARY_FOR_AIDECOMPLEMENTAIREE_ERROR,
    error,
  };
}

export function processDonorsAndBeneficiariesRequest(
  idAideComplementaire,
  beneficiaryIds,
) {
  return {
    type: PROCESS_DONORS_BENEFICIARIES_REQUEST,
    idAideComplementaire,
    beneficiaryIds,
  };
}

export function processDonorsAndBeneficiariesSuccess() {
  return {
    type: PROCESS_DONORS_BENEFICIARIES_SUCCESS,
  };
}

export function processDonorsAndBeneficiariesError(error) {
  return {
    type: PROCESS_DONORS_BENEFICIARIES_ERROR,
    error,
  };
}

export function processDonorsAndBeneficiariesReset() {
  return {
    type: PROCESS_DONORS_BENEFICIARIES_RESET,
  };
}

export function processDonorsRequest(idAideComplementaire, priority, idDonors) {
  return {
    type: PROCESS_DONORS_REQUEST,
    idAideComplementaire,
    priority,
    idDonors,
  };
}

export function processDonorsSuccess() {
  return {
    type: PROCESS_DONORS_SUCCESS,
  };
}

export function processDonorsError(error) {
  return {
    type: PROCESS_DONORS_ERROR,
    error,
  };
}

export function processDonorsReset() {
  return {
    type: PROCESS_DONORS_RESET,
  };
}

////

export function loadDonorsForAideComplementaire(aideComplementaireId) {
  return {
    type: LOAD_DONOR_FOR_AIDECOMPLEMENTAIRE,
    aideComplementaireId,
  };
}

export function donorsForAideComplementaireLoaded(donorsForAideComplementaire) {
  return {
    type: LOAD_DONOR_FOR_AIDECOMPLEMENTAIRE_SUCCESS,
    donorsForAideComplementaire,
  };
}

export function donorsForAideComplementaireLoadingError(error) {
  return {
    type: LOAD_DONOR_FOR_AIDECOMPLEMENTAIREE_ERROR,
    error,
  };
}

//////

export function fetchBudgetLinesRequest(serviceId, aideComplementaireId) {
  return {
    type: FETCH_BUDGET_LINES_REQUEST,
    serviceId,
    aideComplementaireId,
  };
}

export function fetchBudgetLinesSuccess(budgetLines) {
  return {
    type: FETCH_BUDGET_LINES_SUCCESS,
    budgetLines,
  };
}

export function fetchBudgetLinesFailure(error) {
  return {
    type: FETCH_BUDGET_LINES_FAILURE,
    error,
  };
}

/////

export function removeBeneficiaryRequest(
  idAideComplementaire,
  idBeneficiary,
  idDonor,
  typeBeneficiary,
  meta,
) {
  return {
    type: REMOVE_BENEFICIARY_REQUEST,
    idAideComplementaire,
    idBeneficiary,
    idDonor,
    typeBeneficiary,
    meta,
  };
}

export function removeBeneficiarySuccess() {
  return {
    type: REMOVE_BENEFICIARY_SUCCESS,
  };
}

export function removeBeneficiaryFailure(error) {
  return {
    type: REMOVE_BENEFICIARY_FAILURE,
    error,
  };
}

export function removeBeneficiaryReset() {
  return {
    type: REMOVE_BENEFICIARY_RESET,
  };
}

///////

export function updateStatutValidationBeneficiaryRequest(
  idAideComplementaire,
  idBeneficiary,
  idDonor,
  typeBeneficiary,
  meta,
) {
  return {
    type: UPDATE_STATUT_VALIDATION_BENEFICIARY_REQUEST,
    idAideComplementaire,
    idBeneficiary,
    idDonor,
    typeBeneficiary,
    meta,
  };
}

export function updateStatutValidationBeneficiarySuccess() {
  return {
    type: UPDATE_STATUT_VALIDATION_BENEFICIARY_SUCCESS,
  };
}

export function updateStatutValidationBeneficiaryFailure(error) {
  return {
    type: UPDATE_STATUT_VALIDATION_BENEFICIARY_FAILURE,
    error,
  };
}

export function updateStatutValidationBeneficiaryReset() {
  return {
    type: UPDATE_STATUT_VALIDATION_BENEFICIARY_RESET,
  };
}

//////

export function updateMontantBeneficiaryRequest(
  idAideComplementaire,
  idBeneficiary,
  idDonor,
  typeBeneficiary,
  newMontant,
  numberOfMembers,
  meta,

) {
  return {
    type: UPDATE_MONTANT_BENEFICIARY_REQUEST,
    idAideComplementaire,
    idBeneficiary,
    typeBeneficiary,
    idDonor,
    newMontant,
    meta,
    numberOfMembers
  };
}

export function updateMontantBeneficiarySuccess() {
  return {
    type: UPDATE_MONTANT_BENEFICIARY_SUCCESS,
  };
}

export function updateMontantBeneficiaryFailure(error) {
  return {
    type: UPDATE_MONTANT_BENEFICIARY_FAILURE,
    error,
  };
}

export function updateMontantBeneficiaryReset() {
  return {
    type: UPDATE_MONTANT_BENEFICIARY_RESET,
  };
}

//////

export function updateMontantForGroupRequest(
  idAideComplementaire,
  idGroupe,
  typeGroup,
  beneficiaryAmounts,
) {
  return {
    type: UPDATE_MONTANT_FOR_GROUP_REQUEST,
    idAideComplementaire,
    idGroupe,
    typeGroup,
    beneficiaryAmounts,
  };
}

export function updateMontantForGroupSuccess() {
  return {
    type: UPDATE_MONTANT_FOR_GROUP_SUCCESS,
  };
}

export function updateMontantForGroupFailure(error) {
  return {
    type: UPDATE_MONTANT_FOR_GROUP_FAILURE,
    error,
  };
}

export function updateMontantForGroupReset() {
  return {
    type: UPDATE_MONTANT_FOR_GROUP_RESET,
  };
}

//////
export function updateStatutResarvationBudgetLineRequest(
  donorId,
  aideComplementaireId,
  reservedAmount,
  operation,
  isNature
) {
  return {
    type: UPDATE_STATUT_RESERAVATION_BUDGETLINE_REQUEST,
    donorId,
    aideComplementaireId,
    reservedAmount,
    operation,
    isNature
  };
}

export function updateStatutResarvationBudgetLineSuccess() {
  return {
    type: UPDATE_STATUT_RESERAVATION_BUDGETLINE_SUCCESS,
  };
}

export function updateStatutResarvationBudgetLineFailure(error) {
  return {
    type: UPDATE_STATUT_RESERAVATION_BUDGETLINE_FAILURE,
    error,
  };
}

export function updateStatutResarvationBudgetLineReset() {
  return {
    type: UPDATE_STATUT_RESERAVATION_BUDGETLINE_RESET,
  };
}

////////
export function reserveAllBudgetLinesRequest(
  idAideComplementaire,
  donorAmounts,
  operation,
) {
  return {
    type: RESERVE_ALL_BUDGET_LINES_REQUEST,
    idAideComplementaire,
    donorAmounts,
    operation,
  };
}

export function reserveAllBudgetLinesSuccess() {
  return {
    type: RESERVE_ALL_BUDGET_LINES_SUCCESS,
  };
}

export function reserveAllBudgetLinesFailure(error) {
  return {
    type: RESERVE_ALL_BUDGET_LINES_FAILURE,
    error,
  };
}

export function reserveAllBudgetLinesReset() {
  return {
    type: RESERVE_ALL_BUDGET_LINES_RESET,
  };
}

//////
export function updateMontantReservedBudgetLineRequest(
  budgetLineId,
  newAmount,
) {
  return {
    type: UPDATE_MONTANT_RESERVED_BUDGETLINE_REQUEST,
    budgetLineId,
    newAmount,
  };
}

export function updateMontantReservedBudgetLineSuccess() {
  return {
    type: UPDATE_MONTANT_RESERVED_BUDGETLINE_SUCCESS,
  };
}

export function updateMontantReservedBudgetLineFailure(error) {
  return {
    type: UPDATE_MONTANT_RESERVED_BUDGETLINE_FAILURE,
    error,
  };
}

export function updateMontantReservedBudgetLineReset() {
  return {
    type: UPDATE_MONTANT_RESERVED_BUDGETLINE_RESET,
  };
}

//////

export function removeDonorRequest(idAideComplementaire, idDonor) {
  return {
    type: REMOVE_DONOR_REQUEST,
    idAideComplementaire,
    idDonor,
  };
}

export function removeDonorSuccess() {
  return {
    type: REMOVE_DONOR_SUCCESS,
  };
}

export function removeDonorFailure(error) {
  return {
    type: REMOVE_DONOR_FAILURE,
    error,
  };
}

export function removeDonorReset() {
  return {
    type: REMOVE_DONOR_RESET,
  };
}

/////
export function executeAideComplementaireRequest(
  aideComplementaireId,
  validatedAmount,
) {
  return {
    type: EXECUTE_AIDE_COMPLEMENTAIRE_REQUEST,
    aideComplementaireId,
    validatedAmount,
  };
}

export function executeAideComplementaireSuccess() {
  return {
    type: EXECUTE_AIDE_COMPLEMENTAIRE_SUCCESS,
  };
}

export function executeAideComplementaireFailure(error) {
  return {
    type: EXECUTE_AIDE_COMPLEMENTAIRE_FAILURE,
    error,
  };
}

export function executeAideComplementaireReset() {
  return {
    type: EXECUTE_AIDE_COMPLEMENTAIRE_RESET,
  };
}

export function unexecuteAideComplementaireRequest(
  aideComplementaireId
) {
  return {
    type: UNEXECUTE_AIDE_COMPLEMENTAIRE_REQUEST,
    aideComplementaireId
  };
}

export function unexecuteAideComplementaireSuccess() {
  return {
    type: UNEXECUTE_AIDE_COMPLEMENTAIRE_SUCCESS,
  };
}

export function unexecuteAideComplementaireFailure(error) {
  return {
    type: UNEXECUTE_AIDE_COMPLEMENTAIRE_FAILURE,
    error,
  };
}

export function unexecuteAideComplementaireReset() {
  return {
    type: UNEXECUTE_AIDE_COMPLEMENTAIRE_RESET,
  };
}

//////
export function closeAideComplementaireRequest(documentAndEntityDto, target) {
  return {
    type: CLOSE_AIDE_COMPLEMENTAIRE_REQUEST,
    documentAndEntityDto,
    target,
  };
}

export function closeAideComplementaireSuccess() {
  return {
    type: CLOSE_AIDE_COMPLEMENTAIRE_SUCCESS,
  };
}

export function closeAideComplementaireFailure(error) {
  return {
    type: CLOSE_AIDE_COMPLEMENTAIRE_FAILURE,
    error,
  };
}

export function closeAideComplementaireReset() {
  return {
    type: CLOSE_AIDE_COMPLEMENTAIRE_RESET,
  };
}

//////
export function getGroupeBeneficiariesRequest(aideComplementaireId, groupeId) {
  return {
    type: GET_GROUPE_BENEFICIARIES_REQUEST,
    aideComplementaireId,
    groupeId,
  };
}

export function getGroupeBeneficiariesSuccess(groupe) {
  return {
    type: GET_GROUPE_BENEFICIARIES_SUCCESS,
    groupe,
  };
}

export function getGroupeBeneficiariesFailure(error) {
  return {
    type: GET_GROUPE_BENEFICIARIES_FAILURE,
    error,
  };
}

export function getAideBeneficiariesRequest(aideComplementaireId, page,filters) {
  return {
    type: GET_AIDE_BENEFICIARIES_REQUEST,
    aideComplementaireId,
    page,
    filters
  };
}

export function getAideBeneficiariesSuccess(beneficiaries) {
  return {
    type: GET_AIDE_BENEFICIARIES_SUCCESS,
    beneficiaries,
  };
}

export function getAideBeneficiariesFailure(error) {
  return {
    type: GET_AIDE_BENEFICIARIES_FAILURE,
    error,
  };
}

//////
export function pushDocumentDonation(document) {
  return {
    type: PUSH_DOCUMENT_DONATION,
    document,
  };
}

export function updateDocumentDonation(document) {
  return {
    type: UPDATE_DOCUMENT_DONATION,
    document,
  };
}

export function removeDocumentDonation(document) {
  return {
    type: REMOVE_DOCUMENT_DONATION,
    document,
  };
}

export function pushProductDonation(product) {
  return {
    type: PUSH_PRODUCT_DONATION,
    product,
  };
}

export function updateProductDonation(product) {
  return {
    type: UPDATE_PRODUCT_DONATION,
    product,
  };
}

export function removeProductDonation(product) {
  return {
    type: REMOVE_PRODUCT_DONATION,
    product,
  };
}

export function pushNoteDonation(note) {
  return {
    type: PUSH_NOTE_DONATON,
    note,
  };
}

export function updateNoteDonation(note) {
  return {
    type: UPDATE_NOTE_DONATION,
    note,
  };
}

export function removeNoteDonation(note) {
  return {
    type: REMOVE_NOTE_DONATION,
    note,
  };
}

export function pushActionDonation(action) {
  return {
    type: PUSH_ACTION_DONATION,
    action,
  };
}

export function updateActionDonation(action) {
  return {
    type: UPDATE_ACTION_DONATION,
    action,
  };
}

export function removeActionDonation(action) {
  return {
    type: REMOVE_ACTION_DONATION,
    action,
  };
}

/////
export function validateAllBeneficiariesRequest(
  aideComplementaireId,
  filters,
) {
  return {
    type: VALIDATE_ALL_BENEFICIARIES_REQUEST,
    aideComplementaireId,
    filters,
  };
}

export function validateAllBeneficiariesSuccess(successMessage, hasFilters) {
  return {
    type: VALIDATE_ALL_BENEFICIARIES_SUCCESS,
    successMessage,
    hasFilters,
  };
}

export function validateAllBeneficiariesFailure(error) {
  return {
    type: VALIDATE_ALL_BENEFICIARIES_FAILURE,
    error,
  };
}

export function validateAllBeneficiariesReset() {
  return {
    type: VALIDATE_ALL_BENEFICIARIES_RESET,
  };
}
