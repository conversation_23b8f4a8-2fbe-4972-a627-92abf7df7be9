import React from 'react';
import { Link, NavLink } from 'react-router-dom';
import { useParams } from 'react-router-dom';

import styles from '../../../../Css/profileHeader.css';

const HeaderCaisse = props => {
  const params = useParams();
  const id = params.id;

  return (
    <div className={styles.navBar}>
      <p>
        <NavLink
          exact
          to={'/caisses/fiche/' + params.id + '/ChartEnteeSortie'}
          activeClassName={styles.selected}
        >
          Information générale
        </NavLink>
      </p>
      <p>
        <NavLink
          exact
          to={'/caisses/fiche/' + params.id + '/entrees'}
          activeClassName={styles.selected}
        >
          Entrées
        </NavLink>
      </p>
      <p>
        <NavLink
          exact
          to={'/caisses/fiche/' + params.id + '/sorties'}
          activeClassName={styles.selected}
        >
          Sorties
        </NavLink>
      </p>
    </div>
  );
};

export default HeaderCaisse;
