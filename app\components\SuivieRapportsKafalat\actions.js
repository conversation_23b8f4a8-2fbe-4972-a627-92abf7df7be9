// actions.js
import {
  GET_LIST_RAPPORT,
  GET_LIST_RAPPORT_SUCCESS,
  GET_LIST_RAPPORT_ERROR,
  VIEW_RAPPORT,
  VIEW_RAPPORT_SUCCESS,
  VIEW_RAPPORT_ERROR,
  NOTIFY_ASSISTANT,
  NOTIFY_ASSISTANT_SUCCESS,
  NOTIFY_ASSISTANT_ERROR,
  NOTIFY_ASSISTANT_RESET,
  GET_LIST_DONOR,
  GET_LIST_DONOR_SUCCESS,
  GET_LIST_DONOR_ERROR,
  VIEW_RAPPORT_WITH_DONOR,
  VIEW_RAPPORT_WITH_DONOR_SUCCESS,
  VIEW_RAPPORT_WITH_DONOR_ERROR,
  RESET_VIEW_RAPPORT_WITH_DONOR_SUCCESS,
  VIEW_RAPPORT_RESET,
  ADD_AGENDA_RAPPORT_AUTOMATIC,
  ADD_AGENDA_RAPPORT_AUTOMATIC_SUCCESS,
  ADD_AGENDA_RAPPORT_AUTOMATIC_ERROR,
  ADD_AGENDA_RAPPORT_AUTOMATIC_RESET,
} from './constants';

export function getListRapport(pageNumber, filters) {
  return {
    type: GET_LIST_RAPPORT,
    pageNumber,
    filters,
  };
}

export function getListRapportSuccess(data) {
  return {
    type: GET_LIST_RAPPORT_SUCCESS,
    data,
  };
}

export function getListRapportError(error) {
  return {
    type: GET_LIST_RAPPORT_ERROR,
    error,
  };
}

// export function viewRapport(rapport, target) {
//   return {
//     type: VIEW_RAPPORT,
//     rapport,
//     target,
//   };
// }

export function viewRapport({ rapport, language, target }) { 
  return {
    type: VIEW_RAPPORT,
    rapport,
    language,
    target,
  };
}

export function rapportViewed(rapport) {
  return {
    type: VIEW_RAPPORT_SUCCESS,
    rapport,
  };
}

export function rapportViewingError(error) {
  return {
    type: VIEW_RAPPORT_ERROR,
    error,
  };
}

export const rapportViewingReset = () => ({
  type: VIEW_RAPPORT_RESET,
});

export function notifyAssistant({ rapportId }) {
  return {
    type: NOTIFY_ASSISTANT,
    rapportId,
  };
}

export function notifyAssistantSuccess({ success }) {
  return {
    type: NOTIFY_ASSISTANT_SUCCESS,
    success,
  };
}
export function notifyAssistantError({ error }) {
  return {
    type: NOTIFY_ASSISTANT_ERROR,
    error,
  };
}

export function notifyAssistantReset() {
  return {
    type: NOTIFY_ASSISTANT_RESET,
  };
}

export function getListDonor({ idBeneficiary }) {
  return {
    type: GET_LIST_DONOR,
    idBeneficiary,
  };
}

export function getListDonorSuccess(data) {
  return {
    type: GET_LIST_DONOR_SUCCESS,
    data,
  };
}

export function getListDonorError(error) {
  return {
    type: GET_LIST_DONOR_ERROR,
    error,
  };
}

// export function viewRapportWithDonor(rapportId, donorId, target) {
//   return {
//     type: VIEW_RAPPORT_WITH_DONOR,
//     rapportId,
//     donorId,
//     target,
//   };
// }

export function viewRapportWithDonor({ rapportId, donorId, language, target }) {
  return {
    type: VIEW_RAPPORT_WITH_DONOR,
    rapportId,
    donorId,
    language,
    target,
  };
}

export function rapportWithDonorViewed(rapport) {
  return {
    type: VIEW_RAPPORT_WITH_DONOR_SUCCESS,
    rapport,
  };
}

export function rapportWithDonorViewingError(error) {
  return {
    type: VIEW_RAPPORT_WITH_DONOR_ERROR,
    error,
  };
}

export const resetViewRapportWithDonorSuccess = () => ({
  type: RESET_VIEW_RAPPORT_WITH_DONOR_SUCCESS,
});

export function addAgendaRapportAutomatic() {
  return {
    type: ADD_AGENDA_RAPPORT_AUTOMATIC,
  };
}

export function agendaRapportAutomaticAdded(data) {
  return {
    type: ADD_AGENDA_RAPPORT_AUTOMATIC_SUCCESS,
    data,
  };
}

export function agendaRapportAutomaticAddingError(error) {
  return {
    type: ADD_AGENDA_RAPPORT_AUTOMATIC_ERROR,
    error,
  };
}

export function resetAgendaRapportAutomatic() {
  return {
    type: ADD_AGENDA_RAPPORT_AUTOMATIC_RESET,
  };
}
