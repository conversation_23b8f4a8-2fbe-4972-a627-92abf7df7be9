import React from 'react';
import { NavLink, useParams } from 'react-router-dom';
import styles from 'Css/profileHeader.css';
import { hasRoleAssistant } from 'utils/hasAccess';

const HeaderFamily = () => {
  const params = useParams();
  const id = params.id;
  const isAssistant = hasRoleAssistant();
  return (
    <div
      className={styles.navBar}
      style={{
        fontSize: '13px',
      }}
    >
      <p>
        <NavLink
          exact
          to={'/families/fiche/' + params.id + '/members'}
          activeClassName={styles.selected}
        >
          Membres Famille
        </NavLink>
      </p>
      <p>
        <NavLink
          exact
          to={'/families/fiche/' + params.id + '/incomes'}
          activeClassName={styles.selected}
        >
          Revenus
        </NavLink>
      </p>

      <p>
        <NavLink
          exact
          to={'/families/fiche/' + params.id + '/takenInCharge'}
          activeClassName={styles.selected}
        >
          Ka<PERSON>lat
        </NavLink>
      </p>
      {!isAssistant && (
        <p>
          <NavLink
            exact
            to={'/families/fiche/' + params.id + '/bankcards'}
            activeClassName={styles.selected}
          >
            Carte Bancaire
          </NavLink>
        </p>
      )}
      <p>
        <NavLink
          exact
          to={'/families/fiche/' + params.id + '/notes'}
          activeClassName={styles.selected}
        >
          Notes
        </NavLink>
      </p>
      <p>
        <NavLink
          exact
          to={'/families/fiche/' + params.id + '/documents'}
          activeClassName={styles.selected}
        >
          Documents
        </NavLink>
      </p>
      <p>
        <NavLink
          exact
          to={'/families/fiche/' + params.id + '/action'}
          activeClassName={styles.selected}
        >
          Actions
        </NavLink>
      </p>
      <p>
        <NavLink
          exact
          to={'/families/fiche/' + params.id + '/TutorHistory'}
          activeClassName={styles.selected}
        >
          Historique
        </NavLink>
      </p>
    </div>
  );
};

export default HeaderFamily;
