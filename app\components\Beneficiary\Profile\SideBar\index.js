import React, { useState } from 'react';

import { Link } from 'react-router-dom';
import { hasRoleAssistantWithZone } from 'utils/hasAccess';
import sideBarStyles from '../../../../Css/sideBar.css';
import tagStyles from '../../../../Css/tag.css';

const SideBar = props => {
  const image = require('../../../../images/user.png');
  const zoneAssistantId = hasRoleAssistantWithZone();
  let beneficiary;
  let top = null;
  if (props.data) {
    beneficiary = props.data;

    const getStatus = () => {
      if (beneficiary.statut == 'candidat_initial') {
        return 'Pré-candidat Initial';
      }
      if (beneficiary.statut == 'candidat_valider_assistance') {
        return "Pré-candidat Validé par l'Assistant";
      }
      if (beneficiary.statut == 'candidat_valider_kafalat') {
        return 'Pré-candidat Validé par Kafalat';
      }
      if (beneficiary.statut == 'candidat_rejete') {
        return 'Pré-candidat Rejeté';
      }
      if (
        beneficiary.statut == 'candidat_a_completer_par_assistance' ||
        beneficiary.statut == 'candidat_a_completer_par_kafalat'
      ) {
        return 'Pré-candidat à compléter';
      }
      if (beneficiary.statut == 'beneficiary_actif') {
        return 'Actif';
      }
      if (beneficiary.statut == 'beneficiary_enattente') {
        return 'Candidat en attente';
      }
      if (beneficiary.statut == 'candidat_a_updater') {
        return 'Candidat à actualiser';
      }
      if (beneficiary.statut == 'beneficiaire_rejete') {
        return 'Bénéficiaire Rejeté';
      }
      if (beneficiary.statut == 'beneficiary_ancien') {
        return 'Bénéficiaire Archivé';
      }
      if (beneficiary.statut == 'beneficiary_ad_hoc_individual') {
        return 'Personne';
      }
      if (beneficiary.statut == 'beneficiary_ad_hoc_group') {
        return 'Groupe';
      }
      return 'Inactif';
    };
    const lastService = getStatus();

    const getTag = () => {
      const status = beneficiary.statut ? beneficiary.statut : null;

      if (status == 'candidat_initial') {
        return tagStyles.tagBlue;
      }
      if (status == 'candidat_valider_assistance') {
        return tagStyles.tagGreenLight;
      }
      if (status == 'candidat_valider_kafalat') {
        return tagStyles.tagGreen;
      }
      if (status == 'candidat_rejete') {
        return tagStyles.tagRed;
      }
      if (
        status == 'candidat_a_completer_par_assistance' ||
        status == 'candidat_a_completer_par_kafalat'
      ) {
        return tagStyles.tagOrangeJuice;
      }
      if (status == 'beneficiary_actif') {
        return tagStyles.tagGreen;
      }
      if (status == 'beneficiary_enattente') {
        return tagStyles.tagYellow;
      }
      if (status == 'beneficiaire_rejete') {
        return tagStyles.tagRedDark;
      }
      if (status == 'beneficiary_ancien') {
        return tagStyles.tagGreyDark;
      }
      if (status == 'beneficiary_ad_hoc_individual') {
        return tagStyles.tagPurple;
      }
      if (status == 'beneficiary_ad_hoc_group') {
        return tagStyles.tagBlueDark;
      }
      if (beneficiary.statut == 'candidat_a_updater') {
        return tagStyles.tagPurple;
      }
      return tagStyles.tagGrey;
    };

    top = (
      <div className={sideBarStyles.topCard}>
        <div className={sideBarStyles.top}>
          <img
            src={
              beneficiary.person.pictureBase64 == null
                ? image
                : `data:image/png;base64,${atob(
                    beneficiary.person.pictureBase64,
                  )}`
            }
            className={`rounded-circle mb-2 ${sideBarStyles.imgBorder}`}
            style={{
              width: '100px',
              height: '100px',
              objectFit: 'cover',
              borderRadius: '50%',
              border: '2px solid #ddd',
            }}
          />
          {/* Ancien Badge */}
          {beneficiary.oldBeneficiary && (
            <div
              style={{
                position: 'absolute',
                top: '-5px',
                left: '-5px',
                backgroundColor: '#FFA500',
                color: 'white',
                padding: '5px 10px',
                borderTopLeftRadius: '50px',
                borderBottomRightRadius: '50px',
                fontSize: '12px',
                fontWeight: 'bold',
                boxShadow: '0 2px 4px rgba(0,0,0,0.3)',
              }}
            >
              Ancien
            </div>
          )}

          {/* Info Section */}
          <div className={sideBarStyles.info}>
            <h5>
              {beneficiary.person.firstName} {beneficiary.person.lastName}
            </h5>

            {/* Main Code */}
            <p>{beneficiary.code ? beneficiary.code : '-'}</p>

            <div className={getTag()}>{lastService}</div>

            <div className={`${tagStyles.tagGrey} mt-2 text-nowrap `}>
              {beneficiary.independent ? 'Indépendant' : 'Membre de famille'}
            </div>
            {beneficiary.codeBeneficiary && (
              <div
                style={{
                  color: '#0056b3',
                  marginTop: '10px',
                  backgroundColor: '#f0f8ff',
                  padding: '6px 10px',
                  borderRadius: '35px',
                  display: 'inline-block',
                  fontSize: '15px',
                  border: '1px solid #cce5ff',
                }}
              >
                <strong>Code Candidature :</strong>{' '}
                {beneficiary.codeBeneficiary}
              </div>
            )}
            {beneficiary.accountingCode && (
              <div
                style={{
                  color: '#1e7e34',
                  marginTop: '10px',
                  backgroundColor: '#f3fbf5',
                  padding: '6px 10px',
                  borderRadius: '35px',
                  display: 'inline-block',
                  fontSize: '15px',
                  border: '1px solid #c3e6cb',
                }}
              >
                <strong>Code Comptable :</strong> {beneficiary.accountingCode}
              </div>
            )}
          </div>
        </div>

        {/* Additional Status Messages */}
        {beneficiary.beneficiaryStatut &&
          (beneficiary.beneficiaryStatut.id === 4 ||
            beneficiary.beneficiaryStatut.id === 8) && (
            <div className={sideBarStyles.warningMessage}>
              <strong>Détail du complément :</strong>
              <p>{beneficiary.rqComplete}</p>
            </div>
        )}

        {beneficiary.beneficiaryStatut &&
          beneficiary.beneficiaryStatut.id === 9 && (
            <div className={sideBarStyles.errorMessage}>
              <strong>Motif de l'archivage:</strong>
              <p>{beneficiary.rqReject}</p>
            </div>
          )}

        {beneficiary.beneficiaryStatut &&
          (beneficiary.beneficiaryStatut.id === 7 ||
            beneficiary.beneficiaryStatut.id === 5) && (
            <div className={sideBarStyles.errorMessage}>
              <strong>Motif du rejet:</strong>
              <p>{beneficiary.rqReject}</p>
            </div>
          )}
      </div>
    );
  }
  const [isExpanded, setIsExpanded] = useState(false);
  const toggleExpand = () => {
    setIsExpanded(!isExpanded);
  };

  return (
    <div className={sideBarStyles.sideBar}>
      {top}

      {beneficiary && beneficiary.familyId && (
        <>
          <h5 style={{ margin: '20px 0 20px 14px' }}>
            Coordonnées de la famille
          </h5>
          <div className={sideBarStyles.text}>
            <div
              className={sideBarStyles.infoGrid}
              style={{
                display: 'grid',
                gridTemplateColumns: 'max-content 1fr',
                gap: '8px 16px',
                borderRadius: '20px',
                alignItems: 'start',
              }}
            >
              <p style={{ whiteSpace: 'nowrap', fontWeight: 'bold' }}>
                Nom du Tuteur :
              </p>
              <p>{beneficiary.tutorName || '-'}</p>
              <p style={{ whiteSpace: 'nowrap', fontWeight: 'bold' }}>
                Zone de la famille :
              </p>
              <p>{beneficiary.zoneFamilyName || '-'}</p>

              <p style={{ whiteSpace: 'nowrap', fontWeight: 'bold' }}>
                Adresse :
              </p>
              <p>{beneficiary.addressFamily || '-'}</p>

              <p style={{ whiteSpace: 'nowrap', fontWeight: 'bold' }}>
                Téléphone :
              </p>
              <p>{beneficiary.phoneNumberFamily || '-'}</p>

              <p style={{ whiteSpace: 'nowrap', fontWeight: 'bold' }}>
                Ville :
              </p>
              <p>
                {beneficiary.cityFamily && beneficiary.cityFamily.name
                  ? beneficiary.cityFamily.name
                  : '-'}
              </p>

              <p style={{ whiteSpace: 'nowrap', fontWeight: 'bold' }}>
                Région :
              </p>
              <p>
                {beneficiary.cityFamily &&
                beneficiary.cityFamily.region &&
                beneficiary.cityFamily.region.name
                  ? beneficiary.cityFamily.region.name
                  : '-'}
              </p>

              <p style={{ whiteSpace: 'nowrap', fontWeight: 'bold' }}>Pays :</p>
              <p>
                {beneficiary.cityFamily &&
                beneficiary.cityFamily.region &&
                beneficiary.cityFamily.region.country &&
                beneficiary.cityFamily.region.country.nameFrench
                  ? beneficiary.cityFamily.region.country.nameFrench
                  : '-'}
              </p>

              <p style={{ whiteSpace: 'nowrap', fontWeight: 'bold' }}>
                Type d'hébergement :
              </p>
              <p>{beneficiary.accommodationNatureFamily || '-'}</p>

              <p style={{ whiteSpace: 'nowrap', fontWeight: 'bold' }}>
                Nature de l'hébergement :
              </p>
              <p>{beneficiary.accommodationtypeFamily || '-'}</p>
            </div>
          </div>
          {zoneAssistantId &&
          zoneAssistantId !== 0 &&
          zoneAssistantId !== beneficiary.zoneFamilyId ? null : (
            <Link
              to={`/families/fiche/${beneficiary.familyId}/members`}
              style={{
                textDecoration: 'underline',
                color: '#4F89D7',
                fontSize: '16px',
                fontWeight: 'bold',
                display: 'inline-block',
                marginLeft: '14px',
                marginBottom: '10px',
                transition: 'color 0.3s ease, text-decoration-color 0.3s ease',
                textDecorationColor: '#4F89D7',
                marginTop: '10px',
              }}
              onMouseOver={e => {
                e.currentTarget.style.color = '#0056b3';
                e.currentTarget.style.textDecorationColor = '#0056b3';
              }}
              onMouseOut={e => {
                e.currentTarget.style.color = '#4F89D7';
                e.currentTarget.style.textDecorationColor = '#4F89D7';
              }}
            >
              Consulter la fiche de la famille
            </Link>
          )}
        </>
      )}

      {beneficiary && beneficiary.independent ? (
        <>
          <h5 style={{ margin: '20px 0 20px 14px' }}>
            Coordonnées personnelles
          </h5>

          <div className={sideBarStyles.text}>
            <div
              className={sideBarStyles.infoGrid}
              style={{
                display: 'grid',
                gridTemplateColumns: 'max-content 1fr',
                gap: '8px 16px',
                alignItems: 'start',
                borderRadius: '20px',
              }}
            >
              <p style={{ whiteSpace: 'nowrap', fontWeight: 'bold' }}>
                Téléphone :
              </p>
              <p>
                {beneficiary &&
                beneficiary.person &&
                beneficiary.person.phoneNumber
                  ? beneficiary.person.phoneNumber
                  : '-'}
              </p>

              <p style={{ whiteSpace: 'nowrap', fontWeight: 'bold' }}>
                Email :
              </p>
              <p>
                {beneficiary && beneficiary.person && beneficiary.person.email
                  ? beneficiary.person.email
                  : '-'}
              </p>

              <p style={{ whiteSpace: 'nowrap', fontWeight: 'bold' }}>
                Adresse :
              </p>
              <p>
                {beneficiary && beneficiary.person && beneficiary.person.address
                  ? beneficiary.person.address
                  : '-'}
              </p>

              <p style={{ whiteSpace: 'nowrap', fontWeight: 'bold' }}>
                Ville :
              </p>
              <p>
                {beneficiary &&
                beneficiary.person &&
                beneficiary.person.info &&
                beneficiary.person.info.name
                  ? beneficiary.person.info.name
                  : '-'}
              </p>

              <p style={{ whiteSpace: 'nowrap', fontWeight: 'bold' }}>
                Région :
              </p>
              <p>
                {beneficiary &&
                beneficiary.person &&
                beneficiary.person.info &&
                beneficiary.person.info.region &&
                beneficiary.person.info.region.name
                  ? beneficiary.person.info.region.name
                  : '-'}
              </p>

              <p style={{ whiteSpace: 'nowrap', fontWeight: 'bold' }}>Pays :</p>
              <p>
                {beneficiary &&
                beneficiary.person &&
                beneficiary.person.info &&
                beneficiary.person.info.region &&
                beneficiary.person.info.region.country &&
                beneficiary.person.info.region.country.nameFrench
                  ? beneficiary.person.info.region.country.nameFrench
                  : '-'}
              </p>
              <p style={{ whiteSpace: 'nowrap', fontWeight: 'bold' }}>
                Type d'hébergement :
              </p>
              <p>
                {beneficiary &&
                beneficiary.person &&
                beneficiary.person.accommodationNature &&
                beneficiary.person.accommodationNature.name
                  ? beneficiary.person.accommodationNature.name
                  : '-'}
              </p>

              <p style={{ whiteSpace: 'nowrap', fontWeight: 'bold' }}>
                Nature de l'hébergement :
              </p>

              <p>
                {beneficiary &&
                beneficiary.person &&
                beneficiary.person.accommodationType &&
                beneficiary.person.accommodationType.name
                  ? beneficiary.person.accommodationType.name
                  : '-'}
              </p>
            </div>
          </div>
        </>
      ) : (
        <>
          <div>
            <div
              style={{
                cursor: 'pointer',
                fontWeight: 'bold',
                color: '#0056b3', // Un bleu plus professionnel
                textDecoration: 'none',
                display: 'flex',
                alignItems: 'center',
                marginInline: '14px',
                marginBottom: '12px',
                padding: '8px 12px',
                border: '1px solid #0056b3', // Ajout d'une bordure pour un effet bouton
                borderRadius: '20px',
                backgroundColor: '#f0f8ff', // Bleu clair pour l'arrière-plan
                transition: 'background-color 0.3s, transform 0.2s',
              }}
              onMouseEnter={e => {
                e.currentTarget.style.backgroundColor = '#e0efff'; // Couleur plus claire au survol
                e.currentTarget.style.transform = 'scale(1.02)'; // Légère mise en avant au survol
              }}
              onMouseLeave={e => {
                e.currentTarget.style.backgroundColor = '#f0f8ff';
                e.currentTarget.style.transform = 'scale(1)';
              }}
              onClick={toggleExpand}
            >
              <i
                style={{
                  marginRight: '8px',
                  fontSize: '16px',
                  color: '#0056b3',
                }}
              >
                {isExpanded ? (
                  <i className="fas fa-minus-circle"></i> // Icône plus illustrative
                ) : (
                  <i className="fas fa-plus-circle"></i>
                )}
              </i>
              <span>
                {isExpanded
                  ? 'Réduire les détails'
                  : 'Voir les coordonnées personnelles'}
              </span>
            </div>

            {isExpanded && (
              <div
                style={{
                  display: 'grid',
                  gridTemplateColumns: 'max-content 1fr',
                  gap: '12px 16px',
                  alignItems: 'start',
                  padding: '10px',
                  backgroundColor: '#f9f9f9',
                  border: '1px solid #ddd',
                  borderRadius: '20px',
                }}
              >
                {beneficiary &&
                beneficiary.person &&
                (beneficiary.person.phoneNumber ||
                  beneficiary.person.email ||
                  beneficiary.person.address ||
                  (beneficiary.person.info && beneficiary.person.info.name) ||
                  (beneficiary.person.info &&
                    beneficiary.person.info.region &&
                    beneficiary.person.info.region.name) ||
                  (beneficiary.person.info &&
                    beneficiary.person.info.region &&
                    beneficiary.person.info.region.country &&
                    beneficiary.person.info.region.country.nameFrench) ||
                  (beneficiary.person.accommodationType &&
                    beneficiary.person.accommodationType.name) ||
                  (beneficiary.person.accommodationNature &&
                    beneficiary.person.accommodationNature.name)) ? (
                  <>
                      <p style={{ whiteSpace: 'nowrap', fontWeight: 'bold' }}>
                      Téléphone :
                      </p>
                      <p>{beneficiary.person.phoneNumber || '-'}</p>

                      <p style={{ whiteSpace: 'nowrap', fontWeight: 'bold' }}>
                      Email :
                      </p>
                      <p>{beneficiary.person.email || '-'}</p>

                      <p style={{ whiteSpace: 'nowrap', fontWeight: 'bold' }}>
                      Adresse :
                      </p>
                      <p>{beneficiary.person.address || '-'}</p>

                      <p style={{ whiteSpace: 'nowrap', fontWeight: 'bold' }}>
                      Ville :
                      </p>
                    <p>
                      {beneficiary.person.info && beneficiary.person.info.name
                          ? beneficiary.person.info.name
                          : '-'}
                    </p>

                      <p style={{ whiteSpace: 'nowrap', fontWeight: 'bold' }}>
                      Région :
                      </p>
                    <p>
                        {beneficiary.person.info &&
                      beneficiary.person.info.region &&
                      beneficiary.person.info.region.name
                        ? beneficiary.person.info.region.name
                          : '-'}
                    </p>

                      <p style={{ whiteSpace: 'nowrap', fontWeight: 'bold' }}>
                      Pays :
                      </p>
                    <p>
                      {beneficiary.person.info &&
                      beneficiary.person.info.region &&
                      beneficiary.person.info.region.country &&
                      beneficiary.person.info.region.country.nameFrench
                        ? beneficiary.person.info.region.country.nameFrench
                          : '-'}
                    </p>

                    <p style={{ whiteSpace: 'nowrap', fontWeight: 'bold' }}>
                      Type d'hébergement :
                    </p>
                    <p>
                      {beneficiary.person.accommodationNature &&
                      beneficiary.person.accommodationNature.name
                        ? beneficiary.person.accommodationNature.name
                        : '-'}
                    </p>
                    <p style={{ whiteSpace: 'nowrap', fontWeight: 'bold' }}>
                      Nature de l'hébergement :
                    </p>
                    <p>
                      {beneficiary.person.accommodationType &&
                      beneficiary.person.accommodationType.name
                        ? beneficiary.person.accommodationType.name
                        : '-'}
                    </p>
                  </>
                  ) : (
                    <p
                      style={{
                        gridColumn: '1 / -1',
                        textAlign: 'center',
                        fontWeight: 'bold',
                      }}
                  >
                    Pas de coordonnées personnelles
                  </p>
                )}
              </div>
            )}
          </div>
        </>
      )}
    </div>
  );
};

export default SideBar;
