import React, { useEffect, useState } from 'react';
import moment from 'moment';
import DataTable from 'components/Common/DataTable';
import CustomPagination from 'components/Common/CustomPagination';
import { useDispatch, useSelector } from 'react-redux';
import { useLocation, useParams } from 'react-router-dom';
import { useInjectReducer, useInjectSaga } from 'redux-injectors';
import { createStructuredSelector } from 'reselect';
import {
  clearHistoryBeneficiary,
  loadHistoryBeneficiary,
} from 'containers/Beneficiary/BeneficiaryProfile/actions';
import {
  makeSelectError,
  makeSelectHistory,
} from 'containers/Beneficiary/BeneficiaryProfile/selectors';
import beneficiaryReducer from 'containers/Beneficiary/BeneficiaryProfile/reducer';
import { Alert, Button, Modal } from 'react-bootstrap';
import 'bootstrap/dist/css/bootstrap.min.css';
import { familySaga } from 'containers/Beneficiary/BeneficiaryProfile/saga';
import stylesList from 'Css/profileList.css';
import list from 'Css/profileList.css';

const key = 'beneficiaryProfil';

const formatDate = date => moment(date).format('DD/MM/YYYY HH:mm');

const omdbSelector = createStructuredSelector({
  beneficiaryHistory: makeSelectHistory,
  error: makeSelectError,
});

export default function BeneficiaryHistory(prpos) {
  const { data } = prpos;
  const dispatch = useDispatch();
  useInjectReducer({ key, reducer: beneficiaryReducer });
  useInjectSaga({ key, saga: familySaga });
  const { id } = useParams();

  const [currentPage, setCurrentPage] = useState(1);
  const [showModal, setShowModal] = useState(false); // Modal visibility state
  const pageSize = 8;

  const { beneficiaryHistory, error } = useSelector(omdbSelector);

  const [hide, setHide] = useState(false);
  const location = useLocation();

  useEffect(() => {
    if (location && location.state && location.state.isOldBeneficiary) {
      setHide(location.state.isOldBeneficiary);
    } else {
      setHide(false);
    }
  }, [location]);

  useEffect(() => {
    dispatch(loadHistoryBeneficiary(id));
  }, [dispatch, id]);

  useEffect(() => {
    if (data) {
      dispatch(loadHistoryBeneficiary(id));
    }
  }, [data]);

  const handleClearHistory = () => {
    dispatch(clearHistoryBeneficiary(id));
    setShowModal(false); // Close the modal after clearing history
  };

  const listHistory = beneficiaryHistory
    ? [...beneficiaryHistory]
        .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
        .slice((currentPage - 1) * pageSize, currentPage * pageSize)
        .map(item => ({
          id: item.id,
          date: item.createdAt ? formatDate(item.createdAt) : '',
          user: item.createdBy,
          title: item.title,
          description: item.description ? item.description : '-',
        }))
    : [];

  const columns = [
    {
      field: 'date',
      headerName: "Date de l'action",
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
    {
      field: 'user',
      headerName: 'Effectuer par',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
    {
      field: 'title',
      headerName: 'Action',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
    {
      field: 'description',
      headerName: 'Détail',
      flex: 2,
      headerAlign: 'center',
      align: 'center',
    },
  ];

  return (
    <div className={`pb-5 ${stylesList.backgroundStyle}`}>
      {error && (
        <Alert className="alert-style" variant="danger">
          Une erreur est survenue lors du chargement des données.
        </Alert>
      )}
      <div className={list.backgroudStyle}>
        <div className={list.global}>
          <div className={list.header}>
            <h4>Historique de Bénéficiaire</h4>
          </div>
          <DataTable
            rows={listHistory}
            columns={columns}
            fileName={`Historique des modifications ${new Date().toLocaleString()}`}
          />
          {beneficiaryHistory && (
            <div className="justify-content-center my-4">
              <CustomPagination
                totalElements={listHistory.length}
                totalCount={Math.ceil(beneficiaryHistory.length / pageSize)}
                pageSize={pageSize}
                currentPage={currentPage}
                onPageChange={page => setCurrentPage(page)}
              />
            </div>
          )}
        </div>
      </div>

      {/* Confirmation Modal */}
      <Modal show={showModal} onHide={() => setShowModal(false)}>
        <Modal.Header closeButton>
          <Modal.Title>Confirmation</Modal.Title>
        </Modal.Header>
        <Modal.Body>Êtes-vous sûr de vouloir vider l’historique ?</Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowModal(false)}>
            Annuler
          </Button>
          <Button variant="danger" onClick={handleClearHistory}>
            Confirmer
          </Button>
        </Modal.Footer>
      </Modal>
    </div>
  );
}
