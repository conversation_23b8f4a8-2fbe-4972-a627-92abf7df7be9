import { call, put, takeLatest } from 'redux-saga/effects';
import request from 'utils/request';
import {
  caisseChartDetailsLoaded,
  caisseChartDetailsLoadingError,
} from './actions';
import { LOAD_CAISSE_CHART_DETAILS } from './constants';

export function* loadCaisseChartDetails({ id }) {
  const url = `/caisses/data/${id}`;

  try {
    const { data } = yield call(request.get, url);
    yield put(caisseChartDetailsLoaded({ caisseChartDetails: data }));
  } catch (error) {
    yield put(caisseChartDetailsLoadingError(error));
  }
}

export default function* detailCaisseChartSaga() {
  yield takeLatest(LOAD_CAISSE_CHART_DETAILS, loadCaisseChartDetails);
}
