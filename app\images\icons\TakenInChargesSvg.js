import React from 'react';
const TakenInChargesSvg = () => (
  <svg
    width="19"
    height="17"
    viewBox="0 0 19 17"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M10.1544 11.3127C10.3128 10.2906 10.7209 9.88558 11.8088 9.65367C11.8212 9.53674 11.8258 9.4144 11.8475 9.29516C12.0012 8.44688 12.9927 7.83439 13.8089 8.0996C13.9916 8.15884 14.1705 8.27769 14.311 8.41127C14.7779 8.85611 15.2344 9.31296 15.6785 9.78066C15.8388 9.94946 15.9526 10.1628 16.0974 10.3691C16.0792 10.3707 16.0962 10.3746 16.1024 10.368C16.7126 9.72142 17.3986 9.12984 17.9155 8.41591C19.0642 6.82816 19.2408 5.06386 18.492 3.25853C17.6131 1.13881 15.6092 -0.0900392 13.316 0.0106229C12.1193 0.063277 11.0736 0.50619 10.1641 1.27974C10.1498 1.29174 10.1401 1.30916 10.115 1.34169C10.1599 1.39086 10.204 1.44274 10.2516 1.49074C11.3926 2.63945 12.5127 3.81062 13.6838 4.92836C14.3826 5.59544 14.2286 6.44758 13.3207 6.8843C12.5177 7.27069 11.6439 7.12163 11.0035 6.48552C10.1885 5.67635 9.38167 4.85944 8.57173 4.04524C8.27787 3.75022 8.13695 3.74945 7.84541 4.04214C6.18875 5.70655 4.53169 7.37019 2.87541 9.03498C2.6075 9.30445 2.52077 9.63083 2.69809 9.88984C3.01673 10.3552 3.27419 10.5635 3.67645 10.4516C3.82202 10.411 3.96373 10.3056 4.07368 10.1961C5.25607 9.01562 6.4315 7.8282 7.60963 6.6431C7.66848 6.58386 7.7281 6.52153 7.79857 6.47894C7.93949 6.39454 8.08274 6.39338 8.20315 6.52114C8.32046 6.64542 8.32704 6.78673 8.23683 6.92805C8.19928 6.9869 8.1474 7.03761 8.09746 7.08794C6.92048 8.27382 5.74467 9.46125 4.56344 10.6429C4.34431 10.862 4.18054 11.0966 4.29165 11.4133C4.40819 11.7451 4.65055 11.9817 4.9808 12.0963C5.25027 12.19 5.48295 12.0808 5.68002 11.8822C6.32967 11.2271 6.98165 10.5747 7.63325 9.9212C8.16134 9.39117 8.68943 8.86076 9.21829 8.33112C9.27753 8.27189 9.3356 8.21033 9.40258 8.16038C9.56248 8.04114 9.73515 8.05237 9.85633 8.18245C9.97519 8.30983 9.97364 8.49102 9.84472 8.63659C9.76806 8.72332 9.68211 8.8023 9.60003 8.88438C8.46603 10.0222 7.33397 11.1621 6.19649 12.296C5.97426 12.5175 5.82249 12.7699 5.94096 13.0727C6.07337 13.4103 6.31612 13.6798 6.68741 13.7723C6.92358 13.8308 7.12219 13.7278 7.2999 13.5621C7.47296 13.4006 7.65028 13.2307 7.85625 13.1223C8.04867 13.0208 8.28097 12.9957 8.52333 12.9291C8.50901 12.5047 8.70607 12.1443 9.02277 11.8179C9.33792 11.4931 9.69489 11.3045 10.1537 11.3123L10.1544 11.3127Z"
      fill="#A9ABAD"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M1.99835 9.22278C2.09949 8.85862 2.34712 8.61839 2.59361 8.37927L2.59362 8.37926L2.59363 8.37924L2.59364 8.37923C2.63776 8.33643 2.68185 8.29366 2.72505 8.25023C4.26634 6.69964 5.80841 5.14945 7.35086 3.60002C7.9258 3.02238 8.48757 3.02393 9.06212 3.60119C9.30203 3.84215 9.54176 4.0833 9.7815 4.32445C10.3329 4.87913 10.8844 5.43385 11.4381 5.98649C11.9534 6.50064 12.7212 6.55717 13.2675 6.12935C13.5257 5.92726 13.5393 5.73677 13.3043 5.5006C13.0736 5.26876 12.8431 5.03677 12.6126 4.80477L12.6125 4.80469L12.6124 4.80461L12.6123 4.80452L12.6123 4.80451L12.6123 4.8045L12.6123 4.80449C11.7974 3.98434 10.9824 3.16413 10.1613 2.35027C10.0898 2.27939 10.0185 2.20809 9.94715 2.13674L9.94706 2.13666L9.94705 2.13665C9.56679 1.75634 9.18505 1.37456 8.76323 1.0463C7.61491 0.153121 6.28849 -0.141896 4.85948 0.0613636C2.51366 0.395097 0.719552 2.20082 0.379236 4.57064C0.126807 6.32874 0.672705 7.83751 1.87833 9.1225C1.89795 9.1434 1.92076 9.16128 1.94929 9.18364C1.96411 9.19526 1.98047 9.20808 1.99874 9.22317L1.99835 9.22278ZM14.5037 11.6816C14.9745 11.6127 15.2184 11.3351 15.3868 11.0056C15.5409 10.704 15.4081 10.4342 15.1777 10.2057C14.9332 9.9637 14.6907 9.71961 14.4481 9.47554L14.448 9.4754C14.268 9.29437 14.0881 9.11334 13.9074 8.93315C13.5571 8.58393 13.1966 8.58354 12.8435 8.9285L12.8257 8.94592C12.7625 9.00753 12.6989 9.06964 12.643 9.13757C12.4215 9.40626 12.4118 9.76864 12.6515 10.0203C13.1273 10.5194 13.6128 11.0095 14.1103 11.4865C14.1889 11.562 14.3005 11.6031 14.3963 11.6384C14.436 11.653 14.4729 11.6666 14.5037 11.6812V11.6816ZM7.50317 14.5674L7.503 14.5671V14.5675L7.50317 14.5674ZM7.53719 14.5498C7.52585 14.5555 7.5144 14.5614 7.50317 14.5674C7.52987 14.6153 7.55395 14.6656 7.57804 14.716L7.57804 14.716C7.6307 14.8262 7.68339 14.9363 7.76356 15.0205C8.21151 15.4917 8.67301 15.9505 9.13876 16.4042C9.43184 16.6899 9.78842 16.6853 10.0896 16.4116C10.1609 16.3469 10.2298 16.278 10.2941 16.2064C10.5941 15.8726 10.5891 15.5153 10.2751 15.1947C9.84262 14.753 9.40745 14.3139 8.97035 13.8772C8.63158 13.5388 8.26184 13.5412 7.91959 13.8757L7.9039 13.8909C7.71851 14.0716 7.53363 14.2517 7.54869 14.5439L7.53719 14.5498ZM13.8448 12.3882L13.8446 12.3879L13.8202 12.3986L13.7956 12.4095C13.8102 12.6902 13.6338 12.8606 13.4569 13.0314L13.427 13.0603C13.0801 13.3968 12.7212 13.3975 12.377 13.056C12.246 12.9261 12.117 12.794 11.988 12.6618L11.988 12.6618C11.7427 12.4105 11.4974 12.1592 11.2384 11.9232C10.6654 11.4017 10.7204 10.9867 11.2767 10.5105C11.5528 10.2743 11.9271 10.2619 12.1873 10.5159C12.6628 10.9801 13.1355 11.4474 13.5927 11.9294C13.6721 12.0131 13.7222 12.1246 13.7723 12.2362C13.7955 12.2876 13.8186 12.3391 13.8446 12.3879L13.8448 12.3878V12.3882ZM12.1056 14.3117C11.9724 14.652 11.5562 14.9574 11.2387 14.9845V14.9842C11.1968 14.9656 11.1519 14.95 11.107 14.9345C11.0098 14.9008 10.9127 14.8672 10.8454 14.8026C10.3347 14.3109 9.83063 13.8114 9.34436 13.2957C9.11012 13.0472 9.13684 12.6894 9.36023 12.4297C9.40182 12.3816 9.44746 12.3366 9.49304 12.2918C9.50728 12.2777 9.52151 12.2637 9.53561 12.2496C9.89567 11.8911 10.2747 11.89 10.6352 12.2492C10.7967 12.4104 10.9577 12.5721 11.1187 12.7338C11.3763 12.9925 11.6339 13.2512 11.8938 13.5075C12.1261 13.7363 12.2287 13.9969 12.1056 14.3117Z"
      fill="black"
    />
  </svg>
);
export default TakenInChargesSvg;
