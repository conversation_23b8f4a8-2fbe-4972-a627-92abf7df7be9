export const LOAD_TAGS = 'almobadara/OMDBPageTAG/LOAD_TAGS';
export const LOAD_TAG_LIST = 'almobadara/OMDBPageTAG/LOAD_TAG_LIST';
export const LOAD_TAG_LIST_SUCCESS = 'almobadara/OMDBPageTAG/LOAD_TAG_LIST_SUCCESS';
export const LOAD_TAGS_SUCCESS = 'almobadara/OMDBPageTAG/LOAD_TAGS_SUCCESS';
export const LOAD_TAGS_ERROR = 'almobadara/OMDBPageTAG/LOAD_TAGS_ERROR';

export const LOAD_TAGS_BY_TYPE = 'almobadara/OMDBPageTAG/LOAD_TAGS_BY_TYPE';
export const LOAD_TAGS_BY_TYPE_SUCCESS = 'almobadara/OMDBPageTAG/LOAD_TAGS_BY_TYPE_SUCCESS';
export const LOAD_TAGS_BY_TYPE_ERROR = 'almobadara/OMDBPageTAG/LOAD_TAGS_BY_TYPE_ERROR';

export const LOAD_TYPE_TAGS = 'almobadara/OMDBPageTAG/LOAD_TYPE_TAGS';
export const LOAD_TYPE_TAGS_SUCCESS = 'almobadara/OMDBPageTAG/LOAD_TYPE_TAGS_SUCCESS';
export const LOAD_TYPE_TAGS_ERROR = 'almobadara/OMDBPageTAG/LOAD_TYPE_TAGS_ERROR';

export const ADD_TAG = 'almobadara/OMDBPageTAG/ADD_TAGS';
export const ADD_TAG_SUCCESS = 'almobadara/OMDBPageTAG/ADD_TAGS_SUCCESS';
export const ADD_TAG_ERROR = 'almobadara/OMDBPageTAG/ADD_TAGS_ERROR';


export const DELETE_TAG = 'almobadara/OMDBPageTAG/DELETE_TAG';
export const DELETE_TAG_SUCCESS = 'almobadara/OMDBPageTAG/DELETE_TAG_SUCCESS';
export const DELETE_TAG_ERROR = 'almobadara/OMDBPageTAG/DELETE_TAG_ERROR';

export const RESET_DELETE_TAG = 'almobadara/OMDBPageTAG/RESET_DELETE_TAG';
export const RESET_ERROR = 'almobadara/OMDBPageTAG/RESET_ERROR';
