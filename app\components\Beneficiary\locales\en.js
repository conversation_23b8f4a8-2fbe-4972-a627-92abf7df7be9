export default {
  beneficiaryRecord: 'Beneficiary Record',
  personalInfo: 'Personal Information',
  firstName: 'First Name',
  lastName: 'Last Name',
  firstNameAr: 'First Name (Arabic)',
  lastNameAr: 'Last Name (Arabic)',
  email: 'Email',
  phoneNumber: 'Phone Number',
  sex: 'Sex',
  identityCode: 'Identity Code',
  address: 'Address',
  addressAr: 'Address (Arabic)',
  city: 'City',
  region: 'Region',
  profession: 'Profession',
  schoolLevel: 'School Level',
  birthDate: 'Birth Date',
  createdAt: 'Created At',
  beneficiaryCode: 'Beneficiary Code',
  independent: 'Independent',
  yes: 'Yes',
  no: 'No',
  servicesBeneficiaries: 'Service Beneficiaries',
  service: 'Service',
  status: 'Status',
  noServiceFound: 'No service found',
  handicaps: 'Handicaps',
  handicapType: 'Handicap Type',
  cause: 'Cause',
  cost: 'Cost',
  noHandicapFound: 'No handicap found',
  allergies: 'Allergies',
  allergy: 'Allergy',
  noAllergyFound: 'No allergy found',
  diseaseTreatments: 'Disease Treatments',
  treatment: 'Treatment',
  noTreatmentFound: 'No treatment found',
  schoolPath: 'School Path',
  schoolYear: 'School Year',
  schoolName: 'School Name',
  mark: 'Mark',
  honor: 'Honor',
  succeed: 'Succeed',
  noSchoolPathFound: 'No school path found',
  scholarships: 'Scholarships',
  scholarshipName: 'Scholarship Name',
  amount: 'Amount',
  currency: 'Currency',
  startDate: 'Start Date',
  endDate: 'End Date',
  periodicity: 'Periodicity',
  noScholarshipFound: 'No scholarship found',
};
