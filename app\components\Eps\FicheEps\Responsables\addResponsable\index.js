import { USER_PICTURE } from 'components/Common/ListIcons/ListIcons';
import TypeIdentities from 'containers/Common/SubComponents/TypeIdentities';
import btnStyles from 'Css/button.css';
import { Formik } from 'formik';
import React,{ useEffect, useRef, useState } from 'react';
import { useHistory, useParams } from 'react-router-dom';
import * as Yup from 'yup';


import {
    EDIT_ICON
} from 'components/Common/ListIcons/ListIcons';
import { CustomTextArea } from 'containers/Common/CustomInputs/CustomTextArea';
import convertBase64 from 'containers/Common/Scripts/ConvertFileToBase64';
import {
    arabicRegex,
    phoneRegex
} from 'containers/Common/Scripts/Regex';
import DonorContactFunctions from 'containers/Donor/SubComponents/DonorContactFunctions/index';
import { Alert } from 'react-bootstrap';
import { useDispatch, useSelector } from 'react-redux';
import { useInjectReducer, useInjectSaga } from 'redux-injectors';
import { createStructuredSelector } from 'reselect';
import { addResponsable, resetResponsable } from './actions';
import { initialFormik } from './InitialValues';
import reducer from './reducer';
import saga from './saga';
import {
    makeSelectError,
    makeSelectLoading,
    makeSelectResponsable,
    makeSelectSuccess,
} from './selectors';

import { CustomTextInput } from 'containers/Common/CustomInputs/CustomTextInput';
import { CustomTextInputAr } from 'containers/Common/CustomInputs/CustomTextInputAr';

import {
    invalidString
} from 'containers/Common/ValidationSchemas/ErrorMessages';

const labelStyle = {
    fontWeight: '600',
    marginBottom: '5px',
};
function formatDate(date) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
}

const dangerStyle = {
    fontSize: '0.9rem',
    marginLeft: '5px',
};


const omdbSelector = createStructuredSelector({
    success: makeSelectSuccess,
    error: makeSelectError,
    responsable: makeSelectResponsable,
    loading: makeSelectLoading,
});

const backgroundStyle = {
    backgroundColor: 'white',
    border: '2px solid white ',
    borderRadius: '10px'
};

function AddResponsableComp(globalProps) {
    useInjectReducer({ key: 'responsable', reducer });
    useInjectSaga({ key: 'responsable', saga });
    const formikRef = useRef();
    const history = useHistory();
    const params = useParams();
    const [uploadedPicture, setUploadedPicture] = useState(USER_PICTURE);

    const [typeForm, setTypeForm] = useState('Ajouter');
    const [load, setLoading] = useState(false);
    const [showAlert, setShowAlert] = useState(false);
    const [alert, setAlert] = useState(null);
    const { success, error, responsable, loading } = useSelector(omdbSelector);
    const dispatch = useDispatch();

    useEffect(() => {
        if (showAlert) {
            const timer = setTimeout(() => {
                setShowAlert(false);
            }, 4000);
            return () => clearTimeout(timer);
        }
    }, [showAlert]);

    useEffect(() => {
        if (error) {
            setShowAlert(true);
            setAlert(
                <Alert variant="danger" onClose={() => setShowAlert(false)} dismissible>
                    Une erreur est survenue. Veuillez vérifier les informations saisies.
                </Alert>,
            );
        }
    }, [error]);
    useEffect(() => {
        if (success) {
            globalProps.handleClose(false)
            globalProps.setEditResponsable(null)
            dispatch(resetResponsable());
        }
    }, [success]);
    // Replace the problematic useEffect with this corrected version
    useEffect(() => {
        if (globalProps.member && formikRef.current) {
            setTypeForm('Modifier');
            setLoading(true);

            const epsResponsableData = sanitizeData(globalProps.member);

            // Check if picture exists and handle it properly
            if (epsResponsableData.pictureBase64
            ) {
                try {
                    // Convert base64 to displayable format
                    const imageData = `data:image/jpeg;base64,${atob(epsResponsableData.pictureBase64)}`;
                    setUploadedPicture(imageData);
                } catch (error) {
                    console.error("Error processing image:", error);
                    setUploadedPicture(USER_PICTURE); // Use default if error
                }
            } else {
                setUploadedPicture(USER_PICTURE); // Set default picture if no picture64
            }

            // Set form values correctly
            const updatedValues = {
                ...formikRef.current.initialValues,
                ...epsResponsableData,
                // Don't set picture field directly, it should come from file selection
            };

            formikRef.current.setValues(updatedValues);
            setLoading(false);
        }
    }, [globalProps.member]);

    // Also fix the other useEffect that's handling the picture
    useEffect(() => {
        if (globalProps.member) {
            if (!globalProps.member.pictureBase64
            ) {
                setUploadedPicture(USER_PICTURE);
            } else {
                try {
                    setUploadedPicture(`data:image/jpeg;base64,${atob(globalProps.member.pictureBase64)}`);
                } catch (error) {
                    console.error("Error setting image:", error);
                    setUploadedPicture(USER_PICTURE);
                }
            }
        }
    }, [globalProps.member]);


    const sanitizeData = data => {
        if (data === null) return '';
        if (typeof data === 'object') {
            const sanitizedData = Array.isArray(data) ? [] : {};
            for (const key in data) {
                sanitizedData[key] = sanitizeData(data[key]);
            }
            return sanitizedData;
        }
        return data;
    };
    return (
        <div className="m-5 p-4" style={backgroundStyle}>
            {showAlert && alert}
            <Formik
                initialValues={initialFormik} 
                validationSchema={Yup.object().shape({
                    picture: Yup.mixed().nullable(),

                    firstName: Yup.string()
                        .min(3, 'Prénom doit contenir au moins 3 caractères')
                        .matches(
                            /^[a-zA-ZÀ-ÖØ-öø-ÿ\s-]+$/,
                            invalidString('Prénom', 'Veuillez entrer un prénom valide'),
                        )
                        .required('Prénom est requis'),
                    firstNameAr: Yup.string()
                        .min(3, 'Prénom arabe au moins 3 caractère')
                        .required('Prénom arabe est requis')
                        .matches(
                            arabicRegex,
                            invalidString('Prénom arabe', 'veuillez entrer le prénom en arabe'),
                        ),
                    lastName: Yup.string()
                        .min(3, 'Nom au moins 3 caractère')
                        .matches(
                            /^[a-zA-ZÀ-ÖØ-öø-ÿ\s-]+$/,
                            invalidString('Nom', 'veuillez entrer un nom valide'),
                        )
                        .required('Nom est requis'),
                    lastNameAr: Yup.string()
                        .min(3, 'Nom arabe au moins 3 caractère')
                        .required('Nom arabe est requis')
                        .matches(
                            arabicRegex,
                            invalidString('Nom arabe', 'veuillez entrer le nom en arabe'),
                        ),

                    phoneNumber: Yup.string()
                        .matches(phoneRegex, "Format de numéro invalide")
                        .required("Le numéro de téléphone est requis"),

                    typeIdentity: Yup.object().shape({
                        id: Yup.string().required("Le type d'identité est requis"),
                    }),

                    identityCode: Yup.string().required("Le numéro d’identité est requis"),
                    email: Yup.string()
                        .email("Format email invalide")
                        .nullable(),

                    functionContact: Yup.object().shape({
                        id: Yup.string().nullable(), // Optional selection
                    }),

                    comment: Yup.string().nullable(), // Optional comment field
                })}
                innerRef={formikRef}
                onSubmit={(values, { setSubmitting }) => {
                    const body = {
                        ...values,
                        eps: { id: globalProps.epsId },
                    };

                    dispatch(addResponsable(body));


                    setSubmitting(false);
                }}
            >
                {props => (
                    <div>
                        <div className="row">
                            <div className="col-12">
                                <div className="d-flex justify-content-center">
                                    <img
                                        src={uploadedPicture}
                                        style={{ textAlign: 'center' }}
                                        className="rounded-circle row"
                                        width="100px"
                                        height="100px"
                                    />
                                    <label htmlFor="upload">
                                        <img src={EDIT_ICON} width="20px" height="20px" />
                                    </label>
                                    <input
                                        type="file"
                                        hidden
                                        className="row"
                                        id="upload"
                                        name="picture"
                                        onChange={event => {
                                            const file = event.target.files[0];
                                            const base64 = convertBase64(file);
                                            base64.then(data => {
                                                setUploadedPicture(data);
                                            });
                                            formikRef.current.setFieldValue(
                                                'picture',
                                                event.target.files[0],
                                            );
                                        }}
                                    />
                                </div>
                                <div className="form-row ">
                                    <div className="form-group col-md-6">
                                        <CustomTextInput
                                            label="Nom"
                                            isRequired
                                            name={`lastName`}
                                            placeholder="Nom"
                                            formProps={props}
                                        />
                                    </div>
                                    <div className="form-group col-md-6">
                                        <CustomTextInputAr
                                            label="النسب"
                                            isRequired
                                            name={`lastNameAr`}
                                            formProps={props}

                                            placeholder="النسب"
                                        />
                                    </div>
                                </div>

                                <div className="form-row">
                                    <div className="form-group col-md-6">
                                        <CustomTextInput
                                            label="Prénom"
                                            isRequired
                                            name={`firstName`}
                                            placeholder="Prénom"
                                            formProps={props}
                                        />
                                    </div>
                                    <div className="form-group col-md-6">
                                        <CustomTextInputAr
                                            label="الإسم"
                                            isRequired
                                            name={`firstNameAr`}
                                            placeholder="الإسم"
                                            formProps={props}

                                        />
                                    </div>
                                </div>

                                <div className="form-row">
                                    <div className="form-group col-md-6">
                                        <CustomTextInput
                                            label="Téléphone"
                                            isRequired
                                            name={`phoneNumber`}
                                            placeholder="06...."
                                            formProps={props}
                                        />
                                    </div>
                                    <div className="form-group col-md-6">
                                        <CustomTextInput
                                            label="Adresse e-mail"
                                            name={`email`}
                                            placeholder="<EMAIL>"
                                            formProps={props}

                                        />
                                    </div>
                                </div>
                                <div className="form-row  ">
                                    <div className="form-group col-md-6">
                                        <TypeIdentities
                                            label="Type d'identité"
                                            name="typeIdentity.id"
                                            isRequired
                                            formProps={props}

                                        />
                                    </div>
                                    <div className="form-group col-md-6 ">
                                        <CustomTextInput
                                            label="N° identité"
                                            name="identityCode"
                                            placeholder="N° identité"
                                            isRequired
                                            formProps={props}

                                        />
                                    </div>
                                </div>

                                <div className="form-row">
                                    <div className="form-group col-md-6">
                                        <DonorContactFunctions
                                            label="Fonction du contact"
                                            name={`functionContact.id`}
                                        ></DonorContactFunctions>
                                    </div>
                                </div>

                                <div className="form-row ">
                                    <div className="form-group col-md-12">
                                        <CustomTextArea
                                            label="Commentaire"
                                            name="comment"
                                            placeholder="Commentaire"
                                            formProps={props}
                                        />
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div className="d-flex justify-content-end px-1 mt-4">
                            <button
                                hidden={loading}
                                type="button"
                                className={`mr-2 px-3 ${btnStyles.cancelBtn}`}
                                onClick={globalProps.handleClose}
                            >
                                Annuler
                            </button>

                            <button
                                disabled={loading}
                                className={`ml-2 px-4 ${btnStyles.addBtn}`}
                                onClick={() => {
                                    props.submitForm();
                                  }}
                            >
                                {loading ? (
                                    <p>
                                        <span
                                            className="spinner-border spinner-border-sm"
                                            role="status"
                                            aria-hidden="true"
                                        ></span>
                                        <span className="sr-only">Loading...</span>
                                    </p>
                                ) : globalProps.member ? (
                                    'Modifier'
                                ) : (
                                    'Ajouter'
                                )}
                            </button>
                        </div>
                    </div>
                )}
            </Formik>
        </div>
    )
}

export default AddResponsableComp