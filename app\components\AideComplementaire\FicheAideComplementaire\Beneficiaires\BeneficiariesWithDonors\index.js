import React, { useEffect, useState } from 'react';
import moment from 'moment';
import styles from 'Css/profileList.css';
import btnStyles from 'Css/button.css';
import { useDispatch, useSelector } from 'react-redux';
import { useInjectReducer, useInjectSaga } from 'redux-injectors';
import {
  makeSelectExternalIncome,
  makeSelectSuccess,
  makeSelectSuccessDelete,
} from 'containers/Common/Forms/ExternalIncomeForm/selectors';

import reducer from 'containers/Common/Forms/ExternalIncomeForm/reducer';
import { createStructuredSelector } from 'reselect';

import Modal2 from 'components/Common/Modal2';
import {
  pushExternalIncome,
  removeExternalIncome,
  updateExternalIncome,
} from 'containers/Family/FamilyProfile/actions';
import ExternalIncomeForm from 'containers/Common/Forms/ExternalIncomeForm';
import {
  deleteExternalIncome,
  resetExternalIncome,
} from 'containers/Common/Forms/ExternalIncomeForm/actions';
import { deleteExternalIncomeSaga } from 'containers/Common/Forms/ExternalIncomeForm/saga';
import { isAuthorized } from 'utils/AccessControl';
import { Link } from 'react-router-dom';
import CustomPagination from '../../../../Common/CustomPagination';
import DataTable from '../../../../Common/DataTable';

const key = 'externalIncome';

const formatDate = date => moment(date).format('DD/MM/YYYY');

const omdbSelector = createStructuredSelector({
  success: makeSelectSuccess,
  externalIncome: makeSelectExternalIncome,
  successDelete: makeSelectSuccessDelete,
});

export default function BeneficiariesWithDonors(props) {
  const { success, externalIncome, successDelete } = useSelector(omdbSelector);

  const dispatch = useDispatch();
  useInjectReducer({ key, reducer });
  useInjectSaga({ key, saga: deleteExternalIncomeSaga });

  const [showModal, setShowModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [actualExternalIncome, setActualExternalIncome] = useState('');

  const handleActualExternalIncome = externalIncome => {
    setActualExternalIncome(externalIncome);
    setShowModal(true);
  };
  const handleClose = () => setShowModal(false);
  const handleShow = () => setShowModal(true);
  const handleCloseForDeleteModal = () => setShowDeleteModal(false);
  const handleShowForDeleteModal = () => setShowDeleteModal(true);

  // Define pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const pageSize = 5; // Define page size

  const aideComplementaire = props.data;
  const listeRevenus = null;
  const rows = [];
  const mapRows = [];

  const dataGridRows = [];
  let dataGridColumns = [];

  let aideComplementaireCode;

  if (aideComplementaire) {
    aideComplementaireCode = aideComplementaire.code;
  }

  useEffect(() => {
    if (successDelete) {
      dispatch(
        removeExternalIncome(externalIncome, externalIncome.familyMember.id),
      );
      dispatch(resetExternalIncome());
    }
  }, [successDelete]);

  useEffect(() => {
    if (success) {
      if (actualExternalIncome == '') {
        dispatch(
          pushExternalIncome(externalIncome, externalIncome.familyMember.id),
        );
      } else {
        dispatch(
          updateExternalIncome(externalIncome, externalIncome.familyMember.id),
        );
      }
    }
  }, [success]);

  let gridData = [];

  let listbeneficiaryWithDonorAideComplemenatireDTOList = [];

  if (
    aideComplementaire &&
    aideComplementaire.beneficiaryWithDonorAideComplemenatireDTOList &&
    aideComplementaire.beneficiaryWithDonorAideComplemenatireDTOList.length > 0
  ) {
    const beneficiaryWithDonorAideComplemenatireDTOListSorted = [
      ...aideComplementaire.beneficiaryWithDonorAideComplemenatireDTOList,
    ].sort((a, b) => (a.id < b.id ? 1 : -1));

    const startIndex = (currentPage - 1) * pageSize;
    const endIndex = Math.min(
      startIndex + pageSize,
      beneficiaryWithDonorAideComplemenatireDTOListSorted.length,
    );
    const paginatedBeneficiaryWithDonorAideComplemenatireDTOList = beneficiaryWithDonorAideComplemenatireDTOListSorted.slice(
      startIndex,
      endIndex,
    );

    listbeneficiaryWithDonorAideComplemenatireDTOList = paginatedBeneficiaryWithDonorAideComplemenatireDTOList.map(
      BeneficiaryWithDonorAideComplemenatireDTO => ({
        ...BeneficiaryWithDonorAideComplemenatireDTO,
        beneficiaryName: `${BeneficiaryWithDonorAideComplemenatireDTO.firstName} ${BeneficiaryWithDonorAideComplemenatireDTO.lastName}`,
        beneficiaryId: BeneficiaryWithDonorAideComplemenatireDTO.id,
        beneficiaryCode: `${BeneficiaryWithDonorAideComplemenatireDTO.code}`,
        beneficiaryZoneName: `${BeneficiaryWithDonorAideComplemenatireDTO.zone.name}`,
        type: BeneficiaryWithDonorAideComplemenatireDTO.independent
          ? 'Indépendant'
          : 'Membre famille',
        phoneNumber: BeneficiaryWithDonorAideComplemenatireDTO.phoneNumber,
        birthDate: formatDate(
          BeneficiaryWithDonorAideComplemenatireDTO.birthDate,
        ),
      }),
    );
  }

  dataGridColumns = [
    {
      field: 'beneficiaryCode',
      headerName: 'Code bénéficiaire',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
      renderCell: params => (
        <Link to={`/beneficiaries/fiche/${params.row.beneficiaryId}/info`}>
          {params.row.beneficiaryCode}
        </Link>
      ),
    },
    {
      field: 'beneficiaryName',
      headerName: 'Nom Complet',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
    {
      field: 'beneficiaryZoneName',
      headerName: 'Zone',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
    {
      field: 'birthDate',
      headerName: 'Date de naissance',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
    {
      field: 'type',
      headerName: 'Type',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
    {
      field: 'phoneNumber',
      headerName: 'Téléphone',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
  ];

  const connectedUser = useSelector(state => state.app.connectedUser);
  const isUpdateAuthorized = isAuthorized(connectedUser, 'FAMILLE', 'UPDATE');
  {
    /*if (isUpdateAuthorized) {
    // If the user is an admin, add the Actions column
    dataGridColumns.push({
      field: 'actions',
      headerName: 'Actions',
      type: 'actions',
      flex: 1,
      renderCell: params => (
        <div>
          <AccessControl module="FAMILLE" functionality="UPDATE">
            <>
              <input
                type="image"
                src={EDIT_ICON}
                className="p-2"
                width="40px"
                height="40px"
                onClick={() => {
                  const income = params.row;
                  handleActualExternalIncome(params.row.income);
                  setShowModal(true);
                }}
                title="modifier"
              />
              <input
                type="image"
                src={DELETE_ICON}
                className="p-2"
                width="40px"
                height="40px"
                onClick={() => {
                  handleShowForDeleteModal();
                  setActualExternalIncome(params.row.income);
                }}
                title="supprimer"
              />
            </>
          </AccessControl>
        </div>
      ),
    });
  }
  */
  }

  const columns = [
    'Donor',
    'Code',
    'First Name',
    'Last Name',
    'Beneficiary Code',
    'Beneficiary First Name',
    'Beneficiary Last Name',
  ];
  const data = [
    {
      id: 1,
      code: 'D200900001PXX',
      firstName: 'PPPPPP',
      lastName: 'Nom',
      beneficiaries: [],
    },
    {
      id: 2,
      code: 'D200900002PXX',
      firstName: 'Prénom ',
      lastName: 'Nom',
      beneficiaries: [
        {
          id: 1,
          code: 'B203300001IXX',
          firstName: 'test',
          lastName: 'test',
          zone: {
            id: 1,
            code: 'BK1',
            name: 'Casablanca 1',
            nameAr: 'المنطقة 1',
          },
          beneficiaryStatut: {
            id: 6,
            nameStatut: 'beneficiary_actif',
          },
          statut: 'beneficiary_actif',
          phoneNumber: '0987654321',
          birthDate: '2024-09-01T00:00:00.000+00:00',
          independent: true,
          montantAbeneficier: 5000,
        },
      ],
    },
  ];

  return (
    <div>
      <div className={styles.global}>
        <Modal2
          size="lg"
          customWidth="modal-90w"
          title={
            actualExternalIncome
              ? 'Modifier autre source de revenus'
              : 'Ajouter autre source de revenus'
          }
          show={showModal}
          handleClose={handleClose}
        >
          <ExternalIncomeForm
            members={
              aideComplementaire &&
              aideComplementaire.beneficiaryWithDonorAideComplemenatireDTOList
                ? aideComplementaire.beneficiaryWithDonorAideComplemenatireDTOList
                : []
            }
            externalIncome={actualExternalIncome}
            show={showModal}
            button={actualExternalIncome ? 'Modifier' : 'Ajouter'}
            handleClose={handleClose}
          />
        </Modal2>

        <Modal2
          centered
          className="mt-5"
          title="Confirmation de suppression"
          show={showDeleteModal}
          handleClose={handleCloseForDeleteModal}
        >
          <p className="mt-1 mb-5 px-2">
            Êtes-vous sûr de vouloir supprimer cette source de revenu?
          </p>
          <div className="d-flex justify-content-end px-2 my-1">
            <button
              type="button"
              className="btn-style outlined"
              onClick={handleCloseForDeleteModal}
            >
              Annuler
            </button>
            <button
              type="submit"
              className={`ml-3 btn-style primary`}
              onClick={() => {
                dispatch(deleteExternalIncome(actualExternalIncome));
                setActualExternalIncome('');
                handleCloseForDeleteModal();
              }}
            >
              Supprimer
            </button>
          </div>
        </Modal2>

        {/*<div className="d-flex justify-content-end">
          <AccessControl module="FAMILLE" functionality="UPDATE">
            <button
              className={btnStyles.addBtnProfile}
              onClick={() => {
                handleActualExternalIncome('');
                handleShow();
              }}
            >
              <i className="fas fa-plus"></i>&nbsp; Ajouter
            </button>
          </AccessControl>
        </div>
        */}

        <div className={styles.content}>
          <DataTable
            rows={listbeneficiaryWithDonorAideComplemenatireDTOList}
            columns={dataGridColumns}
            fileName={`Liste des revenus externes de la famille ${aideComplementaireCode}, ${new Date().toLocaleString()}`}
          />
          {/*}<MergedCellTable
            columns={columns}
            data={data}
            mergeColumn="firstName"
          />
          */}

          <div className="justify-content-center my-4">
            {aideComplementaire &&
              aideComplementaire.beneficiaryWithDonorAideComplemenatireDTOList && (
                <CustomPagination
                  totalCount={Math.ceil(
                    aideComplementaire
                      .beneficiaryWithDonorAideComplemenatireDTOList.length /
                      pageSize,
                  )}
                  pageSize={pageSize}
                  currentPage={currentPage}
                  onPageChange={setCurrentPage}
                />
              )}
          </div>
        </div>
      </div>
    </div>
  );
}
