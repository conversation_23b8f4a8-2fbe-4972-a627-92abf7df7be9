import React, { useEffect, useState, useCallback } from 'react';
import moment from 'moment';

import { createStructuredSelector } from 'reselect';
import listStyles from 'Css/list.css';
import tagStyles from 'Css/tag.css';
import { Link, useHistory } from 'react-router-dom';

import { useInjectReducer, useInjectSaga } from 'redux-injectors';
import {
  makeSelecRapportWithDonorViewSuccess,
  makeSelectError,
  makeSelectLoading,
  makeSelectNotifyLoading,
  makeSelectNotifySuccess,
  makeSelectPageNumber,
  makeSelectPageSize,
  makeSelectRapports,
  makeSelectTotalElements,
  makeSelecRapportWithDonorViewError,
  makeSelecRapportWithDonorViewLoading,
  makeSelecRapportViewSuccess,
  makeSelectRapportLoading,
  makeSelectSuccessPlanification,
  makeSelectLoadingPlanification,
  makeSelectErrorPlanification,
} from './selectors';
import agendaRapportReducer from './reducer';
import agendaRapportSaga from './saga';
import { useDispatch, useSelector } from 'react-redux';
import {
  addAgendaRapportAutomatic,
  getListRapport,
  notifyAssistant,
  notifyAssistantReset,
  rapportViewingReset,
  resetViewRapportWithDonorSuccess,
  viewRapport,
  viewRapportWithDonor,
} from './actions';
import { principalInputsConfig, additionalInputsConfig } from './constants';
import GenericFilter from 'containers/Common/Filter/GenericFilter';
import DataTable from 'components/Common/DataTable';
import CustomPagination from 'components/Common/CustomPagination';
import btnStyles from '../../Css/button.css';
import {
  ARTICLE_ICON,
  COMMUNICATION_ICON,
  NOTIFY_ICON,
  VIEW_ICON,
} from 'components/Common/ListIcons/ListIcons';
import { useHasRole } from 'utils/hasAccess';
import {
  getStatusName,
  getStatusStyle,
  isAdminNotify,
  isSeviceKafalat,
} from 'components/Beneficiary/Profile/BeneficiaryRapport/statutUtils';
import Modal2 from 'components/Common/Modal2';
import { Alert } from 'react-bootstrap';
import request from 'utils/request';

const formatDate = date => moment(date).format('DD/MM/YYYY');

const fetchDonorsByid = async ({
  beneficiaryId = 0,
  onLoad = () => {},
  onSuccess = () => {},
  onError = () => {},
}) => {
  try {
    onLoad(true);
    const url = `/rapport/beneficiaries/${beneficiaryId}/donors`;
    const { data } = await request.get(url);
    onSuccess(data);
    onLoad(false);
  } catch (error) {
    onLoad(false);
    onError(
      'an error occured while trying to communicate please try again later ...',
    );
  }
};

const key = 'agendaRapport';
const target = 'beneficiary';

const stateSelector = createStructuredSelector({
  rapports: makeSelectRapports,
  loading: makeSelectLoading,
  totalElements: makeSelectTotalElements,
  pageNumber: makeSelectPageNumber,
  pageSize: makeSelectPageSize,
  error: makeSelectError,
  loadingNotify: makeSelectNotifyLoading,
  notificationSuccess: makeSelectNotifySuccess,
  successViewRapportWithDonor: makeSelecRapportWithDonorViewSuccess,
  errorViewRapportWithDonor: makeSelecRapportWithDonorViewError,
  rapportWithDonorViewLoading: makeSelecRapportWithDonorViewLoading,
  rapportViewLoading: makeSelectRapportLoading,
  successView: makeSelecRapportViewSuccess,
  successPlanificationAutomatic: makeSelectSuccessPlanification,
  loadingPlanificationAutomatic: makeSelectLoadingPlanification,
  errorPlanificationAutomatic: makeSelectErrorPlanification,
});

const listLangues = [
  { id: 1, value: 'Arabe', key: 'ar' },
  { id: 2, value: 'Français', key: 'fr' },
  { id: 3, value: 'Anglais', key: 'en' },
];

export default function SuivieRapportsKafalat() {
  useInjectReducer({ key, reducer: agendaRapportReducer });
  useInjectSaga({ key, saga: agendaRapportSaga });
  const dispatch = useDispatch();
  const {
    rapports,
    loading,
    totalElements,
    pageNumber,
    pageSize,
    loadingNotify,
    notificationSuccess,
    successViewRapportWithDonor,
    errorViewRapportWithDonor,
    rapportWithDonorViewLoading,
    successView,
    rapportViewLoading,
    successPlanificationAutomatic,
    loadingPlanificationAutomatic,
    errorPlanificationAutomatic,
  } = useSelector(stateSelector);
  const [rows, setRows] = useState([]);
  const history = useHistory();

  const [showNotificationModal, setShowNotificationModal] = useState({
    visible: false,
    rapportId: null,
  });
  const [errorNotificationMessage, setErrorNotificationMessage] = useState('');
  const [successNotificationMessage, setSuccessNotificationMessage] = useState(
    '',
  );

  const [reportId, setReportId] = useState(null);
  const [chosenReturn, setChosenReturn] = useState(null);
  const [chosenLanguage, setChosenLanguage] = useState(null);
  const [chosenLanguageCommunicate, setChosenLanguageCommunicate] = useState(
    null,
  );
  const [
    showCommunicateRapportModal,
    setShowCommunicateRapportModal,
  ] = useState(false);

  const [showSelectLangRapportModal, setShowSelectLangRapportModal] = useState(
    false,
  );

  const [errorsInfo, setErrorsInfo] = useState({
    selection: {
      isError: false,
      errorMessage: 'Veuiller Choisir un donateur',
    },
  });

  const [errorsLangueInfo, setErrorsLangueInfo] = useState({
    selection: {
      isError: false,
      errorMessage: 'Veuiller Choisir une langue',
    },
  });

  const [
    errorsLangueCommunicateInfo,
    setErrorsLangueCommunicateInfo,
  ] = useState({
    selection: {
      isError: false,
      errorMessage: 'Veuiller Choisir une langue',
    },
  });

  const [donorsPayload, setDonorsPayload] = useState({
    donors: null,
    loading: false,
    error: false,
  });

  const handleCloseForNotificationModal = () => {
    setShowNotificationModal({ visible: false, rapportId: null });
    setErrorNotificationMessage('');
  };

  const handleShowNotificationModal = id =>
    setShowNotificationModal({ visible: true, rapportId: id });

  useEffect(() => {
    if (notificationSuccess) {
      setShowNotificationModal({ visible: false, rapportId: null });
      setSuccessNotificationMessage("L'email a été envoyé avec succès !");
      setTimeout(() => {
        dispatch(notifyAssistantReset());
        setSuccessNotificationMessage('');
      }, 3200);
    }
  }, [notificationSuccess]);

  const handleCloseForCommunicateRapportModal = () => {
    setErrorsInfo({
      selection: {
        isError: false,
        errorMessage: 'Veuiller Choisir un donateur',
      },
    });
    setErrorsLangueCommunicateInfo({
      selection: {
        isError: false,
        errorMessage: 'Veuiller Choisir une langue',
      },
    });
    setShowCommunicateRapportModal(false);
  };

  const handleCloseForSelectLangRapportModal = () => {
    setErrorsLangueInfo({
      selection: {
        isError: false,
        errorMessage: 'Veuiller Choisir une langue',
      },
    });
    setShowSelectLangRapportModal(false);
  };

  const handleChangeSelecttion = e => {
    const value = e.target.value;
    setChosenReturn(value);
    if (errorsInfo.selection.isError) {
      setErrorsInfo(prev => {
        return { ...prev, selection: { ...prev.selection, isError: false } };
      });
    }
  };

  const handleChangeLangueSelecttion = e => {
    const value = e.target.value;
    setChosenLanguage(value);
    if (errorsLangueInfo.selection.isError) {
      setErrorsLangueInfo(prev => {
        return { ...prev, selection: { ...prev.selection, isError: false } };
      });
    }
  };

  const handleChangeLangueCommunicateSelecttion = e => {
    const value = e.target.value;
    setChosenLanguageCommunicate(value);
    if (errorsLangueCommunicateInfo.selection.isError) {
      setErrorsLangueCommunicateInfo(prev => {
        return { ...prev, selection: { ...prev.selection, isError: false } };
      });
    }
  };

  const handleSubmitCommunicate = useCallback(() => {
    if (!chosenReturn) {
      setErrorsInfo(() => {
        return {
          selection: {
            errorMessage: 'Veuiller Choisir un donateur',
            isError: true,
          },
        };
      });
    }
    if (!chosenLanguageCommunicate) {
      setErrorsLangueCommunicateInfo({
        selection: {
          errorMessage: 'Veuiller Choisir une langue',
          isError: true,
        },
      });
    } else {
      dispatch(
        viewRapportWithDonor({
          rapportId: reportId,
          donorId: chosenReturn,
          language: chosenLanguageCommunicate,
          target,
        }),
      );
    }
  }, [chosenReturn, chosenLanguageCommunicate, reportId]);

  useEffect(() => {
    if (successViewRapportWithDonor) {
      handleCloseForCommunicateRapportModal();
      setTimeout(() => {
        dispatch(resetViewRapportWithDonorSuccess());
        setChosenReturn(null);
      }, 3200);
    }
  }, [successViewRapportWithDonor]);

  const handleSubmitViewRapport = useCallback(() => {
    if (!chosenLanguage) {
      console.log({ chosenLanguagechosenLanguage: chosenLanguage });
      setErrorsLangueInfo({
        selection: {
          errorMessage: 'Veuiller Choisir une langue',
          isError: true,
        },
      });
    } else {
      dispatch(
        viewRapport({ rapport: reportId, language: chosenLanguage, target }),
      );
    }
  }, [chosenLanguage, reportId, dispatch, target]);

  useEffect(() => {
    if (successView || successViewRapportWithDonor) {
      handleCloseForSelectLangRapportModal();
      setTimeout(() => {
        dispatch(rapportViewingReset());
        dispatch(resetViewRapportWithDonorSuccess());
        setChosenLanguage(null);
        setChosenLanguageCommunicate(null);
      }, 3200);
    }
  }, [successView, successViewRapportWithDonor]);

  const [showPlanifiedModal, setShowPlanifiedModal] = useState(false);

  const handleCloseForPlanifiedModal = () => {
    setShowPlanifiedModal(false);
  };

  const [isLoadingPlanification, setIsLoadingPlanification] = useState(false);

  const handleSubmitPlannedRapport = useCallback(async () => {
    setIsLoadingPlanification(true);
    try {
      await dispatch(addAgendaRapportAutomatic());
      setSuccessMessage('La planification a été effectuée avec succès !');
    } catch (error) {
      console.error('Erreur lors de la planification :', error);
    } finally {
      setIsLoadingPlanification(false);
      handleCloseForPlanifiedModal();
    }
  }, [dispatch]);

  const [successMessage, setSuccessMessage] = useState('');

  useEffect(() => {
    if (successPlanificationAutomatic) {
      dispatch(getListRapport(0));
      handleCloseForPlanifiedModal();
      setSuccessMessage('La planification a été effectuée avec succès !');

      const timer = setTimeout(() => {
        setSuccessMessage('');
      }, 3000);
      return () => clearTimeout(timer);
    }
  }, [successPlanificationAutomatic]);

  useEffect(() => {
    if (rapports) {
      setRows(rapports);
    }
  }, [rapports]);

  const [filterValues, setFilterValues] = useState({
    beneficiaryCode: '',
    beneficiaryName: '',
    numberRapport: '',
    reportStatus: '',
    plannedDateStart: '',
    plannedDateEnd: '',
    validationDateStart: '',
    validationDateEnd: '',
  });

  const handleResetFilterComplete = () => {
    setFilterValues({
      beneficiaryCode: '',
      beneficiaryName: '',
      numberRapport: '',
      reportStatus: '',
      plannedDateStart: '',
      plannedDateEnd: '',
      validationDateStart: '',
      validationDateEnd: '',
    });
  };

  const handleViewRapport = useCallback(
    rapportId => {
      dispatch(viewRapport(rapportId, target));
    },
    [dispatch],
  );

  const handleNotification = id => {
    if (id) {
      dispatch(notifyAssistant({ rapportId: id }));
    }
  };

  const hasRoleKafalat = useHasRole('GESTIONNAIRE_KAFALAT');
  const hasRoleMarketing = useHasRole('GESTIONNAIRE_MARKETING');
  const hasRoleAssistant = useHasRole('ASSISTANT');
  const hasRoleAdmin = useHasRole('ADMIN');

  const getStatusAssistance = statut => {
    if (
      hasRoleAssistant &&
      (statut === 'RAPPORT_VALIDER_ASSISTANCE' ||
        statut === 'RAPPORT_VALIDER_KAFALAT' ||
        statut === 'RAPPORT_A_COMPLETER_PAR_KAFALAT')
    ) {
      return 'en cours de validation';
    } else if (
      hasRoleAssistant &&
      statut === 'RAPPORT_A_COMPLETER_PAR_ASSISTANCE'
    ) {
      return 'à completer';
    }
  };

  const getStatusKafalat = statut => {
    if (hasRoleKafalat && statut === 'RAPPORT_VALIDER_ASSISTANCE') {
      return 'à valider';
    } else if (hasRoleKafalat && statut === 'RAPPORT_VALIDER_KAFALAT') {
      return 'en cours de validation';
    } else if (hasRoleKafalat && statut === 'RAPPORT_A_COMPLETER_PAR_KAFALAT') {
      return 'à completer';
    }
  };

  const getStatusMarketing = statut => {
    if (hasRoleMarketing && statut === 'RAPPORT_VALIDER_ASSISTANCE') {
      return 'en cours de validation';
    } else if (hasRoleMarketing && statut === 'RAPPORT_VALIDER_KAFALAT') {
      return 'à valider';
    }
  };

  const handleCommunication = beneficiaryId => {
    fetchDonorsByid({
      beneficiaryId: beneficiaryId,
      onLoad: isLoading => {
        setDonorsPayload(prev => ({
          donors: prev.donors,
          loading: isLoading,
          error: false,
        }));
      },
      onError: error => {
        setDonorsPayload({ donors: null, loading: false, error: error });
        setTimeout(() => {
          setDonorsPayload({ donors: null, loading: false, error: false });
        }, 4000);
      },
      onSuccess: data => {
        setDonorsPayload({ donors: data, loading: false, error: false });
        setShowCommunicateRapportModal(true);
      },
    });
  };

  const columns = [
    {
      field: 'code',
      headerName: 'Code Bénéficiaire',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
    {
      field: 'beneficiaryName',
      headerName: 'Nom complet du bénéficiaire',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
      renderCell: params => (
        <Link
          to={{
            pathname: `beneficiaries/fiche/${params.row.beneficiaryId}/info`,
          }}
        >
          {params.row.beneficiaryName}
        </Link>
      ),
    },
    {
      field: 'numberRapport',
      headerName: 'Numéro du Rapport',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
    {
      field: 'fullNameAssistant',
      headerName: 'Assistant Responsable',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
    {
      field: 'datePlanned',
      headerName: 'Date de Planification',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
      renderCell: params => formatDate(params.value),
    },
    {
      field: 'dateValidate',
      headerName: 'Date de Validation ',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
      renderCell: params => (params.value ? formatDate(params.value) : '-'),
    },
    {
      field: 'status',
      headerName: 'Statut',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
      renderCell: params => {
        const statut = params.row.status;
        const statusName = hasRoleAdmin
          ? getStatusName(statut)
          : hasRoleAssistant
          ? getStatusAssistance(statut) || getStatusName(statut)
          : hasRoleKafalat
          ? getStatusKafalat(statut) || getStatusName(statut)
          : hasRoleMarketing
          ? getStatusMarketing(statut) || getStatusName(statut)
          : getStatusName(statut);
        const statusStyle = getStatusStyle(statut);
        return (
          <span
            className={statusStyle}
            style={{ padding: '5px', borderRadius: '4px' }}
          >
            {statusName}
          </span>
        );
      },
    },
    {
      field: 'detailComplete',
      headerName: 'Détail du Complément',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },

    {
      field: 'actions',
      headerName: 'Actions',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
      renderCell: params => (
        <>
          <input
            type="image"
            className="p-2"
            src={VIEW_ICON}
            width="40px"
            height="40px"
            onClick={() => {
              history.push({
                pathname: `/beneficiaries/fiche/${params.row.beneficiaryId}/rapport`,
              });
            }}
            title="consulter"
          />
          {params.row.status !== 'RAPPORT_PLANIFIER' &&
            params.row.status !== 'RAPPORT_A_PREPARE' && (
              <>
                <input
                  type="image"
                  src={ARTICLE_ICON}
                  className="p-2"
                  width="40px"
                  height="40px"
                  title="visualiser"
                  onClick={() => {
                    setReportId(params.row.rapportId);
                    setShowSelectLangRapportModal(true);
                  }}
                />
              </>
            )}

          <>
            {isAdminNotify(params.row.status) && (
              <>
                <input
                  type="image"
                  src={NOTIFY_ICON}
                  className="p-2"
                  width="40px"
                  height="40px"
                  title="notifier"
                  onClick={() => {
                    handleShowNotificationModal(params.row.id);
                  }}
                />
              </>
            )}

            {params.row.status === 'RAPPORT_FINAL' && (
              <>
                <input
                  type="image"
                  src={COMMUNICATION_ICON}
                  className="p-2 mt-[4px]"
                  width="40px"
                  height="40px"
                  disabled={donorsPayload.loading}
                  title="communiquer"
                  onClick={() => {
                    handleCommunication(params.row.beneficiaryId);
                    setReportId(params.row.rapportId);
                  }}
                />
              </>
            )}
          </>
        </>
      ),
    },
  ];

  const populateDataTable = () => {
    if (rapports && rapports.length > 0) {
      setRows(() => {
        const newRows =
          rapports &&
          rapports.map(rapport => ({
            id: rapport.id,
            code: rapport.beneficiary.code || '-',
            detailComplete: rapport.detailComplete || '-',
            beneficiaryName:
              rapport.beneficiary.firstName +
                ' ' +
                rapport.beneficiary.lastName || '-',
            beneficiaryId:
              (rapport.beneficiary && rapport.beneficiary.id) || '-',
            datePlanned: rapport.datePlanned,
            dateValidate: rapport.dateValidate,
            status: rapport.status,
            numberRapport: rapport.numberRapport
              ? rapport.numberRapport + '/' + rapport.year
              : '-',
            rapportId: rapport.rapportId,
            fullNameAssistant: rapport.fullNameAssistant,
          }));

        return newRows;
      });
    }
  };

  useEffect(() => {
    populateDataTable();
  }, [rapports]);

  const handleApplyFilter = filters => {
    dispatch(getListRapport(0, filters));
  };

  useEffect(() => {
    dispatch(getListRapport(0));
  }, []);

  const handlePageChange = pageNumber => {
    setFilterValues(prevFilterValues => {
      dispatch(getListRapport(pageNumber - 1, prevFilterValues));
      return prevFilterValues;
    });
  };

  return (
    <div className={listStyles.backgroundStyle}>
      <div className="col-12">
        {successMessage && (
          <Alert
            variant="success"
            dismissible
            onClose={() => setSuccessMessage('')}
            className="mb-4"
          >
            {successMessage}
          </Alert>
        )}
      </div>

      <GenericFilter
        className="col-12 mb-4"
        principalInputsConfig={principalInputsConfig}
        additionalInputsConfig={additionalInputsConfig}
        onApplyFilter={handleApplyFilter}
        filterValues={filterValues}
        setFilterValues={setFilterValues}
        onResetFilterComplete={handleResetFilterComplete}
      />

      <div className="col-12 mb-4">
        <div className="d-flex justify-content-between align-items-center">
          <h4 className="mb-0"></h4>
          <button
            className={btnStyles.addBtnProfile}
            onClick={() => {
              setShowPlanifiedModal(true);
            }}
          >
            Planifier les rapports trimestriels
          </button>
        </div>
      </div>

      <div className="sub-container">
        {loading ? (
          <div className="d-flex justify-content-center pb-3">
            <div
              className="spinner-border"
              style={{ width: '5rem', height: '5rem' }}
              role="status"
            >
              <span className="sr-only">Loading...</span>
            </div>
          </div>
        ) : (
          <div>
            {notificationSuccess && (
              <Alert variant="success" dismissible className="mb-4">
                {successNotificationMessage}
              </Alert>
            )}
            {donorsPayload.error && (
              <Alert variant="danger" dismissible className="mb-4">
                {donorsPayload.error}
              </Alert>
            )}
            <DataTable
              rows={rows}
              columns={columns}
              fileName="Liste de Suivie des Rapports de Kafalat"
              totalElements={totalElements || 0}
              numberOfElements={(rapports && rapports.length) || 0}
              pageable={{
                pageNumber: pageNumber || 0,
                offset: pageNumber * pageSize,
              }}
            />

            <div className="row justify-content-center mt-4">
              {rapports && (
                <CustomPagination
                  totalCount={
                    pageSize > 0 ? Math.ceil(totalElements / pageSize) : 1
                  }
                  pageSize={pageSize || 10}
                  currentPage={pageNumber + 1 || 1}
                  onPageChange={handlePageChange}
                />
              )}
            </div>
          </div>
        )}
      </div>

      <Modal2
        centered
        className="mt-5"
        title="Notifier l'Assistant"
        show={showNotificationModal.visible}
        handleClose={handleCloseForNotificationModal}
      >
        {errorNotificationMessage && (
          <div className="alert alert-danger" role="alert">
            <p>{errorNotificationMessage.split(':')[0]} :</p>
            <ul>
              {errorNotificationMessage
                .split(':')[1]
                .split(',')
                .map((item, index) => (
                  <li key={index}>
                    <strong>{item.trim()}</strong>
                  </li>
                ))}
            </ul>
          </div>
        )}
        <p className="mt-1 mb-5">
          Voulez-vous envoyer une notification à l'assistant en charge de
          ce rapport pour qu'il puisse avancer sur sa préparation ?
        </p>

        <div className="d-flex justify-content-end px-3 my-1">
          <button
            type="button"
            className={`mx-2 ${btnStyles.cancelBtn}`}
            onClick={handleCloseForNotificationModal}
          >
            Annuler
          </button>
          <button
            disabled={loadingNotify}
            type="submit"
            className={`mx-2 ${btnStyles.rejectBtn}`}
            onClick={() =>
              handleNotification(showNotificationModal.rapportId)
            }
          >
            {loadingNotify ? 'loading...' : 'Valider'}
          </button>
        </div>
      </Modal2>

      <Modal2
        centered
        className="mt-5"
        title="Communiquer le rapport Kafalat"
        show={showCommunicateRapportModal}
        handleClose={handleCloseForCommunicateRapportModal}
      >
        <div className="mb-3">
          <label htmlFor="statutSelect" className="form-label">
            Veuillez sélectionner le donateur à qui vous souhaitez envoyer
            ce rapport. <span className="text-danger">*</span>
          </label>
          <select
            className={`form-select custom-select ${
              errorsInfo.selection.isError ? 'is-invalid' : ''
            }`}
            onChange={handleChangeSelecttion}
          >
            <option value="">
              Sélecteur des donateurs liés au bénéficiaire
            </option>
            {donorsPayload.donors &&
              donorsPayload.donors.length > 0 &&
              donorsPayload.donors.map(el => {
                return (
                  <option
                    key={el.donorId}
                    value={el.donorId}
                    id={el.donorId}
                  >
                    {el.firstName + ' ' + el.lastName}
                  </option>
                );
              })}
          </select>
          {errorsInfo.selection.isError && (
            <div className="text-danger">Ce champ est requis.</div>
          )}
        </div>
        <div className="mb-3">
          <label htmlFor="statutSelect" className="form-label">
            Veuillez sélectionner la langue dans laquelle vous souhaitez
            visualiser le rapport. <span className="text-danger">*</span>
          </label>
          <select
            className={`form-select custom-select ${
              errorsLangueCommunicateInfo.selection.isError
                ? 'is-invalid'
                : ''
            }`}
            onChange={handleChangeLangueCommunicateSelecttion}
          >
            <option value="">sélectionner la langue</option>
            {listLangues.map(el => (
              <option key={el.key} value={el.key} id={el.id}>
                {el.value}
              </option>
            ))}
          </select>
          {errorsLangueCommunicateInfo.selection.isError && (
            <div className="text-danger">Ce champ est requis.</div>
          )}
        </div>
        <div className="d-flex justify-content-end px-3 my-1">
          <button
            type="button"
            className={`mx-2 ${btnStyles.cancelBtn}`}
            onClick={handleCloseForCommunicateRapportModal}
          >
            Annuler
          </button>
          <button
            type="button"
            className={`mx-2 ${btnStyles.rejectBtn}`}
            onClick={handleSubmitCommunicate}
          >
            {rapportWithDonorViewLoading ? 'loading...' : 'Confirmer'}
          </button>
        </div>
      </Modal2>

      <Modal2
        centered
        className="mt-5"
        title="visualisation"
        show={showSelectLangRapportModal}
        handleClose={handleCloseForSelectLangRapportModal}
      >
        <div className="mb-3">
          <label htmlFor="statutSelect" className="form-label">
            Veuillez sélectionner la langue dans laquelle vous souhaitez
            visualiser le rapport. <span className="text-danger">*</span>
          </label>
          <select
            className={`form-select custom-select ${
              errorsLangueInfo.selection.isError ? 'is-invalid' : ''
            }`}
            onChange={handleChangeLangueSelecttion}
          >
            <option value="">sélectionner la langue</option>
            {listLangues.map(el => (
              <option key={el.key} value={el.key} id={el.id}>
                {el.value}
              </option>
            ))}
          </select>
          {errorsLangueInfo.selection.isError && (
            <div className="text-danger">Ce champ est requis.</div>
          )}
        </div>
        <div className="d-flex justify-content-end px-3 my-1">
          <button
            type="button"
            className={`mx-2 ${btnStyles.cancelBtn}`}
            onClick={handleCloseForSelectLangRapportModal}
          >
            Annuler
          </button>
          <button
            type="button"
            className={`mx-2 ${btnStyles.rejectBtn}`}
            onClick={handleSubmitViewRapport}
          >
            {rapportViewLoading ? 'loading...' : 'Confirmer'}
          </button>
        </div>
      </Modal2>

      <Modal2
        centered
        className="mt-5"
        title="Planifier un rapport Kafalat"
        show={showPlanifiedModal}
        handleClose={handleCloseForPlanifiedModal}
      >
        <p className="mt-1 mb-5">
          Êtes-vous sûr de vouloir planifier tout les rapport?
        </p>
        <div className="d-flex justify-content-end px-3 my-1">
          <button
            type="button"
            className={`mx-2 ${btnStyles.cancelBtn}`}
            onClick={handleCloseForPlanifiedModal}
          >
            Annuler
          </button>
          <button
            type="button"
            className={`mx-2 ${btnStyles.rejectBtn}`}
            onClick={handleSubmitPlannedRapport}
          >
            {loadingPlanificationAutomatic ? 'loading...' : 'Confirmer'}
          </button>
        </div>
      </Modal2>
    </div>
  );
}
