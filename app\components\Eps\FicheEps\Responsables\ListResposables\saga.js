import { call, put, takeLatest } from "redux-saga/effects";
import request from 'utils/request';
import { responsableEpsDeleted, responsableEpsDeleteError, responsableEpsLoaded, responsableEpsLoadingError } from "./actions";
import { DELETE_Responsable_EPS, LOAD_Responsable_EPS } from "./constants";

export function* loadResponsableEpsList({epsId}) {
  let requestURL = `/responsable-eps/${epsId}`; 
  try {
    const { data } = yield call(request.get, requestURL);
    yield put(responsableEpsLoaded(data));
    
  } catch (error) {
    yield put(responsableEpsLoadingError(error));
  }
}

export function* deleteResponsableEps({ epsId }) {
  const requestURL = `/responsable-eps/${epsId}`;

  try {
    yield call(request.delete, requestURL);
    yield put(responsableEpsDeleted(epsId));
  } catch (error) {
    const errorMsg = error.response ? error.response.data : error.message;
    yield put(responsableEpsDeleteError(errorMsg.message));
  }
}


export default function* epsSaga() {
  yield takeLatest(LOAD_Responsable_EPS, loadResponsableEpsList);
  yield takeLatest(DELETE_Responsable_EPS, deleteResponsableEps);
}
