import React, { useEffect, useRef, useState } from 'react';
import { Link, useHistory, useLocation } from 'react-router-dom';
import PropTypes from 'prop-types';
import moment from 'moment';
import stylesList from 'Css/profileList.css';
import AccessControl from 'utils/AccessControl';
import ReactToPrint from 'react-to-print';
import Alert from 'react-bootstrap/Alert';
import profile from '../../../Css/personalInfo.css';
import btnStyles from '../../../Css/button.css';
import PrintableContent from '../PrintableContent/PrintableContent';
import {PRINT_ICON, WHITE_UPLOAD_PICTURE} from "../../../containers/Common/RequiredElement/Icons";

const formatDate = date => moment(date).format('DD/MM/YYYY');

export default function PersonalInfoGroupe(props) {
  const payload = props.data;

  const location = useLocation();
  const history = useHistory();
  const [showSuccessMessage, setShowSuccessMessage] = useState(false);
  useEffect(() => {
    if (location.state === 'success') {
      setShowSuccessMessage(true);
    }
  }, [location.state]);

  useEffect(() => {
    if (showSuccessMessage) {
      const timer = setTimeout(() => {
        handleCloseSuccessMessage();
      }, 4000);
      return () => clearTimeout(timer);
    }
  }, [showSuccessMessage]);

  const handleCloseSuccessMessage = () => {
    setShowSuccessMessage(false);
    history.replace({ ...location, state: null });
  };

  let content = <p></p>;
  const emptyValue = <span> ----</span>;

  const componentRef = useRef();

  if (payload) {
    content = (
      <div className={profile.content}>
        <div className={profile.section1}>
          <div className={profile.top}>
            <div className={`${profile.label1} ${profile.data1}`}>
              <p>
                <span style={{ fontWeight: 'bold' }}>Nom de groupe: </span>
                <span style={{ fontWeight: 'normal' }}>
                  {payload.groupName ? payload.groupName : emptyValue}
                </span>
              </p>
              <p>
                <span style={{ fontWeight: 'bold' }}>Nom Complet : </span>
                <span style={{ fontWeight: 'normal' }}>
                  {payload.groupFullNameContact
                    ? payload.groupFullNameContact
                    : emptyValue}
                </span>
              </p>
              <p>
                <span style={{ fontWeight: 'bold' }}>N° Téléphone : </span>
                <span style={{ fontWeight: 'normal' }}>
                  {payload.groupPhoneNumber
                    ? payload.groupPhoneNumber
                    : emptyValue}
                </span>
              </p>
              <p>
                <span style={{ fontWeight: 'bold' }}>Inscrit le : </span>
                <span style={{ fontWeight: 'normal' }}>
                  {payload.groupCreatedAt
                    ? formatDate(payload.groupCreatedAt)
                    : emptyValue}
                </span>
              </p>
              <p>
                <span style={{ fontWeight: 'bold' }}>Commentaire : </span>
                <span style={{ fontWeight: 'normal' }}>
                  {payload.groupComment ? payload.groupComment : emptyValue}
                </span>
              </p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div>
      {showSuccessMessage && (
        <Alert
          className="alert-style"
          variant="success"
          onClose={handleCloseSuccessMessage}
          dismissible
        >
          <p>personne ajouté avec succès</p>
        </Alert>
      )}
      <div className={stylesList.backgroudStyle}>
        <div className={profile.personalInfo}>
          <div className={profile.header}>
            <h4>Informations personnelles</h4>
            <div className="d-flex align-items-center gap-10">
              <ReactToPrint
                trigger={() => (
                  <button className="btn-style primary">
                    <img src={PRINT_ICON} width="16px" height="16px" />
                    Imprimer
                  </button>
                )}
                content={() => componentRef.current}
              />
              <div style={{ display: 'none' }}>
                <PrintableContent
                  ref={componentRef}
                  data={payload}
                  isPerson={false}
                />
              </div>
              <AccessControl module="DONOR" functionality="UPDATE">
                <Link
                  to={{
                    pathname: `/beneficiaries/edit/ad-hoc/groupe/${payload.groupId}`,
                    state: {
                      redirectTo: 'consultation',
                      id: props.personId ? props.personId : '',
                    },
                  }}
                >
                  <button className="btn-style secondary">
                    <img
                        src={WHITE_UPLOAD_PICTURE}
                        width="16px"
                        height="16spx"
                    />
                    Modifier
                  </button>
                </Link>
              </AccessControl>
            </div>
          </div>
          {content}
        </div>
      </div>
    </div>
  );
}

PersonalInfoGroupe.propTypes = {
  data: PropTypes.object.isRequired,
};
