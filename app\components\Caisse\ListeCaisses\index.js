import React, { useState } from 'react';
import { useHistory } from 'react-router-dom';
import listStyle from 'Css/profileList.css';
import { Alert } from 'react-bootstrap';
import AccessControl from 'utils/AccessControl';
import DataTable from 'components/Common/DataTable';
import { EDIT_ICON, VIEW_ICON } from 'components/Common/ListIcons/ListIcons';
import moment from 'moment';

const ListCaisses = ({ caisses }) => {
  const history = useHistory();
  const [showAlert, setShowAlert] = useState(false);
  const [message, setMessage] = useState('');

  const formatDate = date => moment(date).format('DD/MM/YYYY');

  const viewHandler = id => {
    history.push(`/caisses/fiche/${id}/ChartEnteeSortie`, { params: id });
  };

  const editHandler = id => {
    history.push(`/caisses/edit/${id}`);
  };

  const columns = [
    {
      field: 'code',
      headerName: 'Code de la caisse',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
    {
      field: 'name',
      headerName: 'Nom de la caisse',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
    {
      field: 'typePriseEnChargeDescription ',
      headerName: 'Type de la caisse',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
      renderCell: params => (
        <span>
          {params.row.typePriseEnChargeDescription
            ? params.row.typePriseEnChargeDescription.name
            : '---'}
        </span>
      ),
    },
    {
      field: 'creationDateCaisse ',
      headerName: 'Date de création',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
      renderCell: params => (
        <span>
          {params.row.creationDateCaisse
            ? formatDate(params.row.creationDateCaisse)
            : '---'}
        </span>
      ),
    },
    {
      field: 'amount',
      headerName: 'Montant de la caisse',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },

    {
      field: 'actions',
      headerName: 'Actions',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
      renderCell: params => (
        <div
          style={{
            display: 'flex',
            flexDirection: 'row',
            justifyContent: 'space-around',
            alignItems: 'center',
          }}
        >
          <input
            type="image"
            onClick={() => viewHandler(params.row.id)}
            className="p-2"
            src={VIEW_ICON}
            width="40px"
            height="40px"
            title="consulter"
          />
          <AccessControl module="USER" functionality="UPDATE">
            <input
              type="image"
              onClick={() => editHandler(params.row.id)}
              className="p-2"
              src={EDIT_ICON}
              width="40px"
              height="40px"
              title="Modifier"
            />
          </AccessControl>
        </div>
      ),
    },
  ];

  return (
    <div className="table-container">
      {showAlert ? (
        <Alert
          className="alert-style"
          variant="success"
          onClose={() => setShowAlert(false)}
          dismissible
        >
          <p>{message}</p>
        </Alert>
      ) : null}

      <div className={listStyle.global}></div>

      <div>
        <DataTable
          rows={caisses.map(caisse => ({
            ...caisse,
          }))}
          columns={columns}
          fileName={`Liste des caisses, ${new Date().toLocaleString()}`}
        />
      </div>
    </div>
  );
};

export default ListCaisses;
