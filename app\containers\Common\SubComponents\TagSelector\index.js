import React, { useState, useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { makeSelectTagList } from 'containers/tag/selectors';
import { createStructuredSelector } from 'reselect';
import { getTags } from 'containers/tag/actions';
import { useInjectReducer, useInjectSaga } from 'redux-injectors';
import tagReducer from 'containers/tag/reducer';
import tagSaga from 'containers/tag/saga';

const keyTag = 'tagList';

const tagSelector = createStructuredSelector({
  tagList: makeSelectTagList,
});

// Helper function to determine if a color is dark
const isDarkColor = (color) => {
  // Convert hex to RGB
  const hex = color.replace('#', '');
  const r = parseInt(hex.substr(0, 2), 16);
  const g = parseInt(hex.substr(2, 2), 16);
  const b = parseInt(hex.substr(4, 2), 16);

  // Calculate brightness using the formula: (0.299*R + 0.587*G + 0.114*B)
  const brightness = (0.299 * r + 0.587 * g + 0.114 * b) / 255;

  // Return true if the color is dark
  return brightness < 0.5;
};

// Tag Badge Component
const TagBadge = ({ tag, selected, inDropdown = false, onRemove, showType = false }) => {
  const backgroundColor = !inDropdown && selected ? tag.color || '#6c757d' : 'transparent';
  const textColor = !inDropdown && selected
    ? isDarkColor(tag.color || '#6c757d')
      ? 'white'
      : 'black'
    : '#333';

  return (
    <div style={{
      display: 'flex',
      alignItems: 'center',
      backgroundColor: backgroundColor,
      border: !inDropdown && selected ?'0.5px solid #000000': 'none',
      padding: !inDropdown && selected ?'3px 8px' : '0',
      borderRadius: !inDropdown && selected ? '10px' : '0',
      marginRight: '4px',
      marginBottom: '4px',
    }}>
      <div
        style={{
          width: '20px',
          height: '20px',
          borderRadius: '2px',
          border: '0.5px solid #000000',
          backgroundColor: tag.color || '#6c757d',
          marginRight: '6px',
          display: !inDropdown && selected ? 'none' : 'block',
        }}
      />
      <div>
        <span
          style={{
            color: textColor,
            fontSize: '0.8rem',
            opacity: selected ? 1 : 0.8,
          }}
        >
          {tag.name}
        </span>
        {showType && tag.typeTagName && (
          <span style={{
            display: 'block',
            fontSize: '0.7rem',
            opacity: 0.7,
            fontStyle: 'italic',
            color: textColor
          }}>
            {tag.typeTagName}
          </span>
        )}
      </div>
      {!inDropdown && selected && (
        <button
          onClick={(e) => {
            e.stopPropagation();
            onRemove();
          }}
          style={{
            background: 'transparent',
            border: 'none',
            color: textColor,
            cursor: 'pointer',
            padding: '0',
            marginLeft: '6px',
            fontSize: '16px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            width: '16px',
            height: '16px',
            transition: 'opacity 0.2s ease',
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.opacity = '0.7';
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.opacity = '1';
          }}
        >
          ×
        </button>
      )}
    </div>
  );
};

// Main TagSelector Component
const TagSelector = ({ formProps }) => {
  const dispatch = useDispatch();

  // Inject tag reducer and saga
  useInjectReducer({ key: keyTag, reducer: tagReducer });
  useInjectSaga({ key: keyTag, saga: tagSaga });

  const { tagList = [] } = useSelector(tagSelector) || { tagList: [] };
  const [selectedTags, setSelectedTags] = useState([]);
  const [showDropdown, setShowDropdown] = useState(false);
  const [selectedTagType, setSelectedTagType] = useState('');

  // Load tags when component mounts
  useEffect(() => {
    dispatch(getTags());
  }, [dispatch]);

  // Handle tag selection
  const handleTagSelect = (tag) => {
    if (!selectedTags.some(t => t.id === tag.id)) {
      const newSelectedTags = [...selectedTags, tag];
      setSelectedTags(newSelectedTags);
      formProps.setFieldValue('tags', newSelectedTags);
    }
    setShowDropdown(false);
  };

  // Handle tag removal
  const handleTagRemove = (tagId) => {
    const newSelectedTags = selectedTags.filter(tag => tag.id !== tagId);
    setSelectedTags(newSelectedTags);
    formProps.setFieldValue('tags', newSelectedTags);
  };

  // Initialize selected tags from form values
  useEffect(() => {
    if (formProps.values && formProps.values.tags) {
      const documentTags = Array.isArray(formProps.values.tags)
        ? formProps.values.tags
        : [formProps.values.tags];

      // Check if tags are already full objects or just IDs
      if (documentTags.length > 0 && typeof documentTags[0] === 'object') {
        // Tags are already full objects
        setSelectedTags(documentTags);
      } else if (tagList && tagList.length > 0) {
        // Tags are IDs, convert to full objects if tagList is available
        setSelectedTags(tagList.filter(tag => documentTags.includes(tag.id)));
      }
    }
  }, [formProps.values, tagList]);

  return (
    <div className="form-group col-md-12">
      <label style={{ fontWeight: '500' }}>Tags</label>
      <div >
        <div
          onClick={() => setShowDropdown(!showDropdown)}
          style={{
            border: '1px solid rgb(33, 108, 184)',
            borderRadius: '18px',
            padding: '10px 12px 10px 8px',
            cursor: 'pointer',
            minHeight: '32px',
            display: 'flex',
            alignItems: 'center',
            flexWrap: 'wrap',
            gap: '4px',
            backgroundColor: 'white',
          }}
        >
          <div style={{ display: 'flex', alignItems: 'center', flexWrap: 'wrap' }}>
            {selectedTags.length > 0 ? (
              selectedTags.map(tag => (
                <TagBadge
                  key={tag.id}
                  tag={tag}
                  selected={true}
                  onRemove={() => handleTagRemove(tag.id)}
                />
              ))
            ) : (
              <span style={{ color: '#6c757d', fontSize: '0.8rem' }}>Select tags</span>
            )}
          </div>
          <i
            className={`fa fa-chevron-${showDropdown ? 'up' : 'down'}`}
            style={{
              color: '#6c757d',
              fontSize: '12px',
              marginLeft: '8px',
            }}
          />
        </div>
        {showDropdown && (
          <div
            style={{
              position: 'absolute',
              top: '100%',
              left: 0,
              right: 0,
              backgroundColor: 'white',
              border: '1px solid rgb(33, 108, 184)',
              borderRadius: '10px',
              marginTop: '1px',
              zIndex: 1000,
              maxHeight: '300px',
              overflowY: 'auto',
              padding: '8px',
            }}
          >
            {/* Tag Type Selector */}
            <div style={{ marginBottom: '10px' }}>
              <select
                className="form-control form-control-sm"
                value={selectedTagType}
                onChange={(e) => setSelectedTagType(e.target.value)}
                style={{ borderRadius: '8px', fontSize: '0.8rem' }}
              >
                <option value="">Tous les types</option>
                {Array.from(new Set(tagList.map(tag => tag.typeTagId)))
                  .map(typeId => {
                    const tagType = tagList.find(tag => tag.typeTagId === typeId);
                    return (
                      <option key={typeId} value={typeId}>
                        {tagType ? tagType.typeTagName : 'Unknown'}
                      </option>
                    );
                  })}
              </select>
            </div>

            {/* Tag List */}
            {tagList && tagList.length > 0 ? (
              // Group tags by type
              Array.from(new Set(tagList.map(tag => tag.typeTagId)))
                .filter(typeId => !selectedTagType || typeId.toString() === selectedTagType)
                .map(typeId => {
                  const tagsOfType = tagList.filter(tag =>
                    tag.typeTagId === typeId &&
                    !selectedTags.some(selectedTag => selectedTag.id === tag.id)
                  );

                  if (tagsOfType.length === 0) return null;

                  const typeName = tagsOfType[0].typeTagName;

                  return (
                    <div key={typeId} style={{ marginBottom: '10px' }}>
                      {!selectedTagType && (
                        <div style={{
                          padding: '4px 6px',
                          fontWeight: 'bold',
                          fontSize: '0.8rem',
                          backgroundColor: '#f0f0f0',
                          borderRadius: '4px',
                          marginBottom: '4px'
                        }}>
                          {typeName}
                        </div>
                      )}

                      {tagsOfType.map(tag => (
                        <div
                          key={tag.id}
                          onClick={() => handleTagSelect(tag)}
                          style={{
                            marginBottom: '4px',
                            padding: '4px 6px',
                            cursor: 'pointer',
                            borderRadius: '4px',
                            transition: 'background-color 0.2s',
                            ':hover': {
                              backgroundColor: '#f8f9fa'
                            }
                          }}
                        >
                          <TagBadge
                            tag={tag}
                            selected={false}
                            inDropdown={true}
                            showType={false}
                          />
                        </div>
                      ))}
                    </div>
                  );
                }).filter(Boolean)
            ) : (
              <div style={{
                padding: '6px',
                color: '#6c757d',
                textAlign: 'center',
                fontSize: '0.75rem'
              }}>
                Pas de tags
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default TagSelector;
