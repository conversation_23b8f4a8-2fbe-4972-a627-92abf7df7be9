import React from 'react';
import { Mo<PERSON>, But<PERSON> } from 'react-bootstrap';

const ErrorModal = ({ error }) => {
  const renderErrorMessage = () => {
    let title = "Erreur";
    let message = "Une erreur s'est produite. Veuillez réessayer.";
    let iconClass = "fas fa-exclamation";

    if (error && error.response && error.response.status) {
      switch (error.response.status) {
        case 404:
          title = "Utilisateur non trouvé";
          message = "Vous êtes connecté mais n'êtes pas enregistré dans notre système Almobadara. Veuillez contacter l'Admin pour obtenir de l'aide.";
          iconClass = "fas fa-exclamation-triangle";
          break;
        case 500:
          title = "Erreur serveur";
          message = "Une erreur interne s'est produite sur le serveur. Veuillez réessayer plus tard.";
          iconClass = "fas fa-exclamation-circle";
          break;
        default:
          break;
      }
    }

    return (
      <div className="d-flex align-items-start">
        <i className={`${iconClass} text-danger mr-3 mt-1`} style={{ fontSize: '30px' }}></i>
        <div>
          <Modal.Title className="text-danger">{title}</Modal.Title>
          <p className="text-muted">{message}</p>
        </div>
      </div>
    );
  };

  return (
    <Modal show={true} centered backdrop="static">
      <Modal.Header >
        {renderErrorMessage()}
      </Modal.Header>
      <Modal.Footer>
        <Button variant="secondary" onClick={() => window.location.reload()}>
          Fermer
        </Button>
      </Modal.Footer>
    </Modal>
  );
}

export default ErrorModal;
