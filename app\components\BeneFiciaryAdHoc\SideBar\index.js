/* eslint-disable jsx-a11y/alt-text */
import React from 'react';
import style from '../../../Css/sideBar.css';
import tag from '../../../Css/tag.css';

const SideBar = props => {
  let top = null;
  return (
    <div className={style.sideBar}>
      {top}
      {/*<hr className={style.hr} />*/}

      <div className="">
        <h5 className="m-3" style={{ color: 'grey' }}>
          Bénéficiaire ad hoc
        </h5>
        <div className={style.text}>
          <div className={`${style.labelDonation} mr-2`}>
            <p>
              code :{' '}
              {props.data && props.isPerson
                ? props.data.code
                : props.data.groupCode}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SideBar;
