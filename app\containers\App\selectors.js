import { createSelector } from 'reselect';
import { initialState } from './reducer';

const selectRouter = state => state.router;

const makeSelectLocation = () =>
  createSelector(selectRouter, routerState => routerState.location);

const appContainerSelector = state => state.app || initialState;

const makeSelectConnectedUser = createSelector(
  appContainerSelector,
  appState => appState.connectedUser,
);

const makeSelectError = createSelector(
  appContainerSelector,
  appState => appState.error,
);

const makeSelectSuccess = createSelector(
  appContainerSelector,
  appState => appState.success,
);

const makeSelectdisconnectUserSuccess = createSelector(
  appContainerSelector,
  appState => appState.disconnect,
);

const makeSelectConnectedUserError = createSelector(
  appContainerSelector,
  appState => appState.disconnect_error,
);

const makeSelectIsImpersonating = createSelector(
  appContainerSelector,
  appState => appState.isImpersonating,
);

const makeSelectImpersonatedUser = createSelector(
  appContainerSelector,
  appState => appState.impersonatedUser,
);

const makeSelectOriginalUser = createSelector(
  appContainerSelector,
  appState => appState.originalUser,
);

const makeSelectImpersonationLoading = createSelector(
  appContainerSelector,
  appState => appState.impersonationLoading,
);

const makeSelectImpersonationError = createSelector(
  appContainerSelector,
  appState => appState.impersonationError,
);

export {
  makeSelectConnectedUser,
  makeSelectError,
  makeSelectdisconnectUserSuccess,
  makeSelectConnectedUserError,
  makeSelectIsImpersonating,
  makeSelectImpersonatedUser,
  makeSelectOriginalUser,
  makeSelectImpersonationLoading,
  makeSelectImpersonationError,
};

export { makeSelectLocation };
