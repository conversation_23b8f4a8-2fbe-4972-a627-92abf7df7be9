import React, { useEffect, useState } from 'react';
import moment from 'moment';
import { Link, useLocation, useHistory } from 'react-router-dom';
import AccessControl from 'utils/AccessControl';
import Alert from 'react-bootstrap/Alert';
import styles from '../../../../Css/personalInfo.css';
import Scroller from '../../../Common/scroll/scroller';
import btnStyles from '../../../../Css/button.css';

const formatDate = date => moment(date).format('DD/MM/YYYY');

export default function GeneralInfo(props) {
  const donation = props.data;
  const location = useLocation();
  const history = useHistory();
  const [showSuccessMessage, setShowSuccessMessage] = useState(false);

  useEffect(() => {
    if (location.state === 'updateSuccess') {
      setShowSuccessMessage(true);
    }
  }, [location.state]);

  useEffect(() => {
    if (showSuccessMessage) {
      const timer = setTimeout(() => handleCloseSuccessMessage(), 4000);
      return () => clearTimeout(timer);
    }
  }, [showSuccessMessage]);

  const handleCloseSuccessMessage = () => {
    setShowSuccessMessage(false);
    history.replace({ ...location, state: null });
  };

  const renderBudgetLines = () => {
    if (donation && (
      (donation.budgetLines && donation.budgetLines.length > 0) ||
      donation.nonIdentifiedValue
    )) {
      return (
        <div style={budgetLinesContainer}>
          {donation.budgetLines && donation.budgetLines.length > 0 && (
          <BudgetLinesBlock
            title="Lignes de donation identifiées"
            lines={donation.budgetLines}
            getLineDetails={renderLineDetails}
            emptyMessage="-----------------"
          />
          )}

          {donation.type === 'Financière' && donation.nonIdentifiedValue && (
            <DonationBlock
              title="Ligne de donation non identifiée"
              amount={donation.nonIdentifiedValue}
              comment={donation.nonIdentifiedComment}
              status={donation.nonIdentifiedStatus}
              emptyMessage="-----------------"
            />
          )}
        </div>
      );
    }
    return null;
  };

  const BudgetLinesBlock = ({ title, lines, getLineDetails, emptyMessage }) => {
    return (
      <div style={blockStyle}>
        <h5  style={blockTitleStyle}>{title}</h5>
        
        {lines.length > 0 ? (
          <Scroller height="400px">
            <ul style={listStyle}>
              {lines.map((line) => (
                <li key={line.id} style={listItemStyle}>
                  {getLineDetails(line)}
                </li>
              ))}
            </ul>
          </Scroller>
        ) : (
          <p style={emptyMessageStyle}>{emptyMessage}</p>
        )}
      </div>
    );
  };

  const DonationBlock = ({ title, amount, comment, status, emptyMessage }) => (
    <div style={nonIdentifiedBlockStyle}>
      <h5 style={blockTitleStyle}>{title}</h5>
      {amount ? (
        <div style={lineDetailsContainer}>
          <StatutBadge status={status} />
          <div>
            <strong>Montant : </strong>
            {amount} DH
          </div>
          {comment && (
            <div>
              <strong>Description : </strong>
              {comment}
            </div>
          )}
        </div>
      ) : (
        <p style={emptyMessageStyle}>{emptyMessage}</p>
      )}
    </div>
  );

  const StatutBadge = ({ status }) => {
    const statusConfig = {
      DISPONIBLE: { label: 'Disponible', color: '#2e8b57' },
      RESERVED: { label: 'Réservé', color: 'darkgoldenrod' },
      EXECUTED: { label: 'Exécutée', color: '#b22222' },
      REMAINING: { label: 'Restant', color: '#dc3545' },
      default: { label: 'Inconnu', color: '#6c757d' }
    };

    const { label, color } = statusConfig[status] || statusConfig.default;

    return (
      <div style={statusContainer}>
        <strong>Statut : </strong>
        <span
          style={{ 
            backgroundColor: color,
            color: 'white',
            padding: '4px 12px', 
            fontSize: '0.85rem', 
            borderRadius: '20px',
            fontWeight: '500'
          }}
        >
          {label}
        </span>
      </div>
    );
  };

  const renderLineDetails = line => (
    <div style={lineDetailsContainer}>
      <StatutBadge status={line.status} />
      
      <div style={detailRow}>
        <strong>Montant : </strong>
        {line.amount} DH
      </div>

      {line.amountByBeneficiary && (
        <div style={detailRow}>
          <strong>Montant par bénéficiaire : </strong>
          {line.amountByBeneficiary} DH
        </div>
      )}
      <div style={detailRow}>
        <strong>Service : </strong>
        {line.service ? line.service.name : '---'}
      </div>

      {line.comment && (
        <div style={detailRow}>
          <strong>Description : </strong>
          {line.comment}
        </div>
      )}

      {line.typeProductNatures && line.typeProductNatures.length > 0 && (
        <div style={productNatureContainer}>
          <strong>Nature des Produits : </strong>
          {line.typeProductNatures.map((nature, index) => (
            <span key={index} style={productNatureBadge}>
              {nature.name}
           
            </span>
          ))}
        </div>
      )}

      {line.executionDateBudgetLine && (
        <div style={detailRow}>
          <strong>
            Date {line.status === 'EXECUTED' ? 'd’exécution' : 'de réservation'}: 
          </strong>
          {formatDate(line.executionDateBudgetLine)}
        </div>
      )}
    </div>
  );

 // Styles
const budgetLinesContainer = {
  display: 'flex',
  justifyContent: 'space-between',
  gap: '20px',
  marginTop: '20px'
};

const baseBlockStyle = {
  flex: 1,
  padding: '20px',
  borderRadius: '8px',
  backgroundColor: '#fff', 
  border: '1px solid #dee2e6',
  boxShadow: '0 2px 4px rgba(0,0,0,0.05)'
};

const blockStyle = { ...baseBlockStyle };
const identifiedBlockStyle = { ...baseBlockStyle };
const nonIdentifiedBlockStyle = { ...baseBlockStyle };

const blockTitleStyle = {
  color: '#2c3e50',
  marginBottom: '15px',
  paddingBottom: '10px',
  borderBottom: '2px solid #dee2e6'
};

const listStyle = {
  listStyleType: 'none',
  padding: 0,
  margin: 0
};

const listItemStyle = {
  marginBottom: '15px',
  padding: '20px',
  backgroundColor: 'white',
  borderRadius: '10px',
  boxShadow: '0 1px 3px rgba(0,0,0,0.05)',
  border: '1px solid #eaeaea'
};

const nonIdentifiedListItem = { ...listItemStyle };

const showMoreButtonStyle = {
  backgroundColor: '#4F89D7',
  color: '#fff',
  border: 'none',
  padding: '10px 15px',
  borderRadius: '5px',
  cursor: 'pointer',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  width: '100%',
  marginTop: '15px',
  transition: 'background-color 0.2s',
  fontWeight: '500'
};

const chevronStyle = {
  marginLeft: '8px',
  fontSize: '0.9em'
};

const statusContainer = {
  display: 'flex',
  alignItems: 'center',
  gap: '8px',

};

const statusBadgeStyle = {
  padding: '6px 12px',
  borderRadius: '20px',
  color: 'white',
  fontSize: '0.85em',
  fontWeight: '500',
  display: 'inline-block'
};

const lineDetailsContainer = {
  display: 'flex',
  flexDirection: 'column',
  gap: '8px'
};

const detailRow = {
  display: 'flex',
  gap: '5px',
  alignItems: 'baseline',
  marginBottom: '8px'
};

const productNatureContainer = {
  display: 'flex',
  flexWrap: 'wrap',
  gap: '8px',
  alignItems: 'center',

};

const productNatureBadge = {
  backgroundColor: '#e8f4fc',
  padding: '4px 10px',
  borderRadius: '12px',
  display: 'flex',
  alignItems: 'center',
  gap: '6px'
};

const emptyMessageStyle = {
  color: '#6c757d',
  fontStyle: 'italic',
  textAlign: 'center'
};

const separatorStyle = {
  margin: '15px 0',
  border: 0,
  borderTop: '1px solid #dee2e6'
};


  return (
    <div className={styles.container}>
      {showSuccessMessage && (
        <Alert
          className="alert-style w-100 mt-0"
          variant="success"
          onClose={handleCloseSuccessMessage}
          dismissible
        >
          <p>Donation modifiée avec succès !</p>
        </Alert>
      )}

        <div className={styles.container}>
          <div className={styles.personalInfo}>
            <div className={styles.header}>
              <h4>Informations sur la donation</h4>

              {donation && (
                <AccessControl module="DONATION" functionality="UPDATE">
                  <Link
                    to={{
                      pathname: `/donations/edit/${donation.id}`,
                      state: { redirectTo: 'consultation' },
                    }}
                  >
                    <button className="btn-style secondary">
                      Modifier
                    </button>
                  </Link>
                </AccessControl>
              )}
            </div>

          <div className={`d-flex justify-content-center mt-4 ${styles.donationContainer}`}>
            <div className={`d-flex py-3 px-5 ${styles.donationInfo}`}>
              <div className={styles.labelColumn}>
                <p><strong>Montant Total</strong></p>
                {
                  donation && donation.transactionNumber && (
                    <p><strong>Numéro de reçu</strong></p>
                  )
                }
              
                {
                  donation && donation.valueCurrency && donation.value
                  ? <p><strong>Devise</strong></p>
                  : null
                }
                <p><strong>Date de réception</strong></p>
                {donation && donation.canalDonation && donation.canalDonation.name && (
                  <p><strong>Canal de la donation</strong></p>
                )}
                {donation && donation.comment && (
                  <p><strong>Commentaire</strong></p>
                )}

              </div>

              <div style={{ marginLeft: '20px' }}>
                <p>{donation ? donation.value : '---'} DH</p>
                <p>{donation ? donation.transactionNumber : '---'}</p>
                {donation && donation.valueCurrency && donation.value && (
                  <p>{donation.valueCurrency} {donation.currency.name}</p>
                )}

                <p>
                  {donation && donation.receptionDate 
                    ? formatDate(donation.receptionDate)
                    : '---'}
                </p>
                {donation && donation.canalDonation && donation.canalDonation.name && (
                  <p>{donation.canalDonation.name || '---'}</p>
                )}
                {donation && donation.comment && (
                  <p>{donation.comment || '---'}</p>
                )}
              </div>
            </div>
          </div>

            {donation &&
              ((donation.budgetLines && donation.budgetLines.length > 0) ||
                donation.kafalatvalue ||
                donation.nonIdentifiedValue) && (
                <div
                  style={{
                    marginTop: '30px',
                    padding: '10px',
                    border: '1px solid #ccc',
                  }}
                >
                  {renderBudgetLines()}
                </div>
              )}
          </div>
        </div>

    </div>
  );
}