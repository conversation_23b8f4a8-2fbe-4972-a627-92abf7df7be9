import moment from 'moment';

const setLocations = city => ({
  country: {
    id:
      city && city.region && city.region.country && city.region.country.id
        ? city.region.country.id
        : '',
  },
  region: {
    id: city && city.region && city.region.id ? city.region.id : '',
  },
});

const getEditValues = (member, initialValues, locations) => {
  const formatDate = 'YYYY-MM-DD';

  const location = setLocations(member.person.city);
  return {
    ...initialValues,
    ...member,
    ...locations,
    person: {
      ...initialValues.person,
      ...member.person,
      ...location,
      birthDate:
        member.person.birthDate &&
        moment(member.person.birthDate).format(formatDate),
      deathDate:
        member.person.deathDate &&
        moment(member.person.deathDate).format(formatDate),
      uploadedPicture: member.person.pictureBase64
        ? `data:image/png;base64,${atob(member.person.pictureBase64)}`
        : initialValues.person.uploadedPicture,
    },
    tutorEndDate:
      member.tutorEndDate && moment(member.tutorEndDate).format(formatDate),
    tutorStartDate:
      member.tutorStartDate && moment(member.tutorStartDate).format(formatDate),
  };
};

export default getEditValues;
