import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import { useDispatch, useSelector } from 'react-redux';
import moment from 'moment';
import { useInjectReducer, useInjectSaga } from 'redux-injectors';
import { Link, useHistory, useParams } from 'react-router-dom';
import styles from './Solde/styles.css';
import CustomPagination from '../../Common/CustomPagination';
import {
  makeSelectCanalDonations,
  makeSelectError,
  makeSelectLoading,
  makeSelectReleveDonor,
} from './selectors';
import { loadCanalDonations, loadReleveDonor } from './actions';
import reducer from './reducer';
import saga from './saga';
import ReactToPrint from 'react-to-print';
import PrintableContent from './PrintableContent/PrintableContent';
import Modal2 from 'components/Common/Modal2';
import eye from 'images/icons/eye.svg';

const formatDate = date => (date ? moment(date).format('DD/MM/YYYY') : '-');

const sortDataByDate = data => {
  if (!Array.isArray(data)) {
    return data;
  }
  return [...data].sort((a, b) => {
    const dateA = a.receptionDate || a.dateExecution;
    const dateB = b.receptionDate || b.dateExecution;
    return moment(dateA).isBefore(moment(dateB)) ? -1 : 1;
  });
};

const groupAndSumMontantEntreeSortie = data => {
  if (!Array.isArray(data)) return [];
  const map = new Map();
  data.forEach(el => {
    // Group by id and type (entree or sortie)
    const isEntree = el.montantEntree != null;
    const key = isEntree ? `entree-${el.id}` : `sortie-${el.id}`;
    if (map.has(key)) {
      const existing = map.get(key);
      if (isEntree) {
        map.set(key, { ...el, montantEntree: (existing.montantEntree || 0) + (el.montantEntree || 0) });
      } else {
        map.set(key, { ...el, montantSortie: (existing.montantSortie || 0) + (el.montantSortie || 0) });
      }
    } else {
      map.set(key, { ...el });
    }
  });
  return Array.from(map.values());
};

const BodyComponent = ({
  data,
  openModal = ({ list = [], serviceName = '', date = '' }) => {},
}) => {
  const groupedData = groupAndSumMontantEntreeSortie(data);
  return (
    <>
      {groupedData && groupedData.length > 0 ? (
        groupedData.map(el => {
          if (el.montantEntree != null) {
            return (
              <tr key={`entree-${el.id}`}>
                <td>{formatDate(el.receptionDate)}</td>
                <td>{el.montantEntree}</td>
                <td>{el.type}</td>
                <td>{el.canalDonation ? el.canalDonation.name : '-'}</td>
                <td className={styles.backgroundGray}></td>
                <td>-</td>
                <td>-</td>
                <td>-</td>
              </tr>
            );
          }
          // For sortie rows
          return (
            <tr key={`sortie-${el.id}`}>
              <td>-</td>
              <td>-</td>
              <td>-</td>
              <td>-</td>
              <td className={styles.backgroundGray}></td>
              <td>{formatDate(el.dateExecution)}</td>
              <td>{el.montantSortie}</td>
              <td
                style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                }}
              >
                {el.name ? (
                  <Link
                    to={{ pathname: `/aide-complementaire/fiche/${el.id}/info` }}
                    style={{ color: 'blue', cursor: 'pointer' }}
                  >
                    {`${el.name || '-'}`}
                  </Link>
                ) : (
                  <Link
                    to={{ pathname: `/takenInCharges/fiche/${el.id}/planification` }}
                    style={{ color: 'blue', cursor: 'pointer' }}
                  >
                    {`${el.services.name || '-'}`}
                  </Link>
                )}
                {el.beneficiaries && el.beneficiaries.length > 0 && (
                  <span
                    onClick={() => {
                      openModal({
                        list: el.beneficiaries,
                        serviceName: el.name
                          ? el.name
                          : el.services && el.services.name
                          ? el.services.name
                          : '-',
                        date: formatDate(el.dateExecution),
                      });
                    }}
                    style={{
                      width: 30,
                      height: 30,
                      borderRadius: 30,
                      display: 'flex',
                      flexDirection: 'column',
                      justifyContent: 'center',
                      alignItems: 'center',
                      backgroundColor: '#F1F1F1',
                    }}
                  >
                    <div
                      style={{
                        color: 'blue',
                        cursor: 'pointer',
                        fontSize: 15,
                      }}
                    >
                      +
                    </div>
                  </span>
                )}
              </td>
            </tr>
          );
        })
      ) : (
        <tr>
          <td colSpan="8">Aucun relevé disponible</td>
        </tr>
      )}
    </>
  );
};

const key = 'releveDonor';
const pageSize = 10;
export default function ListReleve(props) {
  const history = useHistory();
  const donor = props.data;
  const { error, loading, releves, canalDonations } = useSelector(state => ({
    error: makeSelectError(state),
    loading: makeSelectLoading(state),
    releves: makeSelectReleveDonor(state),
    canalDonations: makeSelectCanalDonations(state),
  }));

  useInjectReducer({ key, reducer });
  useInjectSaga({ key, saga });

  const dispatch = useDispatch();
  const params = useParams();
  const [activePage, setActivePage] = useState(1);
  const [startDate, setStartDate] = useState(moment().startOf('year'));
  const [endDate, setEndDate] = useState(moment().endOf('year'));
  const [typeFilter, setTypeFilter] = useState('');
  const [modalVisible, setModalVisible] = useState(false);
  const [beneficiaries, setBeneficiaries] = useState([]);
  const [currentServiceName, setCurrentServiceName] = useState('');
  const [beneficiaryDate, setBeneficiaryDate] = useState('');
  const [filteredData, setFilteredData] = useState([]);
  const openModal = ({ list = [], serviceName = '', date = '' }) => {
    setBeneficiaries(list);
    setCurrentServiceName(serviceName);
    setBeneficiaryDate(date);
    setModalVisible(true);
  };
  const closeModal = () => {
    setModalVisible(false);
  };

  useEffect(() => {
    dispatch(loadReleveDonor(params.id));
    dispatch(loadCanalDonations());
  }, [dispatch, params.id]);

  const handlePageChange = pageNumber => {
    setActivePage(pageNumber);
  };

  const groupedData = useMemo(() => groupAndSumMontantEntreeSortie(releves), [releves]);

  const paginatedData = useMemo(() => {
    const data = filteredData.length > 0 ? filteredData : groupedData;
    const startIndex = (activePage - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    return data.slice(startIndex, endIndex);
  }, [filteredData, groupedData, activePage]);

  const totalDonation = useMemo(() => {
    if (releves && releves.length > 0) {
      const item = releves.find(
        el => el.typeDonationKafalat === 'donation',
      );
      return item ? item.totalEntree : 0;
    }
    return 0;
  }, [releves]);

  const totalOther = useMemo(() => {
    return paginatedData
      .filter(el => el.montantEntree == null)
      .reduce((sum, el) => sum + (el.montantSortie || 0), 0);
  }, [paginatedData]);

  const totalEntrantMontant = useMemo(() => {
    return paginatedData
      .filter(el => el.montantEntree != null)
      .reduce((sum, el) => sum + (el.montantEntree || 0), 0);
  }, [paginatedData]);

  const componentRef = useRef();

  const applyFilter = () => {
    let data = groupAndSumMontantEntreeSortie(releves);
    if (startDate && endDate) {
      data = data.filter(el => {
        const dateA = el.receptionDate;
        const dateB = el.dateExecution;
        const isAInRange = dateA &&
          moment(dateA).isSameOrAfter(moment(startDate), 'day') &&
          moment(dateA).isSameOrBefore(moment(endDate), 'day');
        const isBInRange = dateB &&
          moment(dateB).isSameOrAfter(moment(startDate), 'day') &&
          moment(dateB).isSameOrBefore(moment(endDate), 'day');
        return isAInRange || isBInRange;
      });
    }
    if (typeFilter && typeFilter !== 'Tous') {
      data = data.filter(el => el.type === typeFilter);
    }
    setFilteredData(data);
    setActivePage(1);
  };

  return (
    <div className="listReleve-container br-bottoms-20">
      <Modal2
        title={'List des Beneficiares'}
        size="lg"
        customWidth="modal-90w"
        show={modalVisible}
        handleClose={closeModal}
      >
        <p>{`List des beneficiares de service :  ${currentServiceName}`}</p>

        <div
          style={{
            gap: 10,
            display: 'flex',
            flexDirection: 'column',
            maxHeight: 400,
            overflowY: 'scroll',
            alignItems: 'center',
          }}
        >
          {beneficiaries &&
            beneficiaries.length > 0 &&
            beneficiaries.map(el => {
              return (
                <div
                  className="border"
                  style={{
                    display: 'flex',
                    flexDirection: 'row',
                    alignItems: 'center',
                    paddingInline: 10,
                    minHeight: 50,
                    width: 300,
                    borderRadius: 10,
                    justifyContent: 'space-between',
                  }}
                >
                  <div
                    style={{
                      display: 'flex',
                      flexDirection: 'row',
                      gap: 5,
                    }}
                  >
                    <div>{el.personLastName}</div>
                    <div>{el.montantAffecter} DH</div>
                    <div>{beneficiaryDate} </div>
                  </div>
                  <div>
                    <img
                      src={eye}
                      style={{
                        cursor: 'pointer',
                        color: 'blue',
                      }}
                      width="20px"
                      height="20px"
                      alt="rightArrow"
                      onClick={() => {
                        history.push(`/beneficiaries/fiche/${el.id}/info`);
                      }}
                    />
                  </div>
                </div>
              );
            })}
        </div>
      </Modal2>

      <div className="d-flex bg-white ">
        <h4>Relevé du compte donateur</h4>
      </div>
      <div className="formContainer" >
        <select
          value={typeFilter}
          onChange={e => setTypeFilter(e.target.value)}
          className="form-control input-border w-30"
        >
          <option value="">Tous</option>
          <option value="Nature">Nature</option>
          <option value="Financière">Financière</option>
        </select>

        <div className="withLabelContainer w-30">
          <label>
            Date Debut:
          </label>
          <input
            type="date"
            value={startDate ? moment(startDate).format('YYYY-MM-DD') : ''}
            max={
              endDate
                ? moment(endDate)
                    .subtract(1, 'days')
                    .format('YYYY-MM-DD')
                : ''
            }
            onChange={e => setStartDate(e.target.value)}
            className="form-control input-border"
          />
        </div>

        <div className="withLabelContainer w-30">
          <label>
            Date Fin :
          </label>
          <input
            type="date"
            value={endDate ? moment(endDate).format('YYYY-MM-DD') : ''}
            min={
              startDate
                ? moment(startDate)
                    .add(1, 'days')
                    .format('YYYY-MM-DD')
                : ''
            }
            onChange={e => setEndDate(e.target.value)}
            className="form-control input-border"
          />
        </div>

      </div>
      <div className="actionBtns">
        <button
          className="btn-style primary"
          onClick={applyFilter}
        >
          Appliquer
        </button>
        <div className="d-flex align-items-center">
          <ReactToPrint
              trigger={() => (
                  <button className="btn-style secondary">Imprimer</button>
              )}
              content={() => componentRef.current}
          />
          <div style={{ display: 'none' }}>
            <PrintableContent
                ref={componentRef}
                data={paginatedData}
                totalDonation={totalDonation}
                totalOther={totalOther}
                donor={donor}
                startDate={moment(startDate).format('DD-MM-YYYY')}
                endDate={moment(endDate).format('DD-MM-YYYY')}
            />
          </div>
        </div>
        <button
            className="btn-style success"
            onClick={() => {
              setTypeFilter('Tous');
              setStartDate(moment().startOf('year'));
              setEndDate(moment().endOf('year'));
              setFilteredData([]);
            }}
        >
          Reset
        </button>
      </div>
      <div className="d-flex bg-white flex-column ">
        <table className="table table-bordered">
          <thead>
            <tr>
              <th colSpan={4} className="text-align-center">
                Donations effectuées
              </th>
              <th className={styles.backgroundGray}></th>
              <th colSpan={4} className="text-align-center">
                Opérations de prise en charges exécutées
              </th>
            </tr>
            <tr>
              <th>Date</th>
              <th>Montant</th>
              <th>Type</th>
              <th>Canal</th>
              <th className={styles.backgroundGray}></th>
              <th style={{ width: 115 }}>Date d'exécution</th>
              <th>Montant</th>
              <th colSpan={3}>Service</th>
            </tr>
          </thead>
          <tbody>
            <BodyComponent data={paginatedData} openModal={openModal} />
          </tbody>
          <tfoot>
            <tr>
              <td>Total</td>
              <td>{`${totalEntrantMontant} DH`}</td>
              <td></td>
              <td></td>
              <td className={styles.backgroundGray}></td>
              <td></td>
              <td>{`${totalOther} DH`}</td>
              <td></td>
            </tr>
          </tfoot>
        </table>
        {groupedData.length > 0 && (
          <div className="d-flex justify-content-center bg-white">
            <CustomPagination
                totalElements={filteredData.length > 0 ? filteredData.length : groupedData.length}
                totalCount={
                    (filteredData.length > 0 ? filteredData.length : groupedData.length) > 0 &&
                    Math.ceil((filteredData.length > 0 ? filteredData.length : groupedData.length) / pageSize)
                }
                pageSize={pageSize}
                currentPage={activePage}
                onPageChange={handlePageChange}
            />
          </div>
        )}
      </div>
    </div>
  );
}
