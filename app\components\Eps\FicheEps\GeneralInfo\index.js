import React, { useEffect, useState } from 'react';
import { Link, useHistory, useLocation } from 'react-router-dom';
import Alert from 'react-bootstrap/Alert';
import styles from '../../../../Css/personalInfo.css';
import btnStyles from '../../../../Css/button.css';

export default function EPSInfo(props) {
  const eps = props.data;
  const location = useLocation();
  const history = useHistory();
  const [showSuccessMessage, setShowSuccessMessage] = useState(false);
  const [message, setMessage] = useState('');

  useEffect(() => {
    if (location.state === 'success') {
      setShowSuccessMessage(true);
      setMessage('EPS modifiée avec succès !');
    }
  }, [location.state]);
  useEffect(() => {
      if (showSuccessMessage) {
        const timer = setTimeout(() => {
          handleCloseSuccessMessage();
        }, 4000);
        return () => clearTimeout(timer);
      }
    }, [showSuccessMessage]);

  const handleCloseSuccessMessage = () => {
    setShowSuccessMessage(false);
    history.replace({ ...location, state: null });
  };

  function formatDateString(dateString) {
    const date = new Date(dateString);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0'); 
    const day = String(date.getDate()).padStart(2, '0'); 
    return `${day}/${month}/${year}`;
}
  let data1 = null;
  if (eps) {
    data1 = (
      <div className={styles.data1}>
        {eps.name && <p>{eps.name} / {eps.nameAr}</p>}
        {eps.code && <p>{eps.code}</p>}
        {eps.address && <p>{eps.address} / {eps.addressAr}</p>}
        {eps.cityDetails && eps.cityDetails.length > 0 && (
          <p>{eps.cityDetails.map((city) => city.name).join(', ')}</p>
        )}
        {eps.beneficiaryType && <p>{eps.beneficiaryType}</p>}
        {eps.ageGroups && <p>{eps.ageGroups}</p>}
        {eps.capacity && <p>{eps.capacity}</p>}
        {eps.dateCreated && <p>{formatDateString(eps.dateCreated) }</p>}
        {eps.comment && <p>{eps.comment}</p>}
        {!eps.comment && <p>Aucun commentaire</p>}
      </div>
    );
  }

  return (
    <div> 
      {showSuccessMessage && (
        <Alert
          className="pb-0"
          variant="success"
          onClose={handleCloseSuccessMessage}
          dismissible
        >
          <p>Eps modifié avec succès</p>
        </Alert>
      )}

      <div className={styles.personalInfo}>
        <div className={styles.header}>
          <h4>Informations sur l’EPS</h4>
          <Link
            to={{
              pathname: `/eps/editEps/${eps.id}`,
              state: { redirectTo: 'consultation' },
            }}
          >
            <button className={btnStyles.editBtnProfile}>Modifier</button>
          </Link>
        </div>

        <div className="d-flex justify-content-center mt-4">
          <div className={`d-flex py-3 px-5 ${styles.donationInfo}`}>
            <div className={styles.label1} style={{ marginRight: '30px' }}>
              {eps.name && <p>Nom de l'établissement</p>}
              {eps.code && <p>Code EPS</p>}
              {eps.address && <p>Adresse</p>}
              {eps.cityDetails && eps.cityDetails.length > 0 && <p>Villes associées</p>}
              {eps.beneficiaryType && <p>Type de bénéficiaires</p>}
              {eps.ageGroups && <p>Tranche d'âge</p>} 
              {eps.capacity && <p>Capacité</p>} 
              {eps.dateCreated && <p>Date de création du centre</p>} 
              {eps.comment && <p>Commentaire</p>}
              {!eps.comment && <p>Commentaire</p>}
            </div>
            {data1}
          </div>
        </div>
      </div>
    </div>
  );
}
