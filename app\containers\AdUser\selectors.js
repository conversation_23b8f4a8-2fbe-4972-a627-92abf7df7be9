// selectors.js
import { createSelector } from 'reselect';
import { initialState } from './reducer';

const selectAdUser = state => state.adUser || initialState;

const makeSelectAdUsers = createSelector(
  selectAdUser,
  adUserState => adUserState.adUsers,
);

const makeSelectLoading = createSelector(
  selectAdUser,
  adUserState => adUserState.loading,
);

const makeSelectError = createSelector(
  selectAdUser,
  adUserState => adUserState.error,
);

const makeSelectLoadingUser = createSelector(
  selectAdUser,
  adUserState => adUserState.loadingUser,
);

const makeSelectErrorUser = createSelector(
  selectAdUser,
  adUserState => adUserState.errorUser,
);
const makeSelectAddUserSuccess = createSelector(
  selectAdUser,
  adUserState => adUserState.addUserSuccess,
);

const makeSelectAddUserError = createSelector(
  selectAdUser,
  adUserState => adUserState.error,
);
const makeSelectDeleteUserSuccess = createSelector(  // New selector for deleteUserSuccess
  selectAdUser,
  adUserState => adUserState.deleteUserSuccess,
);

const makeSelectDeleteUserError = createSelector(    // New selector for deleteUserError
  selectAdUser,
  adUserState => adUserState.deleteUserError,
);

const makeSelectChangeUserRoleSuccess = createSelector(
  selectAdUser,
  adUserState => adUserState.changeUserRoleSuccess,
);





export {
  selectAdUser,
  makeSelectAdUsers,
  makeSelectLoading,
  makeSelectError,
  makeSelectLoadingUser,
  makeSelectErrorUser,
  makeSelectAddUserSuccess,
  makeSelectAddUserError,
  makeSelectDeleteUserSuccess,
  makeSelectDeleteUserError,
  makeSelectChangeUserRoleSuccess,
};
