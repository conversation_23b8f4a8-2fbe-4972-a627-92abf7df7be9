import React from 'react';
import moment from 'moment';
import tagStyles from 'Css/tag.css';
import table from 'Css/table.css';
const getTag = status => {
  if (status == 'Exécuté') {
    return tagStyles.tagGreen;
  }
  if(status == 'Réservé'){
    return tagStyles.tagYellow;
  }
  if (status == 'Clôturé') {
    return tagStyles.tagGrey;
  }
  if (status == 'Planifié') {
    return tagStyles.tagBlue;
  }
  return tagStyles.tagGrey;
};
const formatDate = date => moment(date).format('DD/MM/YYYY');
export default function Details(props) {
  const beneficiaryTakenInCharge = props.data;
  const emptyValue = <span style={{ textAlign: 'center' }}>-</span>;
  let operationsSorted = [];
  let operations = [];
  if (
    beneficiaryTakenInCharge &&
    beneficiaryTakenInCharge.takenInCharge.takenInChargeDonors[0]
  ) {
    operationsSorted = [
      ...beneficiaryTakenInCharge.takenInCharge.takenInChargeDonors[0]
        .takenInChargeOperations,
    ].sort((a, b) => (a.createdAt < b.createdAt ? 1 : -1));

    operations = operationsSorted.map(operation => (
      <tr key={operation.id}>
        <td className="col-md-2">{operation.code}</td>
        <td className="col-md-2">
          {operation.planningDate
            ? formatDate(operation.planningDate)
            : emptyValue}
        </td>
        <td className="col-md-2">
          {operation.executionDate
            ? formatDate(operation.executionDate)
            : emptyValue}
        </td>

        <td className="col-md-2">{operation.amount} DH</td>
        <td className="col-md-2">{operation.managementFees} % </td>

        <td className="col-md-2">
          {operation.planningDate && !operation.reserved && (
            <div className={getTag('Planifié')}>Planifié</div>
          )}
          {operation.reserved && !operation.executionDate && (
            <div className={getTag('Réservé')}>Réservé</div>
          )}

          {operation.executionDate && !operation.closureDate && (
            <div className={getTag('Exécuté')}>Exécuté</div>
          )}

          {operation.closureDate && (
            <div className={getTag('Clôturé')}>Clôturé</div>
          )}
        </td>
      </tr>
    ));
  }

  return (
    <div>
      <div className={table.global}>
        {operations.length > 0 ? (
          <table className="table small mx-auto table-borderless">
            <thead>
              <tr>
                <th scope="col">Code opération</th>
                <th scope="col">Date de planification</th>
                <th scope="col">Date d'execution</th>
                <th scope="col">Montant</th>
                <th scope="col">Frais</th>
                <th scope="col">Statut</th>
              </tr>
            </thead>
            <tbody>{operations}</tbody>
          </table>

        ) : (
          <table className="table small mx-auto table-borderless">
            <div className="text-center">Aucune opération</div>
          </table>
        )}

      </div>
    </div>
  );
}
