import React from 'react';
import {
  Container,
  Grid,
  makeStyles,
  Paper,
  Typography,
} from '@material-ui/core';
import PrintHeader from '../../Common/PrintHeader/PrintHeader';

const useStyles = makeStyles(theme => ({
  container: {
    padding: '40px',
    fontFamily: "'Roboto', sans-serif",
    backgroundColor: '#f5f5f5',
    color: '#333',
  },
  paper: {
    padding: '40px',
    borderRadius: '15px',
    boxShadow: '0 8px 40px rgba(0, 0, 0, 0.12)',
    backgroundColor: '#ffffff',
    position: 'relative',
    overflow: 'hidden',
  },
  sectionTitle: {
    marginBottom: '30px',
    color: '#3f51b5',
    textTransform: 'uppercase',
    letterSpacing: '1.5px',
    borderBottom: '2px solid #3f51b5',
    paddingBottom: '5px',
    marginTop: '20px',
  },
  gridItem: {
    marginBottom: '25px',
  },
  tableContainer: {
    marginBottom: '30px',
    pageBreakInside: 'avoid',
  },
  table: {
    minWidth: 650,
    '& th': {
      backgroundColor: '#3f51b5',
      color: '#ffffff',
    },
    '& td, & th': {
      border: '1px solid #e0e0e0',
      padding: '14px',
      fontSize: '16px',
    },
  },
  logo: {
    width: '60px',
    position: 'absolute',
    top: '20px',
    right: '20px',
  },
  donorPicture: {
    width: '150px',
    height: '150px',
    borderRadius: '50%',
    border: '5px solid #ffffff',
    boxShadow: '0 4px 10px rgba(0, 0, 0, 0.3)',
    marginBottom: '20px',
  },
  section: {
    marginTop: '30px',
    pageBreakInside: 'avoid',
  },
  infoItem: {
    display: 'flex',
    alignItems: 'center',
    marginBottom: '15px',
  },
  infoLabel: {
    marginRight: '15px',
    fontWeight: 'bold',
    color: '#3f51b5',
    whiteSpace: 'nowrap',
  },
  contactInfo: {
    marginTop: theme.spacing(3),
  },
  contactTitle: {
    position: 'relative',
    display: 'inline-block',
  },
  titleUnderline: {
    content: '""',
    display: 'block',
    width: '100%',
    height: '2px',
    backgroundColor: 'black',
    position: 'absolute',
    bottom: 0,
    left: 0,
    color: 'black',
  },
  tableTitle: {
    pageBreakInside: 'avoid',
  },
}));

const DataRow = ({ label, value }) => {
  const classes = useStyles();
  return (
    <Grid item xs={12} sm={6} className={classes.gridItem}>
      <div className={classes.infoItem}>
        <Typography variant="subtitle1" className={classes.infoLabel}>
          {label}:
        </Typography>
        <Typography variant="body1">{value || '-'}</Typography>
      </div>
    </Grid>
  );
};

const PrintableContent = React.forwardRef(({ data, isPerson }, ref) => {
  const classes = useStyles();
  if (!data) {
    return null;
  }
  const parsedData = isPerson
    ? data
    : {
        groupName: data.groupName,
        groupCode: data.groupCode,
        groupPhoneNumber: data.groupPhoneNumber,
        groupFullNameContact: data.groupFullNameContact,
        groupCreatedAt: data.groupCreatedAt,
      };

  const formatDate = date => new Date(date).toLocaleDateString('fr-FR');

  const personalInfoItems = isPerson
    ? [
        { label: 'Prénom', value: parsedData.firstName },
        { label: 'Nom de famille', value: parsedData.lastName },
        { label: 'Prénom (Arabe)', value: parsedData.firstNameAr },
        { label: 'Nom de famille (Arabe)', value: parsedData.lastNameAr },
      ]
    : [
        { label: 'Nom group', value: parsedData.groupName },
        { label: 'Code du groupe', value: parsedData.groupCode },
        { label: 'Télephone', value: parsedData.groupPhoneNumber },
        {
          label: 'Nom Complet du group',
          value: parsedData.groupFullNameContact,
        },
        { label: 'Créé le', value: parsedData.groupCreatedAt },
      ];

  return (
    <Container
      style={{ backgroundColor: '#ffffff' }}
      ref={ref}
      className={classes.container}
    >
      <Paper className={classes.paper}>
        <PrintHeader headerText="Fiche Personne AdHoc" />
        <div className={classes.section}>
          <Typography
            variant="h5"
            gutterBottom
            className={classes.sectionTitle}
          >
            Informations Personnelles
          </Typography>
          <hr className={classes.lineStyle} />
          <Grid container spacing={3}>
            {[
              ...personalInfoItems,
              isPerson &&
                ({
                  label: "Numéro d'identité",
                  value: parsedData.identityCode,
                },
                { label: 'Créé à', value: formatDate(parsedData.createdAt) }),
            ].map((item, index) => (
              <DataRow key={index} label={item.label} value={item.value} />
            ))}
          </Grid>
        </div>
      </Paper>
      <Paper className={classes.paper}>
        <PrintHeader headerText="Fiche Personne AdHoc" />
      </Paper>
    </Container>
  );
});

export default PrintableContent;
