import React, { useEffect, useState, useRef } from 'react';
import moment from 'moment';
import styles from 'Css/profileList.css';
import stylesList from 'Css/profileList.css';
import btnStyles from 'Css/button.css';
import navigationStyle from 'Css/sectionNavigation.css';
import reducer from 'containers/Common/SubComponents/DocumentForm/reducer';
import { deleteDocumentSaga } from 'containers/Common/SubComponents/DocumentForm/saga';
import { createStructuredSelector } from 'reselect';
import { useDispatch, useSelector } from 'react-redux';
import { useInjectReducer, useInjectSaga } from 'redux-injectors';
import { useParams, useLocation } from 'react-router-dom';
import DocumentForm from 'containers/Common/SubComponents/DocumentForm';
import tagStyles from '../../../../Css/tag.css';
import tagReducer from 'containers/tag/reducer';
import tagSaga from 'containers/tag/saga';
import {
  makeSelecDocument,
  makeSelecDocumentDeleteSuccess,
  makeSelecDocumentDownloadSuccess,
  makeSelecDocuments,
  makeSelectDocumentAddDonorSuccess,
  makeSelectError,
  makeSelectSuccess,
} from 'containers/Common/SubComponents/DocumentForm/selectors';
import {
  deleteDocument,
  downloadDocument,
  fetchDataRequest,
  resetDocument,
  viewDocument,
} from 'containers/Common/SubComponents/DocumentForm/actions';
import Modal2 from 'components/Common/Modal2';
import { Alert, Modal } from 'react-bootstrap';
import DataTable from 'components/Common/DataTable';
import CustomPagination from 'components/Common/CustomPagination';
import { makeSelectTagList  } from 'containers/tag/selectors';
import { getTagsByType } from 'containers/tag/actions';
import ArrowForwardIosIcon from '@mui/icons-material/ArrowForwardIos';
import ChevronRightIcon from '@mui/icons-material/ChevronRight';

import {
  DELETE_ICON,
  DOWNLOAD_ICON,
  EDIT_ICON,
  VIEW_ICON,
} from '../../../Common/ListIcons/ListIcons';
import AccessControl, { isAuthorized } from 'utils/AccessControl';
import { CircularProgress } from '@mui/material';
import styled from 'styled-components';

const TagsContainer = styled.div`
  display: flex;
  flex-wrap: nowrap;
  gap: 10px;
  margin: 20px 0;
  padding: 18px 40px;
  overflow-x: hidden;
  border: 2px solid rgb(33, 108, 184);
  border-radius: 20px;
  background-color: #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  position: relative;
`;

const TagsWrapper = styled.div`
  display: flex;
  gap: 10px;
  overflow-x: hidden;
  scroll-behavior: smooth;
  width: 100%;
  padding: 0 20px;
  margin-right: 20px;
  outline: none;
  -webkit-overflow-scrolling: touch;

  &:focus {
    outline: none;
  }

  &::-webkit-scrollbar {
    display: none;
  }

  -ms-overflow-style: none;
  scrollbar-width: none;
`;

const NavigationArrow = styled.button`
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: white;
  border: 1px solid rgb(33, 108, 184);
  color: rgb(33, 108, 184);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  z-index: 1;
  padding: 0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

  &:hover {
    background-color: rgb(33, 108, 184);
    color: white;
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    pointer-events: none;
  }
`;

const LeftArrow = styled(NavigationArrow)`
  left: 8px;
  transform: translateY(-50%) rotate(180deg);
`;

const RightArrow = styled(NavigationArrow)`
  right: 8px;
`;

const Tag = styled.div`
  background-color: ${props => `#${props.color}`};
  color: ${props => {
    const hex = props.color || 'ffffff';
    const r = parseInt(hex.substr(0, 2), 16);
    const g = parseInt(hex.substr(2, 2), 16);
    const b = parseInt(hex.substr(4, 2), 16);
    const brightness = (r * 299 + g * 587 + b * 114) / 1000;
    return brightness > 128 ? '#000000' : '#ffffff';
  }};
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
  flex-shrink: 0;
  border: 1px solid ${props => props.selected ? '#000000' : '#e0e0e0'};
  display: flex;
  align-items: center;
  gap: 8px;
  opacity: ${props => props.selected ? 1 : props.hasSelected ? 0.5 : 1};
  outline: none;

  &:hover {
    transform: scale(1.05);
    border-color: #bdbdbd;
    outline: none;
  }

  &:focus {
    outline: none;
  }
`;

const ResetTag = styled(Tag)`
  background-color: white;
  color: rgb(33, 108, 184);
  border: 1px solid rgb(33, 108, 184);
  border-radius: 50%;
  width: 32px;
  height: 32px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  opacity: 1;
  margin-top: 5px;

  &:hover {
    background-color: rgb(33, 108, 184);
    color: white;
  }
`;

const CloseIcon = styled.span`
  font-size: 16px;
  font-weight: bold;
  margin-left: 4px;
`;

const key = 'document';
const keyTag = 'tagList';

const target = 'family';

const omdbSelector = createStructuredSelector({
  success: makeSelectSuccess,
  error: makeSelectError,
  document: makeSelecDocument,
  documents: makeSelecDocuments,
  successDelete: makeSelecDocumentDeleteSuccess,
  successDownload: makeSelecDocumentDownloadSuccess,
  documentDonorAddSuccess: makeSelectDocumentAddDonorSuccess,
});

const tagSelector = createStructuredSelector({
  tagList: makeSelectTagList,
});

const formatDate = date => moment(date).format('DD/MM/YYYY');

export default function Documents(props) {
  const family = props.data;
  const beneficiary = null;
  let listDocuments = null;
  let successAlert = null;
  const dispatch = useDispatch();
  let familyCode;

  if (family) {
    familyCode = family.code;
  }

  useInjectReducer({ key, reducer });
  useInjectSaga({ key, saga: deleteDocumentSaga });


  useInjectReducer({ key: keyTag, reducer: tagReducer });
  useInjectSaga({ key: keyTag, saga: tagSaga });


  const params = useParams();

  const {
    success,
    error,
    document,
    successDelete,
    documents,
    successDownload,
    documentDonorAddSuccess,
  } = useSelector(omdbSelector);
  const {tagList} = useSelector(tagSelector)
  const [typeDocumentName, setTypeDocumentName] = useState('');
  const [documentToEdit, setDocumentToEdit] = useState('');
  const [message, setMessage] = useState('');
  const [documentToDelete, setDocumentToDelete] = useState('');
  let errorMessage = null;
  const [show, setShow] = useState(false);
  const [showAlert, setShowAlert] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [normalDocument, setNormalDocument] = useState(true);
  const [activeSection, setActiveSection] = useState('type');
  const [hide, setHide] = useState(false);
  const [canScrollLeft, setCanScrollLeft] = useState(false);
  const [canScrollRight, setCanScrollRight] = useState(false);
  const tagsWrapperRef = useRef(null);


  const handleShowForDeleteModal = () => setShowDeleteModal(true);
  const handleCloseForDeleteModal = () => setShowDeleteModal(false);
  const handleClose = () => setShow(false);
  const handleShow = () => setShow(true);

  const location = useLocation();

  useEffect(() => {
    if (location && location.state && location.state.isOldBeneficiary) {
      setHide(location.state.isOldBeneficiary);
    } else {
      setHide(false);
    }
  }, [location]);

  // Define pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const pageSize = 5; // Define page size

  useEffect(()=>{
    dispatch(getTagsByType('document'));
  },[dispatch])

  useEffect(
    () =>
      function cleanup() {

        dispatch(resetDocument());
        setDocumentToEdit('');

      },
    [],
  );

  if (error) {
    errorMessage = 'Une erreur est survenue';
  }

  useEffect(() => {
    if (successDelete) {
      setShowAlert(true);
      setMessage('Document supprimé avec succès');
    }
  }, [successDelete]);

  useEffect(() => {
    if (documentDonorAddSuccess) {
      setShowAlert(true);
      if (documentToEdit) {
        if (normalDocument) {
          setMessage('Document modifié avec succès');
          setActiveSection('other');
        } else {
          setMessage('Piece jointe modifié avec succès');
          setActiveSection('type');
        }
      } else {
        if (normalDocument) {
          setMessage(' Document ajouté avec succès');
          setActiveSection('other');
        } else {
          setMessage('Pièce jointe ajoutée avec succès');
          setActiveSection('type');
        }
      }

      setDocumentToEdit('');
      dispatch(resetDocument());
    }
  }, [documentDonorAddSuccess]);

  // to close the success message after 4 seconds
  useEffect(() => {
    if (showAlert) {
      setTimeout(() => {
        setShowAlert(false);
      }, 4000);
    }
  }, [showAlert]);



  useEffect(() => {
    dispatch(fetchDataRequest(params.id, target));
  }, []);

  useEffect(() => {
    if (successDelete || documentDonorAddSuccess) {
      dispatch(fetchDataRequest(params.id, target));
    }
  }, [successDelete, documentDonorAddSuccess]);

  if (showAlert) {
    successAlert = (
      <Alert className="alert-style" variant="success" onClose={() => setShowAlert(false)} dismissible>
        {message}
      </Alert>
    );
  }

  const getTag = statut => {
    switch (statut) {
      case 'Expiré':
        return tagStyles.tagExpired;
      case 'Non expiré':
        return tagStyles.tagValid;
      default:
        return tagStyles.tagValid;
    }
  };
  useEffect(() => {
    if (documentsWithType && documentsWithoutType && location.state && location.state.openDocumentId) {
      console.log('here is the list ', documents);
      // Find the document by ID (ensure listDocumentsWithType is stable) if(!location.state.isNormaleDoc){
      console.log('location.state.openDocumentId Benif', location.state.openDocumentId);
      console.log('location.state.isNormaleDoc Benif', location.state.isNormaleDoc);
      var foundDocument = null;
      var foundDocumentModified = null;
      console.log(location.state.isNormaleDoc);
      if (!location.state.isNormaleDoc) {
        if (documentsWithType.length > 0) {
          foundDocument = documentsWithType.find(doc => doc.id === location.state.openDocumentId);
        }
        if (foundDocument) {
          foundDocumentModified = {
            id: foundDocument.id,
            label: foundDocument.label || '---',
            code: foundDocument.code || '---',
            type: foundDocument.type ? foundDocument.type.name : '---',
            documentDate: foundDocument.documentDate
              ? formatDate(foundDocument.documentDate)
              : '---',
            expiryDate: foundDocument.expiryDate
              ? formatDate(foundDocument.expiryDate)
              : '---',
            comment: foundDocument.comment || '---',
            statut:
              foundDocument.expiryDate && moment(foundDocument.expiryDate).isBefore(moment())
                ? 'Expiré'
                : 'Non expiré',
            document: foundDocument,
          }
        }
      } else {
        foundDocument = documentsWithoutType.find(doc => doc.id === location.state.openDocumentId);
        if (foundDocument) {
          foundDocumentModified = {
            id: foundDocument.id,
            label: foundDocument.label || '---',
            code: foundDocument.code || '---',
            type: foundDocument.type ? foundDocument.type.name : '---',
            documentDate: foundDocument.documentDate
              ? formatDate(foundDocument.documentDate)
              : '---',
            expiryDate: foundDocument.expiryDate
              ? formatDate(foundDocument.expiryDate)
              : '---',
            comment: foundDocument.comment || '---',
            statut:
              foundDocument.expiryDate && moment(foundDocument.expiryDate).isBefore(moment())
                ? 'Expiré'
                : 'Non expiré',
            document: foundDocument,
          }
        }
      }
      if (foundDocumentModified) {
        setDocumentToEdit(foundDocumentModified.document);
        setNormalDocument(location.state.isNormaleDoc);
        setShow(true);
      }
    }

  }, [location.state, documents]);

  // a useEffect to handle the changelnt of normalDocument state
  useEffect(() => {
    if (normalDocument) {
      setNormalDocument(true);
    } else {
      setNormalDocument(false);
    }
  }, [normalDocument]);

  const [loaderDoc, setLoaderDoc] = useState(false)
  const [selectedTagIds, setSelectedTagIds] = useState([]);

  let documentsWithType = [];
  let documentsWithoutType = [];
  const listDocumentsWithType = [];
  const listDocumentsWithoutType = [];

  useEffect(() => {
    const checkScroll = () => {
      if (tagsWrapperRef.current) {
        const { scrollLeft, scrollWidth, clientWidth } = tagsWrapperRef.current;
        const isAtStart = scrollLeft <= 0;
        const isAtEnd = scrollLeft >= scrollWidth - clientWidth;

        setCanScrollLeft(!isAtStart);
        setCanScrollRight(!isAtEnd);
      }
    };

    checkScroll();

    const handleScroll = () => {
      checkScroll();
    };

    if (tagsWrapperRef.current) {
      tagsWrapperRef.current.addEventListener('scroll', handleScroll);
    }

    window.addEventListener('resize', checkScroll);

    return () => {
      window.removeEventListener('resize', checkScroll);
      if (tagsWrapperRef.current) {
        tagsWrapperRef.current.removeEventListener('scroll', handleScroll);
      }
    };
  }, [tagList, activeSection]);

  // Add reset function for scroll state
  const resetScrollState = () => {
    if (tagsWrapperRef.current) {
      tagsWrapperRef.current.scrollLeft = 0;
      setCanScrollLeft(false);
      setCanScrollRight(tagsWrapperRef.current.scrollWidth > tagsWrapperRef.current.clientWidth);
    }
  };

  // Add useEffect to reset scroll state when section changes
  useEffect(() => {
    resetScrollState();
  }, [activeSection]);

  const scrollTags = (direction) => {
    if (tagsWrapperRef.current) {
      const scrollAmount = 200;
      const currentScroll = tagsWrapperRef.current.scrollLeft;
      const newScrollLeft = direction === 'left'
        ? Math.max(0, currentScroll - scrollAmount)
        : currentScroll + scrollAmount;

      tagsWrapperRef.current.scrollTo({
        left: newScrollLeft,
        behavior: 'smooth'
      });
    }
  };

  const scrollToEnd = () => {
    if (tagsWrapperRef.current) {
      tagsWrapperRef.current.scrollTo({
        left: tagsWrapperRef.current.scrollWidth,
        behavior: 'smooth'
      });
    }
  };

  if (documents) {
    const documentsSorted = [...documents].sort((a, b) =>
      a.id < b.id ? 1 : -1,
    );

    // Filter documents based on selected tags
    const filteredDocuments = selectedTagIds.length > 0
      ? documentsSorted.filter(doc =>
          doc.tags && selectedTagIds.some(tagId => doc.tags.some(tag => tag.id === tagId))
        )
      : documentsSorted;
    console.log(documentsSorted)
    // Separate documents into two categories
    documentsWithType = filteredDocuments.filter(
      doc => doc.type !== null && doc.type.id !== null,
    );
    documentsWithoutType = filteredDocuments.filter(
      doc => doc.type && doc.type.id === null,
    );

    // Implement pagination logic for both categories
    const startIndexWithType = (currentPage - 1) * pageSize;
    const endIndexWithType = Math.min(
      startIndexWithType + pageSize,
      documentsWithType.length,
    );
    const paginatedDocumentsWithType = documentsWithType.slice(
      startIndexWithType,
      endIndexWithType,
    );

    const startIndexWithoutType = (currentPage - 1) * pageSize;
    const endIndexWithoutType = Math.min(
      startIndexWithoutType + pageSize,
      documentsWithoutType.length,
    );
    const paginatedDocumentsWithoutType = documentsWithoutType.slice(
      startIndexWithoutType,
      endIndexWithoutType,
    );

    // Map documents with type
    listDocumentsWithType.push(
      ...paginatedDocumentsWithType.map(document => ({
        id: document.id,
        label: document.label || '---',
        code: document.code || '---',
        type: document.type ? document.type.name : '---',
        documentDate: document.documentDate
          ? formatDate(document.documentDate)
          : '---',
        expiryDate: document.expiryDate
          ? formatDate(document.expiryDate)
          : '---',
        comment: document.comment || '---',
        statut:
          document.expiryDate && moment(document.expiryDate).isBefore(moment())

            ? 'Expiré'
            : 'Non expiré',
        document,
      })),
    );

    listDocumentsWithoutType.push(
      ...paginatedDocumentsWithoutType.map(document => ({
        id: document.id,
        label: document.label || '---',
        documentDate: document.documentDate
          ? formatDate(document.documentDate)
          : '---',
        expiryDate: document.expiryDate
          ? formatDate(document.expiryDate)
          : '---',
        comment: document.comment || '---',
        statut:
          document.expiryDate && moment(document.expiryDate).isBefore(moment())
            ? 'Expiré'
            : 'Non expiré',
        document,
      })),
    );
  }



  const columnsWithType = [
    {
      field: 'label',
      headerName: 'Objet',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
    {
      field: 'type',
      headerName: 'Type Document',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
    {
      field: 'documentDate',
      headerName: "Date d'ajout",
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
    {
      field: 'expiryDate',
      headerName: "Date d'expiration",
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
    {
      field: 'statut',
      headerName: 'Statut',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
      cellClassName: params => getTag(params.value),
    },
    {
      field: 'comment',
      headerName: 'Commentaire',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
    {
      field: 'tags',
      headerName: 'Tags',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
      renderCell: (params) => {
        if (!params.row.document.tags || params.row.document.tags.length === 0) {
          return '---';
        }
        return (
          <div style={{ whiteSpace: 'normal', wordBreak: 'break-word' }}>
            {params.row.document.tags
              .map(tag => tag.name)
              .filter(name => name !== '')
              .join(', ')}
          </div>
        );
      }
    },
  ];

  // Define columns for Autre Document (without type)
  const columnsWithoutType = [
    {
      field: 'label',
      headerName: 'Objet',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
    {
      field: 'documentDate',
      headerName: 'Date Document',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
    {
      field: 'expiryDate',
      headerName: 'Date Expiration',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
    {
      field: 'statut',
      headerName: 'Statut',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
      cellClassName: params => getTag(params.value),
    },
    {
      field: 'comment',
      headerName: 'Commentaire',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
    {
      field: 'tags',
      headerName: 'Tags',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
      renderCell: (params) => {
        if (!params.row.document.tags || params.row.document.tags.length === 0) {
          return '---';
        }
        return (
          <div style={{ whiteSpace: 'normal', wordBreak: 'break-word' }}>
            {params.row.document.tags
              .map(tag => tag.name)
              .filter(name => name !== '')
              .join(', ')}
          </div>
        );
      }
    },
  ];

  const connectedUser = useSelector(state => state.app.connectedUser);
  const isUpdateAuthorized = isAuthorized(
    connectedUser,
    'GERER_BENEFICIAIRES_KAFALAT',
    'WRITE',
  );
  if (isUpdateAuthorized && !hide) {
    // Add actions column for Pièce Jointe only
    columnsWithType.push({
      field: 'actions',
      type: 'actions',
      headerName: 'Actions',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
      renderCell: rowData => (
        <div className="p-0 d-flex align-items-center gap-5">
          <input
            type="image"
            src={VIEW_ICON}
            className="p-0"
            width="20px"
            height="20px"
            title="visualiser"
            onClick={() => {
              dispatch(viewDocument(rowData.row.document, target));
            }}
          />
          <input
            type="image"
            src={DOWNLOAD_ICON}
            className="p-0 mx-2"
            width="20px"
            height="20px"
            title="télécharger"
            onClick={() =>
              dispatch(downloadDocument(rowData.row.document, target))
            }
          />
          <input
            type="image"
            className="p-0"
            onClick={() => {
              setDocumentToEdit(rowData.row.document);
              setNormalDocument(false);
              setShow(true);
            }}
            src={EDIT_ICON}
            width="20px"
            height="20px"
            title="modifier"
          />
        </div>
      ),
    });

    columnsWithoutType.push({
      field: 'actions',
      type: 'actions',
      headerName: 'Actions',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
      renderCell: rowData => (
        <div className="p-0">
          <input
            type="image"
            src={VIEW_ICON}
            className="p-0"
            width="20px"
            height="20px"
            title="visualiser"
            onClick={() => {
              dispatch(viewDocument(rowData.row.document, target));
            }}
          />
          <input
            type="image"
            src={DOWNLOAD_ICON}
            className="p-0 mx-2"
            width="20px"
            height="20px"
            title="télécharger"
            onClick={() =>
              dispatch(downloadDocument(rowData.row.document, target))
            }
          />
          <input
            type="image"
            className="p-0"
            onClick={() => {
              setDocumentToEdit(rowData.row.document);
              setNormalDocument(true);
              setShow(true);
            }}
            src={EDIT_ICON}
            width="20px"
            height="20px"
            title="modifier"
          />
        </div>
      ),
    });
  }

  return (
    <div>
      {successAlert}
      <Modal show={loaderDoc} centered contentClassName="bg-transparent border-0">
        <div className="d-flex justify-content-center align-items-center">
          <CircularProgress style={{ width: '100px', height: '100px' }} />
        </div>
      </Modal>


      <div className={`pb-5 ${stylesList.backgroudStyle}`}>
        <Modal2
          title={
            documentToEdit ? 'Modifier le document' : 'Ajouter un document'
          }
          size="lg"
          customWidth="modal-90w"
          show={show}
          handleClose={() => setShow(false)}
        >
          <DocumentForm
            handleClose={handleClose}
            document={documentToEdit}
            button={documentToEdit ? 'Modifier' : 'Ajouter'}
            target={target}
            id={params.id}
            normalDocument={normalDocument}
          />
        </Modal2>

        <Modal2
          centered
          className="mt-5"
          title="Confirmation de suppression"
          show={showDeleteModal}
          handleClose={handleCloseForDeleteModal}
        >
          <p className="mt-1 mb-5 px-2">
            Êtes-vous sûr de vouloir supprimer ce document?
          </p>
          <div className="d-flex justify-content-end px-2 my-1 ">
            <button
              type="button"
              className="btn-style outlined"
              onClick={handleCloseForDeleteModal}
            >
              Annuler
            </button>
            <button
              type="submit"
              className={`ml-3 btn-style primary`}
              onClick={() => {
                dispatch(deleteDocument(documentToDelete, target));
                setDocumentToEdit('');
                handleCloseForDeleteModal();
              }}
            >
              Supprimer
            </button>
          </div>
        </Modal2>

        <div>
          <div className={styles.global}>
            <div className={styles.header}>
              <h4></h4>
              {!hide && (
                <div className="dropdown">
                  <button
                    className={`btn dropdown-toggle mb-3 ${btnStyles.addBtnProfile}`}
                    style={{
                      backgroundColor: '#FFB290',
                      color: 'white',
                      fontWeight: '700',
                    }}
                    type="button"
                    id="dropdownMenuButton"
                    data-toggle="dropdown"
                    aria-haspopup="true"
                    aria-expanded="false"
                  >
                    Ajouter
                  </button>
                  <div
                    className="dropdown-menu"
                    aria-labelledby="dropdownMenuButton"
                  >
                    <button
                      className="dropdown-item"
                      onClick={() => {
                        setDocumentToEdit('');
                        setShow(true);
                        setNormalDocument(false);
                      }}
                    >
                      Pièce Jointe
                    </button>
                    <button
                      className="dropdown-item"
                      onClick={() => {
                        setDocumentToEdit('');
                        setShow(true);
                        setNormalDocument(true);
                      }}
                    >
                      Document
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>

          <div
            style={{
              marginBottom: '20px',
            }}
            className={navigationStyle.divBtns}
          >
            <button
              className={
                activeSection == 'type'
                  ? navigationStyle.activeBtn
                  : navigationStyle.disabledBtn
              }
              onClick={() => setActiveSection('type')}
            >
              Liste des Pièces Jointes
            </button>

            <button
              className={
                activeSection == 'other'
                  ? navigationStyle.activeBtn
                  : navigationStyle.disabledBtn
              }
              onClick={() => setActiveSection('other')}
            >
              Liste des Documents
            </button>
          </div>

          {activeSection === 'type' && (
            <div>
              <h5 style={{ marginBottom: '10px' }}>Filtre par tags :</h5>
              <TagsContainer>
                <LeftArrow
                  onClick={() => scrollTags('left')}
                  disabled={!canScrollLeft}
                >
                  <ChevronRightIcon />
                </LeftArrow>
                <TagsWrapper ref={tagsWrapperRef}>
                  {selectedTagIds.length > 0 && (
                    <ResetTag
                      onClick={() => setSelectedTagIds([])}
                    >
                      x
                    </ResetTag>
                  )}
                  {tagList && tagList.map((tag, index) => (
                    <Tag
                      key={tag.id}
                      color={tag.color || 'ffffff'}
                      selected={selectedTagIds.includes(tag.id)}
                      hasSelected={selectedTagIds.length > 0}
                      onClick={() => {
                        setSelectedTagIds(prev => {
                          const newSelectedIds = prev.includes(tag.id)
                            ? prev.filter(id => id !== tag.id)
                            : [...prev, tag.id];

                          // If this is the last tag and it's being selected, scroll to end
                          if (index === tagList.length - 1 && !prev.includes(tag.id)) {
                            setTimeout(scrollToEnd, 100);
                          }

                          return newSelectedIds;
                        });
                        setCurrentPage(1);
                      }}
                    >
                      {tag.name}
                      {selectedTagIds.includes(tag.id) && <CloseIcon>×</CloseIcon>}
                    </Tag>
                  ))}
                </TagsWrapper>
                <RightArrow
                  onClick={() => scrollTags('right')}
                  disabled={!canScrollRight}
                >
                  <ChevronRightIcon />
                </RightArrow>
              </TagsContainer>
              <DataTable
                rows={listDocumentsWithType}
                columns={columnsWithType}
                fileName={`Pièce jointe de la famille ${familyCode}`}
              />
              <div className="row justify-content-center my-4">
                <CustomPagination
                  totalElements={documentsWithType.length}
                  currentPage={currentPage}
                  totalCount={Math.ceil(documentsWithType.length / pageSize)}
                  pageSize={pageSize}
                  onPageChange={setCurrentPage}
                />
              </div>
            </div>
          )}
          {activeSection === 'other' && (
            <div>
              <h5 style={{ marginBottom: '10px' }}>Filtre par tags :</h5>
              <TagsContainer>
                <LeftArrow
                  onClick={() => scrollTags('left')}
                  disabled={!canScrollLeft}
                >
                  <ChevronRightIcon />
                </LeftArrow>
                <TagsWrapper ref={tagsWrapperRef}>
                  {selectedTagIds.length > 0 && (
                    <ResetTag
                      onClick={() => setSelectedTagIds([])}
                    >
                      x
                    </ResetTag>
                  )}
                  {tagList && tagList.map((tag, index) => (
                    <Tag
                      key={tag.id}
                      color={tag.color || 'ffffff'}
                      selected={selectedTagIds.includes(tag.id)}
                      hasSelected={selectedTagIds.length > 0}
                      onClick={() => {
                        setSelectedTagIds(prev => {
                          const newSelectedIds = prev.includes(tag.id)
                            ? prev.filter(id => id !== tag.id)
                            : [...prev, tag.id];

                          // If this is the last tag and it's being selected, scroll to end
                          if (index === tagList.length - 1 && !prev.includes(tag.id)) {
                            setTimeout(scrollToEnd, 100);
                          }

                          return newSelectedIds;
                        });
                        setCurrentPage(1);
                      }}
                    >
                      {tag.name}
                      {selectedTagIds.includes(tag.id) && <CloseIcon>×</CloseIcon>}
                    </Tag>
                  ))}
                </TagsWrapper>
                <RightArrow
                  onClick={() => scrollTags('right')}
                  disabled={!canScrollRight}
                >
                  <ChevronRightIcon />
                </RightArrow>
              </TagsContainer>
              <DataTable
                rows={listDocumentsWithoutType}
                columns={columnsWithoutType}
                fileName={`Autre document de la famille ${familyCode}`}
              />
              <div className="row justify-content-center my-4">
                <CustomPagination
                  totalElements={documentsWithoutType.length}
                  currentPage={currentPage}
                  totalCount={Math.ceil(documentsWithoutType.length / pageSize)}
                  pageSize={pageSize}
                  onPageChange={setCurrentPage}
                />
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
