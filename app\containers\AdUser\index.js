import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useInjectReducer, useInjectSaga } from 'redux-injectors';
import { createStructuredSelector } from 'reselect';
import btnStyles from 'Css/button.css';
import listStyles from 'Css/list.css';
import { Alert, Modal } from 'react-bootstrap';
import ListAdUsers from 'components/AdUser/ListAdUsers/index';
import AccessControl from 'utils/AccessControl';
import CustomPagination from 'components/Common/CustomPagination';
import GenericFilter from 'containers/Common/Filter/GenericFilter';

import { makeSelectConnectedUser } from 'containers/App/selectors';
import { makeSelectRoles } from 'containers/Role/Roles/selectors';
import { loadRolesRequest } from 'containers/Role/Roles/actions';
import rolesReducer from 'containers/Role/Roles/reducer';
import rolesSaga from 'containers/Role/Roles/saga';
import {
  makeSelectAddUserError,
  makeSelectAddUserSuccess,
  makeSelectAdUsers,
  makeSelectChangeUserRoleSuccess,
  makeSelectDeleteUserError,
  makeSelectDeleteUserSuccess,
  makeSelectError,
  makeSelectLoading,
} from './selectors';
import {
  addUserRequest,
  fetchUsersRequest,
  resetAddUserError,
  resetAddUserSuccess,
  resetChangeUserRoleSuccess,
  resetDeleteUserSuccess,
} from './actions';
import reducer from './reducer';
import saga from './saga';

const key = 'adUser';
const key3 = 'roleList';

const adUserSelector = createStructuredSelector({
  adUsers: makeSelectAdUsers,
  error: makeSelectError,
  addUserSuccess: makeSelectAddUserSuccess,
  addUserError: makeSelectAddUserError,
  loading: makeSelectLoading,
  deleteUserSuccess: makeSelectDeleteUserSuccess,
  connectedUser: makeSelectConnectedUser,
  deleteUserError: makeSelectDeleteUserError,
  changeUserRoleSuccess: makeSelectChangeUserRoleSuccess,
});

const roleSelector = createStructuredSelector({
  roles: makeSelectRoles,
});

export default function AdUser(props) {
  const [activePage, setActivePage] = useState(1);
  const [errorMessages, setErrorMessages] = useState('');
  const [showModal, setShowModal] = useState(false); // State to control modal visibility
  const [email, setEmail] = useState(''); // State to hold email input value
  const [successMessage, setSuccessMessage] = useState('');
  const [selectedRole, setSelectedRole] = useState('');
  const [modalErrorMessages, setModalErrorMessages] = useState('');

  useInjectReducer({ key, reducer });
  useInjectSaga({ key, saga });

  useInjectReducer({ key: key3, reducer: rolesReducer });
  useInjectSaga({ key: key3, saga: rolesSaga });

  const dispatch = useDispatch();
  const {
    adUsers,
    error,
    addUserSuccess,
    addUserError,
    loading,
    deleteUserSuccess,
    connectedUser,
    deleteUserError,
    changeUserRoleSuccess,
  } = useSelector(adUserSelector);
  const { roles } = useSelector(roleSelector);

  const handleAddUser = () => {
    setShowModal(true); // Open modal when Add User button is clicked
  };

  const handleModalClose = () => {
    setShowModal(false);
    setModalErrorMessages('');
    setEmail('');
    setSelectedRole('');
  };

  const handleEmailChange = event => {
    setEmail(event.target.value); // Update email state when input value changes
  };

  const handleRoleChange = event => {
    setSelectedRole(event.target.value); // Update selected role state when the select input value changes
  };

  const handleFormSubmit = event => {
    event.preventDefault();
    dispatch(addUserRequest(email, selectedRole));
  };

  useEffect(() => {
    if (deleteUserError) {
      setErrorMessages(
        "Erreur lors de la suppression de l'utilisateur, cette utilisateur est lié à une ou plusieurs tâches.",
      );
      dispatch(resetDeleteUserSuccess());
    }
  }, [deleteUserError, dispatch]);

  useEffect(() => {
    if (addUserSuccess) {
      setSuccessMessage('Utilisateur ajouté avec succès !');
      setEmail('');
      setSelectedRole('');
      setModalErrorMessages('');
      dispatch(fetchUsersRequest(0));
      dispatch(resetAddUserSuccess());
      setShowModal(false);
    } else if (addUserError) {
      if (addUserError && addUserError.includes('User already exists')) {
        setModalErrorMessages(
          "Erreur lors de l'ajout de l'utilisateur : l'utilisateur existe déjà.",
        );
      } else {
        setModalErrorMessages(
          "Erreur lors de l'ajout de l'utilisateur : adresse e-mail introuvable.",
        );
      }

      dispatch(resetAddUserError());
    }
  }, [addUserSuccess, addUserError, dispatch]);

  // close automatically the alert after 4 second

  useEffect(() => {
    if (successMessage) {
      const timer = setTimeout(() => {
        setSuccessMessage('');
      }, 4000);
      return () => clearTimeout(timer);
    }
  }, [successMessage]);

  useEffect(() => {
    if (modalErrorMessages) {
      const timer = setTimeout(() => {
        setModalErrorMessages('');
      }, 4000);
      return () => clearTimeout(timer);
    }
  }, [modalErrorMessages]);

  useEffect(() => {
    dispatch(fetchUsersRequest(0));
  }, [dispatch]);

  const handlePageChange = pageNumber => {
    dispatch(fetchUsersRequest(pageNumber - 1, filterValues));
    setActivePage(pageNumber);
  };

  useEffect(() => {
    dispatch(loadRolesRequest(0));
  }, [dispatch]);

  useEffect(() => {
    // Check if there is an error and if it's a 403 error
    if (error && error.response && error.response.status === 403) {
      setErrorMessages(
        "Votre role ne vous permet pas d'accéder à cette ressource.",
      );
    }
  }, [error]);

  useEffect(() => {
    if (changeUserRoleSuccess) {
      dispatch(fetchUsersRequest(activePage - 1));
      setSuccessMessage("Role de l'utilisateur modifié avec succès !");
      dispatch(resetChangeUserRoleSuccess());
    }
  }, [changeUserRoleSuccess, dispatch, activePage]);

  useEffect(() => {
    if (deleteUserSuccess) {
      setSuccessMessage('Utilisateur supprimé avec succès !');
      dispatch(fetchUsersRequest(activePage - 1));
      dispatch(resetDeleteUserSuccess());
    }
  }, [deleteUserSuccess, dispatch, activePage]);

  const principalInputsConfig = [
    {
      field: 'searchByNom',
      type: 'text',
      placeholder: "Nom de l'utilisateur",
      label: "Nom de l'utilisateur",
    },
  ];

  const additionalInputsConfig = [
    {
      field: 'searchByEmail',
      type: 'text',
      placeholder: "Email de l'utilisateur",
    },
    {
      field: 'searchRole',
      type: 'select',
      options:
        roles &&
        roles.content &&
        roles.content.map(role => ({
          value: role.id,
          label: role.name,
        })),

      placeholder: "Role de l'utilisateur",
    },
    {
      field: 'minDate',
      type: 'date',
      placeholder: 'Date de creation minimale',
    },
    {
      field: 'maxDate',
      type: 'date',
      placeholder: 'Date de creation maximale',
    },
  ];

  const [filterValues, setFilterValues] = useState({
    searchByNom: '',
    searchByEmail: '',
    searchRole: '',
    minDate: '',
    maxDate: '',
  });

  const handleFilterApply = filters => {
    dispatch(fetchUsersRequest(activePage - 1, filters));
  };

  const handleResetFilterComplete = () => {
    setFilterValues({
      searchByNom: '',
      searchByEmail: '',
      searchRole: '',
      minDate: '',
      maxDate: '',
    });
  };
  return (
    <div>
      {successMessage && (
        <div className="alert alert-success">
          {successMessage}
          <button
            type="button"
            className="close"
            onClick={() => setSuccessMessage('')}
            aria-label="Close"
          >
            <span aria-hidden="true">&times;</span>
          </button>
        </div>
      )}
      {errorMessages && (
            <Alert
              className='alert-style'
          variant="danger"
          onClose={() => {
            setErrorMessages('');
          }}
          dismissible
        >
          <p>{errorMessages}</p>
        </Alert>
      )}

      <div className={listStyles.backgroundStyle}>
        <Modal show={showModal} onHide={handleModalClose}>
          <Modal.Header closeButton>
            <Modal.Title>L'ajout d'un utilisateur</Modal.Title>
          </Modal.Header>
          <Modal.Body>
            {modalErrorMessages && (
              <Alert
                className='alert-style'
                variant="danger"
                onClose={() => setModalErrorMessages('')}
                dismissible
              >
                {modalErrorMessages}
              </Alert>
            )}
            <form onSubmit={handleFormSubmit}>
              <div className="mb-3">
                <label htmlFor="emailInput" className="form-label">
                  Email de l'utilisateur
                </label>
                <input
                  type="email"
                  placeholder="Email de l'utilisateur"
                  className="form-control"
                  id="emailInput"
                  value={email}
                  onChange={handleEmailChange}
                  required // Add required attribute for email validation
                />
              </div>
              <div className="mb-3">
                <label htmlFor="roleSelect" className="form-label">
                  Role de l'utilisateur
                </label>
                <select
                  id="roleSelect"
                  className="form-control"
                  value={selectedRole}
                  onChange={handleRoleChange}
                  required
                >
                  <option value="">Sélectionner un role</option>
                  {roles &&
                    roles.content &&
                    roles.content.map(role => (
                      <option key={role.id} value={role.id}>
                        {role.name}
                      </option>
                    ))}
                </select>
              </div>
              <div className="d-flex justify-content-end px-1 mt-4">
                <button
                  type="button"
                  c
                  className={`btn-style outlined mr-2`}
                  onClick={handleModalClose}
                >
                  Annuler
                </button>
                <button
                  type="submit"
                  className="btn-style primary"
                  disabled={!email || !selectedRole}
                >
                  {loading ? 'En cours...' : 'Ajouter'}
                </button>
              </div>
            </form>
          </Modal.Body>
        </Modal>

        <GenericFilter
          principalInputsConfig={principalInputsConfig}
          additionalInputsConfig={additionalInputsConfig}
          onApplyFilter={handleFilterApply}
          filterValues={filterValues}
          setFilterValues={setFilterValues}
          onResetFilterComplete={handleResetFilterComplete}
        />
        <div className={listStyles.head}>
          <h4>Liste des Utilisateurs</h4>

          <AccessControl module="USER" functionality="CREATE">
            <button className={btnStyles.addBtnProfile} onClick={handleAddUser}>
              Ajouter un utilisateur
            </button>
          </AccessControl>
        </div>
        <div className="sub-container">
          {loading && (
            <div className="d-flex justify-content-center pb-3">
              <div
                className="spinner-border"
                style={{ width: '5rem', height: '5rem' }}
                role="status"
              >
                <span className="sr-only">Loading...</span>
              </div>
            </div>
          )}
          {!loading && adUsers.error ? (
            <div>Error: {adUsers.error.message}</div>
          ) : !loading &&
            adUsers.data &&
            adUsers.data.content &&
            adUsers.data.content.length > 0 ? (
            <ListAdUsers
              adUsers={adUsers.data.content}
              roles={roles}
              connectedUser={connectedUser}
              liste1={adUsers.data}
            />
          ) : (
            <div
              style={{
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                height: '100px',
              }}
            >
              Aucune ligne
            </div>
          )}

          <div className="justify-content-center mt-3">
            <CustomPagination
              totalElements={adUsers && adUsers.data ? adUsers.data.totalElements : null}
              onPageChange={handlePageChange}
              totalCount={
                adUsers && adUsers.data ? adUsers.data.totalPages : null
              }
              currentPage={activePage}
              pageSize={5}
            />
          </div>
        </div>
      </div>
    </div>
  );
}
