import React, { useEffect, useState } from 'react';
import btnStyles from 'Css/button.css';
import { useDispatch, useSelector } from 'react-redux';
import { fetchProfilesRequest } from 'containers/Profile/Profiles/actions';
import { makeSelectProfiles } from 'containers/Profile/Profiles/selectors';
import reducer from 'containers/Profile/Profiles/reducer';
import saga from 'containers/Profile/Profiles/saga';
import { createStructuredSelector } from 'reselect';
import { useInjectReducer, useInjectSaga } from 'redux-injectors';
import DataTable from 'components/Common/DataTable';
import CustomAccordion from 'components/Common/Accordion';
import { Accordion, Button } from 'react-bootstrap';
import { useHistory } from 'react-router-dom';
import {
  AddCircle,
  Delete,
  Edit,
  Search,
  Visibility,
} from '@mui/icons-material';
import Modal2 from '../../Common/Modal2';

const key = 'profile';

const modalStyle = {
  modalContainer: {
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'stretch',
    height: '100%',
  },
  section: {
    flex: 1,
    flexDirection: 'row',
    marginBottom: '20px',
    marginRight: '20px',
    padding: '20px',
    width: 'auto', // Laisser la largeur s'adapter au contenu// Limiter la largeur maximale pour éviter qu'elle ne devienne trop grande
    margin: 'auto',
  },
  pre: {
    backgroundColor: '#f5f5f5',
    padding: '10px',
    borderRadius: '5px',
    overflow: 'auto',
    width: '100%',
  },
  buttonContainer: {
    alignSelf: 'flex-end',
  },

  modalContent: {
    width: 'auto', // Laisser la largeur s'adapter au contenu// Limiter la largeur maximale pour éviter qu'elle ne devienne trop grande
    margin: 'auto', // Centrez le contenu horizontalement
  },

  table: {
    width: '100%',
    borderCollapse: 'collapse',
    marginBottom: '20px',
  },
  th: {
    padding: '8px',
    border: '1px solid #ddd',
    backgroundColor: '#f2f2f2',
  },
  td: {
    padding: '8px',
    border: '1px solid #ddd',
  },
  modified: {
    color: 'red',
    padding: '8px',
    fontWeight: 'bold',
    border: '1px solid #ddd',
  },
};

const ListPistAudit = ({ audits }) => {
  const dispatch = useDispatch();
  useInjectReducer({ key, reducer });
  useInjectSaga({ key, saga });

  const liste1 = audits.data;

  const omdbSelector = createStructuredSelector({
    profiles: makeSelectProfiles,
  });

  const { profiles } = useSelector(omdbSelector);

  const history = useHistory();

  useEffect(() => {
    dispatch(fetchProfilesRequest(0));
  }, [dispatch]);

  const [showModal, setShowModal] = useState(false);
  const [selectedUser, setSelectedUser] = useState(null);
  const [newProfile, setNewProfile] = useState('');
  const [successMessage, setSuccessMessage] = useState('');
  const [selectedAuditNewValue, setSelectedAuditNewValue] = useState(null);
  const [selectedAuditInitialValue, setSelectedAuditInitialValue] = useState(
    null,
  );
  const [selectedRowFunction, setSelectedRowFunction] = useState(null);
  const [showBudgetLines, setShowBudgetLines] = useState(false);

  const handleOpenModal = row => {
    setSelectedAuditNewValue(row.newValueJson);
    setSelectedAuditInitialValue(row.initialValueJson);
    setSelectedRowFunction(row.subOperation);
    setShowModal(true);
  };

  const handleCloseModal = () => {
    setShowModal(false);
    setSelectedAuditNewValue(null);
    setSelectedAuditInitialValue(null);
    setSelectedRowFunction(null);
  };

  let listeAudits = [];

  if (audits && audits.data) {
    console.log(audits.data.content);
    listeAudits = audits.data.content
      .sort((a, b) => b.id - a.id)
      .map((audit, index) => ({
        id: audit.id,
        activity: audit.activity,
        user: audit.user,
        message: audit.message,
        auditTime: new Date(audit.auditTime).toLocaleDateString('fr-FR', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit',
        }),
        initialValueJson: audit.initialValueJson,
        newValueJson: audit.newValueJson,
        operation: audit.operation,
        subOperation: audit.subOperation,
      }));
  }

  const paginatedAudits = listeAudits;

  const getModalTitle = () => {
    switch (selectedRowFunction) {
      case 'Ajout':
        return "Détails de l'ajout";
      case 'Suppression':
        return 'Détails de la suppression';
      case 'Modification':
        return 'Détails de la modification';
      case 'Recherche':
        return 'Détails de la recherche';
      case 'Consultation':
        return 'Détails de la consultation';
      case 'Cycle de Validation':
        return 'Détails de la Cycle de Validation';
      case 'Telechargement':
            return 'Détails du telechargement';
      default:
        return 'Détails';
    }
  };

  const arraysEqual = (arr1, arr2) => {
    if (arr1.length !== arr2.length) return false;
    return arr1.every((value, index) => value === arr2[index]);
  };
  const expandedRowStyle = {
    backgroundColor: '#fff7dd', // Light blue background for expanded rows
  };
  const removedStyle = {
    color: 'red',
    fontWeight: 'bold',
  };

  return (
    <div>
      {/* Success message */}
      {successMessage && (
        <div className="alert alert-success">
          {successMessage}
          <button
            type="button"
            className="close"
            onClick={() => setSuccessMessage('')}
            aria-label="Close"
          >
            <span aria-hidden="true">&times;</span>
          </button>
        </div>
      )}

      <DataTable
        rows={paginatedAudits}
        columns={[
          {
            field: 'operation',
            headerName: 'Module',
            flex: 1,
            headerAlign: 'center',
            align: 'center',
          },
          {
            field: 'subOperation',
            headerName: 'Fonctionnalité',
            flex: 1,
            headerAlign: 'center',
            align: 'center',
            resizable: true
          },
          {
            field: 'activity',
            headerName: 'Détail',
            flex: 1,
            headerAlign: 'center',
            align: 'center',
            resizable: true
          },
          {
            field: 'user',
            headerName: 'Utilisateur',
            flex: 1,
            headerAlign: 'center',
            align: 'center',
            resizable: true
          },
          {
            field: 'auditTime',
            headerName: "Date de l'audit",
            flex: 1,
            headerAlign: 'center',
            align: 'center',
            resizable: true
          },
          {
            field: 'actions',
            type: 'actions',
            headerName: 'Actions',
            flex: 1,
            headerAlign: 'center',
            align: 'center',
            resizable: true,
            renderCell: rowData => {
              if (
                rowData.row.initialValueJson != null &&
                rowData.row.newValueJson != null &&
                rowData.row.subOperation === 'Modification'
              ) {
                return (
                  <button
                    className="ml-2 px-4 btn-style primary"
                    onClick={() => handleOpenModal(rowData.row)}
                  >
                    Détail de la modification
                  </button>
                );
              }
              if (
                rowData.row.initialValueJson != null &&
                rowData.row.subOperation === 'Recherche'
              ) {
                return (
                  <button
                    className="ml-2 px-4 btn-style primary"
                    onClick={() => handleOpenModal(rowData.row)}
                  >
                    Détail de la Recherche
                  </button>
                );
              }else if (
                rowData.row.initialValueJson != null &&
                rowData.row.subOperation === 'Cycle de Validation'
              ) {
                return (
                  <button
                    className={`ml-2 px-4 ${btnStyles.addBtn}`}
                    onClick={() => handleOpenModal(rowData.row)}
                  >
                    Détail de la Validation
                  </button>
                );
              }
              else if (
                rowData.row.initialValueJson != null &&
                rowData.row.subOperation === 'Telechargement'
              ) {
                return (
                  <button
                    className={`ml-2 px-4 ${btnStyles.addBtn}`}
                    onClick={() => handleOpenModal(rowData.row)}
                  >
                    Détail du telechargement
                  </button>
                );
              }else if (
                rowData.row.newValueJson != null &&
                rowData.row.subOperation === 'Ajout'
              ) {
                return (
                  <button
                    className="ml-2 px-4 btn-style primary"
                    onClick={() => handleOpenModal(rowData.row)}
                  >
                    Détail de l'ajout
                  </button>
                );
              }
              if (
                rowData.row.initialValueJson != null &&
                rowData.row.subOperation === 'Suppression'
              ) {
                return (
                  <button
                    className="ml-2 px-4 btn-style primary"
                    onClick={() => handleOpenModal(rowData.row)}
                  >
                    Détail de la suppression
                  </button>
                );
              }
              if (
                rowData.row.initialValueJson != null &&
                rowData.row.subOperation === 'Consultation'
              ) {
                return (
                  <button
                    className="ml-2 px-4 pd-y-lg-5 btn-style primary "
                    onClick={() => handleOpenModal(rowData.row)}
                  >
                    Détail de la consultation
                  </button>
                );
              }
              return null;
            },
          },
        ]}
        fileName={`Liste des audits, ${new Date().toLocaleString()}`}
        totalElements={audits && audits.data ? liste1.totalElements : null}
        numberOfElements={
          audits && audits.data ? liste1.numberOfElements : null
        }
        pageable={audits && audits.data ? liste1.pageable : null}
      />

      <Modal2
        title={getModalTitle()}
        size="lg"
        show={showModal}
        handleClose={() => setShowModal(false)}
        className="custom-modal"
      >
        {selectedAuditNewValue && selectedAuditInitialValue && (
          <div>
            {Object.keys({
              ...selectedAuditInitialValue,
              ...selectedAuditNewValue,
            }).map(key => {
              const initialValue = selectedAuditInitialValue[key] || '-';
              const newValue = selectedAuditNewValue[key] || '-';

                return (
                  <CustomAccordion
                    key={key}
                    title={key}
                    type="modification"
                    newValue={newValue}
                    oldValue={initialValue}
                  />
                );
            })}
          </div>
        )}

        {selectedAuditInitialValue &&
          selectedAuditNewValue === null &&
          selectedRowFunction === 'Consultation' && (
            <div>
              {Object.entries(selectedAuditInitialValue).map(
              ([key, value], index) => {
                const newValue = selectedAuditInitialValue[key] || '-';
                  // Render other keys as normal
                  return (
                    <CustomAccordion
                      key={index}
                      title={key}
                      newValue={newValue}
                      type="Consultation"
                      />
                  );
              })}
            </div>
          )}
          {selectedAuditInitialValue &&
          selectedAuditNewValue === null &&
          selectedRowFunction === "Cycle de Validation" && (
            <div>
              {Object.entries(selectedAuditInitialValue).map(
              ([key, value], index) => {
                const newValue = selectedAuditInitialValue[key] || '-';
                  return (
                      <CustomAccordion
                      key={index}
                      title={key}
                      newValue={newValue}
                      type="Cycle de Validation"
                    />
                  );
              })}
            </div>
          )}

        {selectedAuditInitialValue &&
          selectedAuditNewValue === null &&
          selectedRowFunction === "Recherche"  && (
            <div>
              {Object.entries(selectedAuditInitialValue).map(([key, value], index) => {
                const newValue = selectedAuditInitialValue[key] || '-';
                  // Render other keys as normal
                  return (
                    <CustomAccordion
                      key={index}
                      title={key}
                      newValue={newValue}
                        type="Recherche"
                    />
                  );
              })}
            </div>
          )}

          {selectedAuditInitialValue &&
            selectedAuditNewValue === null &&
            selectedRowFunction === "Telechargement"  && (
              <div>
                {Object.entries(selectedAuditInitialValue).map(([key, value], index) => {
                  const newValue = selectedAuditInitialValue[key] || '-';
                    return (
                      <CustomAccordion
                        key={index}
                        title={key}
                        newValue={newValue}
                        type="Recherche"
                      />
                    );
                })}
              </div>
            )}
        {selectedAuditInitialValue === null &&
          selectedAuditNewValue &&
          selectedRowFunction === 'Ajout' && (
            <div>
              {Object.entries(selectedAuditNewValue).map(
              ([key, value], index) => {
                const newValue = selectedAuditNewValue[key] || '-';
                  return (
                    <CustomAccordion
                      key={index}
                      title={key}
                      newValue={newValue}
                      oldValue={null}
                      type="Ajout"
                    />
                  );

              })}
            </div>
          )}
        {selectedAuditInitialValue &&
          selectedAuditNewValue === null &&
          selectedRowFunction === 'Suppression' && (
            <div>
              {Object.entries(selectedAuditInitialValue).map(
              ([key, value], index) => {
                const newValue = selectedAuditInitialValue[key] || '-';
                if (key.startsWith('Service ')) {
                  return null; // Don't render "Service" keys here
                  }
                // Check if the current key is "Nombre des Lignes de Budget"

                  // Render other keys as normal
                    return (
                    <CustomAccordion
                        key={index}
                      title={key}
                      newValue={newValue}
                      type="Suppression"
                    />
                  );
              })}
            </div>
          )}

        <div style={{ textAlign: 'right', marginTop: '20px' }}>
          <Button variant="secondary" onClick={() => setShowModal(false)}>
            Fermer
          </Button>
        </div>
      </Modal2>
    </div>
  );
};

export default ListPistAudit;
