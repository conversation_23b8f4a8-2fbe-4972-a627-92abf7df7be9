import React, { useEffect, useState } from 'react';
import moment from 'moment';
import { Link, useHistory } from 'react-router-dom';
import listStyle from 'Css/profileList.css';
import btnStyles from 'Css/button.css';
import Modal2 from 'components/Common/Modal2';
import { resetDonation } from 'containers/Donation/AddDonation/actions';
import styled from 'styled-components';

import {
  makeSelectDeleteSuccess,
  makeSelectDonation,
  makeSelectError,
  makeSelectSuccess,
} from 'containers/Donation/AddDonation/selectors';

import { createStructuredSelector } from 'reselect';
import { useDispatch, useSelector } from 'react-redux';
import { Alert } from 'react-bootstrap';
import { useInjectReducer, useInjectSaga } from 'redux-injectors';
import AccessControl from 'utils/AccessControl';
import DataTable from '../../Common/DataTable';
import {
  DELETE_ICON,
  EDIT_ICON,
  VIEW_ICON,
} from '../../Common/ListIcons/ListIcons';
import aideComplementaireReducer from '../../../containers/AideComplementaire/ListAideComplementaire/reducer';
import aideComplementaireListSaga from '../../../containers/AideComplementaire/ListAideComplementaire/saga';
import { Statut } from '../enum';
import tagStyles from '../../../Css/tag.css';
import { deleteAideComplementaireRequest } from '../../../containers/AideComplementaire/ListAideComplementaire/actions';

const omdbSelector = createStructuredSelector({
  success: makeSelectSuccess,
  donation: makeSelectDonation,
  successDelete: makeSelectDeleteSuccess,
  error: makeSelectError,
});

const keyAideComplementaireList = 'aideComplementaireList';
const keyDonationDelete = 'donationAdd';

// Styled component for the tags container in the table
const TagsContainer = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  max-width: 200px;
`;

// Styled component for individual tags in the table
const TableTag = styled.div`
  padding: 2px 8px;
  font-size: 11px;
  white-space: nowrap;
  border-radius: 10px;
  background-color: ${props => `#${props.color || 'cccccc'}`};
  color: ${props => {
    const hex = props.color || 'cccccc';
    const r = parseInt(hex.substr(0, 2), 16);
    const g = parseInt(hex.substr(2, 2), 16);
    const b = parseInt(hex.substr(4, 2), 16);
    const brightness = (r * 299 + g * 587 + b * 114) / 1000;
    return brightness > 128 ? '#000000' : '#ffffff';
  }};
`;
export default function ListAideComplementaire(props) {
  let listAideComplementaires;

  const liste1 = props.listesGlobal;

  const formatDate = date => moment(date).format('DD/MM/YYYY');

  useInjectReducer({
    key: keyAideComplementaireList,
    reducer: aideComplementaireReducer,
  });
  useInjectSaga({
    key: keyAideComplementaireList,
    saga: aideComplementaireListSaga,
  });

  //useInjectSaga({ key: keyDonationDelete, saga: donationSaga });

  const dispatch = useDispatch();
  const { error } = useSelector(omdbSelector);
  const history = useHistory();
  const [show, setShow] = useState(false);
  const [showAlert, setShowAlert] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [message, setMessage] = useState('');
  const [aideComplemantaireToEdit, setAideComplemantaireToEdit] = useState('');
  const [aideComplemantaireToDelete, setAideComplemantaireToDelete] = useState(
    null,
  );
  let errorMessage = null;

  const getTag = statut => {
    switch (statut) {
      case Statut.PLANIFIER:
        return tagStyles.tagGrey;
      case Statut.EN_COURS:
        return tagStyles.tagGreen;
      case Statut.EN_ATTENTE_D_EXECUTION:
        return tagStyles.tagOrange;
      case Statut.EXECUTER:
        return tagStyles.tagBlue;
      case Statut.CLOTURER:
        return tagStyles.tagYellow;
      default:
        return tagStyles.tagGrey;
    }
  };

  const getStatutText = statut => {
    switch (statut) {
      case Statut.PLANIFIER:
        return 'Planifiée';
      case Statut.EN_COURS:
        return 'En cours';
      case Statut.EN_ATTENTE_D_EXECUTION:
        return "En attente d'exécution";
      case Statut.EXECUTER:
        return 'Exécutée';
      case Statut.CLOTURER:
        return 'Clôturée';
      default:
        return '---';
    }
  };

  useEffect(
    () =>
      function cleanup() {
        dispatch(resetDonation());
      },
    [],
  );

  if (error) {
    errorMessage = 'Une erreur est survenue';
  }

  const viewHandler = id => {
    history.push(`/aide-complementaire/fiche/${id}/info`, { params: id });
  };

  const handleClose = () => {
    setShow(false);
  };

  const handleShow = () => {
    setShow(true);
  };

  const handleCloseForDeleteModal = () => setShowDeleteModal(false);

  if (props.aideComplementaire) {
    let { aideComplementaire } = props;

    if (props.profile) {
      aideComplementaire = props.aideComplementaire.aideComplementaire;
    }

    const aideComplementaireSorted = [...aideComplementaire].sort((a, b) =>
      a.createdAt < b.createdAt ? 1 : -1,
    );
    // a voir
    listAideComplementaires = aideComplementaire.map(aideComplementaire => ({
      id: aideComplementaire.id,
      code: aideComplementaire.code,
      slogan:aideComplementaire.slogan?aideComplementaire.slogan : '-',
      datePlanification: aideComplementaire.datePlanification
        ? formatDate(aideComplementaire.datePlanification)
        : '-',

      name: aideComplementaire.name || <span>-</span>,
      montantPrevu: aideComplementaire.montantPrevu
        ? `${aideComplementaire.montantPrevu} Dhs`
        : '-',
      montantCollecter: aideComplementaire.montantCollecter
        ? `${aideComplementaire.montantCollecter} Dhs`
        : '-',
      type: aideComplementaire.typePriseEnCharge,
      statut: aideComplementaire.statut,
      tags: aideComplementaire.tags || [],
      aideComplementaire: aideComplementaire,
    }));
  }

  const columns = [
    {
      field: 'code',
      headerName: 'Code',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
    /*{
      field: 'receptionDate',
      headerName: 'Date de réception',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },

     */
    {
      field: 'name',
      headerName: 'Nom',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
    {
      field: 'slogan',
      headerName: 'Slogan',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
    {
      field: 'montantPrevu',
      headerName: 'Montant souhaité',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
    {
      field: 'montantCollecter',
      headerName: 'Montant collecté',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
    {
      field: 'datePlanification',
      headerName: 'Date prévue d’exécution',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
    {
      field: 'statut',
      headerName: 'Statut',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
      renderCell: params => (
        <div className={getTag(params.row.statut)}>
          {getStatutText(params.row.statut)}
        </div>
      ),
    },
    {
      field: 'tags',
      headerName: 'Tags',
      flex: 1.5,
      headerAlign: 'center',
      align: 'center',
      renderCell: params => {
        const tags = params.row.tags || [];
        return (
          <TagsContainer>
            {tags && tags.length > 0 ? (
              tags.map(tag => (
                <TableTag key={tag.id} color={tag.color || 'cccccc'}>
                  {tag.name}
                </TableTag>
              ))
            ) : (
              <span style={{ color: '#999', fontSize: '12px' }}>Aucun tag</span>
            )}
          </TagsContainer>
        );
      },
    },
    {
      field: 'actions',
      headerName: 'Actions',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
      renderCell: params => (
        <div>
          {/* Bouton View */}
          <input
            type="image"
            onClick={() => viewHandler(params.row.id)}
            className="p-2"
            src={VIEW_ICON}
            width="40px"
            height="40px"
            title="Consulter"
          />

          {/* Bouton Edit */}
          {!props.profile ? (
            <AccessControl module="DONATION" functionality="UPDATE">
              <Link
                to={{
                  pathname: `/aide-complementaire/edit/${params.row.id}`,
                }}
              >
                <input
                  type="image"
                  src={EDIT_ICON}
                  className="p-2"
                  width="40px"
                  height="40px"
                  title={
                    params.row.statut === Statut.EXECUTER ||
                    params.row.statut === Statut.CLOTURER
                      ? 'Modification désactivée car la campagne est exécutée'
                      : 'Modifier'
                  }
                  onClick={() => {
                    const row = params.row.donation;
                    setAideComplemantaireToEdit(params.row);
                    handleShow();
                  }}
                  disabled={
                    params.row.statut === Statut.EXECUTER ||
                    params.row.statut === Statut.CLOTURER
                  }
                  style={{
                    opacity:
                      params.row.statut === Statut.EXECUTER ||
                      params.row.statut === Statut.CLOTURER
                        ? 0.5
                        : 1,
                    cursor:
                      params.row.statut === Statut.EXECUTER ||
                      params.row.statut === Statut.CLOTURER
                        ? 'not-allowed'
                        : 'pointer',
                  }}
                />
              </Link>
            </AccessControl>
          ) : (
            <AccessControl module="DONATION" functionality="UPDATE">
              <input
                type="image"
                src={EDIT_ICON}
                className="p-2"
                width="40px"
                height="40px"
                title={
                  params.row.statut === Statut.EXECUTER ||
                  params.row.statut === Statut.CLOTURER
                    ? 'Modification désactivée car la campagne est exécutée'
                    : 'Modifier'
                }
                onClick={() => {
                  setAideComplemantaireToEdit(params.row);
                  handleShow();
                }}
                disabled={
                  params.row.statut === Statut.EXECUTER ||
                  params.row.statut === Statut.CLOTURER
                }
                style={{
                  opacity:
                    params.row.statut === Statut.EXECUTER ||
                    params.row.statut === Statut.CLOTURER
                      ? 0.5
                      : 1,
                  cursor:
                    params.row.statut === Statut.EXECUTER ||
                    params.row.statut === Statut.CLOTURER
                      ? 'not-allowed'
                      : 'pointer',
                }}
              />
            </AccessControl>
          )}

          {/* Bouton Delete */}
          <AccessControl module="DONATION" functionality="DELETE">
            <input
              type="image"
              src={DELETE_ICON}
              className="p-2"
              width="40px"
              height="40px"
              title={
                params.row.statut === Statut.EXECUTER ||
                params.row.statut === Statut.CLOTURER
                  ? 'Suppression désactivée car la campagne est exécutée'
                  : 'Supprimer'
              }
              onClick={() => {
                const row = params.row.aideComplementaire;
                setAideComplemantaireToDelete(row);
                setShowDeleteModal(true);
              }}
              disabled={
                params.row.statut === Statut.EXECUTER ||
                params.row.statut === Statut.CLOTURER
              }
              style={{
                opacity:
                  params.row.statut === Statut.EXECUTER ||
                  params.row.statut === Statut.CLOTURER
                    ? 0.5
                    : 1,
                cursor:
                  params.row.statut === Statut.EXECUTER ||
                  params.row.statut === Statut.CLOTURER
                    ? 'not-allowed'
                    : 'pointer',
              }}
            />
          </AccessControl>
        </div>
      ),
    },
  ];

  return (
    <div>
      {showAlert ? (
        <Alert
          className="alert-style"
          variant="success"
          onClose={() => setShowAlert(false)}
          dismissible
        >
          <p>{message}</p>
        </Alert>
      ) : null}

      {error ? (
        <div className="alert alert-danger"> {errorMessage} </div>
      ) : null}

      <div className={` ${!props.profile ? 'p-0' : listStyle.backgroudStyle}`}>
        {/*<Modal2
          title={
            donationToEdit ? 'Modifier la donation' : 'Ajouter une donation'
          }
          size="lg"
          customWidth="modal-90w"
          show={show}
          handleClose={() => {
            setShow(false);
          }}
        >
          <AddDonation
            handleClose={handleClose}
            edit={!!donationToEdit}
            donor={donor || null}
            profile
            donation={donationToEdit}
          />
        </Modal2>
        */}

        <Modal2
          centered
          className="mt-5"
          title="Confirmation de suppression"
          show={showDeleteModal}
          handleClose={handleCloseForDeleteModal}
        >
          {/* Conditional Alert */}
          {aideComplemantaireToDelete &&
            (aideComplemantaireToDelete.nbrDonorsParticipating > 0 ||
              aideComplemantaireToDelete.nbrBeneficiariesValidated > 0) && (
              <div className="alert alert-warning mb-4">
                {aideComplemantaireToDelete.nbrDonorsParticipating > 0 &&
                aideComplemantaireToDelete.nbrBeneficiariesValidated > 0
                  ? 'Cette aide complémentaire contient des bénéficiaires validés et des donateurs participants.'
                  : aideComplemantaireToDelete.nbrDonorsParticipating > 0
                  ? 'Cette aide complémentaire contient des donateurs participants.'
                  : 'Cette aide complémentaire contient des bénéficiaires validés.'}
              </div>
            )}

          <p className="mt-1 mb-4">
            Êtes-vous sûr de vouloir supprimer cette aide complémentaire ?
          </p>
          <div className="d-flex justify-content-end px-3 my-1">
            <button
              type="button"
              className={`mx-2 btn-style outlined`}
              onClick={handleCloseForDeleteModal}
            >
              Annuler
            </button>
            <button
              type="submit"
              className={`mx-2 btn-style primary`}
              onClick={() => {
                dispatch(
                  deleteAideComplementaireRequest(
                    aideComplemantaireToDelete.id,
                  ),
                );
                setAideComplemantaireToDelete('');
                handleCloseForDeleteModal();
              }}
            >
              Supprimer
            </button>
          </div>
        </Modal2>

        {props.profile ? (
          <div className={listStyle.global}>
            <div className={listStyle.header}>
              <h4>Liste des Donations</h4>
            </div>
          </div>
        ) : null}

        <div>
          <DataTable
            rows={listAideComplementaires}
            columns={columns}
            fileName={`Liste des Aides complémentaires , ${new Date().toLocaleString()}`}
            totalElements={liste1 ? liste1.totalElements : 0}
            numberOfElements={liste1 ? liste1.numberOfElements : 0}
            pageable={liste1 ? liste1.pageable : 0}
          />
        </div>
      </div>
    </div>
  );
}
