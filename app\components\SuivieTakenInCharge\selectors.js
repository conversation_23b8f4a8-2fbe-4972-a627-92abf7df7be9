import { createSelector } from 'reselect';
import { initialState } from './reducer';

const SuiviePriseEnChargeSelector = state =>
  state.suiviOperation || initialState;

const makeSelectOperationsTakenInCharge = createSelector(
  SuiviePriseEnChargeSelector,
  stateSlice => stateSlice.operationsTakenInCharge,
);

const makeSelectLoadingOperationsTakenInCharge = createSelector(
  SuiviePriseEnChargeSelector,
  stateSlice => stateSlice.operationsTakenInChargeLoading,
);

const makeSelectOperationChangedStatus = createSelector(
  SuiviePriseEnChargeSelector,
  state => state.operationStatusChanged,
);
const makeSelectOperationChangedStatusLoading = createSelector(
  SuiviePriseEnChargeSelector,
  state => state.operationStatusChangedLoading,
);

const makeSelectOperationChangedStatusError = createSelector(
  SuiviePriseEnChargeSelector,
  state => state.operationStatusChangedError,
);

export {
  makeSelectOperationsTakenInCharge,
  makeSelectLoadingOperationsTakenInCharge,
  makeSelectOperationChangedStatus,
  makeSelectOperationChangedStatusLoading,
  makeSelectOperationChangedStatusError,
};
