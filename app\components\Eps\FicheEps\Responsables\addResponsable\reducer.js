import produce from 'immer';
import {
  ADD_EPS_RESPONSABLE,
  ADD_EPS_RESPONSABLE_ERROR,
  ADD_EPS_RESPONSABLE_RESET,
  ADD_EPS_RESPONSABLE_SUCCESS,
} from './constants';

export const initialState = {
  loading: false,
  error: false,
  success: false,
  responsable: {}
};

const responsableReducer = produce((draft, action) => {
  switch (action.type) {
    case ADD_EPS_RESPONSABLE:
      draft.loading = true;
      draft.error = false;
      draft.success = false;
      break;
    case ADD_EPS_RESPONSABLE_SUCCESS:
      draft.loading = false;
      draft.error = false;
      draft.success = true;
      draft.responsable = action.responsable;
      break;
    case ADD_EPS_RESPONSABLE_ERROR:
      draft.loading = false;
      draft.error = action.error;
      break;
    case ADD_EPS_RESPONSABLE_RESET:
      draft.loading = false;
      draft.error = false;
      draft.success = false; 
      draft.responsable = action.responsable;

      break;
  }
}, initialState);

export default responsableReducer;
