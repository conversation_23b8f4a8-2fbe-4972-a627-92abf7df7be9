import React, { useState } from 'react';
import { useHistory } from 'react-router-dom';
import styles from '../../../Css/profileList.css'; // Include the necessary styles
import stylesList from '../../../Css/profileList.css';
import CustomPagination from 'components/Common/CustomPagination';
import { VIEW_ICON } from '../../Common/ListIcons/ListIcons';
import moment from 'moment';
export default function ListDonations(props) {
  const { data } = props; // Get the donations, donor, and history from the props
  let donationsData = data.donations; // Get the donations from the data prop
  const history = useHistory();
  const [currentPage, setCurrentPage] = useState(1);
  const pageSize = 5; // Define the page size
  const formatDate = date => moment(date).format('DD/MM/YYYY');

  let donations = null; // Initialize donations

  if (donationsData) {
    // Sort donations by creation date
    // first of all we should remove the one with archived is true
    donationsData = donationsData.filter(donation => !donation.archived);
    const sortedDonations = [...donationsData].sort((a, b) =>
      a.createdAt < b.createdAt ? 1 : -1,
    );

    // Pagination logic
    const startIndex = (currentPage - 1) * pageSize;
    const endIndex = Math.min(startIndex + pageSize, sortedDonations.length);
    const paginatedDonations = sortedDonations.slice(startIndex, endIndex);

    donations = paginatedDonations.map(donation => {
      return {
        id: donation.id,
        amount: donation.value,
        date: donation.createdAt ? formatDate(donation.createdAt) : '-',
        receptionDate: donation.receptionDate ? formatDate(donation.receptionDate) : '-',
        canalDonation:
          donation.canalDonation && donation.canalDonation.name
            ? donation.canalDonation.name
            : '-',
        type: donation.type,
      };
    });
  }

  // Columns for table
  const columns = [
    {
      field: 'amount',
      headerName: 'Montant',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
    {
      field: 'date de création',
      headerName: 'Date',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
    {
      field: 'receptionDate',
      headerName: 'Date de réception',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
    {
      field: 'canalDonation',
      headerName: 'Canal de donation',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
    {
      field: 'type',
      headerName: 'Type',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
  ];

  return (
    <div>
      <div className={`pb-5 ${stylesList.backgroudStyle}`}>
        <div>
          <div className={styles.global}>
            <div className={styles.header}>
              <h4>Liste des donations</h4>
            </div>

            <div className={styles.content}>
            <table className="table small">
                <thead>
                  <tr>
                    <th scope="col">Montant (DH)</th>
                    <th scope="col">Date de création</th>
                    <th scope="col">Date de réception</th>
                    <th scope="col">Canal de donation</th>
                    <th scope="col">Type</th>
                    <th scope="col">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {donations && donations.length > 0 ? (
                    donations.map(donation => (
                      <tr key={donation.id}>
                        <td>{donation.amount}</td>
                        <td>{donation.date}</td>
                        <td>{donation.receptionDate}</td>
                        <td>{donation.canalDonation}</td>
                        <td>{donation.type}</td>
                        <td>
                          <input
                            type="image"
                            className="px-2"
                            src={VIEW_ICON}
                            width="40px"
                            height="20px"
                            onClick={() => {
                              history.push(
                                `/donations/fiche/${donation.id}/info`,
                              );
                            }}
                            title="consulter"
                          />
                        </td>
                      </tr>
                    ))
                  ) : (
                    <tr>
                      <td colSpan="6" style={{ textAlign: 'center' }}>
                        Aucune donation trouvée
                      </td>
                    </tr>
                  )}


                </tbody>
              </table>

              {/* Pagination Controls */}
              <div className="justify-content-center my-4">
                {donationsData && (
                  <CustomPagination
                    totalElements={donationsData.length}
                    totalCount={Math.ceil(donationsData.length / pageSize)}
                    pageSize={pageSize}
                    currentPage={currentPage}
                    onPageChange={setCurrentPage}
                  />
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
