import React, { useEffect } from 'react';
import { Container, Typography, Paper, Box, CircularProgress, Grid } from '@mui/material';
import { useDispatch, useSelector } from 'react-redux';
import HomePage from '../../containers/HomePage';
import { useInjectReducer, useInjectSaga } from 'redux-injectors';
import generalDashboardReducer from '../Dashboard-General/reducer';
import generalDashboardSaga from '../Dashboard-General/saga';
import { createStructuredSelector } from 'reselect';
import { makeSelectEPSDashboard, makeSelectEPSDashboardLoading, makeSelectEPSDashboardError, makeSelectEPSDashboardSuccess } from '../Dashboard-General/selector';
import { fetchEPSDashboard } from '../Dashboard-General/actions';
import DonorStatusChart from 'components/charts/DonorStatusChart';
import { pieColors } from 'components/charts/PieCard';
import PersonIcon from '@mui/icons-material/Person';
import { AnimatedBar<PERSON><PERSON> } from 'components/charts/AnimatedBarChart';
import { StatCard } from 'components/charts/StatCard';
import { Row } from 'react-bootstrap';
const stateSelector = createStructuredSelector({
  data: makeSelectEPSDashboard,
  loading: makeSelectEPSDashboardLoading,
  error: makeSelectEPSDashboardError,
  success: makeSelectEPSDashboardSuccess
});

export default function DashboardEPS() {
  useInjectReducer({ key: 'dashboardAll', reducer: generalDashboardReducer });
  useInjectSaga({ key: 'dashboardAll', saga: generalDashboardSaga });

  const { data, loading, error, success } = useSelector(stateSelector);

  const dispatch = useDispatch();

  useEffect(() => {
    dispatch(fetchEPSDashboard());
  }, [dispatch]);


  const epsData =
  [
      {
      status: 'Actif',
      title: 'Actif',
      value: data.epsByStatus && data.epsByStatus.active ? data.epsByStatus.active : 0,
    },
    {
      status: 'Inactif',
      title: 'Inactif',
      value: data.epsByStatus && data.epsByStatus.inactive ? data.epsByStatus.inactive : 0,
    },
  ];
  

  useEffect(() => {
    if (success && data) {
    
      console.log('data from eps dashboard : ', data);
    }
  }, [success,data]);



  return (
    <>
      <HomePage />
      {loading && (
          <Box
            sx={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              zIndex: 1,
              borderRadius: '20px'
            }}
          >
            <CircularProgress sx={{ color: 'black' }} />
          </Box>
        )}
      {success && data && data.epsByStatus && data.serviceCollectionByStatus && (
      <Container maxWidth="xl" sx={{ mt: 2, mb: 4 }}>
        <Grid container spacing={2}>
          <Grid item xs={12} md={4}>
            <DonorStatusChart
               data={epsData}
               icon={<PersonIcon />}
               title="Répartition des bénéficiaires des Eps par statut"
               activeColor={pieColors.info}
               inactiveColor={pieColors.error}
               activeStatus="Actif"
               valueLabel="Eps"
               inactiveStatus="Inactif"
             />
          </Grid>
          <Grid item xs={12} md={8}>
            <AnimatedBarChart
               data={Object.values(data.serviceCollectionByStatus)}
               labels={Object.keys(data.serviceCollectionByStatus).map(key => key.charAt(0).toUpperCase() + key.slice(1))}
               icon={<PersonIcon />}
               title="Répartition des services de collecte par statut"
               colors={[pieColors.info, pieColors.error, pieColors.primary, pieColors.success, pieColors.warning]}
            />
          </Grid>
        </Grid>
      </Container>
      )}
    </>
  );
} 