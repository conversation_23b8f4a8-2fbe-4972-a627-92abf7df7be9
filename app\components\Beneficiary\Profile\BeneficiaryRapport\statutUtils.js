export const rapportStatuses = [
  'RAPPORT_INITIAL',
  'RAPPORT_VALIDER_ASSISTANCE',
  'RAPPORT_VALIDER_KAFALAT',
  'RAPPORT_A_COMPLETER_PAR_ASSISTANCE',
  'RAPPORT_A_COMPLETER_PAR_KAFALAT',
  'RAPPORT_FINAL',
];

export const rapportToCompleteStatuses = [
  'RAPPORT_A_COMPLETER_PAR_ASSISTANCE',
  'RAPPORT_A_COMPLETER_PAR_KAFALAT',
];

export const rapportToDemandeComplementStatuses = [
  'RAPPORT_VALIDER_ASSISTANCE',
  'RAPPORT_VALIDER_KAFALAT',
  'RAPPORT_A_COMPLETER_PAR_KAFALAT',
];

export const inServiceMarketing = ['RAPPORT_VALIDER_KAFALAT'];
export const inServiceKafalat = [
  'RAPPORT_VALIDER_ASSISTANCE',
  'RAPPORT_A_COMPLETER_PAR_KAFALAT',
];
export const inAssistant = [
  'RAPPORT_INITIAL',
  'RAPPORT_A_COMPLETER_PAR_ASSISTANCE',
  1,
  4,
];

export const inAdminNotify = [
  'RAPPORT_INITIAL',
  'RAPPORT_A_PREPARE',
  'RAPPORT_PLANIFIER',
  1,
  7,
  8,
];

const inAssistantUpdate = [
  'RAPPORT_INITIAL',
  'RAPPORT_A_COMPLETER_PAR_ASSISTANCE',
  1,
  4,
];
const inKafalatUpdate = [
  'RAPPORT_A_COMPLETER_PAR_KAFALAT',
  'RAPPORT_VALIDER_ASSISTANCE',
  5,
  2,
];

const inMarketingUpdate = [
  'RAPPORT_VALIDER_ASSISTANCE',
  'RAPPORT_VALIDER_KAFALAT',
  3,
];

const inKafalatDelete = [
  'RAPPORT_INITIAL',
  'RAPPORT_VALIDER_ASSISTANCE',
  'RAPPORT_VALIDER_KAFALAT',
  'RAPPORT_A_COMPLETER_PAR_ASSISTANCE',
  'RAPPORT_A_COMPLETER_PAR_KAFALAT',
  'RAPPORT_A_PREPARE',
  'RAPPORT_PLANIFIER',
  1,
  2,
  3,
  4,
  5,
  7,
  8,
];

const inMarketingDelete = [
  'RAPPORT_INITIAL',
  'RAPPORT_VALIDER_ASSISTANCE',
  'RAPPORT_VALIDER_KAFALAT',
  'RAPPORT_A_COMPLETER_PAR_ASSISTANCE',
  'RAPPORT_A_COMPLETER_PAR_KAFALAT',
  'RAPPORT_A_PREPARE',
  'RAPPORT_PLANIFIER',
  1,
  2,
  3,
  4,
  5,
  7,
  8,
];

export const isAssistantUpdate = statut => inAssistantUpdate.includes(statut);
export const isKafalatUpdate = statut => inKafalatUpdate.includes(statut);
export const isMarketingUpdate = statut => inMarketingUpdate.includes(statut);

export const isKafalatDelete = statut => inKafalatDelete.includes(statut);
export const isMarketingDelete = statut => inMarketingDelete.includes(statut);

export const isRapport = statut => rapportStatuses.includes(statut);

export const isRapportInitial = statut => statut === 'RAPPORT_INITIAL';

export const isRapportToComplete = statut =>
  rapportToCompleteStatuses.includes(statut);

export const isSeviceMerketing = statut => inServiceMarketing.includes(statut);
export const isSeviceKafalat = statut => inServiceKafalat.includes(statut);
export const isAssistant = statut => inAssistant.includes(statut);

export const isAdminNotify = statut => inAdminNotify.includes(statut);

export const isRapportToDemandeComplete = statut =>
  rapportToDemandeComplementStatuses.includes(statut);

export const getButtonTitle = statut => {
  if (
    statut === 'RAPPORT_INITIAL' ||
    statut === 'RAPPORT_A_COMPLETER_PAR_ASSISTANCE'
  ) {
    return 'Valider Assistant';
  } else if (
    (statut === 'RAPPORT_VALIDER_ASSISTANCE') |
    (statut === 'RAPPORT_A_COMPLETER_PAR_KAFALAT')
  ) {
    return 'Valider Kafalat';
  } else if (statut === 'RAPPORT_VALIDER_KAFALAT') {
    return 'Valider Marketing';
  }
  return 'Valider';
};

// we should add a fucntion to get the status name with style of like badget
export const getStatusName = statut => {
  if (statut === 'RAPPORT_PLANIFIER') {
    return 'planifier';
  }
  if (statut === 'RAPPORT_A_PREPARE') {
    return 'À préparer';
  }
  if (statut === 'RAPPORT_INITIAL') {
    return 'Initial';
  }
  if (statut === 'RAPPORT_VALIDER_ASSISTANCE') {
    return "validé par l'assistant";
  }
  if (statut === 'RAPPORT_VALIDER_KAFALAT') {
    return 'validé par Kafalat';
  }
  if (statut === 'RAPPORT_A_COMPLETER_PAR_ASSISTANCE') {
    return "Rapport à compléter par l'assistant";
  }
  if (statut === 'RAPPORT_A_COMPLETER_PAR_KAFALAT') {
    return 'Rapport à compléter par Kafalat';
  } else {
    return 'Final';
  }
};

export const getStatusStyle = statut => {
  if (statut === 'RAPPORT_INITIAL') {
    return 'badge badge-primary';
  }
  if (statut === 'RAPPORT_VALIDER_ASSISTANCE') {
    return 'badge badge-info';
  }
  if (statut === 'RAPPORT_VALIDER_KAFALAT') {
    return 'badge badge-success';
  }
  if (statut === 'RAPPORT_A_COMPLETER_PAR_ASSISTANCE') {
    return 'badge badge-warning';
  }
  if (statut === 'RAPPORT_A_COMPLETER_PAR_KAFALAT') {
    return 'badge badge-warning';
  }
  if (statut === 'RAPPORT_FINAL') {
    return 'badge badge-danger';
  } else {
    return 'badge badge-secondary';
  }
};
