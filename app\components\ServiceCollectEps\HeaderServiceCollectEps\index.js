import React from 'react';
import { NavLink, useParams } from 'react-router-dom';

import styles from 'Css/profileHeader.css';

const HeaderServiceCollectEps= () => {
  const params = useParams();
  return (
    <div className={styles.navBar}>
      <p>
        <NavLink
          exact
          to={'/ServiceCollectEps/fiche/' + params.id + '/info'}
          activeClassName={styles.selected}
        >
          Donations
        </NavLink>
      </p> 
    </div>
  );
};

export default HeaderServiceCollectEps;
