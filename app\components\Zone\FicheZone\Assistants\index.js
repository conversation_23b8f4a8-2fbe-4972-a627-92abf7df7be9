import React, { useState } from 'react';
import moment from 'moment';
import styles from 'Css/profileList.css';
import stylesList from 'Css/profileList.css';
import DataTable from '../../../Common/DataTable';
import CustomPagination from '../../../Common/CustomPagination';

const formatDate = date =>
  date
    ? moment(date).isValid()
      ? moment(date).format('DD/MM/YYYY')
      : '-'
    : '-';

export default function Assistants(props) {
  let listHistoryZone = [];
  const successAlert = null;
  const [currentPage, setCurrentPage] = useState(1);
  const pageSize = 5;

  const zone = props.data;

  let ZoneCode;

  if (zone) {
    ZoneCode = zone.code;
  }

  if (zone && zone.historyZones && zone.historyZones.length > 0) {
    const historyZonesSorted = [...zone.historyZones].sort((a, b) =>
      a.updateDate < b.updateDate ? 1 : -1,
    );

    const startIndex = (currentPage - 1) * pageSize;
    const endIndex = Math.min(startIndex + pageSize, historyZonesSorted.length);
    const paginatedZones = historyZonesSorted.slice(startIndex, endIndex);

    listHistoryZone = paginatedZones.map((history, index) => ({
      ...history,
      assistanceName: history.assistant ? history.assistant : '',
      dateAffectationToZone: history.dateAffectation ? formatDate(history.dateAffectation) : '-----',
      dateEndAffectationToZone: history.dateFinAffectation ? formatDate(history.dateFinAffectation) : '------',
      id: index,

    }));
  }

  const columns = [
    {
      field: 'assistanceName',
      headerName: 'Nom Complet',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
    {
      field: 'dateAffectationToZone',
      headerName: "Date d'affectation",
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
    {
      field: 'dateEndAffectationToZone',
      headerName: "Date de fin d'affectation",
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
  ];

  return (
    <div>
      {successAlert}
      <div className={`pb-5 ${stylesList.backgroudStyle}`}>
        <div>
          <div className={styles.global}>
            <div className={styles.header}>
              <h4>Historique des Assistants</h4>
            </div>
            <DataTable
              rows={listHistoryZone}
              columns={columns}
              fileName={`Liste des assistants de la zone ${ZoneCode}, ${new Date().toLocaleString()}`}
            />
            <div className="justify-content-center my-4">
              {zone && zone.historyZones.length > 0 && (
                <CustomPagination
                  totalElements={zone.historyZones.length}
                  totalCount={Math.ceil(zone.historyZones.length / pageSize)}
                  pageSize={pageSize}
                  currentPage={currentPage}
                  onPageChange={setCurrentPage}
                />
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
