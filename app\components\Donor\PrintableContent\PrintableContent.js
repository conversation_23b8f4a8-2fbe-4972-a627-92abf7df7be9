import React from 'react';
import {
  Container,
  Grid,
  makeStyles,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
} from '@material-ui/core';
import defaultImage from '../../../images/user.png';
import PrintHeader from '../../Common/PrintHeader/PrintHeader';
import { Tab } from 'react-bootstrap';

const useStyles = makeStyles(theme => ({
  container: {
    padding: '40px',
    fontFamily: "'Roboto', sans-serif",
    backgroundColor: '#f5f5f5',
    color: '#333',
  },
  paper: {
    padding: '40px',
    borderRadius: '15px',
    boxShadow: '0 8px 40px rgba(0, 0, 0, 0.12)',
    backgroundColor: '#ffffff',
    position: 'relative',
    overflow: 'hidden',
  },
  sectionTitle: {
    marginBottom: '30px',
    color: '#3f51b5',
    textTransform: 'uppercase',
    letterSpacing: '1.5px',
    borderBottom: '2px solid #3f51b5',
    paddingBottom: '5px',
    marginTop: '20px',
  },
  gridItem: {
    marginBottom: '25px',
  },
  tableContainer: {
    marginBottom: '30px',
    pageBreakInside: 'avoid',
  },
  table: {
    minWidth: 650,
    '& th': {
      backgroundColor: '#3f51b5',
      color: '#ffffff',
    },
    '& td, & th': {
      border: '1px solid #e0e0e0',
      padding: '14px',
      fontSize: '16px',
    },
  },
  logo: {
    width: '60px',
    position: 'absolute',
    top: '20px',
    right: '20px',
  },
  donorPicture: {
    width: '150px',
    height: '150px',
    borderRadius: '50%',
    border: '5px solid #ffffff',
    boxShadow: '0 4px 10px rgba(0, 0, 0, 0.3)',
    marginBottom: '20px',
  },
  section: {

    marginTop: '30px',
    pageBreakInside: 'avoid',
  },
  infoItem: {
    display: 'flex',
    alignItems: 'center',
    marginBottom: '15px',
  },
  infoLabel: {
    marginRight: '15px',
    fontWeight: 'bold',
    color: '#3f51b5',
    whiteSpace: 'nowrap',
  },
  contactInfo: {
    marginTop: theme.spacing(3),
  },
  contactTitle: {
    position: 'relative',
    display: 'inline-block',
   
  },
  titleUnderline: {
    content: '""',
    display: 'block',
    width: '100%',
    height: '2px',
    backgroundColor: 'black',
    position: 'absolute',
    bottom: 0,
    left: 0,
    color:'black',
  },
  tableTitle: {
    pageBreakInside: 'avoid',
  },
}));

const DataRow = ({ label, value }) => {
  const classes = useStyles();
  return (
    <Grid item xs={12} sm={6} className={classes.gridItem}>
      <div className={classes.infoItem}>
        <Typography variant="subtitle1" className={classes.infoLabel}>
          {label}:
        </Typography>
        <Typography variant="body1">{value ? value : '-'}</Typography>
      </div>
    </Grid>
  );
};

const PrintableContent = React.forwardRef(({ data }, ref) => {
  const classes = useStyles();

  if (!data) {
    return null;
  }

  const formatDate = date => new Date(date).toLocaleDateString('fr-FR');
  const isMoral = data.type === 'Moral';

  const personalInfoItems = isMoral
    ? [
        { label: "Nom de l'entreprise", value: data.company },
        { label: "Nom abrégé de l'entreprise", value: data.shortCompany },
        {
          label: "Secteur d'activité",
          value: data.activitySector && data.activitySector.name,
        },
      ]
    : [
        { label: 'Prénom', value: data.firstName },
        { label: 'Nom de famille', value: data.lastName },
        { label: 'Prénom (Arabe)', value: data.firstNameAr },
        { label: 'Nom de famille (Arabe)', value: data.lastNameAr },
        { label: 'Email', value: data.email },
        { label: 'Numéro de téléphone', value: data.phoneNumber },
        { label: 'Sexe', value: data.sex },
      ];

  return (
    <Container
      style={{ backgroundColor: '#ffffff' }}
      ref={ref}
      className={classes.container}
    >
      <Paper className={classes.paper}>
        <PrintHeader headerText="Fiche du Donateur" />
        <div className={classes.section}>
          <Typography
            variant="h5"
            gutterBottom
            className={classes.sectionTitle}
          >
            Informations Personnelles
          </Typography>
          <hr className={classes.lineStyle} /> {/* Add this line */}
          <Grid container spacing={3}>
            {!isMoral && (
              <Grid item xs={12} sm={6}>
                <img
                  src={
                    data.picture64
                      ? `data:image/png;base64,${atob(data.picture64)}`
                      : defaultImage
                  }
                  alt="Donor"
                  className={classes.donorPicture}
                />
              </Grid>
            )}
            {isMoral && (
              <Grid item xs={12} sm={6}>
                <img
                  src={
                    data.logo64
                      ? `data:image/png;base64,${atob(data.logo64)} `
                      : defaultImage
                  }
                  alt="Company Logo"
                  className={classes.donorPicture}
                />
              </Grid>
            )}
            <Grid container spacing={3}>
              {[
                ...personalInfoItems,
                { label: "Numéro d'identité", value: data.identityCode },
                { label: 'Adresse', value: data.address },
                { label: 'Adresse (Arabe)', value: data.addressAr },
                { label: 'Statut', value: data.status.name },
                { label: 'Créé à', value: formatDate(data.createdAt) },
                { label: 'Ville', value: data.city.name },
                { label: 'Région', value: data.city.region.name },
                {
                  label: 'Pays',
                  value: data.info.region ? data.info.region.country.name : '',
                },
              ].map((item, index) => (
                <DataRow key={index} label={item.label} value={item.value} />
              ))}
            </Grid>
          </Grid>
          {isMoral && data.donorContacts && data.donorContacts.length > 0 && (
          <div className={classes.contactInfo}>
          <Typography variant="h6" gutterBottom className={classes.contactTitle}>
            Contact Principal:
            <div className={classes.titleUnderline}></div>
          </Typography>
          {data.donorContacts.map((contact, index) => (
            <Grid container spacing={3} key={index}
            style={{marginTop: '10px'}}
            >
              <DataRow
                label="Nom Complet"
                value={`${contact.firstName} ${contact.lastName}`}
              />
              <DataRow
                label="Nom Complet (Arabe)"
                value={`${contact.firstNameAr} ${contact.lastNameAr}`}
              />
              <DataRow label="Email" value={contact.email} />
              <DataRow label="Téléphone" value={contact.phoneNumber} />
            </Grid>
          ))}
        </div>
         
          )}
        </div>
      </Paper>
      <Paper className={classes.paper}>
        <PrintHeader headerText="Fiche du Donateur" />
        <div className={classes.section}>
          <Typography
            variant="h5"
            gutterBottom
            className={classes.sectionTitle}
          >
            Donations
          </Typography>
          <TableContainer component={Paper} className={classes.tableContainer}>
            <Table className={classes.table}>
              <TableHead>
                <TableRow>
                 
                  <TableCell>Date de réception</TableCell>
                  <TableCell>Montant</TableCell>
                  <TableCell>Type</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {data.donations.length > 0 ? (
                  data.donations.map(donation => (
                    <TableRow key={donation.id}>
                     
                      <TableCell>
                        {formatDate(donation.receptionDate)}
                      </TableCell>
                      <TableCell>{donation.value} DH </TableCell>
                     
                     
                      <TableCell>{donation.type}</TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={5} align="center">
                      Aucune donation trouvée
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </TableContainer>
        </div>
        <div className={classes.section}>
          <Typography
            variant="h5"
            gutterBottom
            className={classes.sectionTitle}
          >
            Pris en Charge
          </Typography>
          <TableContainer component={Paper} className={classes.tableContainer}>
            <Table className={classes.table}>
              <TableHead>
                <TableRow>
                
                  <TableCell>Date de début</TableCell>
                  <TableCell>Date de fin</TableCell>
                  <TableCell>Bénéficiaire</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {data.takenInChargeDonors.length > 0 ? (
                  data.takenInChargeDonors.map(takenInCharge => (
                    <TableRow key={takenInCharge.id}>
                   
                      <TableCell>
                        {formatDate(takenInCharge.takenInCharge.startDate)}
                      </TableCell>
                      <TableCell>
                        {formatDate(takenInCharge.takenInCharge.endDate)}
                      </TableCell>
                      <TableCell>
                      {takenInCharge.takenInCharge.takenInChargeBeneficiaries[0] && takenInCharge.takenInCharge.takenInChargeBeneficiaries[0].beneficiary && takenInCharge.takenInCharge.takenInChargeBeneficiaries[0].beneficiary.person  ? 
                        `${takenInCharge.takenInCharge.takenInChargeBeneficiaries[0].beneficiary.person.firstName} ${takenInCharge.takenInCharge.takenInChargeBeneficiaries[0].beneficiary.person.lastName}`
                        : '-'}
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={4} align="center">
                      Aucune Kafalat trouvée
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </TableContainer>
        </div>
        <div className={classes.section}>
  <Typography
    variant="h5"
    gutterBottom
    className={classes.sectionTitle}
  >
    Opérations Pris en Charge
  </Typography>
  <TableContainer component={Paper} className={classes.tableContainer}>
    <Table className={classes.table}>
      <TableHead>
        <TableRow>
     
          
          <TableCell>Date de création</TableCell>
          <TableCell>Date de planification</TableCell>
        <TableCell> Date d'execution</TableCell>
        <TableCell> Date de cloturation</TableCell>
        <TableCell>Montant</TableCell>
          <TableCell>Frais de gestion</TableCell>
      

        </TableRow>
      </TableHead>
      <TableBody>
        {data.takenInChargeDonors && data.takenInChargeDonors.takenInChargeOperations && data.takenInChargeDonors.takenInChargeOperations.length > 0 ? (
          data.takenInChargeDonors.map(takenInCharge => (
            <React.Fragment key={takenInCharge.id}>
              {takenInCharge.takenInChargeOperations.map(operation => (
                // Only render operations with non-null planning date
                operation.planningDate && (
                  <TableRow key={operation.id}>
                    <TableCell>{formatDate(operation.createdAt)}</TableCell>
                    <TableCell>{formatDate(operation.planningDate)}</TableCell>
                    <TableCell>{formatDate(operation.executionDate)}</TableCell>
                    <TableCell>{formatDate(operation.closureDate)}</TableCell>
                    <TableCell>{operation.amount} DH </TableCell>
                    <TableCell>{operation.managementFees} % </TableCell>
                  
                  </TableRow>
                )
              ))}
            </React.Fragment>
          ))
        ) : (
          <TableRow>
            <TableCell colSpan={6} align="center">
              Aucune opération Kafalat
            </TableCell>
          </TableRow>
        )}
      </TableBody>
    </Table>
  </TableContainer>
</div>

      </Paper>
    </Container>
  );
});

export default PrintableContent;
