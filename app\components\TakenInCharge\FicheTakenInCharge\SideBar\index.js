import React from 'react';
import { Link } from 'react-router-dom';
import sideBarStyles from '../../../../Css/sideBar.css';
import tagStyles from '../../../../Css/tag.css';
const rightArrow = require('images/icons/right-arrow.svg');

const SideBar = props => {
  const { data: takenInCharge, operations } = props;
  let top = null;
  let beneficiaries = '--';
  let donors = '--';
  let donorBalance = '--';
  let donorAmountStillToGive = 0;
  let donorPlannedAmount = 0;
  let reservedAmount = 0;
  let executedAmount = 0;

  const getTag = status => {
    switch (status) {
      case 'Inactif':
        return tagStyles.tagRed;
      case 'Actif':
        return tagStyles.tagGreen;
      case 'Fermé':
        return tagStyles.tagGrey;
      default:
        return tagStyles.tagGrey;
    }
  };

  if (takenInCharge) {
    // Process beneficiaries
    if (takenInCharge.takenInChargeBeneficiaries) {
      beneficiaries = takenInCharge.takenInChargeBeneficiaries.map(
        (el, index) => (
          <div key={index}>
            <Link to={`/beneficiaries/fiche/${el.beneficiary.id}/info`}>
              <p>
                <a>{`${el.beneficiary.person.firstName} ${el.beneficiary.person.lastName}`}</a>
                <img
                  src={rightArrow}
                  width="20px"
                  height="20px"
                  className="ml-3"
                  alt="Right Arrow"
                />
              </p>
            </Link>
          </div>
        ),
      );
    }

    // Calculate donor financial data
    if (operations && operations.length > 0) {
      operations.forEach(operation => {
        const totalAmount = operation.amount;
        if (operation.planningDate && !operation.reserved){
          donorPlannedAmount += totalAmount;
        }
        if (operation.reserved && !operation.executionDate){
          reservedAmount += totalAmount;
        }
        if (operation.executionDate) {
          executedAmount += totalAmount;
        }
      });
    }

    // Calculate donor balance and remaining amount to give
    if (
      takenInCharge.takenInChargeDonors &&
      takenInCharge.takenInChargeDonors[0]
    ) {
      donorBalance = takenInCharge.takenInChargeDonors[0].donorBalance || 0;
      donorAmountStillToGive = Math.max(
        donorPlannedAmount - reservedAmount - donorBalance,
        0,
      );

      donors = takenInCharge.takenInChargeDonors.map(el => (
        <div key={el.donor.id}>
          <Link to={`/donors/fiche/${el.donor.id}/info`}>
            <p>
              <a>
                {el.donor.type === 'Physique'
                  ? `${el.donor.firstName} ${el.donor.lastName}`
                  : el.donor.type === 'Moral'
                  ? el.donor.company
                  : el.donor.name}
              </a>
              <img
                src={rightArrow}
                width="20px"
                height="20px"
                className="ml-3"
                alt="Right Arrow"
              />
            </p>
          </Link>
          <hr className={`${sideBarStyles.hr} mt-3 mb-3`} />
          <div className={`text-center ${sideBarStyles.label}`}>
            <h5 >
              Synthèse financière Kafalat
            </h5>
            <div
              className={sideBarStyles.text}
              style={{ display: 'flex', flexDirection: 'column', gap: '12px' , marginTop: '20px'}}
            >
              {[
                {
                  label: 'Solde donateur disponible',
                  value: donorBalance,
                  color: '#435568',
                },
                {
                  label: 'Total planifié',
                  value: donorPlannedAmount,
                  color: '#4F89D7',
                },
                {
                  label: 'Total réservé',
                  value: reservedAmount,
                  color: 'darkgoldenrod',
                },
                {
                  label: 'Total exécuté',
                  value: executedAmount,
                  color: '#6DD5A0',
                },
                {
                  label: ' Total restant à donner',
                  value: donorAmountStillToGive,
                  color: '#EF8C5A',
                },
              ].map((item, idx) => (
                <div
                  key={idx}
                  style={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    marginBottom: '8px',
                  }}
                >
                  <div style={{ fontWeight: '600', color: '#555' }}>
                    {item.label}
                  </div>
                  <button
                    className={sideBarStyles.itemValueStyle}
                    style={{backgroundColor: item.color }}
                  >
                    {item.value} DH
                  </button>
                </div>
              ))}
            </div>

          </div>
        </div>
      ));
    }

    // Sidebar top section
    top = (
      <div className={sideBarStyles.topCard}>
        <div className={sideBarStyles.top}>
          <h5>{takenInCharge.services.name}</h5>
          <p>{takenInCharge.code}</p>
          {takenInCharge.status && (
            <div className={getTag(takenInCharge.status)}>
              {takenInCharge.status}
            </div>
          )}
        </div>
      </div>
    );
  }

  return (
    <>
      <div className={sideBarStyles.sideBar}>
        {top}
        {/* Beneficiaries Section */}
        <div className={`${sideBarStyles.text} mt-5`}>
          <div className={`text-center ${sideBarStyles.label}`}>
            <h5>Bénéficiaire</h5>
            <p className={sideBarStyles.linkDonor}>{beneficiaries}</p>
          </div>
        </div>

        <hr className={`${sideBarStyles.hr} mt-3 mb-3`} />
        {/* Donors Section */}
        <div className={sideBarStyles.text}>
          <div className={`text-center ${sideBarStyles.label}`}>
            <h5>Donateur</h5>
            <p className={sideBarStyles.linkDonor}>{donors}</p>
          </div>
        </div>
      </div>
    </>
  );
};

export default SideBar;
