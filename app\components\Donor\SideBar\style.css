#sideBar{
    display: flex;
    flex-direction: column;
    align-items: center;
    background-color: white;
    border: 2px solid white ;
    border-radius: 10px;
    padding: 0 2% 0 2%;
}

#top{
    display: flex; 
    justify-content: space-evenly;
   
    margin-top: 8%;
}

#info{
    height: auto;
    display: flex;
    flex-direction: column;
    justify-content: center;
    text-align: center;
}

#info p{
    font-weight: 600;
    margin: 5px 0;
}

#info h5{
    font-weight: 800;
    margin: 5px 0;
}

.imgBorder {
    margin: 0 20px 0 0;
    width: 40%;
    background-color: white;
    box-shadow: 0 1px 3px rgba(34, 25, 25, 0.4);
    -moz-box-shadow: 0 1px 2px rgba(34,25,25,0.4);
    -webkit-box-shadow: 0 1px 3px rgba(34, 25, 25, 0.4);
}

.tag-green{
    color: #6DD5A0;
    border: 1px solid #6DD5A0;
    border-radius: 6px;
    background-color: #D4FBE5;
    padding: 0 7px;
    font-weight: "700";
    margin: 5px 0;
}

.tag-red{
    color: #F23113;
    border: 1px solid #F24726;
    border-radius: 6px;
    background-color: #FFDCD9;
    padding: 0 5px;
    font-weight: 700;
    margin: 5px 0;
  }

  .tag-blue{
    color: #4F89D7;
    border: 1px solid #4F89D7;
    border-radius: 6px;
    background-color: #D5EAFF;
    padding: 0 5px;
    font-weight: 700;
    margin: 5px 0;
  }
  

#hr{
    margin: 30px 0;
    border: 1px solid #f1f4f6cc;
    width: 80%;
}

.text {
    display: flex;
    width: 100%;
}

.text .label{
    flex-grow: 1;
    margin-left: 12px;
    color: #868994;
}

.text .label p{
    font-weight: 700;
}

.text .data{
    flex-grow: 3;
}

.value{
    color : black;
    font-weight: 600;
}