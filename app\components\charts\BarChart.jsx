import React from 'react';
import { Box, Typography, Paper, useTheme } from '@mui/material';
import { styled } from '@mui/material/styles';

const StyledPaper = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(3),
  display: 'flex',
  flexDirection: 'column',
  alignItems: 'center',
  background: 'linear-gradient(145deg, #ffffff, #f0f0f0)',
  boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
  borderRadius: '16px',
  transition: 'transform 0.3s ease, box-shadow 0.3s ease',
  '&:hover': {
    transform: 'translateY(-4px)',
    boxShadow: '0 8px 30px rgba(0, 0, 0, 0.12)',
  },
}));

const ChartContainer = styled(Box)({
  width: '100%',
  height: '350px',
  display: 'flex',
  flexDirection: 'column',
  justifyContent: 'flex-end',
  position: 'relative',
  padding: '24px 16px',
  background: 'rgba(255, 255, 255, 0.8)',
  borderRadius: '12px',
  boxShadow: 'inset 0 2px 4px rgba(0, 0, 0, 0.05)',
});

const BarContainer = styled(Box)({
  display: 'flex',
  alignItems: 'flex-end',
  height: '100%',
  gap: '16px',
  padding: '0 16px',
});

const Bar = styled(Box)(({ height, color }) => ({
  flex: 1,
  height: `${height}%`,
  background: `linear-gradient(to top, ${color}, ${color}80)`,
  borderRadius: '8px 8px 0 0',
  transition: 'all 0.3s ease',
  position: 'relative',
  cursor: 'pointer',
  '&:hover': {
    transform: 'scaleY(1.05)',
    boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
    '& .value-label': {
      opacity: 1,
      transform: 'translateY(-8px)',
    },
  },
}));

const Label = styled(Typography)({
  position: 'absolute',
  bottom: '-28px',
  left: '50%',
  transform: 'translateX(-50%)',
  fontSize: '12px',
  fontWeight: 500,
  whiteSpace: 'nowrap',
  overflow: 'hidden',
  textOverflow: 'ellipsis',
  maxWidth: '100%',
  color: '#666',
});

const ValueLabel = styled(Typography)({
  position: 'absolute',
  top: '-32px',
  left: '50%',
  transform: 'translateX(-50%)',
  fontSize: '12px',
  fontWeight: 600,
  opacity: 0,
  transition: 'all 0.3s ease',
  background: 'rgba(255, 255, 255, 0.9)',
  padding: '4px 8px',
  borderRadius: '4px',
  boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
});

const YAxis = styled(Box)({
  position: 'absolute',
  left: 0,
  top: 0,
  bottom: 0,
  width: '40px',
  display: 'flex',
  flexDirection: 'column',
  justifyContent: 'space-between',
  padding: '16px 0',
});

const YAxisLabel = styled(Typography)({
  fontSize: '12px',
  color: '#666',
  padding: '0 8px',
  textAlign: 'right',
});

export function BarChart({ data, labels, title, stacked = false }) {
  const theme = useTheme();
  const maxValue = Math.max(...data);
  const yAxisLabels = [0, maxValue * 0.25, maxValue * 0.5, maxValue * 0.75, maxValue];
  
  return (
    <StyledPaper>
      {title && (
        <Typography 
          variant="h5" 
          sx={{ 
            fontWeight: 600,
            color: theme.palette.primary.main,
            mb: 3,
            textAlign: 'center',
          }}
        >
          {title}
        </Typography>
      )}
      <ChartContainer>
        <YAxis>
          {yAxisLabels.map((value, index) => (
            <YAxisLabel key={index}>
              {value.toLocaleString()}
            </YAxisLabel>
          ))}
        </YAxis>
        <BarContainer>
          {data.map((value, index) => {
            const height = (value / maxValue) * 100;
            return (
              <Bar
                key={index}
                height={height}
                color={theme.palette.primary.main}
              >
                <ValueLabel className="value-label">
                  {value.toLocaleString()}
                </ValueLabel>
                <Label>
                  {labels[index]}
                </Label>
              </Bar>
            );
          })}
        </BarContainer>
      </ChartContainer>
    </StyledPaper>
  );
}