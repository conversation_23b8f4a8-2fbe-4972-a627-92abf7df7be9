import React, { useState } from 'react';
import { Link, useHistory } from 'react-router-dom';
import plus from 'images/icons/plus.svg';
import minus from 'images/icons/minus.svg';
import styles from '../../../Css/profileList.css';
import stylesList from '../../../Css/profileList.css';
import tagStyles from '../../../Css/tag.css';
import Details from '../OperationsDetail/index.js';
import { VIEW_ICON } from '../../Common/ListIcons/ListIcons';
import CustomPagination from 'components/Common/CustomPagination';

const TakenInChargeItem = ({ takenInCharge, donor, history }) => {
  const [show, setShow] = useState(false);
  const handleViewPlanning = () => {
    setShow(!show);
  };
  let totalPlanned = 0;
  let totalExecuted = 0;
  let totalReserved = 0;
  let serviceName = '-';
  if (takenInCharge && takenInCharge.takenInChargeOperations) {
    takenInCharge.takenInChargeOperations.map(operation => {
      if (operation.planningDate && !operation.reserved) {
        totalPlanned += operation.amount;
      } else if (operation.reserved && !operation.executionDate) {
        totalReserved += operation.amount;
      } else if (operation.executionDate) {
        totalExecuted += operation.amount;
      }
    });
  }
  const fullTakenInCharge = {
    ...takenInCharge,
    donor: donor,
  };

  const getTag = status => {
    if (status === 'Inactif') {
      return tagStyles.tagRed;
    }
    if (status === 'Actif') {
      return tagStyles.tagGreen;
    }
    if (status === 'Fermé') {
      return tagStyles.tagGrey;
    }
    return tagStyles.tagGrey;
  };

  let beneficiaryName = <span>-</span>;

  if (takenInCharge.takenInCharge.takenInChargeBeneficiaries[0]) {
    beneficiaryName = (
      <Link
        to={`/beneficiaries/fiche/${takenInCharge.takenInCharge.takenInChargeBeneficiaries[0].beneficiary.id}/info`}
      >
        {`${takenInCharge.takenInCharge.takenInChargeBeneficiaries[0].beneficiary.person.firstName} ${takenInCharge.takenInCharge.takenInChargeBeneficiaries[0].beneficiary.person.lastName}`}
      </Link>
    );
  }
  if (takenInCharge) {
    serviceName = takenInCharge.takenInCharge.services.name;
  }
  return (
    <>
      <tr key={takenInCharge.takenInCharge.id}>
        <td className="col-md-1">
          {!show ? (
            <button
              type="button"
              onClick={handleViewPlanning}
              className="bg-transparent"
            >
              <img
                src={plus}
                width="20px"
                height="20px"
                className=""
                data-dismiss="modal"
                alt="plus"
              />
            </button>
          ) : (
            <button
              type="button"
              onClick={handleViewPlanning}
              className="bg-transparent"
            >
              <img
                src={minus}
                width="20px"
                height="20px"
                className=""
                data-dismiss="modal"
                alt="minus"
              />
            </button>
          )}
        </td>
        <td className="col-md-1">{beneficiaryName}</td>
        <td className="col-md-1">{serviceName}</td>
        <td className="col-md-1">{totalPlanned}</td>
        <td className="col-md-1">{totalReserved}</td>
        <td className="col-md-1">{totalExecuted}</td>
        <td className="col-md-1">
          <div
            style={{ whiteSpace: 'nowrap' }}
            className={`${
              takenInCharge.takenInCharge.status
                ? getTag(takenInCharge.takenInCharge.status)
                : ''
            }`}
          >
            {takenInCharge.takenInCharge.status
              ? takenInCharge.takenInCharge.status
              : '--'}
          </div>
        </td>
        <td className="col-md-1">
          <input
            type="image"
            className="p-2"
            src={VIEW_ICON}
            width="40px"
            height="40px"
            onClick={() => {
              history.push(
                `/takenInCharges/fiche/${takenInCharge.takenInCharge.id}/info`,
              );
            }}
            title="consulter"
          />
        </td>
      </tr>
      {show && (
        <td colSpan={10} className="border-top-0">
          <Details data={fullTakenInCharge} />
        </td>
      )}
    </>
  );
};

export default function TakenInCharge(props) {
  const donor = props.data;
  const history = useHistory();
  const [currentPage, setCurrentPage] = useState(1);
  const pageSize = 5;
  let listDonorTakenIncharges = null;
  const DonorTakenIncharges = donor && donor.takenInChargeDonors;
  if (DonorTakenIncharges) {
    const sortedDonorTakenIncharges = DonorTakenIncharges.slice().sort((a, b) =>
      a.id < b.id ? 1 : -1,
    );
    const startIndex = (currentPage - 1) * pageSize;
    const endIndex = Math.min(
      startIndex + pageSize,
      sortedDonorTakenIncharges.length,
    );
    const paginatedDonorTakenIncharges = sortedDonorTakenIncharges.slice(
      startIndex,
      endIndex,
    );
    listDonorTakenIncharges = paginatedDonorTakenIncharges.map(
      takenInCharge => (
        <TakenInChargeItem
          key={takenInCharge.id}
          takenInCharge={takenInCharge}
          donor={donor}
          history={history}
        />
      ),
    );
  }
  return (
    <div>
      <div className={`pb-5 ${stylesList.backgroudStyle}`}>
        <div>
          <div className={styles.global}>
            <div className={styles.header}>
              <h4>Liste des Kafalats</h4>
            </div>

            <div className={styles.content}>
              <table className="table small">
                <thead>
                  <tr>
                    <th scope="col"></th>
                    <th style={{ whiteSpace: 'nowrap' }}>Bénéficiaire</th>
                    <th style={{ whiteSpace: 'nowrap' }}>Service</th>
                    <th style={{ whiteSpace: 'nowrap' }}>
                      Total planifié (DH)
                    </th>
                    <th style={{ whiteSpace: 'nowrap' }}>Total réservé (DH)</th>
                    <th style={{ whiteSpace: 'nowrap' }}>Total exécuté (DH)</th>
                    <th style={{ whiteSpace: 'nowrap' }}>Statut</th>
                    <th style={{ whiteSpace: 'nowrap' }}>Actions</th>
                  </tr>
                </thead>
                {listDonorTakenIncharges &&
                listDonorTakenIncharges.length > 0 ? (
                  <tbody>{listDonorTakenIncharges}</tbody>
                ) : (
                  <tbody>
                    <tr>
                      <td colSpan={8} className="text-center">
                        Aucune Kafalat trouvée
                      </td>
                    </tr>
                  </tbody>
                )}
              </table>
              {DonorTakenIncharges && DonorTakenIncharges.length > 0 && (
                <div className="justify-content-center my-4">
                  <CustomPagination
                      totalElements={DonorTakenIncharges.length}
                    totalCount={Math.ceil(
                      DonorTakenIncharges.length / pageSize,
                    )}
                    pageSize={pageSize}
                    currentPage={currentPage}
                    onPageChange={setCurrentPage}
                  />
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
