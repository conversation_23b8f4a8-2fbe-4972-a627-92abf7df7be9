import produce from 'immer';
import { RESET_DELETE_Responsable_EPS, LOAD_Responsable_EPS, LOAD_Responsable_EPS_SUCCESS, LOAD_Responsable_EPS_ERROR, DELETE_Responsable_EPS, DELETE_Responsable_EPS_SUCCESS, DELETE_Responsable_EPS_ERROR } from "./constants";

export const initialState = {
    responsableEps: [],
    loading: false,
    deleteSuccess: false,
    error: null
};

const responsableEpsReducer = produce((draft, action) => {
    switch (action.type) {
        case LOAD_Responsable_EPS:
            draft.loading = true;
            draft.error = false;
            draft.responsableEps = []; 
            break;
        case LOAD_Responsable_EPS_SUCCESS:
            draft.loading = false;
            draft.error = false;
            draft.responsableEps = action.responsableEps;
            break;

        case LOAD_Responsable_EPS_ERROR:
            draft.loading = false;
            draft.error = action.error;
            break;

        case DELETE_Responsable_EPS:
            draft.deleteSuccess = false;
            break;

        case DELETE_Responsable_EPS_SUCCESS:
            draft.deleteSuccess = true;
            break;

        case DELETE_Responsable_EPS_ERROR:
            draft.error = action.error;
            break;

        case RESET_DELETE_Responsable_EPS:
            draft.deleteSuccess = false;
            break;
        default:
            return draft;
    }
}, initialState);

export default responsableEpsReducer;
