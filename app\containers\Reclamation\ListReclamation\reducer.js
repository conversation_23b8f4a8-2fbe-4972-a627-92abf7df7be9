import produce from 'immer';
import {
  FETCH_RECLAMATIONS_REQUEST,
  FETCH_RECLAMATIONS_SUCCESS,
  FETCH_RECLAMATIONS_FAILURE,
  UPDATE_RECLAMATION_REQUEST,
  UPDATE_RECLAMATION_SUCCESS,
  UPDATE_RECLAMATION_FAILURE,
  RESET_ERROR,
} from './constants';

export const initialState = {
  reclamations: null,
  loading: false,
  error: null,
};

/* eslint-disable default-case, no-param-reassign */
const reclamationReducer = (state = initialState, action) =>
  produce(state, draft => {
    switch (action.type) {
      // Fetch reclamations
      case FETCH_RECLAMATIONS_REQUEST:
        draft.loading = true;
        draft.error = null;
        break;
      case FETCH_RECLAMATIONS_SUCCESS:
        draft.loading = false;
        draft.reclamations = action.reclamations;
        break;
      case FETCH_RECLAMATIONS_FAILURE:
        draft.loading = false;
        draft.error = action.error;
        break;

      // Update reclamation
      case UPDATE_RECLAMATION_REQUEST:
        draft.loading = true;
        draft.error = null;
        break;
      case UPDATE_RECLAMATION_SUCCESS:
        draft.loading = false;
        // Update the reclamation in the list
        if (draft.reclamations && draft.reclamations.content) {
          const index = draft.reclamations.content.findIndex(r => r.id === action.reclamation.id);
          if (index !== -1) {
            draft.reclamations.content[index] = action.reclamation;
          }
        }
        break;
      case UPDATE_RECLAMATION_FAILURE:
        draft.loading = false;
        draft.error = action.error;
        break;

      // Reset states
      case RESET_ERROR:
        draft.error = null;
        break;
    }
  });

export default reclamationReducer;
