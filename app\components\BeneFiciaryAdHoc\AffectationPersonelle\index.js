import React, { useState, useMemo, useEffect, useCallback } from 'react';
import stylesList from 'Css/profileList.css';
import btnStyles from 'Css/button.css';
import { Button, CircularProgress, Link } from '@mui/material';
import listStyles from 'Css/list.css';
import { useHistory, useParams } from 'react-router-dom';
import {
  Form,
  InputGroup,
  ListGroup,
  Modal,
  Button as BootStrapButton,
  Alert,
} from 'react-bootstrap';
import { createStructuredSelector } from 'reselect';
import {
  makeSelectAllPersons,
  makeSelectAllPersonsError,
  makeSelectAllPresonsLoading,
  makeSelectAffectedPersonsLoading,
  makeSelectAffectedPersonsResult,
} from 'containers/BeneficiaryAdHoc/BeneficiariesAdHocProfile/selectors';
import { useDispatch, useSelector } from 'react-redux';
import {
  affectPersonsAdhoc,
  affectPersonsAdhocError,
  getBenefAdhockPersonne,
} from 'containers/BeneficiaryAdHoc/BeneficiariesAdHocProfile/actions';
import DataTable from 'components/Common/DataTable';

const stateSelector = createStructuredSelector({
  persons: makeSelectAllPersons,
  personsError: makeSelectAllPersonsError,
  personsLoading: makeSelectAllPresonsLoading,
  affectingPersonsLoading: makeSelectAffectedPersonsLoading,
  affectingPersonsResult: makeSelectAffectedPersonsResult,
});
export const AffectationPersonelle = ({ group = {} }) => {
  const params = useParams();
  const { id } = params;
  const history = useHistory();
  const dispatch = useDispatch();

  const {
    persons,
    affectingPersonsLoading,
    affectingPersonsResult,
  } = useSelector(stateSelector);
  const [selectedPersons, setSelectedPersons] = useState([]);

  const [filter, setFilter] = useState('');
  const [listPersonsModal, setListPersonsModal] = useState(false);
  const [rows, setRows] = useState([]);
  const populateRows = useCallback(() => {
    if (group.beneficiaries && group.beneficiaries.length > 0) {
      setRows(() =>
        group.beneficiaries.map(person => ({
          id: person.id,
          code: person.code ? person.code : '---',
          name: person.fullNameContact
            ? person.fullNameContact
            : `${person.firstName} ${person.lastName}`,
        })),
      );
    }
  }, [group]);

  const columns = [
    {
      field: 'code',
      headerName: 'Code',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
    {
      field: 'name',
      headerName: 'Nom Complet',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
  ];

  useEffect(() => {
    populateRows();
  }, [group]);

  const handleCheckboxChange = id => {
    setSelectedPersons(prevSelected =>
      prevSelected.includes(id)
        ? prevSelected.filter(id => id !== id)
        : [...prevSelected, id],
    );
  };
  const filteredPersons = useMemo(() => {
    if (persons) {
      return persons.filter(person => {
        const fullName = `${person.firstName.toLowerCase()} ${person.lastName.toLowerCase()}`;
        const search = filter.toLowerCase();

        return (
          person.firstName.toLowerCase().includes(search) ||
          person.lastName.toLowerCase().includes(search) ||
          fullName.includes(search)
        );
      });
    }
    return persons;
  }, [persons, filter]);
  const handleSelectAll = () => {
    const allPersonsId = filteredPersons.map(b => b.id);
    if (allPersonsId.every(id => selectedPersons.includes(id))) {
      setSelectedPersons([]);
    } else {
      setSelectedPersons(allPersonsId);
    }
  };

  const uploadAffectingPersons = () => {
    if (selectedPersons.length > 0) {
      dispatch(
        affectPersonsAdhoc({ personsList: selectedPersons, groupId: id }),
      );
    }
  };

  const openModal = () => {
    setListPersonsModal(true);
  };

  const closeModal = () => {
    setListPersonsModal(false);
    setSelectedPersons([]);
  };

  useEffect(() => {
    if (affectingPersonsResult) {
      closeModal();
      setTimeout(() => {
        dispatch(affectPersonsAdhocError({ error: true }));
        dispatch(getBenefAdhockPersonne({ id: params.id, isPerson: false }));
      }, 4000);
    }
  }, [affectingPersonsResult]);

  return (
    <div className={stylesList.backgroudStyle}>
      {affectingPersonsResult && (
        <Alert
          className="alert-style"
          variant="success"
          onClose={() => {
            dispatch(affectPersonsAdhocError({ error: true }));
          }}
          dismissible
        >
          <p>Personel ajouter avec success</p>
        </Alert>
      )}
      <Modal show={listPersonsModal} onHide={closeModal} size="lg">
        <Modal.Header closeButton>
          <Modal.Title>Ajouter des personnes</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <InputGroup className="mb-3">
            <BootStrapButton
              variant="outline-secondary"
              onClick={handleSelectAll}
            >
              {filteredPersons.length === selectedPersons.length
                ? 'Désélectionner tout'
                : 'Sélectionner tout'}
            </BootStrapButton>

            <Form.Control
              type="text"
              placeholder="Filtrer par nom ou prénom"
              value={filter}
              onChange={e => setFilter(e.target.value)}
            />
          </InputGroup>
          <div style={{ maxHeight: '200px', overflowY: 'auto' }}>
            <ListGroup className="mt-3">
              {filteredPersons.map(person => (
                <ListGroup.Item key={person.id}>
                  <Form.Check
                    type="checkbox"
                    label={`${person.firstName} ${person.lastName}`}
                    onChange={() => handleCheckboxChange(person.id)}
                    checked={selectedPersons.includes(person.id)}
                  />
                </ListGroup.Item>
              ))}
            </ListGroup>
          </div>
        </Modal.Body>
        <Modal.Footer>
          <BootStrapButton variant="secondary" onClick={closeModal}>
            Annuler
          </BootStrapButton>
          <BootStrapButton
            i
            variant="primary"
            onClick={() => {
              !affectingPersonsLoading && uploadAffectingPersons();
            }}
          >
            {affectingPersonsLoading ? (
              <CircularProgress style={{ width: '30px', height: '30px' }} />
            ) : (
              <div>Affecter</div>
            )}
          </BootStrapButton>
        </Modal.Footer>
      </Modal>

      <div className={`${listStyles.head} d-flex justify-content-end`}>
        <div
          style={{
            display: 'flex',
            flexDirection: 'row',
            justifyContent: 'flex-end',
          }}
        >
          <div className="dropdown ">
            <button
              className={`dropdown-toggle ${btnStyles.addBtnProfile}`}
              type="button"
              id="dropdownMenuButton"
              data-toggle="dropdown"
              aria-haspopup="true"
              aria-expanded="false"
            >
              Actions
            </button>
            <div
              className="dropdown-menu"
              style={{ width: '161%' }}
              aria-labelledby="dropdownMenuButton"
            >
              <Button
                className="dropdown-item"
                style={{
                  justifyContent: 'flex-start',
                }}
                onClick={() => {
                  history.push('/beneficiaries/add/ad-hoc/personne', {
                    affectingToGroup: id,
                    redirectTo: 'group',
                  });
                }}
              >
                <p
                  style={{
                    fontWeight: 400,
                    color: '#212529',
                    textDecoration: 'none',
                  }}
                >
                  Ajouter personne
                </p>
              </Button>
              <Button
                style={{
                  fontWeight: 400,
                  color: '#212529',
                  textDecoration: 'none',
                }}
                className="dropdown-item"
                onClick={() => {
                  //  modal
                  openModal();
                }}
              >
                <p
                  style={{
                    fontWeight: 400,
                    color: '#212529',
                    textDecoration: 'none',
                  }}
                >
                  Sélection des personnes
                </p>
              </Button>
            </div>
          </div>
        </div>
      </div>

      <DataTable
        rows={rows}
        columns={columns}
        // key={group.beneficiaries ? 'mount' : 'unmount'}
        fileName={`Liste des Beneficiary ad-Hoc , ${new Date().toLocaleString()}`}
        totalElements={
          group.beneficiaries && group.beneficiaries.length
            ? group.beneficiaries.length
            : 0
        }
        numberOfElements={
          group.beneficiaries && group.beneficiaries.length
            ? group.beneficiaries.length
            : 0
        }
        pageable={0}
      />
    </div>
  );
};
