export const LOAD_RELEVE_DONOR = 'almobadara/OMDBPage/LOAD_RELEVE_DONOR';
export const LOAD_RELEVE_DONOR_SUCCESS =
  'almobadara/OMDBPage/LOAD_RELEVE_DONOR_SUCCESS';
export const LOAD_RELEVE_DONOR_ERROR =
  'almobadara/OMDBPage/LOAD_RELEVE_DONOR_ERROR';

export const LOAD_CANAL_DONATIONS = 'almobadara/OMDBPage/LOAD_CANAL_DONATIONS';
export const LOAD_CANAL_DONATIONS_SUCCESS =
  'almobadara/OMDBPage/LOAD_CANAL_DONATIONS_SUCCESS';
export const LOAD_CANAL_DONATIONS_ERROR =
  'almobadara/OMDBPage/LOAD_CANAL_DONATIONS_ERROR';

export const LOAD_SERVICECATEGORIES =
  'almobadara/OMDBPage/LOAD_SERVICECATEGORIES';
export const LOAD_SERVICECATEGORIES_SUCCESS =
  'almobadara/OMDBPage/LOAD_SERVICECATEGORIES_SUCCESS';
export const LOAD_SERVICECATEGORIES_ERROR =
  'almobadara/OMDBPage/LOAD_SERVICECATEGORIES_ERROR';

export const LOAD_SERVICES = 'almobadara/takenInChargeProfile/LOAD_SERVICES';
export const LOAD_SERVICES_SUCCESS =
  'almobadara/takenInChargeProfile/LOAD_SERVICES_SUCCESS';
export const LOAD_SERVICES_ERROR =
  'almobadara/takenInChargeProfile/LOAD_STATUSES_ERROR';
