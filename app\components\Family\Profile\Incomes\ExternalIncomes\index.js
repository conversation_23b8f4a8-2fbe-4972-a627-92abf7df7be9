import React, { useEffect, useState } from 'react';
import moment from 'moment';
import styles from 'Css/profileList.css';
import btnStyles from 'Css/button.css';
import { DELETE_ICON, EDIT_ICON } from 'components/Common/ListIcons/ListIcons';
import { useDispatch, useSelector } from 'react-redux';
import { useInjectReducer, useInjectSaga } from 'redux-injectors';
import {
  makeSelectExternalIncome,
  makeSelectSuccess,
  makeSelectSuccessDelete,
} from 'containers/Common/Forms/ExternalIncomeForm/selectors';

import reducer from 'containers/Common/Forms/ExternalIncomeForm/reducer';
import { createStructuredSelector } from 'reselect';

import Modal2 from 'components/Common/Modal2';
import {
  pushExternalIncome,
  removeExternalIncome,
  updateExternalIncome,
} from 'containers/Family/FamilyProfile/actions';
import ExternalIncomeForm from 'containers/Common/Forms/ExternalIncomeForm';
import {
  deleteExternalIncome,
  resetExternalIncome,
} from 'containers/Common/Forms/ExternalIncomeForm/actions';
import { deleteExternalIncomeSaga } from 'containers/Common/Forms/ExternalIncomeForm/saga';
import DataTable from 'components/Common/DataTable';
import AccessControl, { isAuthorized } from 'utils/AccessControl';
import stylesTag from '../../../../../Css/tag.css';

const key = 'externalIncome';

const formatDate = date => moment(date).format('DD/MM/YYYY');

const omdbSelector = createStructuredSelector({
  success: makeSelectSuccess,
  externalIncome: makeSelectExternalIncome,
  successDelete: makeSelectSuccessDelete,
});

export default function ExternalIncomes(props) {
  const { success, externalIncome, successDelete } = useSelector(omdbSelector);

  const dispatch = useDispatch();
  useInjectReducer({ key, reducer });
  useInjectSaga({ key, saga: deleteExternalIncomeSaga });

  const [showModal, setShowModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [actualExternalIncome, setActualExternalIncome] = useState('');

  const handleActualExternalIncome = externalIncome => {
    setActualExternalIncome(externalIncome);
    setShowModal(true);
  };
  const handleClose = () => setShowModal(false);
  const handleShow = () => setShowModal(true);
  const handleCloseForDeleteModal = () => setShowDeleteModal(false);
  const handleShowForDeleteModal = () => setShowDeleteModal(true);

  // Define pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const pageSize = 5; // Define page size

  const family = props.data;
  const listeRevenus = null;
  const rows = [];
  const mapRows = [];

  const dataGridRows = [];
  let dataGridColumns = [];

  let familyCode;

  if (family) {
    familyCode = family.code;
  }

  useEffect(() => {
    if (successDelete) {
      dispatch(
        removeExternalIncome(externalIncome, externalIncome.familyMember.id),
      );
      dispatch(resetExternalIncome());
    }
  }, [successDelete]);

  useEffect(() => {
    if (success) {
      if (actualExternalIncome == '') {
        dispatch(
          pushExternalIncome(externalIncome, externalIncome.familyMember.id),
        );
      } else {
        dispatch(
          updateExternalIncome(externalIncome, externalIncome.familyMember.id),
        );
      }
    }
  }, [success]);

  let gridData = [];
  const isExpired = endDate => endDate && moment(endDate).isBefore(moment());

  if (family && family.familyMembers) {
    gridData = family.familyMembers.reduce((acc, member) => {
      if (member.externalIncomes && member.externalIncomes.length > 0) {
        member.externalIncomes.forEach(income => {
          acc.push({
            id: income.id,
            member: `${member.familyRelationship.name} - ${member.person.lastName} ${member.person.firstName}`,
            source: income.incomeSource ? income.incomeSource.name : '-',
            amount: income.amount ? income.amount : '-',
            amountByMonth: income.amount
              ? (income.amount / income.periodicity).toFixed(2)
              : '-',
            periodicity: income.periodicity ? income.periodicity : '-',
            startDate: income.startDate ? formatDate(income.startDate) : '-',
            endDate: income.endDate ? formatDate(income.endDate) : '-',
            status: isExpired(income.endDate) ? 'Expiré' : 'En cours',
            comment: income.comment ? income.comment : '-',
            income,
          });
        });
      }
      return acc;
    }, []);
  }

  dataGridColumns = [
    {
      field: 'member',
      headerName: 'Membre',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
    {
      field: 'source',
      headerName: 'Source',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
    {
      field: 'amount',
      headerName: 'Montant (DH)',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
    {
      field: 'amountByMonth',
      headerName: 'Montant par mois (DH)',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
    {
      field: 'periodicity',
      headerName: 'Périodicité',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
    {
      field: 'startDate',
      headerName: 'Date de début',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
    {
      field: 'endDate',
      headerName: 'Date de fin',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
    {
      field: 'status',
      headerName: 'Statut',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
      renderCell: params => (
        <span
          className={
            params.row.status === 'En cours'
              ? stylesTag.tagGreen
              : stylesTag.tagRed
          }
        >
          {params.row.status}
        </span>
      ),
    },
  ];

  const connectedUser = useSelector(state => state.app.connectedUser);
  const isUpdateAuthorized = isAuthorized(
    connectedUser,
    'GERER_CANDIDATS',
    'WRITE',
  );
  if (isUpdateAuthorized) {
    // If the user is an admin, add the Actions columns
    dataGridColumns.push({
      field: 'actions',
      headerName: 'Actions',
      type: 'actions',
      flex: 1,
      renderCell: params => (
        <div>
          <AccessControl module="FAMILLE" functionality="UPDATE">
            <>
              <input
                type="image"
                src={EDIT_ICON}
                className="p-2"
                width="40px"
                height="40px"
                onClick={() => {
                  const income = params.row;
                  handleActualExternalIncome(params.row.income);
                  setShowModal(true);
                }}
                title="modifier"
              />
              <input
                type="image"
                src={DELETE_ICON}
                className="p-2"
                width="40px"
                height="40px"
                onClick={() => {
                  handleShowForDeleteModal();
                  setActualExternalIncome(params.row.income);
                }}
                title="supprimer"
              />
            </>
          </AccessControl>
        </div>
      ),
    });
  }

  return (
    <div>
      <div className={styles.global}>
        <Modal2
          size="lg"
          customWidth="modal-90w"
          title={
            actualExternalIncome
              ? 'Modifier autre source de revenus'
              : 'Ajouter autre source de revenus'
          }
          show={showModal}
          handleClose={handleClose}
        >
          <ExternalIncomeForm
            members={family && family.familyMembers ? family.familyMembers : []}
            externalIncome={actualExternalIncome}
            show={showModal}
            button={actualExternalIncome ? 'Modifier' : 'Ajouter'}
            handleClose={handleClose}
          />
        </Modal2>

        <Modal2
          centered
          className="mt-5"
          title="Confirmation de suppression"
          show={showDeleteModal}
          handleClose={handleCloseForDeleteModal}
        >
          <p className="mt-1 mb-5 px-2">
            Êtes-vous sûr de vouloir supprimer cette source de revenu?
          </p>
          <div className="d-flex justify-content-end px-2 my-1">
            <button
              type="button"
              className="btn-style outlined"
              onClick={handleCloseForDeleteModal}
            >
              Annuler
            </button>
            <button
              type="submit"
              className={`ml-3 btn-style primary`}
              onClick={() => {
                dispatch(deleteExternalIncome(actualExternalIncome));
                setActualExternalIncome('');
                handleCloseForDeleteModal();
              }}
            >
              Supprimer
            </button>
          </div>
        </Modal2>

        <div className="d-flex justify-content-end">
          <AccessControl module="FAMILLE" functionality="UPDATE">
            <button
              className={btnStyles.addBtnProfile}
              onClick={() => {
                handleActualExternalIncome('');
                handleShow();
              }}
            >
              <i className="fas fa-plus"></i>&nbsp; Ajouter
            </button>
          </AccessControl>
        </div>

        <div className={styles.content}>
          <DataTable
            rows={gridData}
            columns={dataGridColumns}
            fileName={`Liste des revenus externes de la famille ${familyCode}, ${new Date().toLocaleString()}`}
          />
        </div>
      </div>
    </div>
  );
}
