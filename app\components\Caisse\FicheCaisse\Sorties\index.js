import React, { useState } from 'react';
import moment from 'moment';
import styles from 'Css/profileList.css';
import stylesList from 'Css/profileList.css';
import { useSelector } from 'react-redux';
import { isAuthorized } from 'utils/AccessControl';
import { Link } from 'react-router-dom';
import DataTable from '../../../Common/DataTable';
import CustomPagination from '../../../Common/CustomPagination';

const formatDate = date =>
  date
    ? moment(date).isValid()
      ? moment(date).format('DD/MM/YYYY')
      : '-'
    : '-';

export default function Sorties(props) {
  let listAidesComplementaires = [];
  const successAlert = null;

  const [currentPage, setCurrentPage] = useState(1);
  const pageSize = 5;

  const caisse = props.data;

  let caisseCode;

  if (caisse) {
    caisseCode = caisse.code;
  }

  if (
    caisse &&
    caisse.aidesComplementaires &&
    caisse.aidesComplementaires.length > 0
  ) {
    const aidesComplementaireSorted = [
      ...caisse.aidesComplementaires,
    ].sort((a, b) => (a.dateDebut < b.dateDebut ? 1 : -1));

    const startIndex = (currentPage - 1) * pageSize;
    const endIndex = Math.min(
      startIndex + pageSize,
      aidesComplementaireSorted.length,
    );
    const paginatedAidesComplementaires = aidesComplementaireSorted.slice(
      startIndex,
      endIndex,
    );

    listAidesComplementaires = paginatedAidesComplementaires.map(
      aidesComplementaire => ({
        ...aidesComplementaire,
        commentaire: `${aidesComplementaire.commentaire}`,
        code: `${aidesComplementaire.code}`,
        dateExecution: `${aidesComplementaire.dateExecution}`,
        montantPrevu: `${aidesComplementaire.montantPrevu}`,
        idComplementaire: `${aidesComplementaire.id}`,
      }),
    );
  }

  const columns = [
    {
      field: 'code',
      headerName: 'Code d’opération',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
      renderCell: params => (
        <Link
          to={`/aide-complementaire/fiche/${params.row.idComplementaire}/info`}
        >
          {params.row.code}
        </Link>
      ),
    },
    {
      field: 'dateExecution',
      headerName: "Date d'opération",
      flex: 1,
      headerAlign: 'center',
      align: 'center',
      renderCell: params => formatDate(params.value),
    },
    {
      field: 'commentaire',
      headerName: 'Libellé d’opération',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
    {
      field: 'montantPrevu',
      headerName: 'Montant',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
  ];

  return (
    <div>
      {successAlert}
      <div className={`pb-5 ${stylesList.backgroudStyle}`}>
        <div>
          <div className={styles.global}>
            <div className={styles.header}>
              <h4>Liste des sorties</h4>
            </div>
            <DataTable
              rows={listAidesComplementaires}
              columns={columns}
              fileName={`Liste des sorties de la caisse ${caisseCode}, ${new Date().toLocaleString()}`}
            />
            <div className="justify-content-center my-4">
              {caisse && caisse.aidesComplementaires.length > 0 && (
                <CustomPagination
                  totalCount={Math.ceil(
                    caisse.aidesComplementaires.length / pageSize,
                  )}
                  pageSize={pageSize}
                  currentPage={currentPage}
                  onPageChange={setCurrentPage}
                />
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
