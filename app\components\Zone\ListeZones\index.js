import React, { useState } from 'react';
import { useHistory } from 'react-router-dom';
import listStyle from 'Css/profileList.css';
import btnStyles from 'Css/button.css';
import { useSelector } from 'react-redux';
import { Alert } from 'react-bootstrap';
import AccessControl from 'utils/AccessControl';
import DataTable from 'components/Common/DataTable';
import {
  DELETE_ICON,
  EDIT_ICON,
  VIEW_ICON,
} from 'components/Common/ListIcons/ListIcons';
import moment from 'moment';
import styles from '../../../Css/tag.css';
import StatusIcon from './StatusIcon';

const ListZones = ({ zones, handleStatusChange }) => {
  const history = useHistory();
  const [showAlert, setShowAlert] = useState(false);
  const [message, setMessage] = useState('');

  const formatDate = date => moment(date).format('DD/MM/YYYY');

  const viewHandler = id => {
    history.push(`/zones/fiche/${id}/assistants`, { params: id });
  };

  const editHandler = id => {
    history.push(`/zones/editZone/${id}`);
  };

  const formatCityNames = cityDetails => {
    return cityDetails && cityDetails.map(city => city.name).join(', ');
  };

  const columns = [
    {
      field: 'code',
      headerName: 'Code de la zone',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
    {
      field: 'name',
      headerName: 'Nom de la zone',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
    {
      field: 'nameAr',
      headerName: 'اسم المنطقة',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
    {
      field: 'assistantName',
      headerName: 'Assistant',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
      renderCell: params => (
        <span>
          {' '}
          {params.row.assistantName ? params.row.assistantName : '---'}
        </span>
      ),
    },
    {
      field: 'nature',
      headerName: 'Nature',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
      renderCell: params => (
        <span>
          {' '}
          {params.row.epsId != null?'Eps':'Zone'}
        </span>
      ),
    },
    {
      field: 'cityNames',
      headerName: 'Villes associées',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
      renderCell: params => (
        <span>{formatCityNames(params.row.cityDetails) ? formatCityNames(params.row.cityDetails) : '---'}</span>
      ),
    },
    {
      field: 'details',
      headerName: 'Détails',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
      renderCell: params => (
        <span>
          {params.row.details ? params.row.details : '---'}
        </span>
      ),
    },

    {
      field: 'beneficiariesCount',
      headerName: 'Nombre de bénéficiaires',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
    {
      field: 'status',
      headerName: 'Statut',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
      renderCell: params => (
        <span className={params.row.status ? styles.tagGreen : styles.tagRed}>
          {params.row.status ? 'Actif' : 'Inactif'}
        </span>
      ),
    },

    {
      field: 'actions',
      headerName: 'Actions',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
      renderCell: params => (
        <div
          style={{
            display: 'flex',
            flexDirection: 'row',
            justifyContent: 'space-around',
            alignItems: 'center',
          }}
        >
          <input
            type="image"
            onClick={() => viewHandler(params.row.id)}
            className="p-2"
            src={VIEW_ICON}
            width="40px"
            height="40px"
            title="consulter"
          />
          <AccessControl module="USER" functionality="UPDATE">
            <StatusIcon
              isActive={params.row.status}
              onClick={() =>
                handleStatusChange(params.row.id, params.row.status)
              }
            />
          </AccessControl>
          <AccessControl module="USER" functionality="UPDATE">
            <input
              type="image"
              onClick={() => editHandler(params.row.id)}
              className="p-2"
              src={EDIT_ICON}
              width="40px"
              height="40px"
              title="Modifier"
            />
          </AccessControl>
        </div>
      ),
    },
  ];

  return (
    <div className="table-container">
      {showAlert ? (
        <Alert
          className="alert-style"
          variant="success"
          onClose={() => setShowAlert(false)}
          dismissible
        >
          <p>{message}</p>
        </Alert>
      ) : null}

      <div className={listStyle.global}>{/* Any additional UI elements */}</div>

      <div>
        <DataTable
          rows={zones.map(zone => ({
            ...zone,
            cityNames: formatCityNames(zone.cityDetails), // Map city names for display
          }))}
          columns={columns}
          fileName={`Liste des zones, ${new Date().toLocaleString()}`}
        />
      </div>
    </div>
  );
};

export default ListZones;
