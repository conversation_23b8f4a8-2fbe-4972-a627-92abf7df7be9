import React, { useEffect, useState } from 'react';
import {
  <PERSON>,
  Button,
  Card,
  CardContent,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Divider,
  IconButton,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TextField,
  Tooltip,
  Typography,
} from '@mui/material';
import {
  Check,
  Code,
  Edit,
  Group,
  MonetizationOn,
  People,
} from '@mui/icons-material';
import { useDispatch } from 'react-redux';
import { updateMontantForGroupRequest } from '../../../containers/AideComplementaire/FicheAideComplementaire/actions';

const GroupModal = ({ groupData, isOpen, onClose, aideComplementaireId }) => {
  const dispatch = useDispatch();
  const [editedAmounts, setEditedAmounts] = useState({});
  const [editMode, setEditMode] = useState({});
  const [globalAmount, setGlobalAmount] = useState('');

  console.log({ editedAmounts });

  useEffect(() => {
    if (groupData) {
      const initialAmounts = groupData.beneficiaries.reduce(
        (acc, beneficiary) => ({
          ...acc,
          [beneficiary.id]: beneficiary.montantAffecter,
        }),
        {},
      );
      setEditedAmounts(initialAmounts);
    }
  }, [groupData]);

  useEffect(() => {
    if (!isOpen) {
      setGlobalAmount('');
    }
  }, [isOpen]);

  const handleEditClick = id => {
    setEditMode(prev => ({ ...prev, [id]: true }));
  };

  const handleAmountChange = (id, value) => {
    setEditedAmounts(prev => ({ ...prev, [id]: parseFloat(value) || 0 }));
  };

  const handleConfirmClick = id => {
    setEditMode(prev => ({ ...prev, [id]: false }));
  };

  const handleGlobalAmountChange = value => {
    setGlobalAmount(value);
  };

  const applyGlobalAmount = () => {
    const amount = parseFloat(globalAmount) || 0;
    const updatedAmounts = groupData.beneficiaries.reduce(
      (acc, beneficiary) => ({ ...acc, [beneficiary.id]: amount }),
      {},
    );
    setEditedAmounts(updatedAmounts);
    setGlobalAmount('');
  };

  const handleSubmit = () => {
    const payload = {
      idAideComplementaire: aideComplementaireId,
      idGroupe: groupData.id,
      type: 'Groupe',
      beneficiaryAmounts: editedAmounts,
    };
    dispatch(
      updateMontantForGroupRequest(
        aideComplementaireId,
        groupData.id,
        'Groupe',
        editedAmounts,
      ),
    );
    onClose();
  };

  const getTotalAmount = () => {
    return Object.values(editedAmounts).reduce((acc, val) => acc + val, 0);
  };

  if (!groupData) return null;

  return (
    <Dialog open={isOpen} onClose={onClose} maxWidth="lg" fullWidth>
      <DialogTitle
        sx={{
          display: 'flex',
          alignItems: 'center',
          gap: 1,
          padding: '16px',
          marginBottom: 1,
        }}
      >
        <Group sx={{ marginRight: 1, fontSize: '1.8rem' }} />
        Modifier le montant affecté au groupe
      </DialogTitle>

      <DialogContent>
        <Card sx={{ mt: 1, mb: 3, borderRadius: '12px', boxShadow: 3 }}>
          <CardContent>
            <Typography
              variant="h6"
              sx={{
                fontWeight: 'bold',
                mb: 2,
                display: 'flex',
                alignItems: 'center',
              }}
            >
              <Group sx={{ marginRight: 1, color: '#1976d2' }} />
              Informations du Groupe
            </Typography>
            <Divider sx={{ mb: 2 }} />

            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2 }}>
              <Box
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: 1,
                  flex: '1 1 45%',
                }}
              >
                <Code sx={{ color: '#ff9800' }} />
                <Typography>
                  <strong>Code :</strong> {groupData.code}
                </Typography>
              </Box>

              <Box
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: 1,
                  flex: '1 1 45%',
                }}
              >
                <Group sx={{ color: '#4caf50' }} />
                <Typography>
                  <strong>Nom :</strong> {groupData.name}
                </Typography>
              </Box>

              <Box
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: 1,
                  flex: '1 1 45%',
                }}
              >
                <People sx={{ color: '#3f51b5' }} />
                <Typography>
                  <strong>Nombre de personnes :</strong>{' '}
                  {groupData.beneficiaries.length}
                </Typography>
              </Box>

              <Box
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: 1,
                  flex: '1 1 45%',
                }}
              >
                <MonetizationOn sx={{ color: '#e91e63' }} />
                <Typography>
                  <strong>Montant total affecté :</strong> {getTotalAmount()}{' '}
                  MAD
                </Typography>
              </Box>
            </Box>
          </CardContent>
        </Card>

        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 3 }}>
          <TextField
            type="number"
            value={globalAmount}
            placeholder="Saisir le montant à affecter par personne"
            onChange={e => handleGlobalAmountChange(e.target.value)}
            sx={{
              flex: 1,
              '& .MuiInputBase-root': {
                borderRadius: '8px',
                backgroundColor: '#f5f5f5',
                padding: '6px 12px',
              },
              '& .MuiInputLabel-root': {
                color: '#1976d2',
              },
            }}
            InputProps={{
              startAdornment: (
                <MonetizationOn sx={{ color: '#1976d2', mr: 1 }} />
              ),
            }}
          />
          <Button
            variant="contained"
            color="primary"
            onClick={applyGlobalAmount}
            disabled={!globalAmount}
            sx={{
              padding: '8px 16px',
              textTransform: 'none',
              boxShadow: 2,
              borderRadius: '8px',
              fontWeight: 'bold',
              '&:hover': {
                backgroundColor: '#1976d2',
              },
              '&:disabled': {
                backgroundColor: '#ccc',
              },
            }}
          >
            Appliquer à tous
          </Button>
        </Box>

        <TableContainer
          component={Paper}
          sx={{
            borderRadius: '8px',
            overflow: 'auto',
            maxHeight: '400px',
            boxShadow: 1,
            border: '1px solid #ddd',
          }}
        >
          <Table>
            <TableHead sx={{ backgroundColor: '#f5f5f5' }}>
              <TableRow>
                <TableCell>Nom Complet</TableCell>
                <TableCell>Numéro d'identité</TableCell>
                <TableCell>Montant Affecté</TableCell>
                <TableCell>Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {groupData.beneficiaries.map(beneficiary => (
                <TableRow
                  key={beneficiary.id}
                  sx={{
                    '&:hover': { backgroundColor: '#f9f9f9' },
                  }}
                >
                  <TableCell>{beneficiary.fullName}</TableCell>
                  <TableCell>{beneficiary.identityCode}</TableCell>
                  <TableCell>
                    {editMode[beneficiary.id] ? (
                      <TextField
                        type="number"
                        value={editedAmounts[beneficiary.id]}
                        onChange={e =>
                          handleAmountChange(beneficiary.id, e.target.value)
                        }
                        autoFocus
                        size="small"
                        sx={{ width: '100px' }}
                      />
                    ) : (
                      editedAmounts[beneficiary.id]
                    )}
                  </TableCell>
                  <TableCell>
                    {editMode[beneficiary.id] ? (
                      <Tooltip title="Valider">
                        <IconButton
                          onClick={() => handleConfirmClick(beneficiary.id)}
                          color="primary"
                        >
                          <Check />
                        </IconButton>
                      </Tooltip>
                    ) : (
                      <Tooltip title="Modifier">
                        <IconButton
                          onClick={() => handleEditClick(beneficiary.id)}
                          color="secondary"
                        >
                          <Edit />
                        </IconButton>
                      </Tooltip>
                    )}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose} variant="outlined" color="inherit">
          Annuler
        </Button>
        <Button onClick={handleSubmit} variant="contained" color="primary">
          Confirmer
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default GroupModal;
