import React, { useEffect, useRef, useState } from 'react';
import { Form, Formik } from 'formik';
import { useDispatch, useSelector } from 'react-redux';
import { useInjectReducer, useInjectSaga } from 'redux-injectors';
import btnStyles from 'Css/button.css';
import { createStructuredSelector } from 'reselect';
import { Alert } from 'react-bootstrap';
import style from 'Css/form.css';
import * as Yup from 'yup';
import { addSousZone, resetAddSousZone } from './actions';
import {makeSelectError, makeSelectSuccess } from './selectors';
import saga from './saga';
import reducer from './reducer';
import { CustomTextInput } from 'containers/Common/CustomInputs/CustomTextInput';
import { CustomTextInputAr } from 'containers/Common/CustomInputs/CustomTextInputAr';
import { CustomTextArea } from 'containers/Common/CustomInputs/CustomTextArea';
import { CustomSelect } from 'containers/Common/CustomInputs/CustomSelect';
const key = 'sousZone';

const backgroundStyle = {
  backgroundColor: 'white',
  border: '2px solid white ',
  borderRadius: '10px',
};

const omdbSelector = createStructuredSelector({
  addSuccess: makeSelectSuccess,
  error: makeSelectError,
});

export default function SousZoneForm(props) {
  const {isRead, handleClose, zoneId, sousZoneToEdit, availableZones } = props;
  const formikRef = useRef();
  useInjectReducer({ key, reducer });
  useInjectSaga({ key, saga });

  const dispatch = useDispatch();
  const [showAlert, setShowAlert] = useState(false);
  const { addSuccess, error } = useSelector(omdbSelector);
  let myAlert = null;

  useEffect(() => {
    if (addSuccess) {
      props.handleClose();
    }
  }, [addSuccess]);

  useEffect(() => {
    if (error) {
      setShowAlert(true);
      dispatch(resetAddSousZone());
    }
  }
  , [error]);

  useEffect(() => {
    if (!props.show) {
      formikRef.current.resetForm();
      if (error) {
        dispatch(resetAddSousZone());
      }
    }
  }, [props.show]);

  if (showAlert) {
    myAlert = (
      <Alert
        className="pb-0"
        variant="danger"
        onClose={() => setShowAlert(false)}
        dismissible
      >
        <p>Erreur lors de l'ajout de la sous-zone !</p>
      </Alert>
    );
  }

  return (
    <div className="p-2 pr-3" style={backgroundStyle}>
      {myAlert}
      <Formik
        initialValues={{
          id: sousZoneToEdit ? sousZoneToEdit.id : '',
          name: sousZoneToEdit ? sousZoneToEdit.name : '',
          nameAr: sousZoneToEdit ? sousZoneToEdit.nameAr : '',
          detail: sousZoneToEdit ? sousZoneToEdit.detail : '',
          zoneId: sousZoneToEdit ? (sousZoneToEdit.zoneId || zoneId) : (zoneId || ''),
        }}
        validationSchema={Yup.object().shape({
          name: Yup.string().required('Le nom est requis'),
          nameAr: Yup.string().required('Le nom en arabe est requis'),
          detail: Yup.string(),
          zoneId: !sousZoneToEdit && !zoneId ? Yup.string().required('La zone est requise') : Yup.string(),
        })}
        innerRef={formikRef}
        validateOnBlur={false}
        validateOnChange={false}
        onSubmit={(values, { setSubmitting }) => {
          const selectedZoneId = values.zoneId || zoneId;
          if (sousZoneToEdit) {
            dispatch(addSousZone(selectedZoneId, values));
          } else {
            dispatch(addSousZone(selectedZoneId, values));
          }
          setSubmitting(false);
        }}
      >
        {formikProps => (
          <Form>
            <div>
              <div className={`pb-4 pt-2  ${style.miniForm}`}>
                <div className="justify-content-md-around px-3 mb-0">
                  <div className="form-row">
                    {/* Zone Selection - show when adding new sous-zone without specific zoneId OR when editing */}
                    {(!sousZoneToEdit && !zoneId) || sousZoneToEdit ? (
                      <div className="form-group col-md-12">
                        <div style={sousZoneToEdit ? { opacity: 0.6 } : {}}>
                          <CustomSelect
                            label="Zone"
                            isRequired
                            name="zoneId"
                            isMeta
                            disabled={sousZoneToEdit || isRead} // Disable when editing or in read mode
                            formProps={formikProps}
                          >
                          <option value="">-- Sélectionnez une zone --</option>
                          {availableZones && availableZones.map(zone => (
                            <option key={zone.id} value={zone.id}>
                              {zone.code} - {zone.name}
                            </option>
                          ))}
                          </CustomSelect>
                        </div>
                      </div>
                    ) : null}

                    <div className="form-group col-md-6">
                      <CustomTextInput
                        label="Nom"
                        placeholder="Nom"
                        isRequired
                        name="name"
                        disabled={isRead}
                        type="text"
                        formProps={formikProps}
                        onBlur={() => {
                          formikProps.setFieldTouched('name');
                          formikProps.validateField('name');
                        }}
                      />
                    </div>
                    <div className="form-group col-md-6">
                      <CustomTextInputAr
                        label="الاسم"
                        placeholder="الاسم"
                        isRequired
                        name="nameAr"
                        disabled={isRead}
                        type="text"
                        formProps={formikProps}
                        onBlur={() => {
                          formikProps.setFieldTouched('nameAr');
                          formikProps.validateField('nameAr');
                        }}
                      />
                    </div>

                    <div className="form-group col-md-12">
                      <CustomTextArea
                        label="Détail"
                        placeholder="Détail"
                        name="detail"
                        disabled={isRead}
                        type="text"
                        formProps={formikProps}
                        onBlur={() => {
                          formikProps.setFieldTouched('detail');
                          formikProps.validateField('detail');
                        }}
                      />
                    </div>
                  </div>
                </div>
              </div>

              <div className="d-flex justify-content-end pl-1 mt-4">
                <button
                  type="button"
                  className={`mr-2 px-3 btn-style outlined`}
                  onClick={handleClose}
                >
                  Annuler
                </button>
                {!isRead && (
                  <button
                    type="submit"
                    className={`ml-2 px-4 btn-style primary`}
                    onClick={e => {
                      e.preventDefault();
                      if (!formikProps.isSubmitting) {
                        formikProps.handleSubmit();
                      }
                    }}
                  >
                    {sousZoneToEdit ? 'Modifier' : 'Ajouter'}
                  </button>
                )}
              </div>
            </div>
          </Form>
        )}
      </Formik>
    </div>
  );
}
