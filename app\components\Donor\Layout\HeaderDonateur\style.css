#navBar{
   
    display: flex;
    justify-content: space-around;
    background-color: white;
    border: 2px solid white ;
    border-radius: 10px;
    height: 60px;
    padding: 5px;

}

.main_nav ul {
    padding: 0;
    margin: 0;
    list-style: none;  
}

.main_nav ul li{
    display: inline-block;
    margin-left: 25px;   
}

#navBar p{
    display: flex;
    align-items: center;
    text-align: center;
    font-weight: 700;
    margin: 0 8px;
    line-height: 1.2em;
}

#navBar p a{
    color: #292929;
    text-decoration-line: none;
    padding: 0;
    transition: all 0.3s ease-in-out;
}

#navBar p a.selected{
    background-color: #4F89D7;
    border-radius: 12px;
    padding: 6px 15px;
    color: white;
}