import {
  ADD_TAG,
  ADD_TAG_SUCCESS,
  ADD_TAG_ERROR,
  LOAD_TAGS,
  LOAD_TAG_LIST,
  LOAD_TAGS_SUCCESS,
  LOAD_TAGS_ERROR,
  LOAD_TAG_LIST_SUCCESS,
  LOAD_TAGS_BY_TYPE,
  LOAD_TAGS_BY_TYPE_SUCCESS,
  LOAD_TAGS_BY_TYPE_ERROR,
  DELETE_TAG,
  DELETE_TAG_SUCCESS,
  DELETE_TAG_ERROR,
  RESET_DELETE_TAG,
  RESET_ERROR,
  LOAD_TYPE_TAGS,
  LOAD_TYPE_TAGS_SUCCESS,
  LOAD_TYPE_TAGS_ERROR,
} from './constants';

export function addTagRequest(tagData){
  return{
  type: ADD_TAG,
  tagData,
};};


export function addTagSuccess(tag) {
  return {
    type: ADD_TAG_SUCCESS,
    tag,
  };
};

export function addTagError(error) {
  return{
  type: ADD_TAG_ERROR,
  error,
};};

export function loadTags(page) {
  console.log('loadTags', page);
  return {
    type: LOAD_TAGS,
    page,
  };
}
export function getTags(tagList) {
  console.log('loadTags', tagList);
  return {
    type: LOAD_TAG_LIST,
    tagList,
  };
}

export function tagsLoaded(tags) {
  return {
    type: LOAD_TAGS_SUCCESS,
    tags,
  };
}
export function tagListLoaded(tagList) {
  return {
    type: LOAD_TAG_LIST_SUCCESS,
    tagList,
  };
}

export function tagLoadingError(error) {
  return {
    type: LOAD_TAGS_ERROR,
    error,
  };
}

export function deleteTag(tagId) {
  return {
    type: DELETE_TAG,
    tagId,
  };
}

export function tagDeleted(tagId) {
  return {
    type: DELETE_TAG_SUCCESS,
    tagId,
  };
}

export function tagDeleteError(error) {
  return {
    type: DELETE_TAG_ERROR,
    error,
  };
}

export function resetDeleteTag() {
  return {
    type: RESET_DELETE_TAG,
  };
}

export function resetError() {
  return {
    type: RESET_ERROR,
  };
}

export function loadTypeTags() {
  return {
    type: LOAD_TYPE_TAGS,
  };
}

export function typeTagsLoaded(typeTags) {
  return {
    type: LOAD_TYPE_TAGS_SUCCESS,
    typeTags,
  };
}

export function typeTagsLoadingError(error) {
  return {
    type: LOAD_TYPE_TAGS_ERROR,
    error,
  };
}

export function getTagsByType(taggableType) {
  console.log('getTagsByType', taggableType);
  return {
    type: LOAD_TAGS_BY_TYPE,
    taggableType,
  };
}

export function tagsByTypeLoaded(tagList) {
  return {
    type: LOAD_TAGS_BY_TYPE_SUCCESS,
    tagList,
  };
}

export function tagsByTypeLoadingError(error) {
  return {
    type: LOAD_TAGS_BY_TYPE_ERROR,
    error,
  };
}