import React, { useEffect, useState } from 'react';
import { Link, useHistory } from 'react-router-dom';
import moment from 'moment';
import tagStyles from 'Css/tag.css';
import { createStructuredSelector } from 'reselect';
import { useDispatch, useSelector } from 'react-redux';
import { Alert } from 'react-bootstrap';
import {
  makeSelectBeneficiary,
  makeSelectDeleteSuccess,
  makeSelectErrorFromAdd,
  makeSelectLoading,
  makeSelectSuccess,
} from 'containers/Beneficiary/BeneficiaryAdd/selectors';
import DataTable from 'components/Common/DataTable';
import { EDIT_ICON, VIEW_ICON } from 'components/Common/ListIcons/ListIcons';
import {
  deleteBeneficiary,
  loadBeneficiary,
} from 'containers/Beneficiary/BeneficiaryAdd/actions';
import Modal2 from 'components/Common/Modal2';
import btnStyles from 'Css/button.css';
import { useInjectReducer, useInjectSaga } from 'redux-injectors';
import { beneficiarySaga } from 'containers/Beneficiary/BeneficiaryAdd/saga';
import beneficiaryReducer from 'containers/Beneficiary/BeneficiaryAdd/reducer';
import {
  isAssistant,
  isBeneficiary,
  isCandidateWithoutRejected,
  isOldBeneficiary,
} from 'containers/Beneficiary/BeneficiaryProfile/statutUtils';
import { HasSingleAccess, useHasRole, useHasRoleToHide } from 'utils/hasAccess';
import { Tooltip, Typography } from '@mui/material';
import { Statut } from '../../AideComplementaire/enum';
import styled from 'styled-components';

const omdbSelector = createStructuredSelector({
  success: makeSelectSuccess,
  beneficiary: makeSelectBeneficiary,
  successDelete: makeSelectDeleteSuccess,
  loading: makeSelectLoading,
  errorDelete: makeSelectErrorFromAdd,
});

const formatDate = date => moment(date).format('DD/MM/YYYY');

// Styled component for the tags container in the table
const TagsContainer = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  max-width: 200px;
`;

// Styled component for individual tags in the table
const TableTag = styled.div`
  padding: 2px 8px;
  font-size: 11px;
  white-space: nowrap;
  border-radius: 10px;
  background-color: ${props => `#${props.color || 'cccccc'}`};
  color: ${props => {
    const hex = props.color || 'cccccc';
    const r = parseInt(hex.substr(0, 2), 16);
    const g = parseInt(hex.substr(2, 2), 16);
    const b = parseInt(hex.substr(4, 2), 16);
    const brightness = (r * 299 + g * 587 + b * 114) / 1000;
    return brightness > 128 ? '#000000' : '#ffffff';
  }};
`;

export default function ListCandidates(props) {
  let listBeneficiaries = <p>Vide</p>;
  useInjectSaga({ key: 'beneficiaryAdd', saga: beneficiarySaga });
  useInjectReducer({ key: 'beneficiaryAdd', reducer: beneficiaryReducer });
  const dispatch = useDispatch();
  const { successDelete, errorDelete } = useSelector(omdbSelector);

  const history = useHistory();

  const [showAlert, setShowAlert] = useState(false);
  // const [show, setShow] = useState(false);
  const [message] = useState('');
  // const [beneficiaryToEdit, setBeneficiaryToEdit] = useState('');
  const [beneficiaryToDelete] = useState('');
  const [showDeleteModal, setShowDeleteModal] = useState(false);

  const hasRoleAssistant = useHasRole('ASSISTANT');
  const hasRoleKafalat = useHasRole('GESTIONNAIRE_KAFALAT');
  const hasRoleMarketing = useHasRole('GESTIONNAIRE_MARKETING');

  const hasRoleAssistantToHide = useHasRoleToHide('ASSISTANT');

  const handleCloseForDeleteModal = () => setShowDeleteModal(false);

  const handleShow = () => {
    setShow(true);
  };

  useEffect(() => {
    if (successDelete || errorDelete) {
      handleCloseForDeleteModal();
    }
  }, [successDelete, errorDelete]);

  // close the alert after 4 seconds
  useEffect(() => {
    if (showAlert) {
      setTimeout(() => {
        setShowAlert(false);
      }, 4000);
    }
  }, [showAlert]);

  const beneficiaries = props.candidats.content;
  const liste1 = props.candidats;

  const viewHandler = (id, destination, beneficiaryStatutId) => {
    if (destination === 'fiche') {
      history.push(`/beneficiaries/fiche/${id}/info`, {
        params: id,
        isCandidate: isCandidateWithoutRejected(beneficiaryStatutId),
        isOldBeneficiary: isOldBeneficiary(beneficiaryStatutId),
        isBeneficiary: isBeneficiary(beneficiaryStatutId),
        isOldBeneficiaryArchived: beneficiaryStatutId === 9 ? true : false,
        isOldBeneficiaryRejected: beneficiaryStatutId === 7 ? true : false,
        isOldCandidateRejected: beneficiaryStatutId === 5 ? true : false,
      });
    }
  };

  const getTag = beneficiaryStatutId => {
    if (beneficiaryStatutId === 4) {
      return hasRoleAssistant ? tagStyles.tagOrange : tagStyles.tagYellow; // "A compléter par assistant" ou "A compléter"
    }

    if (beneficiaryStatutId === 8) {
      return hasRoleKafalat ? tagStyles.tagOrange : tagStyles.tagYellow; // "A compléter par kafalat" ou "A compléter"
    }

    if (beneficiaryStatutId === 2) {
      if (hasRoleAssistant || hasRoleMarketing) {
        return tagStyles.tagGreenLight; // "En cours de validation"
      } else if (hasRoleKafalat) {
        return tagStyles.tagBlue; // "A validé"
      } else {
        return tagStyles.tagGreenLight; // "Validé par assistant"
      }
    }

    if (beneficiaryStatutId === 3) {
      if (hasRoleKafalat || hasRoleAssistant) {
        return tagStyles.tagGreenLight; // "En cours de validation"
      } else if (hasRoleMarketing) {
        return tagStyles.tagBlue; // "A validé"
      } else {
        return tagStyles.tagGreen; // "Validé par kafalat"
      }
    }

    if (beneficiaryStatutId === 1) {
      return tagStyles.tagGrey; // "Initial"
    }

    return tagStyles.tagGrey; // Par défaut pour "---"
  };

  const getStatutText = beneficiaryStatutId => {
    if (hasRoleAssistant && beneficiaryStatutId === 4) {
      return 'A compléter';
    } else if (beneficiaryStatutId === 4) {
      return 'A compléter par assistant';
    }

    if (hasRoleKafalat && beneficiaryStatutId === 8) {
      return 'A compléter';
    } else if (beneficiaryStatutId === 8) {
      return 'A compléter par kafalat';
    }

    if (
      (hasRoleAssistant && beneficiaryStatutId === 2) ||
      (hasRoleMarketing && beneficiaryStatutId === 2)
    ) {
      return 'En cours de validation';
    } else if (hasRoleKafalat && beneficiaryStatutId === 2) {
      return 'A valider';
    } else if (beneficiaryStatutId === 2) {
      return 'Validé par assistant';
    }

    if (
      (hasRoleKafalat && beneficiaryStatutId === 3) ||
      (hasRoleAssistant && beneficiaryStatutId === 3)
    ) {
      return 'En cours de validation';
    } else if (hasRoleMarketing && beneficiaryStatutId === 3) {
      return 'A valider';
    } else if (beneficiaryStatutId === 3) {
      return 'Validé par kafalat';
    }

    if (beneficiaryStatutId === 1) {
      return 'Initial';
    }
    return '---';
  };

  const count = 0;
  if (props.candidats) {
    listBeneficiaries = beneficiaries
      .filter(beneficiary => beneficiary && !beneficiary.archived) // Filter out undefined and archived elements
      .map(beneficiary => {
        return {
          id: beneficiary.id, // Ensure beneficiary is defined before accessing properties
          code: beneficiary.code,
          name: `${beneficiary.firstName} ${beneficiary.lastName}`,
          arabicName: `${beneficiary.firstNameAr} ${beneficiary.lastNameAr}`,
          birthDate: formatDate(beneficiary.birthDate),
          type: beneficiary.independent ? 'Indépendant' : 'Membre famille',
          phoneNumber:
            beneficiary.independent === false
              ? beneficiary.familyPhoneNumber
                ? beneficiary.familyPhoneNumber
                : '---'
              : beneficiary.phoneNumber
              ? beneficiary.phoneNumber
              : '---',
          city: beneficiary.city ? beneficiary.city : '---',
          zone: beneficiary.zoneName ? beneficiary.zoneName : '---',
          status:
            beneficiary.services && beneficiary.services.length > 0
              ? beneficiary.services[0].status
              : '---',
          service:
            beneficiary.services && beneficiary.services.length > 0
              ? beneficiary.services[0].service
              : '---',
          beneficiaryStatutId: beneficiary.beneficiaryStatutId,
          rqComplete: beneficiary.rqComplete,
          identityCode:
            beneficiary.independent === false
              ? beneficiary.familyIdentityCode
                ? beneficiary.familyIdentityCode
                : '---'
              : beneficiary.identityCode
              ? beneficiary.identityCode
              : '---',
          tags: beneficiary.tags || [],
        };
      });
  }

  const columns = [
    {
      field: 'code',
      headerName: 'Code',
      flex: 1,
      headerAlign: 'left',
      align: 'left',
    },
    {
      field: 'name',
      headerName: 'Nom Complet',
      flex: 1,
      headerAlign: 'left',
      align: 'left',
    },
    {
      field: 'arabicName',
      headerName: 'الاسم الكامل',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
    {
      field: 'birthDate',
      headerName: 'Date de naissance',
      flex: 1,
      headerAlign: 'left',
      align: 'left',
    },
    {
      field: 'type',
      headerName: 'Type',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
    {
      field: 'identityCode',
      headerName: "N° d'identité",
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
    {
      field: 'phoneNumber',
      headerName: 'Téléphone',
      flex: 1,
      headerAlign: 'left',
      align: 'left',
    },
    {
      field: 'zone',
      headerName: 'Zone',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },

    {
      field: 'beneficiaryStatutId',
      headerName: 'Statut',
      flex: 1.5,
      headerAlign: 'center',
      align: 'center',
      renderCell: params => {
        const shouldShowTooltip =
          params.row.beneficiaryStatutId === 4 ||
          params.row.beneficiaryStatutId === 8;
        const tooltipText =
          shouldShowTooltip && params.row.rqComplete
            ? params.row.rqComplete
            : '';

        return (
          <Tooltip
            title={
              tooltipText ? (
                <Typography
                  sx={{
                    fontSize: '1rem',
                    color: '#fff',
                    padding: '8px',
                  }}
                >
                  {tooltipText}
                </Typography>
              ) : (
                ''
              )
            }
            arrow
          >
            <div className={getTag(params.row.beneficiaryStatutId)}>
              {getStatutText(params.row.beneficiaryStatutId)}
            </div>
          </Tooltip>
        );
      },
    },
    {
      field: 'tags',
      headerName: 'Tags',
      flex: 1.5,
      headerAlign: 'center',
      align: 'center',
      renderCell: params => {
        const tags = params.row.tags || [];
        return (
          <TagsContainer>
            {tags && tags.length > 0 ? (
              tags.map(tag => (
                <TableTag key={tag.id} color={tag.color || 'cccccc'}>
                  {tag.name}
                </TableTag>
              ))
            ) : (
              <span style={{ color: '#999', fontSize: '12px' }}>Aucun tag</span>
            )}
          </TagsContainer>
        );
      },
    },
  ];

  columns.push({
    field: 'actions',
    type: 'actions',
    headerName: 'Actions',
    flex: 1,
    headerAlign: 'center',
    align: 'center',
    renderCell: params => (
      <div>
        <input
          type="image"
          onClick={() =>
            viewHandler(params.row.id, 'fiche', params.row.beneficiaryStatutId)
          }
          className="p-2"
          src={VIEW_ICON}
          width="40px"
          height="40px"
          title="consulter"
        />

        {!props.profile ? (
          <HasSingleAccess feature="GERER_CANDIDATS" privilege="WRITE">
            <Link
              to={{
                pathname: `/beneficiaries/edit/${params.row.id}`,
                state: {
                  edit: 'personal_info',
                  candidate: true,
                },
              }}
            >
              <input
                type="image"
                src={EDIT_ICON}
                className="p-2"
                width="40px"
                height="40px"
                title={
                  hasRoleAssistantToHide &&
                  !isAssistant(params.row.beneficiaryStatutId)
                    ? 'Modification désactivée (en cours de validation)'
                    : 'Modifier'
                }
                onClick={() => {
                  dispatch(loadBeneficiary(params.row.id));
                }}
                disabled={
                  hasRoleAssistantToHide &&
                  !isAssistant(params.row.beneficiaryStatutId)
                }
                style={{
                  opacity:
                    hasRoleAssistantToHide &&
                    !isAssistant(params.row.beneficiaryStatutId)
                      ? 0.5
                      : 1,
                  cursor:
                    hasRoleAssistantToHide &&
                    !isAssistant(params.row.beneficiaryStatutId)
                      ? 'not-allowed'
                      : 'pointer',
                }}
              />
            </Link>
          </HasSingleAccess>
        ) : (
          <HasSingleAccess feature="GERER_CANDIDATS" privilege="WRITE">
            <input
              type="image"
              src={EDIT_ICON}
              className="p-2"
              width="40px"
              height="40px"
              title={
                hasRoleAssistantToHide &&
                !isAssistant(params.row.beneficiaryStatutId)
                  ? 'Modification désactivée (en cours de validation)'
                  : 'Modifier'
              }
              onClick={() => {
                dispatch(loadBeneficiary(params.row.id));
              }}
              disabled={
                hasRoleAssistantToHide &&
                !isAssistant(params.row.beneficiaryStatutId)
              }
              style={{
                opacity:
                  hasRoleAssistantToHide &&
                  !isAssistant(params.row.beneficiaryStatutId)
                    ? 0.5
                    : 1,
                cursor:
                  hasRoleAssistantToHide &&
                  !isAssistant(params.row.beneficiaryStatutId)
                    ? 'not-allowed'
                    : 'pointer',
              }}
            />
          </HasSingleAccess>
        )}
        {/* <AccessControl module="BENEFICIARY" functionality="DELETE">
          <input
            type="image"
            src={DELETE_ICON}
            className="p-2 "
            width="40px"
            height="40px"
            title="Supprimer"
            onClick={() => {
              setBeneficiaryToDelete(params.row);
              setShowDeleteModal(true);
            }}
          />
        </AccessControl>
        */}
      </div>
    ),
  });

  return (
    <div>
      {showAlert ? (
        <Alert
          className="alert-style"
          variant="success"
          onClose={() => setShowAlert(false)}
          dismissible
        >
          <p>{message}</p>
        </Alert>
      ) : null}

      <Modal2
        centered
        className="mt-5"
        title="Confirmation de suppression"
        show={showDeleteModal}
        handleClose={handleCloseForDeleteModal}
      >
        <p className="mt-1 mb-5">
          Êtes-vous sûr de vouloir supprimer ce candidat?
        </p>
        <div className="d-flex justify-content-end px-3 my-1">
          <button
            type="button"
            className={`mx-2 btn-style outlined`}
            onClick={handleCloseForDeleteModal}
          >
            Annuler
          </button>
          <button
            type="submit"
            className={`mx-2 btn-style primary`}
            onClick={() => {
              dispatch(deleteBeneficiary({ beneficiary: beneficiaryToDelete }));
            }}
          >
            Supprimer
          </button>
        </div>
      </Modal2>
      <DataTable
        rows={listBeneficiaries}
        columns={columns}
        const
        fileName={`Liste des Beneficiaires , ${new Date().toLocaleString()}`}
        totalElements={liste1.totalElements}
        numberOfElements={liste1.numberOfElements}
        pageable={liste1.pageable}
      />
    </div>
  );
}
