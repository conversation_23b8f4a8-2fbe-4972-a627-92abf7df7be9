import React, { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import {
  useLocation,
  Switch,
  Route,
  useParams,
  useHistory,
} from 'react-router-dom';
import { useSelector, useDispatch } from 'react-redux';
import { useInjectReducer, useInjectSaga } from 'redux-injectors';
import { createStructuredSelector } from 'reselect';

import SousZones from 'components/espaceAssistant/ficheAssistant/SousZones';
import { Modal, Alert } from 'react-bootstrap';
import { CircularProgress } from '@material-ui/core';

import HeaderEspaceAssistant from 'components/espaceAssistant/Layout';
import AssistantSideBar from 'components/espaceAssistant/ficheAssistant/SideBar';
import MesRapport from 'components/espaceAssistant/ficheAssistant/MesRapports';
import {
  loadConnectedAssistant,
  loadZonesWithSousZones,
} from './actions';
import {
  makeSelectConnectedAssistant,
  makeSelectSousZones,
  makeSelectLoading,
  makeSelectZonesWithSousZones,
  makeSelectZonesLoading,
  makeSelectZonesError,
} from './selectors';
import ficheAssistantReducer from './reducer';
import saga from './saga';

const key = 'assistantFiche';

const omdbSelector = createStructuredSelector({
  connectedAssistant: makeSelectConnectedAssistant(),
  loading: makeSelectLoading(),
  zonesWithSousZones: makeSelectZonesWithSousZones(),
  zonesLoading: makeSelectZonesLoading(),
  zonesError: makeSelectZonesError(),
});

export function FicheAssistant(props) {
  useInjectReducer({ key, reducer: ficheAssistantReducer });
  useInjectSaga({ key, saga });
  const { connectedAssistant, loading, zonesWithSousZones, zonesLoading, zonesError } = useSelector(omdbSelector);
  const history = useHistory();
  const location = useLocation();
  const [showAlert, setShowAlert] = useState(false);
  const [successMessage, setSuccessMessage] = useState('');
  const [zoneId, setZoneId] = useState('');
  const [assistantId, setAssistantId] = useState('');
  const params = useParams();
  const dispatch = useDispatch();

  useEffect(() => {
    dispatch(loadConnectedAssistant(params.id));
  }, []);

  useEffect(() => {
    if (connectedAssistant && connectedAssistant.zoneId) {
      setZoneId(connectedAssistant.zoneId);
      setAssistantId(connectedAssistant.id);
      dispatch(loadZonesWithSousZones(connectedAssistant.id));
    }
  }, [connectedAssistant]);

  const editHandler = id => {
    history.push({
      pathname: `/assistants/editAssistant/${id}`,
      state: { formEspaceAssistant: true },
    });
  };


  useEffect(() => {
    if (location.state && location.state.successMessage) {
      setShowAlert(true);
      setSuccessMessage(location.state.successMessage);
    }
  }, [location.state]);
  const handleCloseSuccessMessage = () => {
    setShowAlert(false);
    history.replace({ ...props.location, state: null });
  };

  // eslint-disable-next-line consistent-return
  useEffect(() => {
    if (showAlert) {
      const timer = setTimeout(() => {
        handleCloseSuccessMessage();
      }, 4000);
      return () => clearTimeout(timer);
    }
  }, [showAlert]);

  return (
    <div>
      <Modal show={loading || zonesLoading} centered contentClassName="bg-transparent border-0">
        <div className="d-flex justify-content-center align-items-center">
          <CircularProgress style={{ width: '100px', height: '100px' }} />
        </div>
      </Modal>
      <div className="row">
     
        <div className="col-3 pr-0 pl-2">
          <AssistantSideBar data={connectedAssistant || {}} />
          <div className="d-flex flex-column align-items-center">
            <button
              type="button"
              onClick={() => editHandler(connectedAssistant.id)}
              className="btn-style primary"
            >
              Modifier mes informations personnelles
            </button>
          </div>
        </div>
        <div className="col-9 pr-2">
          {showAlert && (
            <Alert
              className="alert-style"
              variant="success"
              onClose={() => handleCloseSuccessMessage()}
              dismissible
            >
              {successMessage}
            </Alert>
          )} 
              <Route exact path="/espace-assistant/fiche/:id">
                <SousZones
                  zones={zonesWithSousZones}
                  loading={zonesLoading}
                  error={zonesError}
                  assistantId={params.id}
                />
              </Route> 
        </div>
      </div>
    </div>
  )
}
FicheAssistant.propTypes = {
  location: PropTypes.string,
};
export default FicheAssistant;
