import React from 'react';
import { makeStyles } from '@material-ui/core';
import Logo from '../../../images/almobadara-logo.png';

const useStyles = makeStyles(theme => ({
  footer: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: '10px 0',
    borderTop: '1px solid #e0e0e0',
    marginTop: '20px',
  },
  logo: {
    width: '60px',
  },
  text: {
    fontSize: '14px',
    color: '#333',
  },
}));

const PrintFooter = () => {
  const classes = useStyles();

  return (
    <div className={classes.footer}>
      <img src={Logo} alt="Logo" className={classes.logo} />
      <span className={classes.text}>Association Al Mobadara</span>
    </div>
  );
};

export default PrintFooter;
