import React from 'react';
import { NavLink, useParams } from 'react-router-dom';

import styles from '../../../../Css/profileHeader.css';

const HeaderServices = props => {
  const params = useParams();
  const id = params.id;

  return (
    <div className={styles.navBar}>
      <p>
        <p>
          <NavLink
            exact
            to={'/services/fiche/' + params.id + '/info'}
            activeClassName={styles.selected}
          >
            Informations générales
          </NavLink>
        </p>
      </p>
      <p>
        <NavLink
          exact
          to={'/services/fiche/' + params.id + '/journal'}
          activeClassName={styles.selected}
          serviceId={id}
        >
          Journal
        </NavLink>
      </p>

      {/*
      <p>
        <NavLink
          exact
          to={'/aide-complementaire/fiche/' + params.id + '/donateurs'}
          activeClassName={styles.selected}
        >
          Donateurs
        </NavLink>
      </p>
      <p>
        <NavLink
          exact
          to={'/aide-complementaire/fiche/' + params.id + '/beneficiaries'}
          activeClassName={styles.selected}
        >
          Bénéficiaires
        </NavLink>
      </p>
      */}
      {/*<p>
        <NavLink
          exact
          to={'/aide-complementaire/fiche/' + params.id + '/documents'}
          activeClassName={styles.selected}
        >
          Documents
        </NavLink>
      </p>

      <p>
        <NavLink
          exact
          to={'/aide-complementaire/fiche/' + params.id + '/notes'}
          activeClassName={styles.selected}
        >
          Notes
        </NavLink>
      </p>
      <p>
        <NavLink
          to={`/aide-complementaire/fiche/${id}/action`}
          activeClassName={styles.selected}
        >
          Actions
        </NavLink>
      </p>
      */}
    </div>
  );
};

export default HeaderServices;
