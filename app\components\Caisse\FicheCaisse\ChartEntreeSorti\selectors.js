import { createSelector } from 'reselect';
import { initialState } from './reducer';

const selectOmdb = state => state.caisseChart || initialState;

const makeSelectCaisseChartDetails = createSelector(
  selectOmdb,
  omdbState => omdbState.caisseChartDetails,
);

const makeSelectLoading = createSelector(
  selectOmdb,
  omdbState => omdbState.loading,
);

const makeSelectError = createSelector(
  selectOmdb,
  omdbState => omdbState.error,
);

export {
  selectOmdb,
  makeSelectLoading,
  makeSelectError,
  makeSelectCaisseChartDetails,
};
