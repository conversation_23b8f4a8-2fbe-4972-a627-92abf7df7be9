import { call, put, takeLatest } from 'redux-saga/effects';
import request from 'utils/request';
import { addTagSuccess, addTagError, tagListLoaded, tagsLoaded, tagLoadingError, tagDeleted, tagDeleteError, typeTagsLoaded, typeTagsLoadingError, tagsByTypeLoaded, tagsByTypeLoadingError } from './actions';
import { ADD_TAG, LOAD_TAGS, DELETE_TAG, LOAD_TAG_LIST, LOAD_TYPE_TAGS, LOAD_TAGS_BY_TYPE } from './constants';
import { serialize } from '../Common/FormDataConverter';

export function* addTag({ tagData }) {
    const url = `/tag`;
    try {
      let formData = new FormData();

      formData = serialize(tagData, { indices: true, nullsAsUndefineds: false });
      const formData2 = new FormData();
      for (const par of formData.entries()) {
        if (par[1]) {
            formData2.append(par[0], par[1]);
        }
      }
      const { data } = yield call(request.post, url, formData2);
      yield put(addTagSuccess(data));
    } catch (error) {
      if(error.response && error.response.data){
         yield put(addTagError(error.response.data));
      }else{
        yield put(addTagError(error));
      }

    }
  }
  export function* loadTagPage({ page }) {
    let requestURL = `/tag?page=${page}`;
    console.log('requestURL', requestURL);
    try {
      const response = yield call(request.get, requestURL);
      console.log('Response data:', response.data);
      yield put(tagsLoaded(response.data.tags || response.data));
    } catch (error) {
      console.error('Error loading tags:', error);
      yield put(tagLoadingError(error));
    }
  }

  export function* loadTagList() {
    let requestURL = `/tag/list`;
    console.log('requestURL', requestURL);
    try {
      const response = yield call(request.get, requestURL);
      console.log('Response data:', response.data);
      yield put(tagListLoaded(response.data.tags || response.data));
    } catch (error) {
      console.error('Error loading tags:', error);
      yield put(tagLoadingError(error));
    }
  }

  export function* deleteTag({ tagId }) {
    const requestURL = `/tag/${tagId}`;

    try {
      yield call(request.delete, requestURL);
      yield put(tagDeleted(tagId));
    } catch (error) {
      const errorMsg = error.response ? error.response.data : error.message;
      yield put(tagDeleteError(errorMsg.message));
    }
  }



  export function* loadTypeTags() {
    const requestURL = `/type-tag`;
    try {
      const response = yield call(request.get, requestURL);
      console.log('Type tags response data:', response.data);
      yield put(typeTagsLoaded(response.data));
    } catch (error) {
      console.error('Error loading type tags:', error);
      yield put(typeTagsLoadingError(error));
    }
  }

  export function* loadTagsByType({ taggableType }) {
    const requestURL = `/tag/${taggableType}/tags`;
    console.log('Loading tags by type URL:', requestURL);
    try {
      const response = yield call(request.get, requestURL);
      console.log('Tags by type response data:', response.data);
      yield put(tagsByTypeLoaded(response.data));
    } catch (error) {
      console.error('Error loading tags by type:', error);
      yield put(tagsByTypeLoadingError(error));
    }
  }

export default function* tagSaga() {
    yield takeLatest(ADD_TAG, addTag);
    yield takeLatest(LOAD_TAGS, loadTagPage);
    yield takeLatest(LOAD_TAG_LIST, loadTagList);
    yield takeLatest(DELETE_TAG, deleteTag);
    yield takeLatest(LOAD_TYPE_TAGS, loadTypeTags);
    yield takeLatest(LOAD_TAGS_BY_TYPE, loadTagsByType);
  }