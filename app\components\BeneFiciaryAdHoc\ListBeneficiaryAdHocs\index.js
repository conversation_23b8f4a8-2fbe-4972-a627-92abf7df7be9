import React, { useCallback, useEffect, useState } from 'react';
import { Link, useHistory } from 'react-router-dom';
import PropTypes from 'prop-types';
import { useDispatch, useSelector } from 'react-redux';
import AccessControl from 'utils/AccessControl';
import {
  deleteDonor,
  deleteDonorReset,
} from 'containers/Donor/DonorProfile/actions';
import Modal2 from 'components/Common/Modal2';
import { useInjectReducer, useInjectSaga } from 'redux-injectors';
import donorReducer from 'containers/Donor/DonorProfile/reducer';
import omdbSaga from 'containers/Donor/DonorProfile/saga';
import { createStructuredSelector } from 'reselect';
import {
  makeSelectDeleteError,
  makeSelectDeleteSuccess,
} from 'containers/Donor/DonorProfile/selectors';
import { removeDonorGlobal } from 'containers/Donor/Donors/actions';
import btnStyles from '../../../Css/button.css';
import DataTable from '../../Common/DataTable';
import styles from '../../../Css/tag.css';

const viewIcon = require('images/icons/eye.svg');
const editIcon = require('images/icons/edit.svg');

const stateSelector = createStructuredSelector({
  successDelete: makeSelectDeleteSuccess,
  errorDelete: makeSelectDeleteError,
});

export const BeneficiaryAdHoc = ({ beneficiaryAdHoc, viewHandler }) => {
  const [actualEmail, setActualEmail] = useState(donor.email);
  const [actualPhoneNumber, setActualPhoneNumber] = useState(donor.phoneNumber);
  const [formLink, setFormLink] = useState(
    '/beneficiaries/edit/ad-hoc/personne/',
  );
  const [name, setName] = useState('');
  const [ArabicName, setArabicName] = useState('');

  const dispatch = useDispatch();

  return (
    <tr key={beneficiaryAdHoc.id}>
      <td style={{ textAlign: 'center' }}>{beneficiaryAdHoc.code}</td>
      <td>{name}</td>
      <td>{ArabicName}</td>
      <td>{beneficiaryAdHoc.type} </td>
      <td>
        {beneficiaryAdHoc.city == null ? (
          <span style={{ textAlign: 'center' }}>-</span>
        ) : (
          beneficiaryAdHoc.city.name
        )}
      </td>
      <td>{actualPhoneNumber}</td>
      <td>{actualEmail}</td>
      <td>
        {beneficiaryAdHoc.status == null ? (
          <span>-</span>
        ) : (
          <span
            className={
              beneficiaryAdHoc.status == 'actif'
                ? styles.tagGreen
                : styles.tagRed
            }
          >
            {beneficiaryAdHoc.status}
          </span>
        )}
      </td>
      <td className="p-0">
        <input
          type="image"
          onClick={() =>
            viewHandler(
              beneficiaryAdHoc.id,
              beneficiaryAdHoc.beneficiaryStatut === 'personne',
            )
          }
          className="p-2"
          src={viewIcon}
          width="40px"
          height="40px"
        />
        <AccessControl module="DONOR" functionality="UPDATE">
          <>
            <Link
              to={{
                pathname: `${formLink}${beneficiaryAdHoc.id}`,
              }}
            >
              {' '}
              <input
                type="image"
                src={editIcon}
                className="p-2"
                width="40px"
                height="40px"
              />
            </Link>
          </>
        </AccessControl>
      </td>
    </tr>
  );
};

BeneficiaryAdHoc.propTypes = {
  beneficiaryAdHoc: PropTypes.object.isRequired,
  viewHandler: PropTypes.func.isRequired,
};

export default function BeneficiaryAdHocForm({
  beneficiaryAdHocPersonnes,
  beneficiaryAdHocPersonnesGlobal,
  onSuccessDelete = () => {},
  setShowErrorAlert = () => {},
  setShowSuccessAlert = () => {},
}) {
  const history = useHistory();
  const viewHandler = (a, isPerson) => {
    history.push(`/beneficiaries/ad-hoc/fiche/${a}/info`, {
      params: a,
      isPerson,
    });
  };

  const dispatch = useDispatch();

  useInjectReducer({ key: 'donorProfil', reducer: donorReducer });
  useInjectSaga({ key: 'donorProfil', saga: omdbSaga });
  const { successDelete, errorDelete } = useSelector(stateSelector);

  const [donorToDelete, setDonorToDelete] = useState('');
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [showAlert, setShowAlert] = useState(false);
  const handleCloseForDeleteModal = () => setShowDeleteModal(false);

  const displaySuccessMessage = () => {
    setShowSuccessAlert(true);
  };

  const failedToDelete = useCallback(() => {
    if (errorDelete) {
      setShowAlert(true);
      setShowErrorAlert(errorDelete);
    }
  }, [errorDelete]);

  useEffect(() => {
    errorDelete && failedToDelete();
  }, [errorDelete]);

  useEffect(() => {
    if (successDelete) {
      displaySuccessMessage();
      dispatch(removeDonorGlobal(donorToDelete));
      dispatch(deleteDonorReset());
      onSuccessDelete({}, true);
    }
  }, [successDelete]);

  const [formLink, setFormLink] = useState('/beneficiaries-ad-hoc/edit/');
  const [rows, setRows] = useState([]);
  const populateRows = () => {
    setRows(() =>
      beneficiaryAdHocPersonnes.map(beneAdHoc => ({
        id: beneAdHoc.id,
        code: beneAdHoc.code ? beneAdHoc.code : '---',
        name: beneAdHoc.fullNameContact
          ? beneAdHoc.fullNameContact
          : `${beneAdHoc.firstName} ${beneAdHoc.lastName}`,
        type: beneAdHoc.type ? beneAdHoc.type : '---',
        numberOfMembers: beneAdHoc.numberOfMembers
          ? beneAdHoc.numberOfMembers
          : '---',
        beneficiaryStatut: beneAdHoc.beneficiaryStatut
          ? beneAdHoc.beneficiaryStatut === 'beneficiary_ad_hoc_individual'
            ? 'personne'
            : 'groupe'
          : '---',
        status: beneAdHoc.status ? beneAdHoc.status : '---',
      })),
    );
  };

  useEffect(() => {
    populateRows();
  }, []);

  const columns = [
    {
      field: 'code',
      headerName: 'Code',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
    {
      field: 'name',
      headerName: 'Nom Complet',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
    {
      field: 'beneficiaryStatut',
      headerName: 'Type',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
    {
      field: 'numberOfMembers',
      headerName: 'Nombre des membres',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
    {
      field: 'status',
      headerName: 'Statut',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
      renderCell: params => (
        <span
          className={
            params.row.status === 'actif' ? styles.tagGreen : styles.tagRed
          }
        >
          {params.row.status}
        </span>
      ),
    },
    {
      field: 'actions',
      type: 'actions',
      headerName: 'Actions',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
      renderCell: params => (
        <>
          <input
            type="image"
            onClick={() =>
              viewHandler(
                params.row.id,
                params.row.beneficiaryStatut === 'personne',
              )
            }
            className="p-2"
            src={viewIcon}
            width="40px"
            height="40px"
            title="consulter"
          />
          <AccessControl module="DONOR" functionality="UPDATE">
            <Link
              to={`/beneficiaries/edit/ad-hoc/${
                params.row.beneficiaryStatut === 'groupe'
                  ? 'groupe'
                  : 'personne'
              }/${params.row.id}`}
            >
              {' '}
              <input
                type="image"
                src={editIcon}
                className="p-2"
                width="40px"
                height="40px"
                title="modifier"
              />
            </Link>
          </AccessControl>
          {/* <AccessControl module="DONOR" functionality="DELETE">
            <input
              type="image"
              src={DELETE_ICON}
              className="p-2 "
              width="40px"
              height="40px"
              title="Supprimer"
              onClick={() => {
                setDonorToDelete(params.row);
                setShowDeleteModal(true);
              }}
            />
          </AccessControl> */}
        </>
      ),
    },
  ];

  return (
    <div>
      <Modal2
        centered
        className="mt-5"
        title="Confirmation de suppression"
        show={showDeleteModal}
        handleClose={handleCloseForDeleteModal}
      >
        <p className="mt-1 mb-5">
          Êtes-vous sûr de vouloir supprimer ce bénéficiaire?
        </p>
        <div className="d-flex justify-content-end px-3 my-1">
          <button
            type="button"
            className={`mx-2 btn-style outlined`}
            onClick={handleCloseForDeleteModal}
          >
            Annuler
          </button>
          <button
            type="submit"
            className={`mx-2 btn-style primary`}
            onClick={() => {
              dispatch(deleteDonor(donorToDelete));
              handleCloseForDeleteModal();
            }}
          >
            Supprimer
          </button>
        </div>
      </Modal2>
      <DataTable
        rows={rows}
        columns={columns}
        fileName={`Liste des Beneficiary ad-Hoc , ${new Date().toLocaleString()}`}
        totalElements={beneficiaryAdHocPersonnesGlobal.totalElements}
        numberOfElements={beneficiaryAdHocPersonnesGlobal.numberOfElements}
        pageable={beneficiaryAdHocPersonnesGlobal.pageable}
      />
    </div>
  );
}
