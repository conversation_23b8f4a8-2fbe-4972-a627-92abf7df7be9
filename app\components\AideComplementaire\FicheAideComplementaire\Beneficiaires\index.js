import React, { useEffect, useState } from 'react';
import stylesList from 'Css/profileList.css';
import CustomPagination from 'components/Common/CustomPagination';

import { useDispatch, useSelector } from 'react-redux';
import {
  Alert,
  Button,
  Form,
  InputGroup,
  ListGroup,
  Modal,
  OverlayTrigger,
  Spinner,
  Tooltip,
} from 'react-bootstrap';
import BeneficiariesWithOutDonors from './BeneficiariesWithOutDonors';
import {
  loadBeneficiariesForAideComplementaire,
  processDonorsAndBeneficiariesRequest,
  processDonorsAndBeneficiariesReset,
  removeBeneficiaryReset,
  updateMontantBeneficiaryReset,
  updateMontantForGroupReset,
  updateStatutValidationBeneficiaryReset,
} from '../../../../containers/AideComplementaire/FicheAideComplementaire/actions';
import { useHistory, useLocation } from 'react-router-dom';
import FilterListIcon from '@mui/icons-material/FilterList';
import {
  beneficiaryAdHocStatuses,
  beneficiaryEnAttenteStatuses,
  beneficiaryOnlyStatuses,
} from '../../../../containers/Beneficiary/BeneficiaryProfile/statutUtils';
import { createStructuredSelector } from 'reselect';
import { makeSelectLoading2 } from '../../../../containers/AideComplementaire/FicheAideComplementaire/selectors';
import { useInjectReducer, useInjectSaga } from 'redux-injectors';
import aideComplementaireReducer from '../../../../containers/AideComplementaire/FicheAideComplementaire/reducer';
import detailAideComplementaireSaga from '../../../../containers/AideComplementaire/FicheAideComplementaire/saga';

const key = 'aideComplementaireFiche';
const omdbSelector = createStructuredSelector({
  loading2: makeSelectLoading2,
});
export default function Beneficiaires(props) {
  const dispatch = useDispatch();
  useInjectReducer({ key, reducer: aideComplementaireReducer });
  useInjectSaga({ key, saga: detailAideComplementaireSaga });
  const { loading2 } = useSelector(omdbSelector);

  const [message, setMessage] = useState('');
  const [showAlert, setShowAlert] = useState(false);
  const [showModal, setShowModal] = useState(false);
  const [filterText, setFilterText] = useState('');
  const [beneficiaryStatut, setBeneficiaryStatut] = useState('');
  const [independent, setIndependent] = useState('');
  const [selectedBeneficiaries, setSelectedBeneficiaries] = useState([]);
  const [showSuccessMessage, setShowSuccessMessage] = useState(false);

  // State pour le filtre avancé
  const [showAdvancedFilter, setShowAdvancedFilter] = useState(false);
  const [filters, setFilters] = useState({
    type: '',
    beneficiaryStatut: '',
    text: '',
    withMemberParticipant: '',
    withDonor: '',
    withDonorParticipant: '',
    fromPreviousCampaign: '',
  });

  const history = useHistory();
  const location = useLocation();

  useEffect(() => {
    if (location.state === 'proceesBeneficiariesSuccess') {
      setShowSuccessMessage(true);
      setMessage('Bénéficiaires ajoutée avec succès !');
      dispatch(processDonorsAndBeneficiariesReset());
      history.replace({ ...location, state: null });
    }
  }, [location.state]);

  useEffect(() => {
    if (showSuccessMessage) {
      const timer = setTimeout(() => {
        handleCloseSuccessMessage();
      }, 4000);
      return () => clearTimeout(timer);
    }
  }, [showSuccessMessage]);

  useEffect(() => {
    if (location.state === 'updateStatutValidationBeneficiarySuccess') {
      //setShowSuccessMessage(true);
      dispatch(updateStatutValidationBeneficiaryReset());
      history.replace({ ...location, state: null });
    }
  }, [location.state]);

  useEffect(() => {
    if (location.state === 'updateMontantGroupSuccess') {
      setShowSuccessMessage(true);
      setMessage('Montant affecté au groupe est modifié avec succès !');
      dispatch(updateMontantForGroupReset());
      history.replace({ ...location, state: null });
    }
  }, [location.state]);

  useEffect(() => {
    if (location.state === 'removeBeneficiarySuccess') {
      setShowSuccessMessage(true);
      setMessage('Bénéficiaire supprimé avec succès !');
      dispatch(removeBeneficiaryReset());
      history.replace({ ...location, state: null });
    }
  }, [location.state]);

  useEffect(() => {
    if (location.state === 'updateMontantBeneficiarySuccess') {
      setShowSuccessMessage(true);
      setMessage('Montant du bénéficiaire est modifié avec succès !');
      dispatch(updateMontantBeneficiaryReset());
      history.replace({ ...location, state: null });
    }
  }, [location.state]);

  const handleCloseSuccessMessage = () => {
    setShowSuccessMessage(false);
    history.replace({ ...location, state: null });
  };
  const [activePage, setActivePage] = useState(0)
  let successAlert = null;
  const aideComplementaire = props.data;
  const beneficiariesForAideComplementaire =
    props.beneficiariesForAideComplementaire;
  let beneficiaries = null;
  const handlePageChange = pageNumber => {
    const nextPage = pageNumber - 1;
    if (nextPage >= 0 && nextPage < beneficiariesForAideComplementaire.totalPages) {
      setActivePage(nextPage);
      dispatch(loadBeneficiariesForAideComplementaire(nextPage, aideComplementaire.id));
    }
  };
  useEffect(() => {
    if (showAlert) {
      setTimeout(() => {
        setShowAlert(false);
      }, 4000);
    }
  }, [showAlert]);

  if (showAlert) {
    successAlert = (
      <Alert className="alert-style" variant="success" onClose={() => setShowAlert(false)} dismissible>
        {message}
      </Alert>
    );
  }

  if (aideComplementaire) {
    beneficiaries = <BeneficiariesWithOutDonors data={aideComplementaire} />;
  }

  const filteredBeneficiaries = props.beneficiariesForAideComplementaire.content
  const handleCheckboxChange = beneficiaryCode => {
    setSelectedBeneficiaries(prevSelected =>
      prevSelected.includes(beneficiaryCode)
        ? prevSelected.filter(code => code !== beneficiaryCode)
        : [...prevSelected, beneficiaryCode],
    );
  };

  const handleSelectAll = () => {
    const allBeneficiaryCodes = filteredBeneficiaries.map(b => b.code);
    if (
      allBeneficiaryCodes.every(code => selectedBeneficiaries.includes(code))
    ) {
      setSelectedBeneficiaries([]);
    } else {
      setSelectedBeneficiaries(allBeneficiaryCodes);
    }
  };

  const handleCloseModal = () => {
    setSelectedBeneficiaries([]);
    setShowModal(false);
    setIndependent('');
    setBeneficiaryStatut('');
    setFilterText('');
    setFilters({
      type: '',
      beneficiaryStatut: '',
      text: '',
      withMemberParticipant: '',
      withDonor: '',
      withDonorParticipant: '',
      fromPreviousCampaign: '',
    });
    setShowAdvancedFilter(false);
  };

  console.log({ filteredBeneficiaries });

  const handleAdvancedFilterChange = (field, value) => {
    setFilters(prevFilters => ({
      ...prevFilters,
      [field]: value,
    }));
  };

  const handleResetFilters = () => {
    setFilters({
      type: '',
      beneficiaryStatut: '',
      text: '',
      withMemberParticipant: '',
      withDonor: '',
      withDonorParticipant: '',
      fromPreviousCampaign: '',
    });
    const aideComplementaireId = aideComplementaire.id;
    dispatch(loadBeneficiariesForAideComplementaire(0, aideComplementaireId));
  };


  const handleApplyFilters = () => {
    const aideComplementaireId = aideComplementaire.id;
    dispatch(
      loadBeneficiariesForAideComplementaire(0, aideComplementaireId, filters),
    );
  };

  return (
    <div>
      {showSuccessMessage && message && (
        <Alert
          className="alert-style"
          variant="success"
          onClose={handleCloseSuccessMessage}
          dismissible
        >
          <p>{message}</p>
        </Alert>
      )}
      {successAlert}

      <div className={`pb-5 pt-4 ${stylesList.backgroudStyle}`}>
        {aideComplementaire &&
          aideComplementaire.statut != 'executer' &&
          aideComplementaire.statut != 'cloturer' && (
            <div style={{ textAlign: 'right', marginBottom: '20px' }}>
              <Button
                variant="primary"
                className="align-right-btn"
                style={{ borderRadius: '25px' ,padding: '10px 10'}}
                onClick={() => {
                  const aideComplementaireId2 = aideComplementaire.id;
                  dispatch(
                    loadBeneficiariesForAideComplementaire(0,
                      aideComplementaireId2,
                    ),
                  );
                  setShowModal(true);
                }}
              >
                Ajouter des bénéficiaires
              </Button>
            </div>
          )}
        <div>{beneficiaries}</div>
      </div>

      <Modal show={showModal} onHide={handleCloseModal} size="xl" centered>
        <Modal.Header closeButton className="bg-light">
          <Modal.Title className="text-dark">
            Ajouter des bénéficiaires
          </Modal.Title>
        </Modal.Header>
        <Modal.Body className="p-4">
          {loading2 ? (
            <div className="d-flex justify-content-center align-items-center" style={{ height: '200px' }}>
              <Spinner animation="border" role="status" />
            </div>
          ) : (
            <>
              <InputGroup className="mb-3">
                <Button variant="outline-secondary" onClick={handleSelectAll}>
                  {filteredBeneficiaries && filteredBeneficiaries.length === selectedBeneficiaries.length
                    ? 'Désélectionner tout'
                    : 'Sélectionner tout'}
                </Button>
                <Form.Control
                  type="text"
                  placeholder="Filtrer par nom ou prénom ou CIN"
                  value={filters.text}
                  onChange={e => setFilters({ ...filters, text: e.target.value })}
                  className="border-secondary"
                  style={{ width: '200px' }}
                />

                <Form.Control
                  as="select"
                  value={filters.beneficiaryStatut}
                  onChange={e => setFilters({ ...filters, beneficiaryStatut: e.target.value })}
                  className="border-secondary mx-2"
                  style={{ width: '150px' }}
                >
                  <option value="">Catégorie</option>
                  <option value="beneficiary_actif">
                    Bénéficiaire Kafalat
                  </option>
                  <option value="beneficiary_enattente">Candidat</option>
                  <option value="beneficiary_ad_hoc_individual">
                    Bénéficiaire Ad Hoc
                  </option>
                </Form.Control>

                <Form.Control
                  as="select"
                  value={filters.type}
                  onChange={e => setFilters({ ...filters, type: e.target.value })}
                  className="border-secondary"
                  style={{ width: '150px' }}
                >
                  <option value="">Type</option>
                  <option value="Indépendant">Indépendant</option>
                  <option value="Membre Famille">Membre Famille</option>
                  <option value="Groupe">Groupe</option>
                </Form.Control>
 
              </InputGroup>

              <div>
                {/* Filtre en disposition en deux colonnes */}
                <div className="row g-3">
                  {/* Bénéficiaires avec membres participant */}
                  <div className="col-md-6">
                    <Form.Group className="d-flex align-items-center">
                      <Form.Label className="fw-bold me-3 mb-0" style={{ width: '350px' }}>
                        Bénéficiaires avec membres participant :
                      </Form.Label>
                      <Form.Control
                        as="select"
                        value={filters.withMemberParticipant}
                        onChange={(e) => handleAdvancedFilterChange('withMemberParticipant', e.target.value)}
                        className="border-secondary"
                        style={{ width: '150px' }}
                      >
                        <option value="">Tous</option>
                        <option value="true">Oui</option>
                        <option value="false">Non</option>
                      </Form.Control>
                    </Form.Group>
                  </div>

                  {/* Bénéficiaires avec donateurs */}
                  <div className="col-md-6">
                    <Form.Group className="d-flex align-items-center">
                      <Form.Label className="fw-bold me-3 mb-0" style={{ width: '350px' }}>
                        Bénéficiaires avec donateurs :
                      </Form.Label>
                      <Form.Control
                        as="select"
                        value={filters.withDonor}
                        onChange={(e) => handleAdvancedFilterChange('withDonor', e.target.value)}
                        className="border-secondary"
                        style={{ width: '150px' }}
                      >
                        <option value="">Tous</option>
                        <option value="true">Oui</option>
                        <option value="false">Non</option>
                      </Form.Control>
                    </Form.Group>
                  </div>

                  {/* Bénéficiaires avec donateurs participant */}
                  <div className="col-md-6">
                    <Form.Group className="d-flex align-items-center">
                      <Form.Label className="fw-bold me-3 mb-0" style={{ width: '350px' }}>
                        Bénéficiaires avec donateurs participant :
                      </Form.Label>
                      <Form.Control
                        as="select"
                        value={filters.withDonorParticipant}
                        onChange={(e) => handleAdvancedFilterChange('withDonorParticipant', e.target.value)}
                        className="border-secondary"
                        style={{ width: '150px' }}
                      >
                        <option value="">Tous</option>
                        <option value="true">Oui</option>
                        <option value="false">Non</option>
                      </Form.Control>
                    </Form.Group>
                  </div>

                  {/* Bénéficiaires avec ancienne campagne */}
                  <div className="col-md-6">
                    <Form.Group className="d-flex align-items-center">
                      <Form.Label className="fw-bold me-3 mb-0" style={{ width: '350px' }}>
                        Bénéficiaires avec ancienne campagne :
                      </Form.Label>
                      <Form.Control
                        as="select"
                        value={filters.fromPreviousCampaign}
                        onChange={(e) => handleAdvancedFilterChange('fromPreviousCampaign', e.target.value)}
                        className="border-secondary"
                        style={{ width: '150px' }}
                      >
                        <option value="">Tous</option>
                        <option value="true">Oui</option>
                        <option value="false">Non</option>
                      </Form.Control>
                    </Form.Group>
                  </div>
                </div>

                {/* Boutons d'application et de réinitialisation */}
                <div className="d-flex justify-content-end mt-4">
                  <Button
                    variant="outline-success"
                    size="sm"
                    className="px-3 py-1 rounded-pill"
                    style={{ marginRight: '4px' }}
                    onClick={handleApplyFilters}
                  >
                    Appliquer
                  </Button>
                  <Button
                    variant="outline-secondary"
                    size="sm"
                    className="px-3 py-1 rounded-pill"
                    onClick={handleResetFilters}
                  >
                    Réinitialiser
                  </Button>
                </div>
              </div>

              {/* Nombre de bénéficiaires sélectionnés */}
              <p className="text-muted">
                Nombre de bénéficiaires sélectionnés:{' '}
                {selectedBeneficiaries.length}
              </p>

              <div style={{ maxHeight: '400px', overflowY: 'auto' }}>
                <ListGroup className="mt-3">
                  {filteredBeneficiaries && filteredBeneficiaries.map(beneficiary => (
                    <ListGroup.Item
                      key={beneficiary.code}
                      className="d-flex align-items-center"
                    >
                      <Form.Check
                        type="checkbox"
                        label={
                          [beneficiary.firstName, beneficiary.lastName]
                            .filter(name => name)
                            .join(' ') +
                          (beneficiary.identityCode
                            ? ` - ${beneficiary.identityCode}`
                            : '')
                        }
                        onChange={() => handleCheckboxChange(beneficiary.code)}
                        checked={selectedBeneficiaries.includes(
                          beneficiary.code,
                        )}
                      />
                    </ListGroup.Item>
                  ))}
                </ListGroup>
              </div>
 
            </>
          )}
          <div className="row justify-content-center my-4">
            <CustomPagination
              onPageChange={handlePageChange}
              totalElements={beneficiariesForAideComplementaire
                ? beneficiariesForAideComplementaire.numberOfElements : 0}
              totalCount={beneficiariesForAideComplementaire
                ? beneficiariesForAideComplementaire.totalPages : 1}
              currentPage={beneficiariesForAideComplementaire ? beneficiariesForAideComplementaire.number + 1 : activePage}
              pageSize={beneficiariesForAideComplementaire ? beneficiariesForAideComplementaire.pageSize : 5}
            />
          </div>
        </Modal.Body>
        <Modal.Footer className="bg-light">
          <Button variant="secondary" onClick={handleCloseModal}>
            Annuler
          </Button>
          <Button
            variant="primary"
            onClick={() => {
              const aideComplementaireId2 = aideComplementaire.id;
              dispatch(
                processDonorsAndBeneficiariesRequest(
                  aideComplementaireId2,
                  selectedBeneficiaries,
                ),
              );
              handleCloseModal();
            }}
          >
            Ajouter
          </Button>
        </Modal.Footer>
      </Modal>
    </div>
  );
}
