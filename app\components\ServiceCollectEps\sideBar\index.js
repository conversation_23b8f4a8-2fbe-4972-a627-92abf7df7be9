import React from 'react';
import sideBarStyles from 'Css/sideBar.css';
import moment from 'moment';
import { Link, useHistory } from 'react-router-dom';
import { EDIT_ICON } from 'components/Common/ListIcons/ListIcons';

const formatDate = (date) => moment(date).format('DD/MM/YYYY');

const SideBar = ({ data: ficheServiceCollectEps }) => {
  const history = useHistory();
  function determinerStatut(ficheServiceCollectEps) {
    const currentDate = new Date();
    const givenDate = new Date(ficheServiceCollectEps.annee, ficheServiceCollectEps.mois - 1);

    if (ficheServiceCollectEps.isCloture) {
      return "Cloturé";
    }


    if (
      givenDate.getFullYear() === currentDate.getFullYear() &&
      givenDate.getMonth() === currentDate.getMonth()
    ) {
      return "Ouvert"; // Current month and year
    } else if (givenDate < currentDate) {
      return "Fermé"; // Past month or year
    } else {
      return "Planifié"; // Future month or year
    }
  }

  return (
    <div className={sideBarStyles.sideBar}style={{position:"relative"}}>
      <div className="text-center mt-4" >
        <Link
          to={{
            pathname: `/ServiceCollectEps/edit/${ficheServiceCollectEps.id}`,
            state: { redirectTo: 'consultation' },
          }}
          style={{position:"absolute", right:0, top:0}}
        > <input
            type="image"
            src={EDIT_ICON}
            className="p-2 ml-auto"
            title="modifier les informations communes de la famille"
            width="40px"
            height="40px" 
          />
        </Link>
        <h5 className="mb-3"> Service de collecte</h5>
        <div className={sideBarStyles.infoContainer}>
          <p><strong>Nom :</strong> {ficheServiceCollectEps.nom}</p>
          <p><strong>Code :</strong> {ficheServiceCollectEps.code}</p>
          <p><strong>Statut :</strong> {determinerStatut(ficheServiceCollectEps)}</p>
          <p><strong>Date :</strong> {ficheServiceCollectEps.mois}/{ficheServiceCollectEps.annee} </p>
          <p><strong>Commentaire :</strong> {ficheServiceCollectEps.commentaire} </p>
        </div>
      </div>

      <hr className={sideBarStyles.hr} />

      <div className={`text-center ${sideBarStyles.label}`}>
        <div className={sideBarStyles.infoContainer}>
          <p><strong>Eps associée :</strong><br />{ficheServiceCollectEps.eps && (
            <Link to={`/eps/fiche/${ficheServiceCollectEps.eps.id}/info`}>
              {ficheServiceCollectEps.eps.name}
            </Link>
          )}</p>

          <p>
            <strong>Service associé :</strong><br />
            {ficheServiceCollectEps.eps && (
              <Link to={`/services/fiche/${ficheServiceCollectEps.
                serviceLightDTO
                .id}/info`}>
                {ficheServiceCollectEps.
                  serviceLightDTO
                  .name}
              </Link>
            )}
          </p>


        </div>
      </div>
    </div>
  );
};

export default SideBar;
