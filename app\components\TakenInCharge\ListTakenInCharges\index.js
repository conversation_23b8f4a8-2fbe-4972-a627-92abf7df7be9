import React, { useCallback, useEffect, useState } from 'react';
import moment from 'moment';
import { Link } from 'react-router-dom';
import listStyle from 'Css/profileList.css';
import btnStyles from 'Css/button.css';
import tagStyles from 'Css/tag.css';
import styled from 'styled-components';
import {
  DELETE_ICON,
  EDIT_ICON,
  VIEW_ICON,
} from '../../Common/ListIcons/ListIcons';
import DataTable from '../../Common/DataTable';
import AccessControl from 'utils/AccessControl';
import {
  deleteTakenInCharge,
  deleteTakenInChargeReset,
} from 'containers/TakenInCharge/AddTakenInCharge/actions';
import Modal2 from 'components/Common/Modal2';
import { useDispatch, useSelector } from 'react-redux';
import { useInjectReducer, useInjectSaga } from 'redux-injectors';
import takenInChargeReducer from 'containers/TakenInCharge/AddTakenInCharge/reducer';
import takenInChargeSaga from 'containers/TakenInCharge/AddTakenInCharge/saga';
import { createStructuredSelector } from 'reselect';
import {
  makeSelectDeleteError,
  makeSelectDeleteSuccess,
} from 'containers/TakenInCharge/AddTakenInCharge/selectors';
import { removeTakenInChargeGlobal } from 'containers/TakenInCharge/TakenInCharges/actions';
const stateSelector = createStructuredSelector({
  successDelete: makeSelectDeleteSuccess,
  errorDelete: makeSelectDeleteError,
});

// Styled component for the tags container in the table
const TagsContainer = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  max-width: 200px;
`;

// Styled component for individual tags in the table
const TableTag = styled.div`
  padding: 2px 8px;
  font-size: 11px;
  white-space: nowrap;
  border-radius: 10px;
  background-color: ${props => `#${props.color || 'cccccc'}`};
  color: ${props => {
    const hex = props.color || 'cccccc';
    const r = parseInt(hex.substr(0, 2), 16);
    const g = parseInt(hex.substr(2, 2), 16);
    const b = parseInt(hex.substr(4, 2), 16);
    const brightness = (r * 299 + g * 587 + b * 114) / 1000;
    return brightness > 128 ? '#000000' : '#ffffff';
  }};
`;

export default function ListTakenInCharges(props) {
  let listTakenInCharges;
  useInjectReducer({ key: 'addTakenInCharge', reducer: takenInChargeReducer });
  useInjectSaga({ key: 'addTakenInCharge', saga: takenInChargeSaga });
  const formatDate = date => moment(date).format('DD/MM/YYYY');
  const { successDelete, errorDelete } = useSelector(stateSelector);

  const liste1 = props.takenInChargesGlobal;
  let donorName;
  let beneficiaryName;

  const getTag = status => {
    if (status == 'Inactif') {
      return tagStyles.tagRed;
    }
    if (status == 'Actif') {
      return tagStyles.tagGreen;
    }
    if (status == 'Fermé') {
      return tagStyles.tagGrey;
    }
    return tagStyles.tagGrey;
  };
  const dispatch = useDispatch();
  const [takenInChargeToDelete, setTakenInChargeToDelete] = useState('');
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [showAlert, setShowAlert] = useState(false);

  const failedToDelete = useCallback(() => {
    if (errorDelete) {
      setShowAlert(true);
    }
  }, [errorDelete]);
  // close alert after 4 seconds
  useEffect(() => {
    if (showAlert) {
      setTimeout(() => {
        setShowAlert(false);
        dispatch(deleteTakenInChargeReset());
      }, 4000);
    }
  }, [showAlert]);

  useEffect(() => {
    errorDelete && failedToDelete();
  }, [errorDelete]);
  const handleCloseForDeleteModal = () => setShowDeleteModal(false);
  useEffect(() => {
    if (successDelete) {
      dispatch(removeTakenInChargeGlobal(takenInChargeToDelete));
      dispatch(deleteTakenInChargeReset());
    }
  }, [successDelete]);
  if (props.takenInCharges) {
    let { takenInCharges } = props;

    if (props.profile) {
      takenInCharges = props.takenInCharges.takenInCharges;
    }

    const takenInChargesSorted = [...takenInCharges];

    listTakenInCharges = takenInChargesSorted.map(takenInCharge => {
      if (!props.profile) {
        if (
          takenInCharge.takenInChargeDonors[0] &&
          takenInCharge.takenInChargeDonors[0].donor.type == 'Physique'
        ) {
          donorName = (
            <Link
              to={`/donors/fiche/${takenInCharge.takenInChargeDonors[0].donor.id}/info`}
            >
              {`${takenInCharge.takenInChargeDonors[0].donor.firstName} ${takenInCharge.takenInChargeDonors[0].donor.lastName}`}
            </Link>
          );
        } else if (
          takenInCharge.takenInChargeDonors[0] &&
          takenInCharge.takenInChargeDonors[0].donor.type == 'Moral'
        ) {
          donorName = (
            <Link
              to={`/donors/fiche/${takenInCharge.takenInChargeDonors[0].donor.id}/info`}
            >
              {takenInCharge.takenInChargeDonors[0].donor.company}
            </Link>
          );
        } else if (
          takenInCharge.takenInChargeDonors[0] &&
          takenInCharge.takenInChargeDonors[0].donor.type == 'Anonyme'
        ) {
          donorName = (
            <Link
              to={`/donors/fiche/${takenInCharge.takenInChargeDonors[0].donor.id}/info`}
            >
              {takenInCharge.takenInChargeDonors[0].donor.name}
            </Link>
          );
        }
      }

      if (takenInCharge.takenInChargeBeneficiaries[0]) {
        beneficiaryName =
          takenInCharge.takenInChargeBeneficiaries[0].beneficiary !== true ? (
            <Link
              to={`/beneficiaries/fiche/${takenInCharge.takenInChargeBeneficiaries[0].beneficiary.id}/info`}
            >
              {`${takenInCharge.takenInChargeBeneficiaries[0].beneficiary.person.firstName} ${takenInCharge.takenInChargeBeneficiaries[0].beneficiary.person.lastName}`}
            </Link>
          ) : (
            <p>{`${takenInCharge.takenInChargeBeneficiaries[0].beneficiary.person.firstName} ${takenInCharge.takenInChargeBeneficiaries[0].beneficiary.person.lastName}`}</p>
          );
      }

      return {
        id: takenInCharge.id,
        code: takenInCharge.code,
        service: takenInCharge.services ? takenInCharge.services.name : '-',
        beneficiary: takenInCharge.takenInChargeBeneficiaries[0]
          ? `${takenInCharge.takenInChargeBeneficiaries[0].beneficiary.person.firstName} ${takenInCharge.takenInChargeBeneficiaries[0].beneficiary.person.lastName}`
          : '-',
        donor: props.profile
          ? '-'
          : takenInCharge.takenInChargeDonors[0]
          ? takenInCharge.takenInChargeDonors[0].donor.type === 'Physique'
            ? `${takenInCharge.takenInChargeDonors[0].donor.firstName} ${takenInCharge.takenInChargeDonors[0].donor.lastName}`
            : takenInCharge.takenInChargeDonors[0].donor.company
            ? takenInCharge.takenInChargeDonors[0].donor.company
            : takenInCharge.takenInChargeDonors[0].donor.name
          : '-',
        priseEnCharge: props.profile
          ? '-'
          : takenInCharge.takenInChargeDonors.length === 1
          ? 'Totale'
          : 'Partielle',
        startDate: takenInCharge.startDate
          ? formatDate(takenInCharge.startDate)
          : '-',
        endDate: takenInCharge.endDate
          ? formatDate(takenInCharge.endDate)
          : '-',
        status: takenInCharge.status ? takenInCharge.status : '-',
        profile: props.profile,
        takenInCharge,
        takenInCharges: props.takenInCharges,
        tags: takenInCharge.tags || [],
      };
    });
  }

  const columns = [
    {
      field: 'code',
      headerName: 'Code',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
    {
      field: 'beneficiary',
      headerName: 'Bénéficiaire',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
      renderCell: params => {
        const takenInCharge = params.row.takenInCharges.find(
          item => item.id === params.row.id,
        );
        if (
          !params.row.takenInCharge ||
          !params.row.takenInCharge.takenInChargeBeneficiaries[0]
        )
          return <span>-</span>;
        const {
          beneficiary,
        } = params.row.takenInCharge.takenInChargeBeneficiaries[0];
        if (!params.row.beneficiary) return <span>-</span>;
        const beneficiaryName = params.row.beneficiary;
        const beneficiaryLink = (
          <Link to={`/beneficiaries/fiche/${beneficiary.id}/info`}>
            {beneficiaryName}
          </Link>
        );
        return beneficiary ? beneficiaryLink : <span>-</span>;
      },
    },

    {
      field: 'BeneficciaryarabicName',
      headerName: ' المستفيد',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
      renderCell: params => {
        const takenInCharge = params.row.takenInCharges.find(
          item => item.id === params.row.id,
        );
        if (
          !params.row.takenInCharge ||
          !params.row.takenInCharge.takenInChargeBeneficiaries[0]
        )
          return <span>-</span>;
        const {
          beneficiary,
        } = params.row.takenInCharge.takenInChargeBeneficiaries[0];
        const beneficiaryPerson =
          params.row.takenInCharge.takenInChargeBeneficiaries[0].beneficiary
            .person;
        if (!params.row.beneficiary) return <span>-</span>;
        const beneficiaryName =
          beneficiaryPerson.firstNameAr + ' ' + beneficiaryPerson.lastNameAr;
        const beneficiaryLink = (
          <Link to={`/beneficiaries/fiche/${beneficiary.id}/info`}>
            {beneficiaryName}
          </Link>
        );
        return beneficiary ? beneficiaryLink : <span>-</span>;
      },
    },

    {
      field: 'donor',
      headerName: 'Donateur',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
      renderCell: params => {
        if (
          !params.row.takenInCharge ||
          !params.row.takenInCharge.takenInChargeDonors[0]
        )
          return <span>-</span>;
        const { donor } = params.row.takenInCharge.takenInChargeDonors[0];
        if (!donor) return <span>-</span>;
        const donorName = params.row.donor;
        const donorLink = (
          <Link to={`/donors/fiche/${donor.id}/info`}>{donorName}</Link>
        );
        return donor ? donorLink : <span>-</span>;
      },
    },
    {
      field: 'DonorarabicName',
      headerName: 'المتبرع ',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
      renderCell: params => {
        if (
          !params.row.takenInCharge ||
          !params.row.takenInCharge.takenInChargeDonors[0]
        )
          return <span>-</span>;
        const { donor } = params.row.takenInCharge.takenInChargeDonors[0];
        if (!donor) return <span>-</span>;
        const donorPerson =
          params.row.takenInCharge.takenInChargeDonors[0].donor;
        const donorName = donorPerson.firstNameAr
          ? donorPerson.firstNameAr + ' ' + donorPerson.lastNameAr
          : '-';

        const donorLink = (
          <Link to={`/donors/fiche/${donor.id}/info`}>{donorName}</Link>
        );
        return donor ? donorLink : <span>-</span>;
      },
    },
    {
      field: 'startDate',
      headerName: 'Date début',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
    {
      field: 'endDate',
      headerName: 'Date fin',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
    {
      field: 'service',
      headerName: 'Service',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
    },
    {
      field: 'status',
      headerName: 'Statut',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
      renderCell: params => (
        <div className={getTag(params.row.status)}>
          {params.row.status || '---'}
        </div>
      ),
    },
    {
      field: 'tags',
      headerName: 'Tags',
      flex: 1.5,
      headerAlign: 'center',
      align: 'center',
      renderCell: params => {
        const tags = params.row.tags || [];
        return (
          <TagsContainer>
            {tags && tags.length > 0 ? (
              tags.map(tag => (
                <TableTag key={tag.id} color={tag.color || 'cccccc'}>
                  {tag.name}
                </TableTag>
              ))
            ) : (
              <span style={{ color: '#999', fontSize: '12px' }}>Aucun tag</span>
            )}
          </TagsContainer>
        );
      },
    },
    {
      field: 'actions',
      headerName: 'Actions',
      type: 'actions',
      sortable: false,
      flex: 1,
      headerAlign: 'center',
      align: 'center',
      renderCell: params => (
        <div>
          <Link to={`/takenInCharges/fiche/${params.row.id}/info`}>
            <input
              type="image"
              src={VIEW_ICON}
              className="p-0"
              width="20px"
              height="20px"
              title="consulter"
            />
          </Link>
          {!params.row.profile && (
            <AccessControl module="TAKEINCHARGE" functionality="CREATE">
              <Link to={{ pathname: `/takenInCharges/edit/${params.row.id}` }}>
                <input
                  disabled={['Fermé', 'Actif'].includes(params.row.status)}
                  type="image"
                  src={EDIT_ICON}
                  width="20px"
                  height="20px"
                  className="mx-2"
                  style={{
                    opacity: ['Fermé', 'Actif'].includes(params.row.status)
                      ? 0.5
                      : 1,
                    cursor: ['Fermé', 'Actif'].includes(params.row.status)
                      ? 'not-allowed'
                      : 'pointer',
                  }}
                  title={
                    ['Fermé', 'Actif'].includes(params.row.status)
                      ? 'Kafalat Fermé ou Actif ne peut pas être modifiée'
                      : 'Modifier'
                  }
                />
              </Link>
            </AccessControl>
          )}
          <AccessControl module="TAKEINCHARGE" functionality="DELETE">
            <input
              type="image"
              src={DELETE_ICON}
              className="p-0"
              width="20px"
              height="20px"
              title="Supprimer"
              onClick={() => {
                setShowDeleteModal(true);
                setTakenInChargeToDelete(params.row);
              }}
            />
          </AccessControl>
        </div>
      ),
    },
  ];

  return (
    <div>
      <div>
        {props.profile ? (
          <div className={listStyle.global}>
            <div className={listStyle.header}>
              <h4>Liste des TakenInCharges</h4>

              <button
                className={btnStyles.addBtnProfile}
                onClick={() => {
                  handleShow();
                  // setTakenInChargeToEdit('');
                }}
              >
                Ajouter
              </button>
            </div>
          </div>
        ) : null}

        <div className={listStyle.content}>
          <Modal2
            centered
            className="mt-5"
            title="Confirmation de suppression"
            show={showDeleteModal}
            handleClose={handleCloseForDeleteModal}
          >
            <p className="mt-1 mb-5">
              Êtes-vous sûr de vouloir supprimer cette Kafalat?
            </p>
            <div className="d-flex justify-content-end px-3 my-1">
              <button
                type="button"
                className={`mx-2 btn-style outlined`}
                onClick={handleCloseForDeleteModal}
              >
                Annuler
              </button>
              <button
                type="submit"
                className={`mx-2 btn-style primary`}
                onClick={() => {
                  dispatch(deleteTakenInCharge(takenInChargeToDelete));
                  handleCloseForDeleteModal();
                }}
              >
                Supprimer
              </button>
            </div>
          </Modal2>
          <DataTable
            rows={listTakenInCharges}
            columns={columns}
            fileName={`Liste des Prises en charges , ${new Date().toLocaleString()}`}
            totalElements={liste1.totalElements}
            numberOfElements={liste1.numberOfElements}
            pageable={liste1.pageable}
          />
        </div>
      </div>
    </div>
  );
}
