import { call, put, takeLatest } from 'redux-saga/effects';
import request from 'utils/request';
import {
  changeZoneFailure,
  changeZoneSuccess,
  deleteAssistantFailure,
  deleteAssistantSuccess,
  fetchAssistantsFailure,
  fetchAssistantsSuccess,
  getAllAssistantsFailure,
  getAllAssistantsSuccess,
} from './actions';

import {
  CHANGE_ZONE_REQUEST,
  DELETE_ASSISTANT_REQUEST,
  FETCH_ASSISTANTS_REQUEST,
  GET_ALL_ASSISTANTS_REQUEST,
} from './constants';

export function* fetchAssistants({ page }) {
  const url = `/assistants?page=${page}`;
  try {
    const { data } = yield call(request.get, url);
    yield put(fetchAssistantsSuccess(data));
  } catch (error) {
    yield put(fetchAssistantsFailure(error));
  }
}

export function* deleteAssistant({ assistantId }) {
  const url = `/assistants/${assistantId}`;
  try {
    // Make the DELETE request to the server
    yield call(request.delete, url);
    
    // Dispatch success action if the request is successful
    yield put(deleteAssistantSuccess(assistantId));
  } catch (error) {
    // Log the error for debugging
    console.error('Error occurred during assistant deletion:', error);

    // Check if the error response exists and handle it accordingly
    const errorMsg = error.response
      ? error.response.data
      : error.message;

    // Log the detailed error message to the console for debugging
    console.error('Error response:', errorMsg);

    // Dispatch failure action with the error message
    yield put(deleteAssistantFailure(errorMsg.detail || 'An error occurred.'));
  }
}


// changeZoneRequest

export function* changeZone(action) {
  const { assistantId, endDate, newZoneId, newZoneDate } = action;

  const url = `/assistants/${assistantId}/change-zone`;
  try {
    yield call(request.put, url, null, {
      params: {
        endDate,
        newZoneId,
        newZoneDate,
      },
    });
    yield put(changeZoneSuccess());
  } catch (error) {
    const errorMsg = error.response ? error.response.data : error.message;

    yield put(changeZoneFailure(errorMsg.message));
  }
}

export function* getAllAssistants() {
  const url = `assistants/getAllAssistants`;
  try {
    const { data } = yield call(request.get, url);
    console.log('API response for /getAllAssistants:', data);
    yield put(getAllAssistantsSuccess(data));
  } catch (error) {
    console.error('Error fetching assistants:', error);
    yield put(getAllAssistantsFailure(error));
  }
}

export default function* assistantsSaga() {
  yield takeLatest(FETCH_ASSISTANTS_REQUEST, fetchAssistants);
  yield takeLatest(DELETE_ASSISTANT_REQUEST, deleteAssistant);
  yield takeLatest(CHANGE_ZONE_REQUEST, changeZone);
  yield takeLatest(GET_ALL_ASSISTANTS_REQUEST, getAllAssistants);
}
