import produce from 'immer';
import {
  LOAD_RELEVE_DONOR,
  LOAD_RELEVE_DONOR_ERROR,
  LOAD_RELEVE_DONOR_SUCCESS,
  LOAD_CANAL_DONATIONS,
  LOAD_CANAL_DONATIONS_SUCCESS,
  LOAD_CANAL_DONATIONS_ERROR,
} from './constants';

export const initialState = {
  loading: false,
  error: false,
  success: false,
  releves: [],
  canalDonations: false,
};

/* eslint-disable default-case, no-param-reassign */
const donorReducer = produce((draft, action) => {
  switch (action.type) {
    case LOAD_RELEVE_DONOR:
      draft.loading = true;
      draft.error = false;
      draft.releves = false;
      break;
    case LOAD_RELEVE_DONOR_SUCCESS:
      draft.loading = false;
      draft.error = false;
      draft.releves = action.releves;
      break;
    case LOAD_RELEVE_DONOR_ERROR:
      draft.loading = false;
      draft.error = action.error;
      break;
    case LOAD_CANAL_DONATIONS:
      draft.loading = true;
      draft.error = false;
      draft.canalDonations = false;
      break;
    case LOAD_CANAL_DONATIONS_SUCCESS:
      draft.loading = false;
      draft.error = false;
      draft.canalDonations = action.canalDonations;
      break;
    case LOAD_CANAL_DONATIONS_ERROR:
      draft.loading = false;
      draft.error = action.error;
      break;
  }
}, initialState);

export default donorReducer;
