import { createSelector } from 'reselect';
import { initialState } from './reducer';

const selectOmdb = state => state.aideComplementaireAdd || initialState;

const makeSelectSuccess = createSelector(
  selectOmdb,
  omdbState => omdbState.success,
);

const makeSelectLoading = createSelector(
  selectOmdb,
  omdbState => omdbState.loading,
);

const makeSelectError = createSelector(
  selectOmdb,
  omdbState => omdbState.error,
);

const makeSelectAideComplementaire = createSelector(
  selectOmdb,
  omdbState => omdbState.aideComplementaire,
);

const makeSelectDeleteSuccess = createSelector(
  selectOmdb,
  omdbState => omdbState.successDelete,
);

const makeSelectAideComplementaires = createSelector(
  selectOmdb,
  omdbState => omdbState.aideComplementaire1,
);

const makeSelectBeneficiairesWithTypePriseEnCharge = createSelector(
  selectOmdb,
  omdbState => omdbState.beneficiairesWithTypePriseEnCharge,
);

const makeSelectBeneficiairesWithTypePriseEnChargeLoading = createSelector(
  selectOmdb,
  omdbState => omdbState.loadingBeneficiairesWithTypePrise,
);

const makeSelectBeneficiairesWithTypePriseEnChargeSuccess = createSelector(
  selectOmdb,
  omdbState => omdbState.successBeneficiairesWithTypePrise,
);

const makeSelectBeneficiairesWithTypePriseEnChargeError = createSelector(
  selectOmdb,
  omdbState => omdbState.errorBeneficiairesWithTypePrise,
);

export {
  selectOmdb,
  makeSelectSuccess,
  makeSelectLoading,
  makeSelectError,
  makeSelectAideComplementaire,
  makeSelectDeleteSuccess,
  makeSelectAideComplementaires,
  makeSelectBeneficiairesWithTypePriseEnCharge,
  makeSelectBeneficiairesWithTypePriseEnChargeSuccess,
  makeSelectBeneficiairesWithTypePriseEnChargeError,
  makeSelectBeneficiairesWithTypePriseEnChargeLoading,
};
