import React, { useEffect, useState } from 'react';
import { Alert } from 'react-bootstrap';

import { useDispatch, useSelector } from 'react-redux';
import { useInjectReducer, useInjectSaga } from 'redux-injectors';
import Card from 'components/Common/Card';
import { createStructuredSelector } from 'reselect';
import styles from 'Css/profileList.css';
import stylesList from 'Css/profileList.css';
import btnStyles from 'Css/button.css';
import Modal2 from 'components/Common/Modal2';
import BankCardForm from 'containers/Common/Forms/BankCardForm';
import reducer from 'containers/Common/Forms/BankCardForm/reducer';

import {
  deleteBankCard,
  loadBankCardData,
  resetBankCard,
} from 'containers/Common/Forms/BankCardForm/actions';
import {
  pushBankCard,
  removeBankCard,
  updateBankCard,
} from 'containers/Beneficiary/BeneficiaryProfile/actions';
import {
  makeSelecBankCards,
  makeSelectBankCard,
  makeSelectBankCardAddSuccess,
  makeSelectSuccess,
  makeSelectSuccessDelete,
} from 'containers/Common/Forms/BankCardForm/selectors';
import { bankCardSaga } from 'containers/Common/Forms/BankCardForm/saga';
import getTutors from '../../../Family/Common/Functions/GetTutors';
import AccessControl from 'utils/AccessControl';
import { useHistory, useLocation } from 'react-router-dom';

const key = 'bankCard';

const omdbSelector = createStructuredSelector({
  success: makeSelectSuccess,
  bankCard: makeSelectBankCard,
  successDelete: makeSelectSuccessDelete,
  bankCards: makeSelecBankCards,
  bankCardAddSuccess: makeSelectBankCardAddSuccess,
});

export default function BankCards(props) {
  useInjectSaga({ key, saga: bankCardSaga });
  useInjectReducer({ key, reducer });

  const beneficiary = props.data;
  const family = props.f;
  let beneficiaryCards = null;
  let familyCards = null;
  let cards = [];
  let tutorName = '';
  let successAlert = null;
  const adaptedCards = [];
  let isBenefciary = null;

  const [hide, setHide] = useState(false);
  const location = useLocation();
  const history = useHistory();

  useEffect(() => {
    if (location && location.state && location.state.isOldBeneficiary) {
      setHide(location.state.isOldBeneficiary);
    } else {
      setHide(false);
    }
  }, [location]);

  const dispatch = useDispatch();
  const {
    success,
    bankCard,
    successDelete,
    bankCards,
    bankCardAddSuccess,
  } = useSelector(omdbSelector);

  const [show, setShow] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [message, setMessage] = useState('');
  const [showAlert, setShowAlert] = useState(false);
  const [actualBankCard, setActualBankCard] = useState('');
  const [tutorId, setTutorId] = useState(false);
  const [redirectionMessage, setRedirectionMessage] = useState('');
  const [takeInchageId, setTakeInChargeId] = useState(null);
  const [openOperationId, setOpenOperationId] = useState(null);
  const handleTutorId = tutorId => {
    setTutorId(tutorId);
  };

  const handleActualBankCard = bankCard => {
    setActualBankCard(bankCard);
  };

  const handleCloseModal = () => setShow(false);
  const handleShowModal = () => setShow(true);
  const handleCloseForDeleteModal = () => setShowDeleteModal(false);
  const handleShowForDeleteModal = () => setShowDeleteModal(true);

  useEffect(() => {
    if (successDelete) {
      dispatch(removeBankCard(bankCard));
      setMessage('La carte bancaire a été bien supprimée !');
      dispatch(resetBankCard());
    }
  }, [successDelete]);

  useEffect(() => {
    if (success) {
      setShowAlert(true);
      if (actualBankCard === '') {
        dispatch(pushBankCard(bankCard));
        setMessage('La carte bancaire a été bien ajoutée !');
        if (takeInchageId && openOperationId) {
          history.push({
            pathname: `/takenInCharges/fiche/${takeInchageId}/planification`,
            state: { openOperationId: openOperationId },
          });
        } else if (takeInchageId) {
          history.push(`/takenInCharges/fiche/${takeInchageId}/planification`);
        }
      } else {
        dispatch(updateBankCard(bankCard));
        setMessage('La carte bancaire a été bien modifiée !');
      }
    }
  }, [success]);

  useEffect(() => {
    if (location.state && location.state.takenInChargeId) {
      // we shols afficeh the modal of the bank card
      handleActualBankCard('');
      handleShowModal();
      setTakeInChargeId(location.state.takenInChargeId);
      setOpenOperationId(location.state.operationId);
      setRedirectionMessage(
        "Dès que la carte bancaire sera ajoutée avec succès, vous serez redirigé vers l'opération pour continuer votre action.",
      );
    } else {
      setRedirectionMessage('');
    }
  }, []);
  // to close the success message after 4 seconds
  useEffect(() => {
    if (showAlert) {
      setTimeout(() => {
        setShowAlert(false);
      }, 3000);
    }
  }, [showAlert]);

  useEffect(() => {
    if (beneficiary && beneficiary.id) {
      dispatch(loadBankCardData(beneficiary.person.id));
    }
  }, [beneficiary]);

  useEffect(() => {
    if (successDelete || bankCardAddSuccess) {
      dispatch(loadBankCardData(beneficiary.person.id));
    }
  }, [successDelete, bankCardAddSuccess]);

  if (showAlert) {
    successAlert = (
      <div>
        <Alert
          className="alert-style"
          variant="success"
          onClose={() => setShowAlert(false)}
          dismissible
        >
          {message}
        </Alert>
      </div>
    );
  }
  if (family && family.familyMembers) {
    const tutors = getTutors(family);
    if (tutors && tutors.length > 0) {
      tutors.map(tutor => {
        if (tutor.person.bankCards && tutor.person.bankCards.length > 0) {
          tutorName = '';
          if (tutor.person.sex) {
            tutorName = tutor.person.sex === 'Femme' ? 'Mme ' : 'M. ';
          }
          tutorName += `${tutor.person.lastName} ${tutor.person.firstName}`;
          if (beneficiary && beneficiary.person.id === tutor.person.id) {
            isBenefciary = tutor.person.id;
          }

          tutor.person.bankCards.map(card => {
            adaptedCards.push({
              ...card,
              tutorName,
              tutorId: tutor.person.id,
            });
          });
        }
      });

      const sortedCards = [...adaptedCards].sort((a, b) =>
        a.createdAt < b.createdAt ? 1 : -1,
      );

      familyCards = sortedCards.map(card => (
        <Card
          key={card.id}
          tutorName={card.tutorName}
          tutorId={card.tutorId}
          data={card}
          handleShowModal={handleShowModal}
          handleActualBankCard={handleActualBankCard}
          handleShowForDeleteModal={handleShowForDeleteModal}
          handleTutorId={handleTutorId}
          showDeleteIcon={beneficiary.independent}
          showModifyIcon={beneficiary.independent}
          hide={hide}
        />
      ));
    }
  }

  if (beneficiary) {
    if (beneficiary.person.sex) {
      tutorName = beneficiary.person.sex == 'Femme' ? 'Mme ' : 'M. ';
    }
    tutorName += `${beneficiary.person.lastName} ${beneficiary.person.firstName}`;

    const sortedCards = [...beneficiary.person.bankCards].sort((a, b) =>
      a.createdAt < b.createdAt ? 1 : -1,
    );
    if (beneficiary.person.id !== isBenefciary) {
      beneficiaryCards = sortedCards.map(card => (
        <Card
          key={card.id}
          tutorName={tutorName}
          data={card}
          handleShowModal={handleShowModal}
          handleActualBankCard={handleActualBankCard}
          handleShowForDeleteModal={handleShowForDeleteModal}
          showDeleteIcon={beneficiary.independent}
          showModifyIcon={beneficiary.independent}
          hide={hide}
        />
      ));
    }
  }

  cards = cards.concat(
    beneficiaryCards ? beneficiaryCards : [],
    familyCards ? familyCards : [],
  );

  return (
    <div>
      {successAlert}
      <div className={`pb-5 ${stylesList.backgroudStyle}`}>
        <div className={styles.global}>
          <Modal2
            size="lg"
            customWidth="modal-90w"
            title={
              actualBankCard
                ? 'Modifier une carte bancaire'
                : 'Ajouter une carte bancaire'
            }
            show={show}
            handleClose={handleCloseModal}
          >
            <BankCardForm
              bankCard={actualBankCard}
              show={show}
              handleClose={handleCloseModal}
              personId={beneficiary ? beneficiary.person.id : null}
              redirectionMessage={redirectionMessage}
            />
          </Modal2>

          <Modal2
            centered
            className="mt-5"
            title="Confirmation de suppression"
            show={showDeleteModal}
            handleClose={handleCloseForDeleteModal}
          >
            <p className="mt-1 mb-5 px-2">
              Êtes-vous sûr de vouloir supprimer cette carte bancaire?
            </p>
            <div className="d-flex justify-content-end px-2 my-1">
              <button
                type="button"
                className="btn-style outlined"
                onClick={handleCloseForDeleteModal}
              >
                Annuler
              </button>
              <AccessControl module="BENEFICIARY" functionality="UPDATE">
                <button
                  type="submit"
                  className={`ml-3 btn-style primary`}
                  onClick={() => {
                    dispatch(deleteBankCard(actualBankCard));
                    setActualBankCard('');
                    handleCloseForDeleteModal();
                  }}
                >
                  Supprimer
                </button>
              </AccessControl>
            </div>
          </Modal2>

          <div className={styles.header}>
            <h4>Informations Bancaires</h4>

            {beneficiary && beneficiary.independent && !hide && (
              <AccessControl module="BENEFICIARY" functionality="UPDATE">
                <button
                  className={btnStyles.addBtnProfile}
                  onClick={() => {
                    handleActualBankCard('');
                    handleShowModal();
                  }}
                  data-toggle="modal"
                  data-target="#exampleModal"
                >
                  Ajouter
                </button>
              </AccessControl>
            )}
          </div>

          {cards && cards.length === 0 ? (
            <p className="my-4">
              Il n'y a pas de carte bancaire enregistrée pour ce bénéficiaire !
            </p>
          ) : (
            cards
          )}
        </div>
      </div>
    </div>
  );
}
