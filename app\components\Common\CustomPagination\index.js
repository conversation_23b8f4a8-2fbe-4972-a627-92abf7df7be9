import React, { useState } from 'react';
import { Pagination, Stack, Typography } from '@mui/material';
import './customPaginationStyles.css';
import Wrapper from './Wrapper';

function CustomPagination({
  totalCount,
  pageSize,
  currentPage,
  onPageChange,
  totalElements,
  className = '',
}) {
  const [internalPage, setInternalPage] = useState(currentPage);

  const handlePageChange = (event, newPage) => {
    if (onPageChange) {
      onPageChange(newPage);
    } else {
      // Default client-side handling logic
      const totalPages = Math.ceil(totalCount / pageSize);
      if (newPage >= 1 && newPage <= totalPages) {
        setInternalPage(newPage);
      }
    }
  };

  return (
    <Wrapper className={className}>
      <Stack direction="row" gap="5px">
        <Typography variant="subtitle2_Lexend_Regular">
          Résultats trouvées:
        </Typography>
        <Typography variant="subtitle2_Lexend_Bold">{totalElements}</Typography>
      </Stack>
      <Pagination
        count={totalCount || null}
        page={onPageChange ? currentPage : internalPage}
        onChange={handlePageChange}
        variant="outlined"
        boundaryCount={2}
      />
    </Wrapper>
  );
}

export default CustomPagination;
