import React, { useState, useEffect } from 'react';
import { useHistory } from 'react-router-dom';
import listStyle from 'Css/profileList.css';
import btnStyles from 'Css/button.css';
import { Alert } from 'react-bootstrap';
import AccessControl from 'utils/AccessControl';
import { Link } from 'react-router-dom';
import { Chip } from '@material-ui/core';

import { useDispatch, useSelector } from 'react-redux';
import DataTable from 'components/Common/DataTable';

import Modal2 from 'components/Common/Modal2';
import {
    deleteEps,
    resetEps,
    resetDeleteEps,
} from 'containers/Eps/EpsList/actions';
import {
    DELETE_ICON,
    EDIT_ICON,
    VIEW_ICON,
} from 'components/Common/ListIcons/ListIcons';
import styles from '../../../Css/tag.css';
import { deleteServiceCollectEps } from 'containers/ServiceCollectEps/ListServiceCollectEps/actions';

const DonationServiceCollectEps = ({ donationServiceCollectEpsList }) => {

    const handleCloseForDeleteModal = () => setShowDeleteModal(false);
    const dispatch = useDispatch();
    const history = useHistory();
    const [showAlert, setShowAlert] = useState(false);
    const [message, setMessage] = useState('');

    const [showDeleteModal, setShowDeleteModal] = useState(false);
    const [epsToEdit, setEpsToEdit] = useState('');
    const [epsToDelete, setEpsToDelete] = useState('');
    const formatDate = (isoString) => {
        const date = new Date(isoString);
        return date.toLocaleDateString('fr-FR'); // Formats to DD/MM/YYYY
    };
    const columns = [
        {
            field: 'donationCode',
            headerName: 'Code de donation',
            flex: 1,
            headerAlign: 'center',
            align: 'center',
            renderCell:(params)=>{
                return <Link to={`/donations/fiche/${params.row.donationId}/info`}>
                    {params.row.donationCode}
                </Link>
            }
        },
        {
            field: 'donorName',
            headerName: 'Nom de donateur',
            flex: 1,
            headerAlign: 'center',
            align: 'center',
            renderCell:(params)=>{
                return <Link to={`/donors/fiche/${params.row.donorId}/info`}>
                    {params.row.donorName}
                </Link>
            }
        },
        {
            field: 'donationType',
            headerName: 'Type de donation',
            flex: 1,
            headerAlign: 'center',
            align: 'center',
        },
        {
            field: 'donationDate',
            headerName: 'Date de donation',
            flex: 1,
            headerAlign: 'center',
            align: 'center',
            renderCell: (params) => formatDate(params.value),
        },
        {
            field: 'montant',
            headerName: 'Montant de donation(DH)',
            flex: 1,
            headerAlign: 'center',
            align: 'center'
        },
    ];
    return (
        <div className="table-container">
            <div className={listStyle.global}>{/* Any additional UI elements */}</div>
            <div>
                <DataTable
                    rows={donationServiceCollectEpsList.map((donationServiceCollectEps, index) => ({
                        id: index + 1, // Auto-incrementing ID starting from 1
                        ...donationServiceCollectEps,
                    }))}
                    columns={columns}
                    fileName={`Liste des donation Service de collect , ${new Date().toLocaleString()}`}
                />

            </div>
        </div>
    );
};

export default DonationServiceCollectEps;