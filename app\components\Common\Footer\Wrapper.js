import styled from 'styled-components';
const Wrapper = styled.footer`
   position: relative;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 44px;
  background-color: #435568;
  color: #fff;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 95px;
  z-index: 100;


  .leftContainerStyle {
    text-align: left;

    .userInfoStyle {
      font-size: 14px;
      color: #ffffff;
      margin: 0;
      font-weight: bold;
    }

    .userInfoValueStyle {
      margin-left: 5px;
      color: #ffffff;
    }
  }

  .centerContainerStyle {
    .versionStyle {
      font-size: 12px;
      font-weight: bold;
    }
  }

  .rightContainerStyle {
    text-align: right;
    .linkStyle {
      text-decoration: none;
      font-size: 12px;
      color: #ffffff;
      font-weight: 700;
      cursor: pointer;
      transition: color 0.3s;
      &:hover {
        color: #4f89d7;
      }
    }
  }
`;
export default Wrapper;
