import React from 'react';
import { NavLink, useParams } from 'react-router-dom';

import styles from '../../../../Css/profileHeader.css';

const HeaderAideComplementaire = props => {
  const params = useParams();
  const id = params.id;
  const aideComplementaire = props.data;

  //const isWithDraft = aideComplementaire.propositionSystem ? true : false;
  //const isWithPriority = aideComplementaire.priority ? true : false;

  return (
    <div className={styles.navBar}>
      <p>
        <NavLink
          exact
          to={'/aide-complementaire/fiche/' + params.id + '/info'}
          activeClassName={styles.selected}
        >
          Informations générales
        </NavLink>
      </p>
      {/*
      {!isWithPriority && (
        <p>
          <NavLink
            exact
            to={'/aide-complementaire/fiche/' + params.id + '/donateurs'}
            activeClassName={styles.selected}
          >
            Donateurs
          </NavLink>
        </p>
      )}
      */}
      <p>
        <NavLink
          exact
          to={'/aide-complementaire/fiche/' + params.id + '/collect'}
          activeClassName={styles.selected}
        >
          Collecte des donations
        </NavLink>
      </p>
      <p>
        <NavLink
          exact
          to={'/aide-complementaire/fiche/' + params.id + '/beneficiaries'}
          activeClassName={styles.selected}
        >
          Répartition des donations
        </NavLink>
      </p>

      {/*<p>
        <NavLink
          exact
          to={'/aide-complementaire/fiche/' + params.id + '/donateurs'}
          activeClassName={styles.selected}
        >
          {isWithDraft && isWithPriority
            ? 'Donateurs avec leurs bénéficiaires'
            : 'Donateurs'}
        </NavLink>
      </p>
      */}

      {/*<p>
        <NavLink
          exact
          to={'/aide-complementaire/fiche/' + params.id + '/documents'}
          activeClassName={styles.selected}
        >
          Documents
        </NavLink>
      </p>

      <p>
        <NavLink
          exact
          to={'/aide-complementaire/fiche/' + params.id + '/notes'}
          activeClassName={styles.selected}
        >
          Notes
        </NavLink>
      </p>
      <p>
        <NavLink
          to={`/aide-complementaire/fiche/${id}/action`}
          activeClassName={styles.selected}
        >
          Actions
        </NavLink>
      </p>
      */}
    </div>
  );
};

export default HeaderAideComplementaire;
