export default {
    beneficiaryRecord: 'سجل المستفيد',
    personalInfo: 'معلومات شخصية',
    firstName: 'الاسم ',
    lastName: 'النسب',
    firstNameAr: 'الاسم الأول (بالعربية)',
    lastNameAr: 'اسم العائلة (بالعربية)',
    email: 'البريد الإلكتروني',
    phoneNumber: 'رقم الهاتف',
    sex: 'الجنس',
    identityCode: 'رمز الهوية',
    address: 'العنوان',
    addressAr: 'العنوان (بالعربية)',
    city: 'المدينة',
    region: 'المنطقة',
    profession: 'المهنة',
    schoolLevel: 'المستوى التعليمي',
    birthDate: 'تاريخ الميلاد',
    createdAt: 'أنشئت في',
    beneficiaryCode: 'رمز المستفيد',
    independent: 'مستقل',
    yes: 'نعم',
    no: 'لا',
    servicesBeneficiaries: 'خدمات المستفيدين',
    service: 'خدمة',
    status: 'الحالة',
    noServiceFound: 'لم يتم العثور على خدمة',
    handicaps: 'الإعاقات',
    handicapType: 'نوع الإعاقة',
    cause: 'السبب',
    cost: 'التكلفة',
    noHandicapFound: 'لم يتم العثور على إعاقة',
    allergies: 'الحساسيات',
    allergy: 'حساسية',
    noAllergyFound: 'لم يتم العثور على حساسية',
    diseaseTreatments: 'علاجات المرض',
    treatment: 'العلاج',
    noTreatmentFound: 'لم يتم العثور على علاج',
    schoolPath: 'المسار الدراسي',
    schoolYear: 'السنة الدراسية',
    schoolLevel: 'المستوى التعليمي',
    schoolName: 'اسم المدرسة',
    mark: 'العلامة',
    honor: 'التقدير',
    succeed: 'نجاح',
    noSchoolPathFound: 'لم يتم العثور على مسار دراسي',
    scholarships: 'المنح الدراسية',
    scholarshipName: 'اسم المنحة',
    amount: 'المبلغ',
    currency: 'العملة',
    startDate: 'تاريخ البدء',
    endDate: 'تاريخ الانتهاء',
    periodicity: 'الدورية',
    noScholarshipFound: 'لم يتم العثور على منحة دراسية',
  };
  