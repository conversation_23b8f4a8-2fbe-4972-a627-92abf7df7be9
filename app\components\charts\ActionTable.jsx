import React from 'react';
import { Box, Typography, Paper, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Chip, useTheme } from '@mui/material';
import { styled } from '@mui/material/styles';
import AssignmentIcon from '@mui/icons-material/Assignment';
import SendIcon from '@mui/icons-material/Send';
import HandshakeIcon from '@mui/icons-material/Handshake';
import LocalShippingIcon from '@mui/icons-material/LocalShipping';
import BusinessIcon from '@mui/icons-material/Business';

const StyledTableContainer = styled(TableContainer)(({ theme }) => ({
  borderRadius: '12px',
  border: `1px solid ${theme.palette.grey[300]}`,
  boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
  '& .MuiTableHead-root': {
    backgroundColor: theme.palette.grey[50],
  },
  '& .MuiTableCell-root': {
    borderBottom: `1px solid ${theme.palette.grey[200]}`,
    padding: theme.spacing(2),
  },
  '& .MuiTableRow-root:hover': {
    backgroundColor: theme.palette.grey[50],
    cursor: 'pointer',
  },
}));

const StatusChip = styled(Chip)(({ theme, status }) => ({
  backgroundColor: status === 'completed' 
    ? theme.palette.success.main 
    : status === 'pending'
    ? theme.palette.warning.main
    : theme.palette.primary.main,
  color: '#ffffff',
  fontWeight: 600,
  fontSize: '0.875rem',
  '& .MuiChip-label': {
    padding: '0 12px',
  },
}));

const IconWrapper = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  gap: theme.spacing(1),
  '& .MuiSvgIcon-root': {
    color: theme.palette.primary.main,
  },
}));

// Mock data for the action table
const actions = [
  {
    id: 1,
    type: 'donor',
    description: 'New donor registration',
    amount: '€1,000',
    date: '2024-03-10',
    status: 'completed',
    icon: AssignmentIcon,
  },
  {
    id: 2,
    type: 'donation',
    description: 'Monthly donation received',
    amount: '€500',
    date: '2024-03-09',
    status: 'completed',
    icon: SendIcon,
  },
  {
    id: 3,
    type: 'kafalat',
    description: 'New Kafalat application',
    amount: '€750',
    date: '2024-03-08',
    status: 'pending',
    icon: HandshakeIcon,
  },
  {
    id: 4,
    type: 'aide',
    description: 'Aid package delivered',
    amount: '€250',
    date: '2024-03-07',
    status: 'completed',
    icon: LocalShippingIcon,
  },
  {
    id: 5,
    type: 'eps',
    description: 'EPS fund transfer',
    amount: '€2,000',
    date: '2024-03-06',
    status: 'processing',
    icon: BusinessIcon,
  },
];

export function ActionTable({ actions = [] }) {
  const theme = useTheme();

  const getStatusColor = (status) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'processing':
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <StyledTableContainer component={Paper}>
      <Table>
        <TableHead>
          <TableRow>
            <TableCell>Type</TableCell>
            <TableCell>Description</TableCell>
            <TableCell>Amount</TableCell>
            <TableCell>Date</TableCell>
            <TableCell>Status</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {actions.map((action) => (
            <TableRow 
              key={action.id}
              onClick={action.onClick}
              hover
            >
              <TableCell>
                <IconWrapper>
                  {action.icon && <action.icon />}
                  <Typography variant="body2" color="textPrimary">
                    {action.type}
                  </Typography>
                </IconWrapper>
              </TableCell>
              <TableCell>
                <Typography variant="body2" color="textPrimary">
                  {action.description}
                </Typography>
              </TableCell>
              <TableCell>
                <Typography variant="body2" color="textPrimary">
                  {action.amount}
                </Typography>
              </TableCell>
              <TableCell>
                <Typography variant="body2" color="textPrimary">
                  {action.date}
                </Typography>
              </TableCell>
              <TableCell>
                <StatusChip
                  label={action.status}
                  status={action.status}
                />
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </StyledTableContainer>
  );
}