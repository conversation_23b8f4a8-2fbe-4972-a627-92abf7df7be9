// reducer.js
import {
  ADD_USER_FAILURE,
  ADD_USER_REQUEST,
  ADD_USER_SUCCESS,
  CHANGE_USER_ROLE_FAILURE,
  CHANGE_USER_ROLE_REQUEST,
  CHANGE_USER_ROLE_SUCCESS,
  DELETE_USER_FAILURE,
  DELETE_USER_REQUEST,
  DELETE_USER_SUCCESS,
  FETCH_USERS_FAILURE,
  FETCH_USERS_REQUEST,
  FETCH_USERS_SUCCESS,
  RESET_ADD_USER_ERROR,
  RESET_ADD_USER_SUCCESS,
  RESET_CHANGE_USER_ROLE_SUCCESS,
  RESET_DELETE_USER_SUCCESS,
} from './constants';

export const initialState = {
  adUsers: [],
  loading: false,
  error: null,
  changeUserRoleSuccess: false,
  loadingUser: false,
  errorUser: null,
  addUserSuccess: false,
  deleteUserSuccess: false,
  deleteUserError: null,
};

const adUserReducer = (state = initialState, action) => {
  switch (action.type) {
    case FETCH_USERS_REQUEST:
      return {
        ...state,
        loading: true,
        error: null,
      };
    case FETCH_USERS_SUCCESS:
      return {
        ...state,
        adUsers: action.payload,
        loading: false,
        error: null,
      };
    case FETCH_USERS_FAILURE:
      return {
        ...state,
        loading: false,
        error: action.payload,
      };
    case ADD_USER_REQUEST:
      return {
        ...state,
        loading: true,
        error: null,
      };
    case ADD_USER_SUCCESS:
      return {
        ...state,
        loading: false,
        error: null,
        addUserSuccess: true,
      };
    case ADD_USER_FAILURE:
      return {
        ...state,
        loading: false,
        error: action.payload,
        addUserSuccess: false,
      };
    case RESET_ADD_USER_SUCCESS:
      return {
        ...state,
        addUserSuccess: false,
      };
    case RESET_ADD_USER_ERROR:
      return {
        ...state,
        error: null,
      };
    case DELETE_USER_REQUEST:
      return {
        ...state,
        loading: true,
        deleteUserError: null, // Reset deleteUserError on DELETE_USER_REQUEST
      };
    case DELETE_USER_SUCCESS:
      return {
        ...state,
        loading: false,
        deleteUserSuccess: true, // Set deleteUserSuccess to true on successful deletion
      };
    case DELETE_USER_FAILURE:
      return {
        ...state,
        loading: false,
        deleteUserError: action.payload, // Set deleteUserError on deletion failure
      };
    case RESET_DELETE_USER_SUCCESS:
      return {
        ...state,
        deleteUserSuccess: false, // Reset deleteUserSuccess on RESET_DELETE_USER_SUCCESS
        deleteUserError: null, // Reset deleteUserError on RESET_DELETE_USER_SUCCESS
      };
    case CHANGE_USER_ROLE_REQUEST:
      return {
        ...state,
        loading: true,
        error: null,
      };
    case CHANGE_USER_ROLE_SUCCESS:
      return {
        ...state,
        loading: false,
        error: null,
        changeUserRoleSuccess: true,
      };
    case CHANGE_USER_ROLE_FAILURE:
      return {
        ...state,
        loading: false,
        error: action.payload,
        changeUserRoleSuccess: false,
      };
    case RESET_CHANGE_USER_ROLE_SUCCESS:
      return {
        ...state,
        changeUserRoleSuccess: false,
      };

    default:
      return state;
  }
};

export default adUserReducer;
