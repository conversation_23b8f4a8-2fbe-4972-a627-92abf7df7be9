import React from 'react';
import { <PERSON><PERSON>, Button, Box } from '@mui/material';
import { Link, useLocation } from 'react-router-dom';
import DashboardIcon from '@mui/icons-material/Dashboard';
import PeopleIcon from '@mui/icons-material/People';
import CardGiftcardIcon from '@mui/icons-material/CardGiftcard';
import SchoolIcon from '@mui/icons-material/School';
import HelpOutlineIcon from '@mui/icons-material/HelpOutline';
import BusinessIcon from '@mui/icons-material/Business';
import ArticleIcon from '@mui/icons-material/Article';

const navigationItems = [
  { text: 'Général', path: '/dashboard-general', icon: DashboardIcon },
  { text: 'Donateurs', path: '/dashboard-donors', icon: PeopleIcon },
  { text: 'Bénéficiaires', path: '/dashboard-beneficiaries', icon: PeopleIcon },
  { text: 'Donations', path: '/dashboard-donations', icon: CardGiftcardIcon },
  { text: '<PERSON><PERSON><PERSON>', path: '/dashboard-kafalat', icon: SchoolIcon },
  { text: 'Aide Complementaire', path: '/dashboard-aide', icon: HelpOutlineIcon },
  { text: 'EPS', path: '/dashboard-eps', icon: BusinessIcon },
];

export default function DashboardNavigation() {
  const location = useLocation();
  const currentPath = location.pathname;

  return (
    <Box sx={{ 
      backgroundColor: '#ffffff',  // Creamy background
      width: 'fit-content',
      py: 0.5,
      px: 0.7,
      borderRadius: '30px',
      display: 'inline-block',
    }}>
      <Stack 
        direction="row" 
        spacing={1} 
        alignItems="center"
      >
        {navigationItems.map((item) => {
          const Icon = item.icon;
          const isActive = currentPath === item.path;
          return (
            <Button
              key={item.path}
              variant="text"
              component={Link}
              to={item.path}
              startIcon={<Icon sx={{ 
                color: isActive ? '#fff' : '#000',
                fontSize: '1.5rem'
              }} />}
              sx={{
                textTransform: 'none',
                color: isActive ? '#fff' : '#000',
                backgroundColor: isActive ? '#000' : 'transparent',
                fontSize: '1rem',
                fontWeight: 500,
                padding: '6px 12px',
                borderRadius: '8px',
                minWidth: 'auto',
                '&:hover': {
                  backgroundColor: isActive ? 'rgba(0, 0, 0, 0.8)' : 'rgba(0, 0, 0, 0.08)',
                  color: isActive ? '#fff' : '#000'
                }
              }}
            >
              {item.text}
            </Button>
          );
        })}
      </Stack>
    </Box>
  );
} 